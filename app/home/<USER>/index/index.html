<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8">
  <meta name="google-site-verification" content="CB_qp4gkQWQmAb4nM_Co6tf2cmVw3h8FRceivL6oGx4">
  <title>{$system.sy_title}</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="keywords" content="{$system.sy_title},租号,租号玩,游戏租号,租号平台,租号网,吃鸡租号,CF租号,DNF租号,LOL租号,逆战租号">
  <meta name="description"
        content="{$system.sy_title}是国内专业的游戏帐号出租、租用平台，玩家无需帐号密码即可畅玩游戏极品账号，同时租号软件还有防外挂等安全防护功能，能从根源上保证游戏帐号安全，为游戏玩家打造一个安全、方便、快捷的游戏账号租赁交易平台。">
  <link rel="icon" href="" type="image/x-icon">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- Tailwind CSS -->
  <link href="/static/css/tailwind.min.css" rel="stylesheet">
  <!-- Font Awesome 图标库 -->
  <link rel="stylesheet" href="/static/css/font-awesome/all.min.css">

  <!-- 原有脚本引用 -->
  <script src="/static/da.js"></script>
  <script src="/static/da-web-fxIndexPvUv.js"></script>
  <script src="/static/core.js"></script>
  <script src="/static/common.js"></script>
  <script src="/static/public.js"></script>
  <script src="/static/default/core.js"></script>
  <script src="/static/default/index.js"></script>
  <script src="/static/plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>

  <!-- 在头部添加Swiper CSS -->
  <link rel="stylesheet" href="/static/plugins/swiper/swiper-bundle.min.css" />

  <style>
    /* 基础样式设置 */
    body {
      font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    /* 警告提示样式 */
    .hint_con {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      line-height: 30px;
      padding-left: 30px;
      border-radius: 8px;
      background: #FFF8F9 url(/static/images/hint.png) no-repeat;
      background-size: 15px 16px;
      background-position: 9px 50%;
      font-size: 12px;
      color: #777;
      box-sizing: border-box;
    }

    .hint_con span {
      color: #FE2B3D;
    }

    /* 游戏卡片悬停效果 */
    .game-card {
      transition: all 0.3s ease;
    }

    .game-card:hover {
      transform: translateY(-6px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }

    /* 轮播图导航按钮 */
    .carousel-nav {
      opacity: 0.6;
      transition: opacity 0.3s ease;
    }

    .carousel-nav:hover {
      opacity: 1;
    }

    /* 弹窗样式 */
    .alert_box_box {
      display: none;
      position: fixed;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      top: 0;
      z-index: 999;
      backdrop-filter: blur(5px);
    }

    .alert_box {
      display: none;
      width: 480px;
      max-width: 90%;
      position: fixed;
      background: white;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1000;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* 数量选择器 */
    .duration-input-group {
      display: flex;
      align-items: center;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      overflow: hidden;
      width: fit-content;
    }

    .duration-btn {
      width: 36px;
      height: 36px;
      border: none;
      background: #f3f4f6;
      color: #4b5563;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .duration-btn:hover {
      background: #e5e7eb;
      color: #1f2937;
    }

    #durationInput {
      width: 60px;
      border: none;
      text-align: center;
      font-size: 16px;
      padding: 8px 0;
    }

    /* 单位切换按钮 */
    .toggle-btn {
      padding: 8px 16px;
      border: 1px solid #e5e7eb;
      background: white;
      color: #6b7280;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;
      border-radius: 6px;
    }

    .toggle-btn.active {
      background: #3b82f6;
      color: white;
      border-color: #3b82f6;
    }

    .toggle-btn:hover:not(.active) {
      background: #f9fafb;
      border-color: #d1d5db;
    }

    /* Tab切换 */
    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    /* 轮播导航按钮优化 */
    .swiper-button-next:after, .swiper-button-prev:after {
      font-size: 16px !important;
      font-weight: bold;
    }

    .swiper-container {
      overflow: hidden;
      position: relative;
    }

    .swiper-button-next, .swiper-button-prev {
      z-index: 20 !important;
    }

    /* 修复精彩专栏样式 */
    .special_swiper .swiper-slide {
      height: 100%;
      box-sizing: border-box;
      padding: 4px;
    }

    .special_swiper .swiper-slide a {
      display: block;
      height: 100%;
      text-decoration: none;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .special_swiper .swiper-slide a:hover {
      transform: translateY(-4px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .special_swiper .swiper-slide img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      transform: none !important; /* 防止任何变换 */
      perspective: none !important; /* 移除透视效果 */
    }

    /* 确保轮播图布局正确 */
    .special_swiper {
      overflow: hidden;
      position: relative;
    }

    .special_swiper .swiper-wrapper {
      display: flex;
      align-items: stretch;
      height: 100%;
    }

    /* 确保导航按钮位于图片上方 */
    .special_swiper .swiper-button-next,
    .special_swiper .swiper-button-prev {
      z-index: 30;
    }

    /* 分页器样式优化 */
    .special_swiper .swiper-pagination {
      bottom: 8px !important;
      z-index: 20;
    }

    /* 精彩专栏完全重写 */
    .special_swiper {
      width: 100%;
      height: 280px;
      margin: 0 auto;
      position: relative;
      overflow: hidden;
    }

    .special_swiper .swiper-slide {
      width: 100%;
      height: 100%;
      padding: 8px;
      box-sizing: border-box;
    }

    .special_swiper .slide-content {
      width: 100%;
      height: 100%;
      display: block;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .special_swiper .slide-content:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .special_swiper .slide-content img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .special_swiper .empty-content {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f9fafb;
      color: #9ca3af;
    }

    /* 导航按钮样式 */
    .special_swiper .swiper-button-next,
    .special_swiper .swiper-button-prev {
      background-color: rgba(0, 0, 0, 0.3);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      opacity: 0.7;
      transition: opacity 0.3s ease;
    }

    .special_swiper .swiper-button-next:hover,
    .special_swiper .swiper-button-prev:hover {
      opacity: 1;
    }

    .special_swiper .swiper-button-next:after,
    .special_swiper .swiper-button-prev:after {
      font-size: 16px;
      font-weight: bold;
    }

    /* 分页器样式 */
    .special_swiper .swiper-pagination {
      bottom: 10px;
    }

    .special_swiper .swiper-pagination-bullet {
      background: rgba(255, 255, 255, 0.7);
      opacity: 1;
    }

    .special_swiper .swiper-pagination-bullet-active {
      background: white;
    }

    /* 新的精彩专栏样式 - 移除所有Swiper相关的样式 */
    #columns-container .column-item {
      backface-visibility: hidden;
      transform-style: preserve-3d;
      will-change: transform;
    }

    #columns-container .column-item img {
      transform: translateZ(0);
      backface-visibility: hidden;
    }

    /* 在小屏幕上隐藏除了当前显示的专栏项外的所有项 */
    @media (max-width: 640px) {
      #columns-container {
        display: block;
        position: relative;
        overflow: hidden;
      }

      #columns-container .column-item {
        display: none;
        width: 100%;
      }

      #columns-container .column-item.active {
        display: block;
      }
    }

    /* 服务特点卡片样式优化 */
    .service-feature {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
    }

    .service-feature:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
  </style>
</head>

<body class="bg-gray-50">
<!-- 顶部导航 -->
<header class="bg-white shadow">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16 items-center">
      <div class="flex">
        <div class="flex-shrink-0 flex items-center">
          <a href="/">
            <img class="h-8 w-auto" src="{$system.sy_logo}" alt="{$system.sy_title}">
          </a>
        </div>
      </div>

      <!-- 导航菜单 -->
      <nav class="hidden md:flex space-x-8">
        <a href="/" class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
        <a href="/user/index/feedback" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">工单管理</a>
        <a href="/user/CdkExchange/index" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">卡密兑换</a>
        <a href="/user/index/notice" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">帮助中心</a>
        <a href="/home/<USER>" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">全部游戏</a>
        <a href="/user/index/onshop" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">用户中心</a>
      </nav>

      <!-- 用户登录状态 -->
      <div class="hidden md:flex items-center">
        {if condition="$isLoggedIn"}
        <a href="/user/index/onshop" class="flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
            <span class="mr-2 h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
              <i class="fas fa-user"></i>
            </span>
          <span>{$user.us_username}</span>
        </a>
        {else /}
        <a href="/user/login" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
          登录
        </a>
        {/if}
      </div>

      <!-- 移动端菜单按钮 -->
      <div class="flex items-center md:hidden">
        <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
          <span class="sr-only">打开菜单</span>
          <i class="fas fa-bars"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- 移动端菜单 -->
  <div class="md:hidden hidden" id="mobile-menu">
    <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
      <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50">首页</a>
      <a href="/user/index/feedback" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">工单管理</a>
      <a href="/user/CdkExchange/index" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">卡密兑换</a>
      <a href="/user/index/notice" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">帮助中心</a>
      <a href="/home/<USER>" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">全部游戏</a>
      <a href="/user/index/onshop" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">用户中心</a>

      <!-- 移动端用户状态 -->
      {if condition="$isLoggedIn"}
      <div class="pt-4 pb-3 border-t border-gray-200">
        <div class="flex items-center px-5">
          <div class="flex-shrink-0">
            <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
              <i class="fas fa-user text-gray-500"></i>
            </div>
          </div>
          <div class="ml-3">
            <div class="text-base font-medium text-gray-800">{$user.us_username}</div>
          </div>
        </div>
        <div class="mt-3 px-2 space-y-1">
          <a href="/user/index/onshop" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">用户中心</a>
        </div>
      </div>
      {else /}
      <div class="pt-4 pb-3 border-t border-gray-200">
        <a href="/user/login" class="block px-3 py-2 rounded-md text-base font-medium text-blue-600 hover:bg-gray-50">
          登录
        </a>
      </div>
      {/if}
    </div>
  </div>
</header>

<!-- 提示信息 -->
<div class="bg-rose-50 py-2">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="hint_con">
      根据相关法律法规的要求，我们<span>严禁向未成年人提供游戏账号租售服务</span>，如果您是未成年人，请退出。
    </div>
  </div>
</div>

<!-- 搜索区域 -->
<div class="bg-white py-4 shadow-sm">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col md:flex-row items-center justify-between">
      <a href="/" class="mb-4 md:mb-0">
        <img src="{$system.sy_logo}" alt="{$system.sy_title}" class="h-16 w-auto">
      </a>
      <div class="w-full md:w-1/2 lg:w-2/5">
        <form name="search" action="/home/<USER>" method="get" class="relative">
          <input id="keyWords" type="text" name="keyWords" value="" placeholder="输入关键词为您查找" class="w-full pl-4 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          <button id="search" type="submit" class="absolute right-0 top-0 h-full px-4 flex items-center justify-center text-gray-400 hover:text-blue-600">
            <i class="fas fa-search text-lg"></i>
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- 主要内容 -->
<main>
  <!-- 主区域布局 -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- 左侧导航 -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <h3 class="text-lg font-medium py-4 px-6 bg-gray-50 border-b border-gray-200">热门游戏</h3>
          <ul class="divide-y divide-gray-200">
            {volist name="goodsDisplay" id="item"}
            <li>
              <a id="{$item['id']}" class="block px-6 py-4 hover:bg-gray-50 transition duration-150 buy">
                <div class="flex items-center justify-between">
                  <span class="text-gray-700 truncate" style="max-width: 80%;">{$item['goods_name']}</span>
                  <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
              </a>
            </li>
            {/volist}
          </ul>
        </div>

        <!-- 用户信息卡片 -->
        <div class="mt-6 bg-white rounded-lg shadow overflow-hidden">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  <img src="https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/tx1.png" alt="用户头像" class="h-full w-full object-cover">
                </div>
              </div>
              <div class="ml-4">
                {if condition="$isLoggedIn"}
                <h3 class="text-lg font-medium text-gray-900">{$user.us_username}</h3>
                <p class="text-sm text-gray-500">欢迎回来</p>
                {else /}
                <h3 class="text-lg font-medium text-gray-900">欢迎您</h3>
                <p class="text-sm text-gray-500">您还没有登录哦</p>
                {/if}
              </div>
            </div>

            <div class="mt-6">
              {if condition="$isLoggedIn"}
              <a href="/user/index/onshop" class="block w-full text-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                用户中心
              </a>
              {else /}
              <a href="/user/login" class="block w-full text-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                用户登录
              </a>
              {/if}
            </div>
          </div>
        </div>

        <!-- 快捷操作卡片 -->
        <div class="mt-6 bg-white rounded-lg shadow overflow-hidden">
          <h3 class="text-lg font-medium py-4 px-6 bg-gray-50 border-b border-gray-200">快捷操作</h3>
          <div class="p-4">
            <div class="grid grid-cols-2 gap-4">
              <a href="/user/index/feedback" class="flex flex-col items-center justify-center p-4 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition duration-300">
                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-2">
                  <i class="fas fa-ticket-alt text-blue-600"></i>
                </div>
                <span class="text-sm font-medium text-gray-700">工单管理</span>
              </a>

              <a href="/user/CdkExchange/index" class="flex flex-col items-center justify-center p-4 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition duration-300">
                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-2">
                  <i class="fas fa-key text-green-600"></i>
                </div>
                <span class="text-sm font-medium text-gray-700">卡密兑换</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧主内容 -->
      <div class="lg:col-span-3">
        <!-- 轮播图 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="swiper-container swiper_banner h-72 sm:h-80 md:h-96 relative">
            <div class="swiper-wrapper h-full">
              {foreach $banner as $val}
              <div class="swiper-slide h-full relative">
                <!-- 轮播图骨架屏 -->
                <div class="banner-skeleton absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200">
                  <div class="banner-shimmer"></div>
                  <div class="banner-content">
                    <div class="banner-icon">
                      <i class="fas fa-image text-gray-400 text-4xl"></i>
                    </div>
                    <div class="banner-text">轮播图加载中...</div>
                  </div>
                </div>
                <a href="/">
                  <img class="banner-image w-full h-full object-cover opacity-0 transition-all duration-700"
                       data-src="{$val.ba_url}"
                       alt="轮播图">
                </a>
              </div>
              {/foreach}

              <!-- 如果没有轮播图数据，显示一个默认项 -->
              {if condition="empty($banner)"}
              <div class="swiper-slide h-full">
                <a href="/">
                  <img class="w-full h-full object-cover" src="/static/images/default-banner.jpg" alt="默认轮播图">
                </a>
              </div>
              {/if}
            </div>

            <!-- 轮播图导航按钮调整 -->
            <div class="swiper-button-prev banner-prev !text-white !opacity-70 hover:!opacity-100 !w-10 !h-10 !rounded-full !bg-black/30"></div>
            <div class="swiper-button-next banner-next !text-white !opacity-70 hover:!opacity-100 !w-10 !h-10 !rounded-full !bg-black/30"></div>
            <div class="swiper-pagination swiper-pagination-banner !bottom-3"></div>
          </div>
        </div>

        <!-- 服务特点 - 移到公告与帮助中心上方 -->
        <div class="mt-6 bg-white rounded-lg shadow overflow-hidden">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-0 divide-y md:divide-y-0 md:divide-x divide-gray-200">
            <div class="p-4 service-feature">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <i class="fas fa-shield-alt text-blue-600 text-sm"></i>
                  </div>
                </div>
                <div class="ml-3">
                  <h3 class="text-base font-medium text-gray-900">屏蔽外挂</h3>
                  <p class="mt-1 text-xs text-gray-500">系统级安全防护 实时有效拦截外挂</p>
                </div>
              </div>
            </div>

            <div class="p-4 service-feature">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                    <i class="fas fa-lock text-green-600 text-sm"></i>
                  </div>
                </div>
                <div class="ml-3">
                  <h3 class="text-base font-medium text-gray-900">账号加密</h3>
                  <p class="mt-1 text-xs text-gray-500">技术手段账号加密 保障用户账号安全</p>
                </div>
              </div>
            </div>

            <div class="p-4 service-feature">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                    <i class="fas fa-headset text-yellow-600 text-sm"></i>
                  </div>
                </div>
                <div class="ml-3">
                  <h3 class="text-base font-medium text-gray-900">售后仲裁</h3>
                  <p class="mt-1 text-xs text-gray-500">售后客服处理机制 公平交易互惠互利</p>
                </div>
              </div>
            </div>

            <div class="p-4 service-feature">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                    <i class="fas fa-user-plus text-indigo-600 text-sm"></i>
                  </div>
                </div>
                <div class="ml-3">
                  <h3 class="text-base font-medium text-gray-900">免费注册</h3>
                  <p class="mt-1 text-xs text-gray-500">零成本发布账号 轻松实现账号价值</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 公告与帮助 -->
        <div class="mt-6 bg-white rounded-lg shadow overflow-hidden">
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex" aria-label="Tabs">
              <button class="text-blue-600 border-blue-500 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm tab-trigger active" data-tab="announcements">
                系统公告
              </button>
              <button class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-6 border-b-2 border-transparent font-medium text-sm tab-trigger" data-tab="help">
                帮助中心
              </button>
              <a href="/user/index/notice" class="ml-auto flex items-center px-6 text-sm text-gray-500 hover:text-blue-600">
                更多 <i class="fas fa-angle-right ml-1"></i>
              </a>
            </nav>
          </div>

          <!-- 系统公告内容 -->
          <div class="p-4 tab-content active" id="announcements">
            <ul>
              {volist name="announcementList" id="announcement"}
              <li class="py-2">
                <a href="/user/index/noticeList?id={$announcement.id}" target="_blank" class="hover:text-blue-600 flex items-center">
                  <span class="text-red-500 whitespace-nowrap">【公告】</span>
                  <span class="ml-2 text-gray-700 truncate">{$announcement.not_title}</span>
                </a>
              </li>
              {/volist}
            </ul>
          </div>

          <!-- 帮助中心内容 -->
          <div class="p-4 tab-content" id="help">
            <ul>
              {volist name="helpCenterList" id="help"}
              <li class="py-2">
                <a href="/user/index/noticeList?id={$help.id}" target="_blank" class="hover:text-blue-600 flex items-center">
                  <span class="text-blue-500 whitespace-nowrap">【帮助】</span>
                  <span class="ml-2 text-gray-700 truncate">{$help.not_title}</span>
                </a>
              </li>
              {/volist}
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 游戏推荐区域 -->
    <div class="mt-12">
      <div class="flex items-start justify-between mb-6">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">游戏大全</h2>
          <p class="mt-2 text-gray-500">游戏大全列表，各种账号任你挑选</p>
        </div>
      </div>

      <!-- 游戏列表 -->
      <div id="game-list" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <?php foreach ($goods as $item): ?>
        <div class="game-card bg-white rounded-lg shadow hover:shadow-lg overflow-hidden cursor-pointer buy" id="<?php echo htmlentities($item['id']); ?>">
          <div class="h-40 overflow-hidden relative">
            <!-- 骨架屏占位 -->
            <div class="skeleton-placeholder absolute inset-0 bg-gray-200">
              <div class="skeleton-shimmer"></div>
              <div class="skeleton-content">
                <div class="skeleton-game-icon">
                  <i class="fas fa-gamepad text-gray-400"></i>
                </div>
                <div class="skeleton-text">
                  <div class="skeleton-line skeleton-line-1"></div>
                  <div class="skeleton-line skeleton-line-2"></div>
                </div>
              </div>
            </div>
            <!-- 真实图片 -->
            <img class="game-image w-full h-full object-cover transition-all duration-500 opacity-0"
                 data-src="<?php echo htmlentities($item['goods_img']); ?>"
                 alt="<?php echo htmlentities($item['goods_name']); ?>">
          </div>
          <div class="p-5">
            <h3 class="font-semibold text-gray-900 mb-2 truncate"><?php echo htmlentities($item['goods_name']); ?></h3>

            <div class="mt-2 space-y-1">
              <?php if($accountStatus[$item['id']]['offline']): ?>
              <div class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                会员免费（离线）
              </div>
              <?php endif; ?>

              <?php if($accountStatus[$item['id']]['online'] && isset($comboPrices[$item['id']])): ?>
              <div class="text-gray-700 text-sm">
                <?php if($comboPrices[$item['id']]['day_price'] > 0): ?>
                <div class="flex items-center mb-1">
                  <span class="text-gray-500 mr-2">日租：</span>
                  <span class="font-medium text-red-600">￥<?php echo htmlentities($comboPrices[$item['id']]['day_price']); ?></span>
                </div>
                <?php endif; ?>

                <?php if($comboPrices[$item['id']]['hour_price'] > 0): ?>
                <div class="flex items-center">
                  <span class="text-gray-500 mr-2">时租：</span>
                  <span class="font-medium text-red-600">￥<?php echo htmlentities($comboPrices[$item['id']]['hour_price']); ?></span>
                </div>
                <?php endif; ?>
              </div>
              <?php endif; ?>

              <?php if(isset($accountStatus[$item['id']])): ?>
              <?php if(!$accountStatus[$item['id']]['online'] && !$accountStatus[$item['id']]['offline']): ?>
              <div class="text-gray-500 text-sm">暂无套餐出售</div>
              <?php endif; ?>
              <?php else: ?>
              <div class="text-gray-500 text-sm">暂无账号信息</div>
              <?php endif; ?>
            </div>

            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
              <div class="flex space-x-2">
                <?php if($accountStatus[$item['id']]['online']): ?>
                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">在线</span>
                <?php endif; ?>

                <?php if($accountStatus[$item['id']]['offline']): ?>
                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">离线</span>
                <?php endif; ?>
              </div>
              <button class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                查看详情
              </button>
            </div>
          </div>
        </div>
        <?php endforeach; ?>
      </div>
    </div>

    <!-- 精彩专栏 -->
    <div class="mt-12 bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-5 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">精彩专栏</h2>
        <p class="mt-1 text-sm text-gray-500">精彩信息专栏一定不要错过的内容</p>
      </div>

      <div class="p-6">
        <div id="columns-container" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {if condition="!empty($forumSections)"}
          {volist name="forumSections" id="section"}
          <div class="column-item">
            {if $forumLoginInfo.isLoggedIn}
            <a href="http://xiuluo.zuhaom.com/phone_register.php?type=login&username={$forumLoginInfo.username}&password={$forumLoginInfo.password}&redirect_url=http://xiuluo.zuhaom.com/?forum-{$section.fid}.htm" target="_blank" class="block h-64 rounded-lg overflow-hidden shadow hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 relative">
              <!-- 专栏骨架屏 -->
              <div class="column-skeleton absolute inset-0 bg-gray-200">
                <div class="column-shimmer"></div>
                <div class="column-content">
                  <div class="column-icon">
                    <i class="fas fa-newspaper text-gray-400 text-3xl"></i>
                  </div>
                  <div class="column-title">{$section.name}</div>
                </div>
              </div>
              <img class="column-image w-full h-full object-cover opacity-0 transition-all duration-500"
                   data-src="{$section.icon_url}"
                   alt="{$section.name}">
            </a>
            {else}
            <a href="{:url('home/index/forumLoginGuide', ['url' => 'http://xiuluo.zuhaom.com/?forum-'.$section.fid.'.htm'])}" target="_blank" class="block h-64 rounded-lg overflow-hidden shadow hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 relative">
              <!-- 专栏骨架屏 -->
              <div class="column-skeleton absolute inset-0 bg-gray-200">
                <div class="column-shimmer"></div>
                <div class="column-content">
                  <div class="column-icon">
                    <i class="fas fa-newspaper text-gray-400 text-3xl"></i>
                  </div>
                  <div class="column-title">{$section.name}</div>
                </div>
              </div>
              <img class="column-image w-full h-full object-cover opacity-0 transition-all duration-500"
                   data-src="{$section.icon_url}"
                   alt="{$section.name}">
            </a>
            {/if}
          </div>
          {/volist}
          {else}
          <div class="column-item col-span-full flex justify-center items-center h-64 bg-gray-50 rounded-lg">
            <div class="text-center text-gray-400">
              <i class="fas fa-images text-4xl mb-2"></i>
              <p>暂无专栏内容</p>
            </div>
          </div>
          {/if}
        </div>

        <!-- 仅在移动设备上显示翻页按钮 -->
        <div class="flex justify-center mt-4 md:hidden">
          <button id="prev-btn" class="px-4 py-2 bg-gray-100 rounded-l-lg text-gray-700 hover:bg-gray-200 focus:outline-none">
            <i class="fas fa-chevron-left"></i>
          </button>
          <button id="next-btn" class="px-4 py-2 bg-gray-100 rounded-r-lg text-gray-700 hover:bg-gray-200 focus:outline-none">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</main>

<!-- 页脚 -->
<footer class="bg-white mt-12 border-t border-gray-200">
  <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 text-center text-gray-500 text-sm">
    <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">备案号：蜀ICP备2024116123</a>
  </div>
</footer>

<!-- 购买弹窗 -->
<div class="alert_box_box"></div>

<div class="alert_box rounded-xl overflow-hidden">
  <div class="bg-gray-100 py-4 px-6 border-b border-gray-200">
    <h3 class="text-lg font-medium text-gray-900 text-center">购买上车 · 在线工单 · 给你更多售后保障</h3>
  </div>

  <div class="p-6 bg-white">
    <div class="flex flex-col md:flex-row gap-6">
      <div class="w-32 h-32 mx-auto md:mx-0 rounded-lg overflow-hidden flex-shrink-0">
        <img src="" alt="" id="goods_img" class="w-full h-full object-cover">
      </div>
      <div class="flex-1">
        <h3 class="text-xl font-bold text-gray-900 mb-4 alert_box_title">商品名称</h3>

        <div class="alert_box_price_info mb-4">
          <div class="alert_box_money text-3xl font-bold text-red-600">￥0.00</div>
          <div class="price_type_tag text-gray-500 text-sm"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 在线账号租用选项 -->
  <div class="py-5 px-6 bg-gray-50 border-t border-gray-200 online_options">
    <h4 class="font-medium text-gray-900 mb-2">请选择租用时长</h4>
    <p class="text-sm text-red-600 mb-4">租用时长默认7，可自行更改为自己想租用的时长</p>

    <div class="flex flex-wrap gap-4 items-center">
      <div class="duration-input-group">
        <button class="duration-btn minus">
          <i class="fas fa-minus"></i>
        </button>
        <input id="durationInput" value="7" min="1" class="focus:outline-none">
        <button class="duration-btn plus">
          <i class="fas fa-plus"></i>
        </button>
      </div>

      <div class="flex gap-2">
        <button class="toggle-btn active" data-unit="day">按天</button>
        <button class="toggle-btn" data-unit="hour">按小时</button>
        <input type="radio" name="unit" value="day" checked style="display: none;">
        <input type="radio" name="unit" value="hour" style="display: none;">
      </div>
    </div>
  </div>

  <div class="p-6 bg-white border-t border-gray-200">
    <div class="space-y-3">
      <button class="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200" id="submitOnline">
        独享账号在线出租付款
      </button>
      <button class="w-full py-3 px-4 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition duration-200" id="submitOffline">
        离线账号购买
      </button>
    </div>
  </div>
</div>

<!-- 系统JS -->
<div>{$system.sy_js|raw}</div>
{include file="../app/home/<USER>/index/float.html" /}

<!-- 骨架屏样式 -->
<link rel="stylesheet" href="/static/css/skeleton-loader.css">
<!-- 无图片占位样式 -->
<link rel="stylesheet" href="/static/css/no-image-placeholder.css">

<script src="/static/js/alert.js"></script>
<script src="/static/js/jquery.min.js"></script>
<script src="/static/plugins/swiper/swiper-bundle.min.js"></script>
<!-- 无图片处理脚本 -->
<script src="/static/js/no-image-handler.js"></script>
<!-- 骨架屏懒加载脚本 -->
<script src="/static/js/skeleton-lazy-loader.js"></script>
<script>
  // 移动端菜单控制
  document.getElementById('mobile-menu-button').addEventListener('click', function() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu.classList.contains('hidden')) {
      mobileMenu.classList.remove('hidden');
    } else {
      mobileMenu.classList.add('hidden');
    }
  });

  // 标签页切换
  document.addEventListener('DOMContentLoaded', function() {
    const tabTriggers = document.querySelectorAll('.tab-trigger');

    tabTriggers.forEach(trigger => {
      trigger.addEventListener('click', function() {
        // 移除所有标签页的激活状态
        tabTriggers.forEach(t => {
          t.classList.remove('text-blue-600', 'border-blue-500');
          t.classList.add('text-gray-500', 'border-transparent');
        });

        // 添加当前标签页的激活状态
        this.classList.add('text-blue-600', 'border-blue-500');
        this.classList.remove('text-gray-500', 'border-transparent');

        // 隐藏所有内容
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
          content.classList.remove('active');
          content.classList.add('hidden');
        });

        // 显示对应内容
        const tabId = this.getAttribute('data-tab');
        const tabContent = document.getElementById(tabId);
        if (tabContent) {
          tabContent.classList.add('active');
          tabContent.classList.remove('hidden');
        }
      });
    });

    // 初始化主轮播图 - 延迟初始化以确保懒加载脚本已准备就绪
    setTimeout(() => {
      if (document.querySelector('.swiper_banner')) {
        const bannerSlides = document.querySelectorAll('.swiper_banner .swiper-slide');
        const bannerSwiperOptions = {
          loop: bannerSlides.length > 1,
          autoplay: bannerSlides.length > 1 ? {
            delay: 5000,
            disableOnInteraction: false,
          } : false,
          pagination: {
            el: '.swiper-pagination-banner',
            clickable: true,
          },
          navigation: {
            nextEl: '.banner-next',
            prevEl: '.banner-prev',
          },
          // 添加懒加载支持
          on: {
            init: function() {
              // 轮播图初始化后，手动触发第一张图片的懒加载
              const firstSlide = this.slides[0];
              if (firstSlide) {
                const img = firstSlide.querySelector('img[data-src]');
                if (img && window.skeletonLoader) {
                  window.skeletonLoader.loadImageManually(img);
                }
              }
            },
            slideChange: function() {
              // 切换到新幻灯片时，加载当前和下一张图片
              const currentSlide = this.slides[this.activeIndex];
              const nextSlide = this.slides[this.activeIndex + 1];

              [currentSlide, nextSlide].forEach(slide => {
                if (slide) {
                  const img = slide.querySelector('img[data-src]');
                  if (img && window.skeletonLoader) {
                    window.skeletonLoader.loadImageManually(img);
                  }
                }
              });
            }
          }
        };

        const bannerSwiper = new Swiper('.swiper_banner', bannerSwiperOptions);

        // 如果只有一张图，隐藏导航按钮和分页
        if (bannerSlides.length <= 1) {
          const navButtons = document.querySelectorAll('.banner-prev, .banner-next, .swiper-pagination-banner');
          navButtons.forEach(button => {
            button.style.display = 'none';
          });
        }
      }
    }, 500); // 延迟500ms确保懒加载脚本已初始化

    // 将精彩专栏转换为Swiper轮播图，按照460*215的图片比例调整
    function setupSpecialColumns() {
      // 获取原有的内容容器
      const columnsContainer = document.getElementById('columns-container');
      if (!columnsContainer) return;

      // 获取所有专栏项
      const columnItems = columnsContainer.querySelectorAll('.column-item');
      if (columnItems.length === 0) return;

      // 创建Swiper容器结构
      const swiperContainer = document.createElement('div');
      swiperContainer.className = 'swiper-container special_swiper';
      swiperContainer.style.height = 'auto'; // 高度自适应

      const swiperWrapper = document.createElement('div');
      swiperWrapper.className = 'swiper-wrapper';

      // 将原有的专栏项移动到swiper-wrapper中，调整为正确的宽高比例
      columnItems.forEach(item => {
        const slide = document.createElement('div');
        slide.className = 'swiper-slide';

        const link = item.querySelector('a').cloneNode(true);

        // 设置正确的高宽比例
        link.style.height = 'auto';
        link.style.paddingBottom = '46.7%'; // 215/460 ≈ 0.467，设置宽高比
        link.style.position = 'relative';

        const img = link.querySelector('img');
        if (img) {
          img.style.position = 'absolute';
          img.style.top = '0';
          img.style.left = '0';
          img.style.width = '100%';
          img.style.height = '100%';
          img.style.objectFit = 'cover';

          // 保持懒加载属性
          if (img.dataset.src) {
            // 保持data-src属性用于懒加载
            img.src = ''; // 清空src，让懒加载处理
            img.style.opacity = '0'; // 初始透明
            img.classList.add('column-image'); // 添加类名用于识别
          }
        }

        // 保持骨架屏结构
        const skeleton = link.querySelector('.column-skeleton');
        if (skeleton) {
          // 骨架屏保持不变
        }

        slide.appendChild(link);
        swiperWrapper.appendChild(slide);
      });

      swiperContainer.appendChild(swiperWrapper);

      // 添加分页和导航
      const pagination = document.createElement('div');
      pagination.className = 'swiper-pagination';

      const prevButton = document.createElement('div');
      prevButton.className = 'swiper-button-prev';

      const nextButton = document.createElement('div');
      nextButton.className = 'swiper-button-next';

      swiperContainer.appendChild(pagination);
      swiperContainer.appendChild(prevButton);
      swiperContainer.appendChild(nextButton);

      // 替换原有容器
      columnsContainer.parentNode.replaceChild(swiperContainer, columnsContainer);

      // 移除原有的移动端导航按钮
      const mobileNav = document.querySelector('.flex.justify-center.mt-4.md\\:hidden');
      if (mobileNav) {
        mobileNav.remove();
      }

      // 向页面添加专栏轮播图样式
      const styleEl = document.createElement('style');
      styleEl.textContent = `
          .special_swiper .swiper-slide {
            height: auto;
            box-sizing: border-box;
            padding: 10px;
          }

          .special_swiper .swiper-slide a {
            display: block;
            position: relative;
            height: 0;
            padding-bottom: 46.7%;
            width: 100%;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          }

          .special_swiper .swiper-slide a:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.15);
          }

          .special_swiper .swiper-slide img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .special_swiper .swiper-pagination {
            bottom: 0px;
          }

          .special_swiper .swiper-button-next,
          .special_swiper .swiper-button-prev {
            color: #fff;
            background: rgba(0, 0, 0, 0.3);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-top: -18px;
          }

          .special_swiper .swiper-button-next:after,
          .special_swiper .swiper-button-prev:after {
            font-size: 16px;
          }
        `;
      document.head.appendChild(styleEl);

      // 初始化Swiper
      return new Swiper('.special_swiper', {
        slidesPerView: 1,
        spaceBetween: 20,
        loop: columnItems.length > 1,
        autoplay: columnItems.length > 1 ? {
          delay: 4000,
          disableOnInteraction: false
        } : false,
        pagination: {
          el: '.special_swiper .swiper-pagination',
          clickable: true
        },
        navigation: {
          nextEl: '.special_swiper .swiper-button-next',
          prevEl: '.special_swiper .swiper-button-prev'
        },
        breakpoints: {
          640: {
            slidesPerView: 2,
            spaceBetween: 20
          },
          768: {
            slidesPerView: 3,
            spaceBetween: 20
          },
          1024: {
            slidesPerView: 4,
            spaceBetween: 20
          }
        },
        on: {
          init: function() {
            // 如果只有一张或没有图片，隐藏导航和分页
            if (columnItems.length <= 1) {
              document.querySelectorAll(
                      '.special_swiper .swiper-button-next, .special_swiper .swiper-button-prev, .special_swiper .swiper-pagination'
              ).forEach(el => el.style.display = 'none');
            }

            // 初始化后，重新观察所有懒加载图片
            setTimeout(() => {
              if (window.skeletonLoader) {
                const lazyImages = this.el.querySelectorAll('img[data-src]');
                lazyImages.forEach(img => {
                  window.skeletonLoader.observer.observe(img);
                });

                // 手动加载可见的图片
                const visibleSlides = this.slides.slice(0, this.params.slidesPerView);
                visibleSlides.forEach(slide => {
                  const img = slide.querySelector('img[data-src]');
                  if (img) {
                    window.skeletonLoader.loadImageManually(img);
                  }
                });
              }
            }, 100);
          },
          slideChange: function() {
            // 切换幻灯片时，加载新可见的图片
            if (window.skeletonLoader) {
              const visibleSlides = this.slides.slice(this.activeIndex, this.activeIndex + this.params.slidesPerView + 1);
              visibleSlides.forEach(slide => {
                if (slide) {
                  const img = slide.querySelector('img[data-src]');
                  if (img) {
                    window.skeletonLoader.loadImageManually(img);
                  }
                }
              });
            }
          }
        }
      });
    }

    // 初始化精彩专栏轮播 - 延迟初始化确保懒加载脚本准备就绪
    setTimeout(() => {
      const specialSwiper = setupSpecialColumns();
    }, 600); // 比轮播图稍晚一点初始化
  });

  // 购买弹窗处理
  (function () {
    let yhjId = null; // 优惠券ID始终为null
    let duration;
    let unit;
    let goodsId;
    let isOfflinePurchase = false;
    let comBoData = [];
    let currentPurchaseType = 'online'; // 'online' 或 'offline'

    // 处理无定价的情况
    function handleNoPricing() {
      $(".alert_box_money").html("暂无定价");
      $(".alert_box_money").after('<div class="text-red-600 text-sm mt-2">该商品暂未设置价格，请联系客服</div>');
      $("#submitOnline, #submitOffline").prop("disabled", true).addClass("opacity-50");
    }

    // 处理有定价的情况
    function handlePricing(res) {
      // 设置默认为在线账号模式
      currentPurchaseType = 'online';

      // 显示在线账号选项
      $(".online_options").show();

      // 设置默认为按小时计费
      $("input[name='unit'][value='hour']").prop('checked', true);
      $(".toggle-btn[data-unit='hour']").addClass('active');
      $(".toggle-btn[data-unit='day']").removeClass('active');

      // 设置默认7小时
      $("#durationInput").val(7);

      // 更新初始价格
      const defaultPrice = parseFloat(res.comBo[0].hour_price) * 7;
      $(".alert_box_money").html(`￥${defaultPrice.toFixed(2)}`);
      $(".price_type_tag").text("(7小时)");

      // 启用按钮
      $("#submitOnline, #submitOffline").prop("disabled", false).removeClass("opacity-50");
    }

    // 更新价格显示
    function updatePrice() {
      if (currentPurchaseType === 'offline') {
        // 显示离线账号固定价格
        const offlinePrice = comBoData[0]?.offline_price || 0;
        $(".alert_box_money").html(`￥${offlinePrice.toFixed(2)}`);
        $(".price_type_tag").text("(永久账号)");
      } else {
        // 显示在线账号租用价格
        const duration = parseInt($("#durationInput").val()) || 0;
        const unit = $("input[name='unit']:checked").val();

        if (!comBoData || !comBoData[0]) return;

        const price = unit === 'day'
                ? parseFloat(comBoData[0].day_price)
                : parseFloat(comBoData[0].hour_price);

        const totalPrice = price * duration;
        $(".alert_box_money").html(`￥${totalPrice.toFixed(2)}`);
        $(".price_type_tag").text(`(${duration}${unit === 'day' ? '天' : '小时'})`);
      }
    }

    // 监听时长输入变化
    $("#durationInput").on('input', updatePrice);

    // 监听单位切换
    $("input[name='unit']").on('change', updatePrice);

    // 切换购买类型
    function switchPurchaseType(type) {
      currentPurchaseType = type;
      $(".online_options").toggle(type === 'online');
      updatePrice();
    }

    // 绑定事件处理
    $(document).ready(function() {
      // 点击购买按钮
      $(document).on('click', '.buy', function() {
        goodsId = this.id;

        // 修改为请求新的URL
        window.location.href = `/home/<USER>/goodsDetail?id=${goodsId}`;
      });

      // 点击遮罩层关闭弹窗
      $(".alert_box_box").click(function() {
        $(".alert_box").fadeOut(300);
        $(".alert_box_box").fadeOut(300);
        // 重置状态
        currentPurchaseType = 'online';
        $(".online_options").show();
        $(".no-price-notice").remove();
      });

      // 离线账号购买按钮点击
      $("#submitOffline").click(function() {
        $.ajax({
          url: "/user/index/offlineOrder",
          type: "post",
          dataType: "json",
          data: {
            goodsId: goodsId,
            goods_name: $(".alert_box_title").text()
          },
          success: function(res) {
            if (res.src) {
              window.location.href = res.src;
            } else {
              createAlert("red", res.msg || "购买失败，请重试", 0);
            }
          },
          error: function() {
            createAlert("red", "网络错误，请重试", 0);
          }
        });
      });

      // 在线账号购买按钮点击
      $("#submitOnline").click(function() {
        duration = $("#durationInput").val();
        unit = $("input[name='unit']:checked").val();

        if (!duration || duration <= 0) {
          createAlert("red", "请输入有效的租用时长", 0);
          return;
        }

        $.ajax({
          url: "/user/index/order",
          type: "post",
          dataType: "json",
          data: {
            goodsId: goodsId,
            duration: duration,
            unit: unit,
          },
          success: function(res) {
            if (res.src) {
              window.location.href = res.src;
            } else {
              createAlert("red", res.msg || "购买失败，请重试", 0);
            }
          },
          error: function() {
            createAlert("red", "网络错误，请重试", 0);
          }
        });
      });
    });

    // 初始化计数器控制
    const durationInput = document.getElementById('durationInput');
    const minusBtn = document.querySelector('.minus');
    const plusBtn = document.querySelector('.plus');

    // 减少按钮点击事件
    minusBtn.addEventListener('click', () => {
      let value = parseInt(durationInput.value) || 0;
      if (value > 1) {
        durationInput.value = value - 1;
        updatePrice();
      }
    });

    // 增加按钮点击事件
    plusBtn.addEventListener('click', () => {
      let value = parseInt(durationInput.value) || 0;
      durationInput.value = value + 1;
      updatePrice();
    });

    // 直接输入验证
    durationInput.addEventListener('input', () => {
      let value = durationInput.value.replace(/[^\d]/g, '');
      durationInput.value = value;
      updatePrice();
    });

    durationInput.addEventListener('blur', () => {
      let value = parseInt(durationInput.value) || 1;
      if (value < 1) value = 1;
      durationInput.value = value;
      updatePrice();
    });

    // 添加切换按钮的事件处理
    const toggleBtns = document.querySelectorAll('.toggle-btn');
    toggleBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        // 移除所有按钮的active类
        toggleBtns.forEach(b => b.classList.remove('active'));
        // 添加当前按钮的active类
        btn.classList.add('active');
        // 更新隐藏的radio input
        const unit = btn.dataset.unit;
        document.querySelector(`input[name="unit"][value="${unit}"]`).checked = true;
        // 触发change事件以更新价格
        document.querySelector('input[name="unit"]:checked').dispatchEvent(new Event('change'));
      });
    });
  })();

  // 论坛帖子实现函数 - 使用ThinkPHP模板引擎传值 + 图片尺寸优化
  function initForumPosts() {
    console.log('开始创建论坛帖子区域');

    // 1. 找到游戏大全区域
    var gameListSection = document.querySelector('.mt-12 h2.text-2xl.font-bold.text-gray-900');

    if (gameListSection && gameListSection.textContent.trim() === '游戏大全') {
      var gameListContainer = gameListSection.closest('.mt-12');

      // 2. 创建论坛帖子区域HTML
      var forumPostsHTML = '<div class="mt-12">' +
              '<div class="flex items-start justify-between mb-6">' +
              '<div>' +
              '<h2 class="text-2xl font-bold text-gray-900">论坛帖子</h2>' +
              '<p class="mt-2 text-gray-500">来自论坛的热门讨论和最新动态</p>' +
              '</div>' +
              '</div>' +
              '<div id="forum-posts" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">' +
              '<!-- 这里将通过JavaScript动态填充论坛帖子 -->' +
              '</div>' +
              '</div>';

      // 添加特定的CSS样式，确保图片按照460*215的比例展示
      var style = document.createElement('style');
      style.textContent = `
          .forum-post-image {
            position: relative;
            width: 100%;
            padding-bottom: 46.7%; /* 215/460 = 0.467 或 46.7% */
            overflow: hidden;
          }

          .forum-post-image img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          /* 论坛帖子卡片悬停效果 */
          #forum-posts a:hover .forum-post-image img {
            transform: scale(1.05);
          }

          #forum-posts a:hover {
            text-decoration: none;
          }

          #forum-posts a:hover h4 {
            color: #2563eb;
          }

          /* 确保卡片在悬停时有明显的视觉反馈 */
          #forum-posts a {
            transition: all 0.3s ease;
          }

          #forum-posts a:hover {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
          }
        `;
      document.head.appendChild(style);

      // 3. 将论坛帖子区域插入到游戏大全前面
      gameListContainer.insertAdjacentHTML('beforebegin', forumPostsHTML);

      // 4. 获取论坛帖子数据并填充
      var forumPostsContainer = document.getElementById('forum-posts');

      // 尝试获取ThinkPHP传递的goodsDisplay2变量
      var forumPosts = [];

      // 检查全局变量
      if (typeof goodsDisplay2 !== 'undefined') {
        console.log('直接从全局变量goodsDisplay2获取数据');
        forumPosts = goodsDisplay2;
      }
      // 或者从window对象中获取
      else if (typeof window.goodsDisplay2 !== 'undefined') {
        console.log('从window.goodsDisplay2获取数据');
        forumPosts = window.goodsDisplay2;
      }
      // 如果上述方法都失败，再尝试从其他变量获取
      else {
        console.log('无法从goodsDisplay2获取数据，检查页面是否有数据变量');

        // 遍历window对象中的所有变量，查找可能包含论坛帖子的数据
        for (var key in window) {
          try {
            if (window[key] &&
                    typeof window[key] === 'object' &&
                    Array.isArray(window[key]) &&
                    window[key].length > 0 &&
                    window[key][0].title &&
                    window[key][0].content) {

              console.log('从window.' + key + '找到可能的论坛帖子数据');
              forumPosts = window[key];
              break;
            }
          } catch (e) {
            // 忽略访问某些属性可能导致的错误
          }
        }
      }

      if (forumPosts && forumPosts.length > 0) {
        console.log('找到 ' + forumPosts.length + ' 条论坛帖子数据');

        // 遍历帖子数据并创建HTML
        for (var i = 0; i < forumPosts.length; i++) {
          var post = forumPosts[i];

          // 创建帖子卡片 - 改为可点击的链接
          var postCard = document.createElement('a');
          postCard.href = post.target_url;
          postCard.target = '_blank';
          postCard.className = 'block bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition duration-200 cursor-pointer transform hover:scale-105';

          // 格式化帖子内容，移除HTML标签
          var content = post.content || '';
          content = content.replace(/<[^>]*>/g, '');
          content = content.trim() || '查看详情...';
          content = content.length > 60 ? content.substring(0, 60) + '...' : content;

          // 设置帖子卡片内容
          var cardHTML = '';

          // 图片区域 - 使用新的样式类以确保比例正确
          if (post.img_url) {
            cardHTML += '<div class="forum-post-image">' +
                    '<img src="' + post.img_url + '" alt="' + post.title + '" onerror="this.src=\'https://via.placeholder.com/460x215?text=' + encodeURIComponent(post.title) + '\'">' +
                    '</div>';
          }

          // 内容区域
          cardHTML += '<div class="p-4">' +
                  '<h4 class="font-medium text-gray-900 mb-2 truncate">' + post.title + '</h4>' +
                  '<div class="text-sm text-gray-500 h-16 overflow-hidden mb-4">' +
                  '<p>' + content + '</p>' +
                  '</div>' +
                  '<div class="flex items-center justify-between pt-3 border-t border-gray-100">' +
                  '<div class="flex items-center">' +
                  '<img src="' + (post.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(post.author) + '&background=random') + '" alt="' + post.author + '" class="w-6 h-6 rounded-full mr-2" onerror="this.src=\'https://ui-avatars.com/api/?name=' + encodeURIComponent(post.author) + '&background=random\'">' +
                  '<span class="text-xs text-gray-500">' + post.author + '</span>' +
                  '</div>' +
                  '<div class="text-sm font-medium text-blue-600 flex items-center">' +
                  '点击查看 <i class="fas fa-arrow-right ml-1 text-xs"></i>' +
                  '</div>' +
                  '</div>' +
                  '</div>';

          postCard.innerHTML = cardHTML;
          forumPostsContainer.appendChild(postCard);
        }
      } else {
        console.log('未找到论坛帖子数据，显示空状态');

        // 如果没有帖子数据，显示空状态
        forumPostsContainer.innerHTML = '<div class="col-span-full flex flex-col items-center justify-center py-10 text-center">' +
                '<div class="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 mb-4">' +
                '<i class="fas fa-comments text-2xl"></i>' +
                '</div>' +
                '<h3 class="text-lg font-medium text-gray-900 mb-1">暂无论坛帖子</h3>' +
                '<p class="text-sm text-gray-500">论坛帖子将很快添加，请稍后再查看</p>' +
                '</div>';
      }

      console.log('论坛帖子区域创建完成');
    } else {
      console.log('未找到游戏大全区域');
    }
  }


  // 结合两个修复，创建完整的解决方案
  document.addEventListener('DOMContentLoaded', function() {
    // 1. 修复perfect-scrollbar
    var oldScript = document.querySelector('script[src*="perfect-scrollbar"]');
    if (oldScript) {
      var newScript = document.createElement('script');
      newScript.src = "/static/plugins/perfect-scrollbar/perfect-scrollbar.min.js";
      oldScript.parentNode.replaceChild(newScript, oldScript);
      console.log('已使用本地perfect-scrollbar脚本');
    }

    // 2. 添加论坛帖子区域
    setTimeout(initForumPosts, 100); // 稍微延迟执行，确保DOM已完全加载
  });

  // 将ThinkPHP模板变量转换为JavaScript变量
  var goodsDisplay2 = {:json_encode($goodsDisplay2 ?: [])};
</script>
</body>
</html>