<?php

namespace app\admin\controller;

use app\BaseController;
use app\admin\model\System;
use app\admin\model\Config;
use think\facade\View;
use app\admin\model\Goodsclass;
use app\admin\model\Goods;
use app\admin\model\Account;
use app\admin\model\Combo;
use app\admin\model\Notice;
use app\admin\model\Coupons;
use app\admin\model\Workorder;
use app\admin\model\Withdraw;
use app\admin\model\Banner;
use app\admin\model\Offline;
use app\admin\model\User as Us;
use think\facade\Request;
use think\facade\Db;
use think\facade\Cookie;
use app\admin\controller\Base;
use think\facade\Log;
use think\exception\ValidateException;
// 引入 ThinkPHP 的日志门面

class Tags extends BaseController
{
    // 标签列表页
    public function index()
    {
        return View::fetch('tags/index');
    }
    
    // 获取标签列表
    public function list()
    {
        try {
            $page = max(1, intval(input('page', 1)));
            $limit = max(1, min(100, intval(input('limit', 10))));
            
            $query = Db::table('tk_game_tags');
            $count = $query->count();
            $list = $query->page($page, $limit)
                         ->order('id', 'desc')
                         ->select();
                
            return json([
                'code' => 0,
                'msg' => 'success',
                'count' => $count,
                'data' => $list
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 1, 
                'msg' => '获取数据失败', 
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : ''
            ]);
        }
    }

    // 添加标签
    public function add()
    {
        if(request()->isPost()) {
            try {
                $data = input('post.');
                
                if(empty($data['name'])) {
                    throw new ValidateException('标签名称不能为空');
                }
                
                // 检查标签名是否重复
                $exists = Db::table('tk_game_tags')
                    ->where('name', $data['name'])
                    ->find();
                    
                if($exists) {
                    throw new ValidateException('标签名称已存在');
                }
                
                Db::table('tk_game_tags')->insert($data);
                return json(['code' => 0, 'msg' => '添加成功']);
            } catch (ValidateException $e) {
                return json(['code' => 422, 'msg' => $e->getMessage()]);
            } catch (\Exception $e) {
                return json([
                    'code' => 1, 
                    'msg' => '添加失败',
                    'error' => $e->getMessage(),
                    'trace' => DEBUG ? $e->getTraceAsString() : ''
                ]);
            }
        }
        return View::fetch('tags/form');
    }

    // 编辑标签
    public function edit($id)
    {
        if(request()->isPost()) {
            try {
                $data = input('post.');
                
                if(empty($data['name'])) {
                    throw new ValidateException('标签名称不能为空');
                }
                
                // 检查标签名是否重复(排除自身)
                $exists = Db::table('tk_game_tags')
                    ->where('name', $data['name'])
                    ->where('id', '<>', $id)
                    ->find();
                    
                if($exists) {
                    throw new ValidateException('标签名称已存在');
                }
                
                $rows = Db::table('tk_game_tags')
                    ->where('id', $id)
                    ->update($data);
                    
                if($rows === 0) {
                    throw new ValidateException('标签不存在或未做任何修改');
                }
                    
                return json(['code' => 0, 'msg' => '更新成功']);
            } catch (ValidateException $e) {
                return json(['code' => 422, 'msg' => $e->getMessage()]);
            } catch (\Exception $e) {
                return json([
                    'code' => 1, 
                    'msg' => '更新失败',
                    'error' => $e->getMessage(),
                    'trace' => DEBUG ? $e->getTraceAsString() : ''
                ]);
            }
        }
        
        try {
            $tag = Db::table('tk_game_tags')
                     ->where('id', $id)
                     ->find();
            if (!$tag) {
                throw new ValidateException('标签不存在');
            }
            View::assign('tag', $tag);
            return View::fetch('tags/form');
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 删除标签
    public function delete()
    {
        try {
            $id = input('id');
            
            // 暂时跳过检查标签是否被使用
            // TODO: 确认正确的表和列名后恢复此检查
            
            Db::table('tk_game_tags')
                ->where('id', $id)
                ->delete();
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
} 