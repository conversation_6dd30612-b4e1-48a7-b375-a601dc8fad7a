<?php
/**
 * ===========================================================================
 * Copyright (c) 2024 Kongzue
 *
 * Licensed under the MIT License.
 * https://opensource.org/licenses/MIT
 *
 * 本代码为企业级支付流程控制器：
 *  - 根据订单请求参数进行支付处理（余额支付、优惠券处理、第三方支付跳转等）
 *  - 根据支付类型和订单信息更新数据库记录（订单状态、用户余额、商品销售统计等）
 *  - 构建支付参数表单并自动提交至第三方支付接口
 *
 * 作者: Kongzue (空祖 / 悠然)
 * 日期: 2024-12-08
 * 版本: 1.0.0
 * 项目名称: Cinema Payment System
 * 包名: com.kongzu.cinema.controller
 *
 * 维护和扩展：
 * - 后续可增加更多的支付方式（如微信、支付宝、银联）
 * - 可添加异常处理和日志记录
 * - 优化性能和安全性（如参数过滤、签名方式升级）
 * ===========================================================================
 */

namespace app\admin\controller;

use app\BaseController;
use app\admin\model\System;
use app\admin\model\Config;
use app\admin\model\Goodsclass;
use app\admin\model\Goods;
use app\admin\model\Combo;
use app\admin\model\Coupons;
use app\admin\model\Account;
use app\admin\model\User as Us;
use think\facade\Request;
use app\admin\model\Order;
use think\facade\Cookie;
use think\facade\Db;

/**
 * Epay类
 *
 * 本类主要负责处理支付相关的业务逻辑，包括余额支付、优惠券扣减及生成第三方支付参数。
 */
class Epay extends BaseController
{
    /**
     * @var array $orderData 静态变量用于暂存订单数据，以便在后续处理参数时使用
     */
    static $orderData;

    /**
     * 获取指定配置项的值
     *
     * 从Config模型中获取指定字段的值，用于动态获取商户ID、支付密钥等关键配置信息。
     *
     * @param string $val 配置项字段名
     * @return mixed 配置项对应的值
     */
    public function con($val)
    {
        // 从Config模型中查找ID为1的记录，并返回指定字段的值
        return Config::find(1)[$val];
    }

    /**
     * 支付处理入口函数
     */
    public function index()
    {
        try {
            // 获取请求参数
            $sub = Request::param();
            // 检查请求参数是否为空
            if (empty($sub)) {
                throw new \Exception('请求参数不能为空');
            }

            // 判断订单类型是否为余额支付
            if ($sub["ord_type"] == "yepay") {
                // 处理余额支付
                return $this->handleBalancePayment($sub);
            }

            // 如果需要，应用优惠券
            $this->applyCouponIfNeeded($sub);
            // 将订单数据存储在静态变量中
            Epay::$orderData = $sub;
            // 返回生成的支付表单HTML
            return json($this->buildHtml());

        } catch (\Exception $e) {
            // 处理异常并返回错误信息
            return $this->handleError('支付处理失败', $e);
        }
    }

    /**
     * 构建支付表单HTML
     *
     * 根据getParams()方法生成的参数创建自动提交的表单，用于提交至第三方支付接口页面。
     *
     * @return string 完整的HTML表单字符串
     */
    public function buildHtml()
    {
        // 获取支付接口的URL
        $actionUrl = $this->con("pay_url") . "submit.php";
        // 获取支付参数
        $params = $this->getParams();

        // 初始化HTML表单字符串
        $sHtml = "<form id='alipaysubmit' name='alipaysubmit' action='" . $actionUrl . "' method='post'>";
        // 遍历参数数组，生成隐藏输入域
        foreach ($params as $key => $val) {
            $sHtml .= "<input type='hidden' name='" . $key . "' value='" . $val . "'/>";
        }
        // 添加提交按钮和自动提交脚本
        $sHtml .= "<input type='submit' value=''></form>";
        $sHtml .= "<script>document.forms['alipaysubmit'].submit();</script>";

        // 返回完整的HTML表单字符串
        return $sHtml;
    }

    /**
     * 获取支付参数并进行签名处理
     *
     * 根据第三方支付接口要求生成所需参数，并对参数进行排序和MD5签名，保证数据完整性和安全性。
     *
     * @return array 带签名的参数数组
     */
    public function getParams()
    {
        try {
            // 从System模型中获取系统URL
            $sysUrl = System::find(1)["sy_url"];
            // 检查系统URL是否配置
            if (empty($sysUrl)) {
                throw new \Exception('系统URL未配置');
            }

            // 检查订单数据是否存在
            if (empty(Epay::$orderData)) {
                throw new \Exception('订单数据不存在');
            }

            // 构建支付参数数组
            $data = array(
                "pid" => $this->con("pay_pid"), // 商户ID
                "type" => Epay::$orderData["ord_type"], // 订单类型
                "name" => Epay::$orderData["ord_name"], // 订单名称
                "money" => Epay::$orderData["ord_money"], // 订单金额
                "return_url" => $sysUrl . "/admin/notify", // 同步通知URL
                "notify_url" => $sysUrl . "/admin/notify", // 异步通知URL
                "out_trade_no" => Epay::$orderData["ord_bbh"], // 订单编号
            );

            // 验证必要参数是否为空
            foreach ($data as $key => $value) {
                if (empty($value)) {
                    throw new \Exception("支付参数 {$key} 不能为空");
                }
            }

            // 对参数进行排序
            ksort($data);
            reset($data);
            // 初始化签名字符串
            $signStr = '';
            // 构建签名字符串
            foreach ($data as $key => $val) {
                if ($val == '' || $key == 'sign')
                    continue;
                if ($signStr)
                    $signStr .= '&';
                $signStr .= "$key=$val";
            }
            // 生成MD5签名
            $sign_ok = md5($signStr . $this->con("pay_key"));

            // 将签名和签名类型添加到参数数组
            $data['sign'] = $sign_ok;
            $data['sign_type'] = "MD5";

            // 返回带签名的参数数组
            return $data;

        } catch (\Exception $e) {
            // 抛出异常
            throw new \Exception('支付参数生成失败：' . $e->getMessage());
        }
    }

    /**
     * 余额支付处理逻辑
     *
     * @param array $sub 请求参数数组
     * @return string 返回支付结果或错误信息
     */
    private function handleBalancePayment($sub)
    {
        try {
            // 获取用户ID
            $uid = $this->getUserId();
            // 检查用户余额是否足够支付订单金额
            $this->checkUserBalance($uid, $sub["ord_money"]);

            // 开始数据库事务
            Db::startTrans();
            try {
                // 更新订单为已支付状态
                $this->updateOrderAsPaid($sub);
                // 扣减用户余额
                $this->deductUserBalance($uid, $sub["ord_money"]);
                // 处理订单逻辑
                $this->processOrder($sub);

                // 提交事务
                Db::commit();
                // 返回成功页面的重定向脚本
                return "<script>window.location.href='/user/index/onmoney'</script>";

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 重定向到错误页面
                return $this->redirectToErrorPage($e);
            }

        } catch (\Exception $e) {
            // 处理错误并返回错误信息
            return $this->handleError($e->getMessage(), $e);
        }
    }

    /**
     * 获取用户ID
     *
     * @return int 用户ID
     * @throws \Exception 如果用户未登录
     */
    private function getUserId()
    {
        // 从Cookie中获取用户ID
        $uid = Cookie::get("id");
        // 如果用户ID不存在，抛出异常
        if (!$uid) {
            throw new \Exception('用户未登录');
        }
        // 返回用户ID
        return $uid;
    }

    /**
     * 检查用户余额
     *
     * @param int $uid 用户ID
     * @param float $amount 订单金额
     * @throws \Exception 如果余额不足
     */
    private function checkUserBalance($uid, $amount)
    {
        // 查询用户余额
        $userBalance = Us::where("id", $uid)->value("us_money");
        // 如果余额不足，抛出异常
        if ($userBalance < $amount) {
            throw new \Exception('余额不足');
        }
    }

    /**
     * 处理订单
     */
    private function processOrder($sub)
    {
        $upOrder2 = Order::where("ord_bbh", $sub["ord_bbh"])->find();
        if (!$upOrder2) {
            throw new \Exception('订单不存在');
        }

        // 定义会员类型列表
        $membershipTypes = ['月费会员', '季费会员', '年费会员', '至尊会员'];

        // 检查是否为会员订单
        if (in_array($sub["ord_name"], $membershipTypes)) {
            // 会员订单直接处理会员升级
            $this->upgradeUserMembership($upOrder2["ord_uid"], $sub["ord_name"]);

            // 更新订单状态为已完成
            Order::where("ord_bbh", $sub["ord_bbh"])->update([
                "ord_aid" => null,  // 会员订单不需要绑定账号
                "or_maturity" => 1, // 标记订单已完成
                "ord_remarks" => "会员升级成功：" . $sub["ord_name"]
            ]);
        } else {
            // 非会员订单需要验证套餐信息
            $combo = Combo::where("id", $upOrder2["ord_combo"])->find();
            if (!$combo) {
                throw new \Exception('套餐信息不存在');
            }
            $this->handleGoodsDistribution($upOrder2, $combo, $sub["ord_bbh"]);
        }
    }

    /**
     * 处理会员升级
     *
     * @param int $uid 用户ID
     * @param string $membershipType 会员类型
     * @return void
     */
    private function upgradeUserMembership($uid, $membershipType)
    {
        // 计算会员到期时间
        $expiryTime = $this->calculateMembershipExpiry($membershipType);

        // 更新用户会员信息
        Us::where("id", $uid)->update([
            "membership_level" => $membershipType,
            "exit_time" => $expiryTime
        ]);
    }

    /**
     * 计算会员到期时间
     *
     * @param string $membershipType 会员类型
     * @return string 到期时间，格式：Y-m-d H:i:s
     */
    private function calculateMembershipExpiry($membershipType)
    {
        $now = time();

        switch ($membershipType) {
            case '月费会员':
                $expiry = strtotime('+1 month', $now);
                break;
            case '季费会员':
                $expiry = strtotime('+3 months', $now);
                break;
            case '年费会员':
                $expiry = strtotime('+1 year', $now);
                break;
            case '至尊会员':
                // 将99年改为最大可用时间（2038-01-19）
                $expiry = strtotime('2038-01-19');
                break;
            default:
                throw new \Exception('未知的会员类型');
        }

        return date('Y-m-d H:i:s', $expiry);
    }

    /**
     * 错误处理
     */
    private function handleError($message, $exception)
    {
        return json([
            'status' => 'error',
            'message' => $message . '：' . $exception->getMessage()
        ]);
    }

    /**
     * 重定向到错误页面
     */
    private function redirectToErrorPage($exception)
    {
        return "<script>window.location.href='/error?message=" . urlencode($exception->getMessage()) . "'</script>";
    }

    /**
     * 优惠券处理（根据需要修改订单金额）
     *
     * @param array &$sub 引用请求参数数组，可能会修改ord_money值
     * @return void
     */
    private function applyCouponIfNeeded(&$sub)
    {
        // 检查请求参数中是否存在折扣信息和优惠券ID
        if (isset($sub["discount"]) && $sub["discount"] != 0 && Cookie::get("yhjId")) {
            // 从Cookie中获取套餐ID
            $tcId = Cookie::get("tcId");
            // 如果套餐ID不存在，直接返回
            if (!$tcId) {
                return;
            }

            // 根据套餐ID查找套餐信息
            $combo = Combo::find($tcId);
            // 如果套餐信息不存在，直接返回
            if (!$combo) {
                return;
            }

            // 获取套餐的原始价格
            $coID = $combo["co_money"];
            // 根据优惠券ID查找优惠券信息
            $coupID = Coupons::find(Cookie::get("yhjId"));

            // 如果优惠券信息不存在，直接返回
            if (!$coupID) {
                return;
            }

            // 根据优惠券类型计算支付金额
            $sub["ord_money"] = $this->calculateDiscountedPrice($coID, $coupID);
        }
    }

    /**
     * 计算折扣后的价格
     *
     * @param float $originalPrice 原始价格
     * @param array $coupon 优惠券信息
     * @return float 折扣后的价格
     */
    private function calculateDiscountedPrice($originalPrice, $coupon)
    {
        // 如果优惠券类型为1，直接减去优惠券的面值
        if ($coupon["coupon_type"] == 1) {
            return $originalPrice - $coupon["coupon_value"];
        }
        // 如果优惠券类型为2，按百分比折扣计算
        elseif ($coupon["coupon_type"] == 2) {
            return $originalPrice - ($originalPrice * $coupon["coupon_value"] / 100);
        }
        // 如果没有匹配的优惠券类型，返回原始价格
        return $originalPrice;
    }

    /**
     * 更新订单为已支付状态
     *
     * @param array $sub 请求参数
     * @return void
     */
    private function updateOrderAsPaid($sub)
    {
        // 准备更新数据，将订单状态设置为已支付
        $data = [
            "ord_ybh" => null, // 清空订单原有编号
            "ord_ifpay" => 1,    // 设置订单支付状态为已支付
            "or_maturity" =>1,
            "time" => date("Y-m-d H:i:s"), // 更新支付时间为当前时间
        ];

        // 更新数据库中对应订单编号的记录
        Order::where(["ord_type" => "yepay", "ord_bbh" => $sub["ord_bbh"]])->update($data);
    }

    /**
     * 扣减用户余额
     *
     * @param int $uid 用户ID
     * @param float $amount 扣减金额
     * @return void
     */
    private function deductUserBalance($uid, $amount)
    {
        // 查询当前用户的余额
        $currentMoney = Us::where("id", $uid)->value("us_money");
        // 更新用户余额，扣减订单金额
        Us::where("id", $uid)->update(["us_money" => $currentMoney - $amount]);
    }

    /**
     * 将用户升级为至尊会员
     *
     * @param int $uid 用户ID
     * @return void
     */
    private function upgradeUserToPermanentMember($uid)
    {
        Us::where("id", $uid)->update(["membership_level" => "至尊会员"]);
    }

    /**
     * 根据订单类型处理商品发放或销量更新逻辑
     *
     * @param Order $upOrder2 当前订单对象
     * @param Combo $combo 套餐对象
     * @param string $orderBbh 订单编号
     * @return void
     */
    private function handleGoodsDistribution($upOrder2, $combo, $orderBbh)
    {
        if ($upOrder2['GoodType'] == 0) {
            $this->handleTypeZeroPurchase($upOrder2, $combo, $orderBbh);
        } else if ($upOrder2['GoodType'] == 1) {
            $this->handleSinglePurchase($upOrder2, $combo, $orderBbh);
        } else if ($upOrder2['GoodType'] == 3) {
            $this->handleTypeThreePurchase($upOrder2, $combo, $orderBbh);
        }
    }


    /**
     * 选取可用账号并绑定给用户，同时更新订单和商品销量
     * 离线账号
     * @param Order $upOrder2 当前订单
     * @param Combo $combo 当前所选套餐
     * @param string $orderBbh 订单编号
     * @return void
     */
    private function handleTypeZeroPurchase($upOrder2, $combo, $orderBbh)
    {
        Db::startTrans();

        try {
            // 查找可用的离线账号（离线账号独立存在，不依赖在线账号状态）
            $validAccount = Account::where([
                "ac_goods" => $combo["co_goods"],
                "goods_Type" => 0,
                "ac_states" => 1,  // 只查找可用状态的离线账号
                "ac_sell" => 1     // 只查找可售卖的离线账号
            ])->find();

            if (!$validAccount) {
                throw new \Exception("暂无可用离线账号，请联系管理员添加账号");
            }

            $acId = $validAccount['id'];

            // 更新订单状态
            $orderResult = Order::where("ord_bbh", $orderBbh)->update([
                "ord_aid" => $acId,
                "or_maturity" => 1
            ]);

            if (!$orderResult) {
                throw new \Exception("订单更新失败");
            }

            // 更新商品销量
            //$this->updateGoodsSales($validAccount["ac_goods"]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();

            // 回滚用户余额
            $this->refundUserBalance($upOrder2["ord_uid"], $upOrder2["ord_money"]);

            // 更新订单状态
            Order::where("ord_bbh", $orderBbh)->update([
                "ord_ifpay" => 0,
                "ord_remarks" => '处理失败：' . $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 选取可用账号并绑定给用户，同时更新订单和商品销量
     * 在线账号
     * @param Order $upOrder2 当前订单
     * @param Combo $combo 当前所选套餐
     * @param string $orderBbh 订单编号
     * @return void
     */
    private function handleSinglePurchase($upOrder2, $combo, $orderBbh)
    {
        Db::startTrans();

        try {
            // 检查配置项 allow_expire_no_offline
            $allowExpireNoOffline = Config::find(1)['allow_expire_no_offline'];

            $data = [
                "ac_goods" => $combo["co_goods"],
                "ac_states" => 1,
                "goods_Type" => 1,
            ];

            // 定义分配模式优先级
            $assignModePriority = [
                '最近租用账号' => 1,
                '已过期账号' => 2,
                '正常可用账号' => 3,
            ];

            // 初始化分配模式、优先级和账号变量
            $assignMode = '';
            $assignPriority = PHP_INT_MAX;
            $AccountItem = null;

            // 查询用户所有符合条件的历史订单（修复：区分普通订单和永久版订单）
            $recentOrders = Order::where([
                "ord_uid" => $upOrder2['ord_uid'],
                "ord_combo" => $combo["id"],
                "GoodType" => 1,
                "ord_ifpay" => 1
            ])->where('is_permanent', 'in', [0, null]) // 只查询非永久版订单
            ->order('id', 'asc')->select();

            // 同时查询永久版提取订单（通过商品ID匹配）
            $permanentOrders = Order::where([
                "ord_uid" => $upOrder2['ord_uid'],
                "ord_combo" => $combo["co_goods"], // 永久版订单的ord_combo存储的是商品ID
                "GoodType" => 1,
                "ord_ifpay" => 1,
                "is_permanent" => 2 // 永久版提取订单
            ])->order('id', 'asc')->select();

            // 合并历史订单
            $allRecentOrders = array_merge($recentOrders->toArray(), $permanentOrders->toArray());

            // 遍历订单,检查对应账号的可用性（修复：使用合并后的历史订单）
            foreach ($allRecentOrders as $recentOrder) {
                $recentAccount = Account::where([
                    "id" => $recentOrder["ord_aid"],
                    "ac_states" => 1,
                    "goods_Type" => 1,
                    "ac_goods" => $combo["co_goods"] // 新增：确保账号属于正确的商品
                ])->find();

                if ($recentAccount) {
                    $currentMode = '最近租用账号';
                    $currentPriority = $assignModePriority[$currentMode];
                    if ($currentPriority < $assignPriority) {
                        $AccountItem = $recentAccount;
                        $assignMode = $currentMode;
                        $assignPriority = $currentPriority;
                    }
                }
            }

            // ✅ 如果允许分配已过期账号,且优先级更高
            if ($allowExpireNoOffline && $assignModePriority['已过期账号'] < $assignPriority) {
                $expiredAccount = Account::where([
                    "ac_goods" => $combo["co_goods"],
                    "ac_states" => 0,
                    "goods_Type" => 1,
                    "expire_status" => 1  // 只查找标记过期等待重新分配的账号
                ])->where('exit_time', '<', time())->find();

                if ($expiredAccount) {
                    $AccountItem = $expiredAccount;

                    // ✅ 分配时才执行改密操作
                    try {
                        $apiController = new \app\controller\Api();
                        $apiController->changePasswordForReassignment($expiredAccount);
                    } catch (\Exception $e) {
                        // 改密失败，记录日志但不阻止分配
                        Db::table('monitor_logs')->insert([
                            'account_name' => $expiredAccount['ac_name'],
                            'action' => '重新分配改密失败',
                            'monitor_type' => 'Epay分配',
                            'timestamp' => date('Y-m-d H:i:s'),
                            'status' => '警告',
                            'error_message' => '改密失败但继续分配: ' . $e->getMessage()
                        ]);
                    }

                    $assignMode = '已过期账号';
                    $assignPriority = $assignModePriority[$assignMode];
                }
            }

            // 如果还未找到可用账号,则按正常逻辑分配
            if (!$AccountItem) {
                $AccountItem = Account::where($data)->find();
                $assignMode = '正常可用账号';
            }

            // 修改：处理没有可用账号的情况
            if (!$AccountItem) {
                // 更新订单状态，标记为已支付但未分配账号
                Order::where("ord_bbh", $orderBbh)->update([
                    "ord_ifpay" => 1,
                    "account_status" => 0, // 0表示未分配账号
                    "or_maturity" => 1,//订单未到期
                    "ord_remarks" => "暂无可用账号，系统将在账号可用时自动分配",
                    "pending_reason" => ""
                ]);

                // 更新商品销量
                $this->updateGoodsSales($combo["co_goods"]);

                // 提交事务
                Db::commit();
                return true;
            }

            $acId = $AccountItem['id'];
            // 获取账号名称
            $acName = Account::where('id', $acId)->value('ac_name');

            // 计算到期时间
            $duration = intval($upOrder2['duration']);
            $unit = strtolower($upOrder2['unit']);
            $seconds = $this->calculateExpirationSeconds($duration, $unit);
            $exitTime = time() + $seconds;

            // ✅ 使用统一的账号分配方法
            $assignResult = \app\controller\Api::assignAccount(
                $acName,
                $upOrder2['ord_uid'],
                $exitTime
            );

            if (!$assignResult) {
                throw new \Exception("账号绑定失败");
            }

            // 更新订单状态
            $orderResult = Order::where("ord_bbh", $orderBbh)->update([
                "ord_aid" => $acId,
                "or_maturity" => 1,
                "account_status" => 1, // 1表示已分配账号
                "ord_remarks" => "分配模式：" . $assignMode . "，账号：" . $acName
            ]);

            if (!$orderResult) {
                throw new \Exception("订单更新失败");
            }

            // 更新商品销量
            $this->updateGoodsSales($AccountItem["ac_goods"]);

            Db::commit();
            return true;

        } catch (\Exception $e) {
            Db::rollback();

            // 回滚用户余额
            $this->refundUserBalance($upOrder2["ord_uid"], $upOrder2["ord_money"]);

            // 更新订单状态
            Order::where("ord_bbh", $orderBbh)->update([
                "ord_ifpay" => 0,
                "account_status" => 0, // 0表示未分配账号
                "pending_reason" => "处理异常",
                "ord_remarks" => '处理失败：' . $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 计算到期时间秒数
     */
    private function calculateExpirationSeconds($duration, $unit)
    {
        $seconds = 0;
        switch ($unit) {
            case 'day':
                $seconds = $duration * 24 * 3600;
                break;
            case 'hour':
                $seconds = $duration * 3600;
                break;
            default:
                throw new \Exception("无效的时间单位");
        }
        return $seconds;
    }

    /**
     * 退还用户余额
     */
    private function refundUserBalance($uid, $amount)
    {
        $user = Us::where("id", $uid)->find();
        if ($user) {
            Us::where("id", $uid)->update([
                "us_money" => $user["us_money"] + $amount
            ]);
        }
    }

    /**
     * 更新商品销量
     *
     * @param int $goodsId 商品ID
     * @return void
     */
    private function updateGoodsSales($goodsId)
    {
        $goods = Goods::where("id", $goodsId)->find();
        Goods::where("id", $goodsId)->update([
            "goods_xl" => $goods["goods_xl"] + 1
        ]);
    }

    /**
     * 查询账号（根据原有逻辑保留此方法占位）
     *
     * @param int $id 账号ID
     * @param int $uid 用户ID
     * @return mixed 原逻辑未给出该方法实现，保留原有方法调用占位
     */
    private function queryAccount($id, $uid)
    {
        // 由于原始代码此处只是示例引用，未给出实现逻辑，这里不修改逻辑。
        // 原逻辑使用$this->queryAccount($Account->id, Cookie::get("id"))查询账号。
        // 假设此方法在原有代码中实现或在其他地方定义。
        // 在此保留占位，不更改逻辑。
        return null;
    }

    private function changePassword($account)
    {
        $curl = curl_init();

        // 从数据库中获取当前密码
        $currentPassword = $account['ac_password'];

        // 生成随机11位中英文字符的新密码
        $newPassword = $this->generateRandomPassword(11);

        // 查询 tk_steamaccountdata 表获取所需数据
        $steamAccountData = Db::table('tk_steamaccountdata')
            ->where('account_name', $account['ac_name'])
            ->find();

        if (!$steamAccountData) {
            throw new \Exception('无法找到对应的 Steam 账号数据');
        }

        // 从 Config 表中获取 token_api
        $config = Config::find(1);
        $tokenApi = $config ?  : 'http://api.kongzu.com.cn/';

        // 构建完整的 URL
        // $url = $tokenApi . '/api/v1/change_password';
        $url = $tokenApi . 'change_password.php';

        curl_setopt_array($curl, array(
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode([
                "account_name" => $account['ac_name'],
                "now_password" => $currentPassword, // 使用从数据库获取的当前密码
                "new_password" => $newPassword, // 使用生成的随机新密码
                "shared_secret" => $steamAccountData['shared_secret'],
                "identity_secret" => $steamAccountData['identity_secret'],
                "device_id" => $steamAccountData['device_id'],
                "steamid" => $steamAccountData['SteamID']
            ]),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        if ($response === false) {
            throw new \Exception('密码修改失败');
        }

        // 处理响应
        $responseData = json_decode($response, true);
        if ($responseData['msg_code'] !== 0) {
            throw new \Exception('密码修改失败: ' . $responseData['message']);
        }

        // 更新数据库中所有 ac_name 为 $account['ac_name'] 的新密码
        Account::where('ac_name', $account['ac_name'])->update(['ac_password' => $newPassword]);
    }

    private function generateRandomPassword($length = 11)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomPassword = '';
        for ($i = 0; $i < $length; $i++) {
            $randomPassword .= $characters[random_int(0, $charactersLength - 1)];
        }
        return $randomPassword;
    }

    /**
     * 处理 GoodType 为 3 的商品购买逻辑
     *
     * @param Order $upOrder2 当前订单对象
     * @param Combo $combo 套餐对象
     * @param string $orderBbh 订单编号
     * @return void
     */
    private function handleTypeThreePurchase($upOrder2, $combo, $orderBbh)
    {
        // 在这里实现 GoodType 为 3 的处理逻辑
        // 例如，更新订单状态、处理库存、通知用户等
        try {
            // 示例逻辑：更新订单状态为已处理
            Order::where("ord_bbh", $orderBbh)->update([
                "ord_ifpay" => 1,
                "ord_remarks" => 'GoodType 3 处理完成'
            ]);

            // 其他处理逻辑...

        } catch (\Exception $e) {
            // 错误处理
            throw new \Exception('处理 GoodType 3 失败：' . $e->getMessage());
        }
    }
}