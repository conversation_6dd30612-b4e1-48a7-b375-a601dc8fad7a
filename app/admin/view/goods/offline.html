<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>离线商品价格定义</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <div class="layui-form-item">
                <!-- <div class="layui-inline">
                    <label class="layui-form-label">商品ID</label>
                    <div class="layui-input-inline">
                        <input type="text" name="product_id" placeholder="请输入商品ID" autocomplete="off" class="layui-input">
                    </div>
                </div> -->
                <div class="layui-inline">
                    <label class="layui-form-label">商品名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="goods_name" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layuiadmin-btn-comm" data-type="reload" lay-submit lay-filter="data-search-btn">
                        <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- <div class="layui-card-body">
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layuiadmin-btn-comm" data-type="add" lay-event="add">添加</button>
                <button class="layui-btn layuiadmin-btn-comm" data-type="batchdel">删除</button>
            </div> -->

            <script type="text/html" id="toolbarDemo">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm" lay-event="add">添加</button>
                    <!-- <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batchdel">删除</button> -->
                </div>
            </script>

            <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>

            <script type="text/html" id="currentTableBar">
                <a class="layui-btn layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete"><i class="layui-icon layui-icon-delete"></i>删除</a>
            </script>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>  
<script>
layui.use(['form', 'table'], function () {
    var $ = layui.jquery,
        form = layui.form,
        table = layui.table;

    // 表格渲染
    table.render({
        elem: '#currentTableId',
        url: '/admin/Base/getOfflineProducts',
        toolbar: '#toolbarDemo',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: "checkbox", width: 50},
            {field: 'id',  title: 'ID', sort: true},
            // {field: 'product_id',  title: '商品ID'},
            {field: 'goods_name',  title: '商品名称'},
            {field: 'product_amount', title: '商品金额', templet: function(d){
                return '￥' + d.product_amount;
            }},
            {field: 'created_at', title: '创建时间'},
            {field: 'updated_at',  title: '更新时间'},
            {title: '操作', minWidth: 150, toolbar: '#currentTableBar', align: "center"}
        ]],
        limits: [10, 15, 20, 25, 50, 100],
        limit: 15,
        page: true,
        skin: 'line'
    });

    // 搜索按钮事件
    form.on('submit(data-search-btn)', function (data) {
        table.reload('currentTableId', {
            page: {
                curr: 1
            }
            , where: {
                product_id: data.field.product_id,
                goods_name: data.field.goods_name
            }
        });
        return false;
    });

    // 重置按钮事件
    form.on('submit(data-reset-btn)', function () {
        $('input[name="product_id"]').val('');
        table.reload('currentTableId', {
            page: {
                curr: 1
            }
            , where: {}
        });
        return false;
    });

    // 监听添加操作
    table.on('toolbar(currentTableFilter)', function (obj) {
        if (obj.event === 'add') {
            var index = layer.open({
                title: '添加离线商品',
                type: 2,
                shade: 0.2,
                maxmin:true,
                shadeClose: true,
                //,maxmin: true
                area: ['550px', '550px'],
                content: '/admin/Index/addOfflineProduct',
            });
            $(window).on("resize", function () {
                layer.full(index);
            });
        }
    });

    // 监听工具条操作
    table.on('tool(currentTableFilter)', function (obj) {
        var data = obj.data;
        if (obj.event === 'edit') {
            layer.prompt({
                formType: 0,
                value: data.product_amount,
                title: '修改商品金额',
            }, function(value, index){
                $.ajax({
                    url: '/admin/Index/updateOfflineProductPrice',
                    type: 'POST',
                    data: {id: data.id, product_amount: value},
                    success: function(res){
                        if(res.code === 0){
                            obj.update({product_amount: value});
                            layer.msg('修改成功');
                        } else {
                            layer.msg('修改失败：' + res.msg);
                        }
                    }
                });
                layer.close(index);
            });
        } else if (obj.event === 'delete') {
            layer.confirm('确定删除该商品吗？', function (index) {
                $.ajax({
                    url: '/admin/Base/deleteOfflineProduct',
                    type: 'POST',
                    data: {id: data.id},
                    success: function(res){
                        if(res.code === 0){
                            obj.del();
                            layer.msg('删除成功');
                        } else {
                            layer.msg('删除失败：' + res.msg);
                        }
                    }
                });
                layer.close(index);
            });
        }
    });
});
</script>
</body>
</html> 