<?php
namespace app\home\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Db;
use app\admin\model\System;
use app\admin\model\Config;

class Products extends BaseController
{
    public function index()
    {
        // 获取搜索关键词 - 同时支持keyWords和q两种参数名
        $searchQuery = input('get.keyWords', '');
        if (empty($searchQuery)) {
            $searchQuery = input('get.q', '');
        }
        
        // 构建查询
        $query = Db::name('goods')->where('goods_show', 1);
        
        // 如果有搜索关键词，添加模糊搜索条件
        if (!empty($searchQuery)) {
            $query->where(function($q) use ($searchQuery) {
                // 搜索商品名称
                $q->where('goods_name', 'like', '%' . $searchQuery . '%')
                  // 或搜索关键词
                  ->whereOr('goods_key', 'like', '%' . $searchQuery . '%')
                  // 或搜索商品描述
                  ->whereOr('goods_des', 'like', '%' . $searchQuery . '%');
            });
        }
        
        // 获取商品数据
        $goods = $query->order('time desc, id desc')
            ->select()
            ->toArray();

        // 获取所有游戏标签 - 移除 is_show 条件
        $gameTags = Db::name('game_tags')
            ->select()
            ->toArray();
        
        return View::fetch('index', [
            'goods' => $goods,
            'gameTags' => $gameTags,
            "system" => System::find(1),
            "config" => Config::find(1),
            'searchQuery' => $searchQuery, // 将搜索关键词传递给视图
        ]);
    }

    public function filterByTags()
    {
        $tagIds = input('post.tagIds/a', []);
        
        $query = Db::name('goods')->where('goods_show', 1);
        
        if (!empty($tagIds)) {
            $query->whereIn('tag_ids', $tagIds);
        }
        
        $goods = $query->order('time desc, id desc')
            ->select()
            ->toArray();
        
        return json(['success' => true, 'data' => $goods]);
    }
} 