/**
 * 代理后台Session过期处理脚本
 * 
 * 功能：
 * 1. 统一处理所有AJAX请求的Session过期响应
 * 2. 自动跳转到登录页面
 * 3. 显示友好的提示信息
 * 4. 防止重复跳转
 * 
 * 使用方法：
 * 在代理后台的所有页面中引入此脚本
 */

(function() {
    'use strict';
    
    // 防止重复跳转的标志
    let isRedirecting = false;
    
    /**
     * 处理Session过期响应
     * @param {Object} response - 服务器响应数据
     * @returns {boolean} - 是否为Session过期
     */
    function handleSessionExpired(response) {
        // 检查是否为Session过期相关的响应
        if (response && (
            response.session_expired === true ||
            response.account_not_found === true ||
            response.account_disabled === true ||
            (response.code === 401 && response.redirect) ||
            (response.code === 403 && response.redirect) ||
            (response.code === 404 && response.redirect)
        )) {
            
            // 防止重复跳转
            if (isRedirecting) {
                return true;
            }
            isRedirecting = true;
            
            // 显示提示信息
            let message = response.msg || '登录已过期，请重新登录';
            
            // 根据不同的错误类型显示不同的提示
            if (response.account_disabled === true) {
                message = '您的代理账号已被禁用，请联系管理员';
            } else if (response.account_not_found === true) {
                message = '代理账号不存在，请联系管理员';
            }
            
            // 显示提示并跳转
            if (typeof layer !== 'undefined') {
                // 使用Layui的提示
                layer.alert(message, {
                    icon: 2,
                    title: '提示',
                    closeBtn: 0,
                    shadeClose: false
                }, function() {
                    redirectToLogin(response.redirect);
                });
            } else if (typeof alert !== 'undefined') {
                // 使用原生alert
                alert(message);
                redirectToLogin(response.redirect);
            } else {
                // 直接跳转
                redirectToLogin(response.redirect);
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 跳转到登录页面
     * @param {string} redirectUrl - 跳转URL
     */
    function redirectToLogin(redirectUrl) {
        const loginUrl = redirectUrl || '/agent/index/login';
        
        // 清除可能的定时器
        if (window.sessionCheckTimer) {
            clearInterval(window.sessionCheckTimer);
        }
        
        // 延迟跳转，确保用户看到提示
        setTimeout(function() {
            if (window.location.pathname !== loginUrl) {
                window.location.href = loginUrl;
            }
        }, 1000);
    }
    
    /**
     * 拦截jQuery AJAX请求
     */
    if (typeof $ !== 'undefined' && $.ajaxSetup) {
        // 设置全局AJAX错误处理
        $(document).ajaxComplete(function(event, xhr, settings) {
            try {
                const response = JSON.parse(xhr.responseText);
                handleSessionExpired(response);
            } catch (e) {
                // 忽略JSON解析错误
            }
        });
        
        // 设置全局AJAX错误处理
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status === 401 || xhr.status === 403) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (!handleSessionExpired(response)) {
                        // 如果不是Session过期，显示通用错误
                        console.error('AJAX请求失败:', thrownError);
                    }
                } catch (e) {
                    console.error('AJAX请求失败:', thrownError);
                }
            }
        });
    }
    
    /**
     * 拦截原生fetch请求
     */
    if (typeof fetch !== 'undefined') {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            return originalFetch.apply(this, args)
                .then(response => {
                    // 克隆响应以便多次读取
                    const clonedResponse = response.clone();
                    
                    // 检查状态码
                    if (response.status === 401 || response.status === 403) {
                        clonedResponse.json().then(data => {
                            handleSessionExpired(data);
                        }).catch(() => {
                            // 忽略JSON解析错误
                        });
                    }
                    
                    return response;
                })
                .catch(error => {
                    console.error('Fetch请求失败:', error);
                    throw error;
                });
        };
    }
    
    /**
     * 定期检查Session状态
     */
    function startSessionCheck() {
        // 每5分钟检查一次Session状态
        window.sessionCheckTimer = setInterval(function() {
            // 只在代理后台页面执行检查
            if (window.location.pathname.indexOf('/agent/') === 0) {
                checkSessionStatus();
            }
        }, 5 * 60 * 1000); // 5分钟
    }
    
    /**
     * 检查Session状态
     */
    function checkSessionStatus() {
        if (typeof $ !== 'undefined') {
            $.ajax({
                url: '/agent/index/checkStatus',
                type: 'GET',
                dataType: 'json',
                timeout: 10000,
                success: function(response) {
                    handleSessionExpired(response);
                },
                error: function(xhr) {
                    if (xhr.status === 401 || xhr.status === 403) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            handleSessionExpired(response);
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            });
        }
    }
    
    /**
     * 页面加载完成后启动Session检查
     */
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟启动，避免与页面初始化冲突
            setTimeout(startSessionCheck, 3000);
        });
    } else {
        setTimeout(startSessionCheck, 3000);
    }
    
    /**
     * 页面卸载时清除定时器
     */
    window.addEventListener('beforeunload', function() {
        if (window.sessionCheckTimer) {
            clearInterval(window.sessionCheckTimer);
        }
    });
    
    // 暴露全局方法供手动调用
    window.AgentSessionHandler = {
        handleSessionExpired: handleSessionExpired,
        checkSessionStatus: checkSessionStatus,
        redirectToLogin: redirectToLogin
    };
    
})();
