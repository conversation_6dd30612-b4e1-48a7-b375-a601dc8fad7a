/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */
;
layui.define(["table", "form", "element"],
function(e) {
	var t = layui.$,
	i = layui.table,
	r = (layui.form, layui.element);
	i.render({
		elem: "#LAY-app-system-order",
		url: "/admin/base/withdraw",
		cols: [[{
			type: "numbers",
			fixed: "left"
		},
		{
			field: "id",
			width: 100,
			title: "提现用户",
			sort: !0
		},
		{
			field: "wi_money",
			width: 100,
			title: "提现金额"
		},
		{
			field: "wi_state",
			width: 100,
			title: "提现状态",
		},
		{
			field: "progress",
			title: "进度",
			width: 200,
			align: "center",
			templet: "#progressTpl"
		},
		{
			field: "wi_type",
			width: 100,
			title: "提现方式"
		},
		{
			field: "wi_username",
			minWidth: 100,
			title: "提现账号"
		},
		{
			field: "time",
			title: "申请时间",
			minWidth: 80,
			align: "center"
		},
		{
			title: "操作",
			align: "center",
			fixed: "right",
			toolbar: "#table-system-order"
		}]],
		page: !0,
		limit: 10,
		limits: [10,20,30,40,50,60,70,80,100],
		text: "对不起，加载出现异常！",
		done: function() {
			r.render("progress")
		}
	}),
	i.on("tool(LAY-app-system-order)",
	function(e) {
		e.data;
		if("del" === e.event){
		    t(e.tr);
            layer.confirm("确定删除此申请？",
			function() {
				layui.$.ajax({
                            url: '/admin/base/delWithdraw',
                            method: 'post',
                            data: {id:e.data.id},
                            success: function(res){
                            layer.msg('删除成功');
                            }
                        });
			})
		}
		if ("edit" === e.event) {
			t(e.tr);
			layer.open({
				type: 2,
				title: "编辑申请",
				content: "/admin/index/upWithdraw?id="+e.data.id,
				area: ["450px", "450px"],
				btn: ["确定", "取消"],
				yes: function(e, t) {
					var r = window["layui-layer-iframe" + e],
					l = "LAY-app-workorder-submit",
					o = t.find("iframe").contents().find("#" + l);
					r.layui.form.on("submit(" + l + ")",
					function(t) {
						t.field;
						i.reload("LAY-user-front-submit"),
						layer.close(e)
                            layui.$.ajax({
                                url: '/admin/base/upWithdraw',
                                method: 'post',
                                data: t.field,
                                success: function(res){
                                layer.msg('保存成功');
                                }
                            });
						
					}),
					o.trigger("click")
				},
				success: function(e, t) {}
			})
		}
		
	}),
	e("withdraw", {})
});