<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDK卡密管理</title>
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2>CDK卡密管理</h2>
            </div>
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">模糊搜索</label>
                        <div class="layui-input-inline" style="width: 300px;">
                            <input type="text" name="search" placeholder="请输入卡密代码或商品名称进行搜索" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">代理账号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="agent_username" placeholder="请输入代理账号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">卡密状态</label>
                        <div class="layui-input-inline">
                            <select name="status" lay-search="">
                                <option value="">全部状态</option>
                                <option value="unused">未使用</option>
                                <option value="used">已使用</option>
                                <option value="banned">已封禁</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">卡密类型</label>
                        <div class="layui-input-inline">
                            <select name="cdk_type" lay-search="">
                                <option value="">全部类型</option>
                                <option value="day">日卡</option>
                                <option value="hour">小时卡</option>
                                <option value="permanent">永久卡</option>
                                <option value="offline">离线卡</option>
                                <option value="membership">会员卡</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-comm" data-type="reload" lay-submit lay-filter="LAY-app-cdk-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary" data-type="clear">
                            <i></i> 重置条件
                        </button>
                    </div>
                </div>
            </div>
            <div class="layui-card-body">
                <div style="padding-bottom: 10px;">
                    <button class="layui-btn layuiadmin-btn-comm" data-type="add" id="addCdkBtn">新增在线卡密</button>
                    <button class="layui-btn layui-btn-normal" data-type="addOffline" id="addOfflineBtn">新增离线卡密</button>
                    <button class="layui-btn layui-btn-normal" data-type="addMembership" id="addMembershipBtn">新增会员卡密</button>
                    <button class="layui-btn layui-btn-warm" data-type="addPermanent" id="addPermanentBtn">新增永久版卡密</button>
                    <button class="layui-btn layui-btn-primary" data-type="batchGenerate" id="batchGenerateBtn">批量生成</button>
                    <button class="layui-btn layui-btn-orange" data-type="batchBan" id="batchBanBtn">🚫 批量封禁</button>
                    <button class="layui-btn layui-btn-danger" data-type="batchdel" id="batchDeleteBtn">删除选中</button>
                </div>
                <table id="LAY-app-cdk-list" lay-filter="LAY-app-cdk-list"></table>

                <!-- 操作模板 -->
                <script type="text/html" id="table-cdk-options">
                    {{# if(d.is_banned == 1) { }}
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">
                            <i class="layui-icon layui-icon-delete"></i>删除
                        </a>
                    {{# } else { }}
                        <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="ban">
                            <i class="layui-icon layui-icon-close"></i>封禁
                        </a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">
                            <i class="layui-icon layui-icon-delete"></i>删除
                        </a>
                    {{# } }}
                </script>

                <!-- 状态模板 -->
                <script type="text/html" id="statusTpl">
                    {{# if(d.is_banned == 1) { }}
                        <span class="layui-badge layui-bg-red">已封禁</span>
                    {{# } else if(d.status === 'used') { }}
                        <span class="layui-badge layui-bg-gray">已使用</span>
                    {{# } else { }}
                        <span class="layui-badge layui-bg-green">未使用</span>
                    {{# } }}
                </script>
            </div>
        </div>
    </div>

    <script src="/layuiadmin/layui/layui.js"></script>
    <script>
        layui.config({
            base: '/layuiadmin/' // 静态资源所在路径
        }).extend({
            index: 'lib/index' // 主入口模块
        }).use(['index', 'table', 'form'], function () {
            var $ = layui.$
                , table = layui.table
                , form = layui.form;

            // 定义类型映射
            var typeMapping = {
                'year': '年卡',
                'month': '月卡',
                'day': '日卡',
                'hour': '小时卡',
                'permanent': '永久卡',
                'membership': '会员卡',
                'offline': '离线卡'
            };

            // 渲染表格
            table.render({
                elem: '#LAY-app-cdk-list'
                , url: '/admin/CdkController/getList' // 数据接口
                , cols: [[
                    { type: 'checkbox', fixed: 'left' }
                    , { field: 'id', title: 'ID', width: 80, sort: true }
                    , { field: 'cdk_code', title: '卡密代码', minWidth: 200 }
                    , { field: 'shop', title: '商品名称', minWidth: 150, templet: function(d) {
                        return d.shop || (typeMapping[d.cdk_type] || d.cdk_type);
                    }}
                    , { field: 'cdk_type', title: '卡密类型', width: 100, templet: function(d) {
                        return typeMapping[d.cdk_type] || d.cdk_type;
                    }}
                    , { field: 'agent_username', title: '代理账号', width: 120, templet: function(d) {
                        return d.agent_username ? '<span class="layui-badge layui-bg-orange">' + d.agent_username + '</span>' : '<span>管理员</span>';
                    }}
                    , { field: 'expiry_date2', title: '具体时长', width: 100 }
                    , { field: 'expiry_date', title: '到期时间', width: 180, sort: true }
                    , { field: 'status', title: '卡密状态', width: 100, toolbar: '#statusTpl' }
                    , { title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-cdk-options' }
                ]]
                , page: true
                , limit: 10
                , text: {
                    none: '暂无相关数据'
                }
            });

            // 监听搜索
            form.on('submit(LAY-app-cdk-search)', function (data) {
                var field = data.field;
                table.reload('LAY-app-cdk-list', {
                    where: field
                });
            });

            // 监听清空按钮
            $('[data-type="clear"]').on('click', function() {
                // 重新载入页面，清空所有搜索条件
                window.location.reload();
            });

            // 监听工具条操作
            table.on('tool(LAY-app-cdk-list)', function (obj) {
                var data = obj.data;

                if (obj.event === 'ban') {
                    // 封禁卡密
                    openBanCdkModal(data);

                } else if (obj.event === 'del') {
                    layer.confirm('确实删除此卡密？', function (index) {
                        $.ajax({
                            url: '/admin/CdkController/delete',
                            type: 'POST',
                            data: { id: data.id },
                            success: function (response) {
                                if (response.msg == 1) {
                                    layer.msg('删除成功');
                                    obj.del();
                                } else {
                                    layer.msg(response.content);
                                }
                            },
                            error: function () {
                                layer.msg('服务器错误，请稍后再试');
                            }
                        });
                        layer.close(index);
                    });
                }
            });

            // 批量删除
            var active = {
                batchdel: function () {
                    var checkStatus = table.checkStatus('LAY-app-cdk-list')
                        , checkData = checkStatus.data;

                    if (checkData.length === 0) {
                        return layer.msg('请选择数据');
                    }

                    layer.confirm('确实删除选中的数据吗？', function (index) {
                        var ids = checkData.map(function (item) { return item.id; });
                        $.ajax({
                            url: '/admin/CdkController/deleteBatch',
                            type: 'POST',
                            contentType: "application/json",
                            data: JSON.stringify({ ids: ids }),
                            success: function (res) {
                                if (res.msg == 1) {
                                    layer.msg('批量删除成功');
                                    table.reload('LAY-app-cdk-list');
                                } else {
                                    layer.msg('删除失败：' + (res.content || '未知错误'));
                                }
                            },
                            error: function () {
                                layer.msg('服务器错误，请稍后再试');
                            }
                        });
                        layer.close(index);
                    });
                },
                add: function () {
                    layer.open({
                        type: 2
                        , title: '新增在线卡密'
                        , content: '/admin/CdkController/add'
                        , maxmin: true
                        , area: ['90%', '90%']
                        , resize: true
                        // , btn: ['确定', '取消']
                        , yes: function (index, layero) {
                            var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
                            submit.click();
                        }
                    });
                },
                addOffline: function () {
                    layer.open({
                        type: 2
                        , title: '新增离线卡密'
                        , content: '/admin/CdkController/addOfflineCdk'
                        , maxmin: true
                        , area: ['80%', '80%']
                        , resize: true
                        // , btn: ['确定', '取消']
                        , yes: function (index, layero) {
                            var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
                            submit.click();
                        }
                    });
                },
                addMembership: function () {
                    layer.open({
                        type: 2
                        , title: '新增会员卡密'
                        , content: '/admin/CdkController/addMembershipCdk'
                        , maxmin: true
                        , area: ['80%', '80%']
                        , resize: true
                        // , btn: ['确定', '取消']
                        , yes: function (index, layero) {
                            var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
                            submit.click();
                        }
                    });
                },
                addPermanent: function () {
                    layer.open({
                        type: 2
                        , title: '新增永久版卡密'
                        , content: '/admin/CdkController/addPermanentCdk'
                        , maxmin: true
                        , area: ['80%', '80%']
                        , resize: true
                        // , btn: ['确定', '取消']
                        , yes: function (index, layero) {
                            var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
                            submit.click();
                        }
                    });
                },
                batchGenerate: function () {
                    layer.open({
                        type: 2
                        , title: '批量生成卡密'
                        , content: '/admin/CdkController/batchGenerate'
                        , maxmin: true
                        , area: ['650px', '700px']
                        , btn: ['关闭']
                        , yes: function (index, layero) {
                            layer.close(index);
                        }
                    });
                },
                batchBan: function () {
                    layer.open({
                        type: 2
                        , title: '🚫 批量封禁卡密'
                        , content: '/admin/CdkController/batchBanCdk'
                        , maxmin: true
                        , area: ['900px', '700px']
                        , resize: true
                        , btn: ['关闭']
                        , yes: function (index, layero) {
                            layer.close(index);
                        }
                    });
                }
            };

            // 监听按钮点击
            $('#batchDeleteBtn, #addCdkBtn, #addOfflineBtn, #addMembershipBtn, #addPermanentBtn, #batchGenerateBtn, #batchBanBtn').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });

            // 封禁卡密弹窗
            window.openBanCdkModal = function(data) {
                var statusText = data.status === 'used' ? '已使用' : '未使用';
                var statusColor = data.status === 'used' ? '#909399' : '#67c23a';

                // 直接弹出确认对话框
                layer.confirm(`
                    <div style="padding: 10px 0;">
                        <div style="margin-bottom: 15px; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #409eff;">
                            <h4 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">📋 卡密信息</h4>
                            <p style="margin: 4px 0; color: #666;"><strong>卡密码：</strong>${data.cdk_code}</p>
                            <p style="margin: 4px 0; color: #666;"><strong>类型：</strong>${data.cdk_type}</p>
                            <p style="margin: 4px 0; color: #666;"><strong>状态：</strong><span style="color: ${statusColor};">${statusText}</span></p>
                            <p style="margin: 4px 0; color: #666;"><strong>商品：</strong>${data.shop || '未知'}</p>
                            ${data.agent_username ? '<p style="margin: 4px 0; color: #666;"><strong>代理：</strong>' + data.agent_username + '</p>' : '<p style="margin: 4px 0; color: #666;"><strong>生成者：</strong>管理员</p>'}
                        </div>

                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 12px; border-radius: 4px; margin-bottom: 15px;">
                            <strong>⚠️ 警告：</strong>封禁操作不可逆！
                            ${data.status === 'used' ? '<br><strong>该卡密已使用，封禁后将扣除用户对应权益！</strong>' : ''}
                        </div>

                        <div style="margin-bottom: 10px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">封禁原因：</label>
                            <textarea id="banReason" placeholder="请输入封禁原因（选填）" style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-family: inherit;"></textarea>
                        </div>
                    </div>
                `, {
                    icon: 3,
                    title: '🚫 确认封禁卡密',
                    area: ['500px', 'auto'],
                    btn: ['确认封禁', '取消'],
                    btn1: function(index) {
                        var reason = $('#banReason').val().trim();
                        // 封禁原因不是必填的，可以为空

                        // 显示加载
                        var loadIndex = layer.load(1, {shade: [0.1,'#fff']});

                        $.ajax({
                            url: '/admin/CdkController/banCdk',
                            type: 'POST',
                            data: {
                                cdk_code: data.cdk_code,
                                reason: reason
                            },
                            success: function(res) {
                                layer.close(loadIndex);

                                if (res.code === 1) {
                                    layer.msg(res.msg, {icon: 1, time: 2000});
                                    layer.close(index);
                                    // 刷新表格
                                    table.reload('LAY-app-cdk-list');
                                } else {
                                    layer.msg(res.msg, {icon: 2, time: 3000});
                                }
                            },
                            error: function() {
                                layer.close(loadIndex);
                                layer.msg('封禁失败，请稍后重试', {icon: 2});
                            }
                        });
                    },
                    btn2: function(index) {
                        layer.close(index);
                    }
                });
            };
        });
    </script>
</body>
</html>