/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */ ;
layui.define(["table", "form"], function(t) {
	var e = layui.$,
        $ = layui.$,
		i = layui.table,
		n = layui.form;
	i.render({
			elem: "#LAY-app-content-list2",
			url: "/admin/base/orderList",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 60,
						title: "ID",
						sort: true
					},
					{
						field: "ord_uid",
						width: 120,
						title: "发起账户",
						sort: true
					},
                    // {
					// 	field: "ord_ybh",
					// 	title: "易支付订单号",
					// 	minWidth: 100
					// },
					{
						field: "ord_bbh",
						minWidth: 120,
						title: "平台订单号",
					},
					{
						field: "ord_type",
						title: "支付类型",
						minWidth: 120,
						sort: true,
						toolbar: "#ord_type"
					},
					{
						field: "GoodType",
						title: "账号类型",
						minWidth: 120,
						sort: true,
						toolbar: "#co_type"
					},
					{
						field: "ord_money",
						title: "订单金额",
						minWidth: 100,
						sort: true,
					},
					{
						field: "ord_name",
						title: "商品名称",
						minWidth: 100,
						align: "center",
					},
					{
						field: "ord_ifpay",
						title: "是否支付",
						minWidth: 100,
						align: "center",
						toolbar: "#ord_ifpay"
					},
					{
						field: "ord_combo",
						title: "对应套餐",
						minWidth: 160,
						align: "center",
					},
					{
						field: "time",
						title: "更新时间",
						minWidth: 160,
					},
					{
						title: "操作",
						width: 120,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-com"
					}
				]
			],
			page: true,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list2)",
			function(t) {
				t.data;
				"del" === t.event ? layer.confirm("确定删除此订单？",
					function(e) {
					    console.log(t.data)
						t.del(), layer.close(e)
						layui.$.ajax({
                                    url: '/admin/base/delOrder',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "编辑套餐",
					content: "/admin/index/upOrder?id="+t.data.id,
					area: ["500px", "500px"],
					btn: ["确定", "取消"],
					yes: function(t, e) {
						var n = window["layui-layer-iframe" + t],
							l = "layuiadmin-app-form-submit",
							a = e.find("iframe")
							.contents()
							.find("#" + l);
						n.layui.form.on("submit(" + l + ")",
							function(e) {
							    console.log(e.field)
								    $.ajax({
                                        url: '/admin/base/upOrder',
                                        method: 'post',
                                        data: e.field,
                                        success: function(res){
                                        }
                                    });
								e.field;
								layer.msg('保存成功');
								i.reload("LAY-app-content-list2"),
									layer.close(t)
							}), a.trigger("click")
					},
					success: function(t, e) {
                            
					}
				})

			}),
		t("order", {})
});