<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>代理表单</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

<div class="layui-form" lay-filter="layuiadmin-form-admin" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label">代理账号</label>
        <div class="layui-input-inline">
            <input type="text" name="username" lay-verify="required" placeholder="请输入代理账号(3-50字符)" autocomplete="off" class="layui-input" maxlength="50">
        </div>
        <div class="layui-form-mid layui-word-aux">用于代理登录的唯一标识</div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">登录密码</label>
        <div class="layui-input-inline">
            <input type="password" name="password" placeholder="请输入密码(6-50字符)" autocomplete="off" class="layui-input" maxlength="50">
        </div>
        <div class="layui-form-mid layui-word-aux">编辑时不填则不修改</div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">折扣比例</label>
        <div class="layui-input-inline">
            <input type="number" name="discount_rate" lay-verify="required|number" placeholder="请输入折扣比例(0-100)" autocomplete="off" class="layui-input" min="0" max="100" step="0.01">
        </div>
        <div class="layui-form-mid layui-word-aux">%</div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">余额</label>
        <div class="layui-input-inline">
            <input type="number" name="balance" lay-verify="required|number" placeholder="请输入余额" autocomplete="off" class="layui-input" min="0" step="0.01">
        </div>
        <div class="layui-form-mid layui-word-aux">元</div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <input type="checkbox" name="status" lay-skin="switch" lay-text="启用|禁用" value="1" checked>
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <button class="layui-btn" lay-submit lay-filter="LAY-agent-submit" id="LAY-agent-submit">提交</button>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
layui.config({
    base: '/layuiadmin/'
}).extend({
    index: 'lib/index'
}).use(['form'], function(){
    var form = layui.form
    ,$ = layui.$;

    // 获取编辑数据
    var id = location.search.match(/id=(\d+)/);
    if(id){
        $.ajax({
            url: '/admin/index/getAgentInfo'
            ,type: 'GET'
            ,data: {id: id[1]}
            ,success: function(res){
                if(res.code == 0){
                    var data = res.data;
                    // 处理状态字段的回显
                    if(data.status == 1){
                        data.status = 'on';
                    } else {
                        data.status = '';
                    }
                    form.val('layuiadmin-form-admin', data);
                }
            }
        });
    }
});
</script>
</body>
</html>