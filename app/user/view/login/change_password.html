<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <script src="/static/js/jquery.min.js"></script>
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --success-color: #10b981;
            --error-color: #ef4444;
            --neutral-light: #f3f4f6;
            --neutral-dark: #1f2937;
        }
        
        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 25px 30px -5px rgba(0, 0, 0, 0.1), 0 15px 15px -5px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .btn-primary:hover::after {
            opacity: 1;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(59, 130, 246, 0.3);
        }
        
        .input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .input-group input {
            width: 100%;
            padding: 0.875rem 1.25rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
            background-color: #f9fafb;
        }
        
        .input-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            background-color: white;
        }
        
        .input-group .icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 1rem;
            color: #9ca3af;
        }
        
        .input-group.with-icon input {
            padding-left: 3rem;
        }
        
        .send-code {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            background-color: var(--neutral-light);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .send-code:hover {
            background-color: #e5e7eb;
        }
        
        .form-step {
            display: none;
            animation: fadeIn 0.5s ease forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .form-step.active {
            display: block;
        }
        
        .steps-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2.5rem;
            position: relative;
        }
        
        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 120px;
            z-index: 10;
        }
        
        .step-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.75rem;
            color: #6b7280;
            font-weight: bold;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .step-circle::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .step-circle.active {
            background-color: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }
        
        .step-circle.active::after {
            border-color: var(--primary-color);
            transform: scale(1.2);
            opacity: 0.5;
        }
        
        .step-circle.completed {
            background-color: var(--success-color);
            color: white;
        }
        
        .step-label {
            font-weight: 500;
            color: #4b5563;
            transition: all 0.3s ease;
        }
        
        .step-label.active {
            color: var(--primary-color);
        }
        
        .step-line {
            height: 3px;
            position: absolute;
            top: 18px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 70%;
            background-color: #e5e7eb;
            z-index: 1;
        }
        
        .step-line-progress {
            height: 100%;
            width: 0%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            transition: width 0.5s ease;
        }
        
        .alert {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            animation: fadeIn 0.3s ease forwards;
        }
        
        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--error-color);
            color: #991b1b;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-left: 4px solid var(--success-color);
            color: #065f46;
        }
        
        .alert-icon {
            margin-right: 0.75rem;
            flex-shrink: 0;
        }
        
        .alert-content {
            flex-grow: 1;
        }
        
        .alert-close {
            margin-left: 0.75rem;
            cursor: pointer;
            flex-shrink: 0;
        }
        
        #captcha {
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .password-strength {
            margin-top: 0.5rem;
            height: 4px;
            border-radius: 2px;
            background-color: #e5e7eb;
            overflow: hidden;
        }
        
        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: width 0.3s ease, background-color 0.3s ease;
        }
        
        .password-strength-text {
            display: flex;
            justify-content: space-between;
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .password-hint {
            color: #6b7280;
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }
        
        .password-hint.active {
            color: var(--success-color);
        }
        
        .checkmark-circle {
            width: 80px;
            height: 80px;
            position: relative;
            display: inline-block;
            vertical-align: top;
            margin-bottom: 1rem;
        }
        
        .checkmark-circle .background {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--success-color);
            position: absolute;
        }
        
        .checkmark-circle .checkmark {
            border-radius: 5px;
        }
        
        .checkmark-circle .checkmark.draw:after {
            animation-delay: 100ms;
            animation-duration: 1s;
            animation-timing-function: ease;
            animation-name: checkmark;
            transform: scaleX(-1) rotate(135deg);
            animation-fill-mode: forwards;
        }
        
        .checkmark-circle .checkmark:after {
            opacity: 0;
            height: 40px;
            width: 20px;
            transform-origin: left top;
            border-right: 4px solid #ffffff;
            border-top: 4px solid #ffffff;
            border-radius: 2px !important;
            content: '';
            left: 31px;
            top: 45px;
            position: absolute;
        }
        
        @keyframes checkmark {
            0% {
                height: 0;
                width: 0;
                opacity: 1;
            }
            20% {
                height: 0;
                width: 20px;
                opacity: 1;
            }
            40% {
                height: 40px;
                width: 20px;
                opacity: 1;
            }
            100% {
                height: 40px;
                width: 20px;
                opacity: 1;
            }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="card max-w-md w-full p-8">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">修改密码</h2>
            <p class="text-gray-600">设置安全的密码以保护您的账户</p>
        </div>
        
        <!-- 步骤进度条 -->
        <div class="step-line">
            <div class="step-line-progress" id="step-progress"></div>
        </div>
        
        <!-- 步骤指示器 -->
        <div class="steps-indicator mb-8">
            <div class="step-item">
                <div class="step-circle active" id="step-circle-1">1</div>
                <div class="step-label active" id="step-label-1">手机验证</div>
            </div>
            <div class="step-item">
                <div class="step-circle" id="step-circle-2">2</div>
                <div class="step-label" id="step-label-2">设置密码</div>
            </div>
            <div class="step-item">
                <div class="step-circle" id="step-circle-3">3</div>
                <div class="step-label" id="step-label-3">完成</div>
            </div>
        </div>
        
        <!-- 消息提示区域 -->
        <div id="alert-container" class="mb-4 hidden"></div>

        <!-- 第一步：手机验证 -->
        <div id="step1" class="form-step active">
            <div class="input-group with-icon">
                <svg class="icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <input type="tel" id="phone" name="phone" required 
                       pattern="^1[3-9]\d{9}$"
                       placeholder="您的手机号"
                       value="{$user.us_username ? $user.us_username : cookie('username')}"
                       readonly
                       class="appearance-none bg-gray-50">
            </div>

            <div id="captcha" class="transform hover:scale-[1.01] transition-transform"></div>

            <div class="input-group with-icon">
                <svg class="icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <input type="text" id="code" name="code" required
                       placeholder="请输入验证码"
                       class="appearance-none">
                <span class="send-code" onclick="sendCode()">发送验证码</span>
            </div>

            <button type="button" 
                    onclick="verifyCode()"
                    class="btn-primary w-full py-4 px-6 border border-transparent rounded-lg
                           text-white font-medium focus:outline-none text-base mt-4">
                验证并继续
            </button>
        </div>

        <!-- 第二步：设置新密码 -->
        <div id="step2" class="form-step">
            <div class="input-group with-icon">
                <svg class="icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <input type="password" id="newPassword" name="newPassword" required 
                       placeholder="请输入新密码"
                       minlength="6"
                       class="appearance-none"
                       oninput="checkPasswordStrength()">
            </div>
            
            <div class="password-strength">
                <div class="password-strength-bar" id="strength-bar"></div>
            </div>
            <div class="password-strength-text">
                <span>弱</span>
                <span>中</span>
                <span>强</span>
            </div>
            
            <div class="mt-4 mb-6">
                <div class="password-hint" id="length-hint">✓ 至少6个字符</div>
                <div class="password-hint" id="number-hint">✓ 包含数字</div>
                <div class="password-hint" id="letter-hint">✓ 包含字母</div>
                <div class="password-hint" id="special-hint">✓ 包含特殊字符</div>
            </div>

            <div class="input-group with-icon">
                <svg class="icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                <input type="password" id="confirmPassword" name="confirmPassword" required
                       placeholder="请确认新密码"
                       minlength="6"
                       class="appearance-none"
                       oninput="checkPasswordMatch()">
            </div>
            <div id="password-match" class="hidden mt-1 text-xs text-green-600">两次密码输入一致</div>
            <div id="password-mismatch" class="hidden mt-1 text-xs text-red-600">两次密码输入不一致</div>

            <button type="button" 
                    onclick="changePassword()"
                    class="btn-primary w-full py-4 px-6 border border-transparent rounded-lg
                           text-white font-medium focus:outline-none text-base mt-6">
                确认修改
            </button>
        </div>

        <!-- 第三步：完成 -->
        <div id="step3" class="form-step">
            <div class="text-center py-8">
                <div class="checkmark-circle">
                    <div class="background"></div>
                    <div class="checkmark draw"></div>
                </div>
                <h3 class="text-2xl font-semibold text-gray-900 mb-3">密码修改成功</h3>
                <p class="text-gray-600 mb-8">您的密码已经成功修改，请使用新密码登录</p>
                <button type="button" 
                        onclick="goToLoginPage()"
                        class="btn-primary py-4 px-8 border border-transparent rounded-lg
                               text-white font-medium focus:outline-none inline-block">
                    返回首页
                </button>
            </div>
        </div>
    </div>

    <!-- 极验 -->
    <script src="https://static.geetest.com/static/js/gt.0.4.9.js"></script>
    <script>
        let countdown = 60;
        let timer = null;
        let geetestObj;
        let verificationToken = '';

        // 显示提示信息
        function showAlert(type, message) {
            const container = document.getElementById('alert-container');
            container.innerHTML = `
                <div class="alert ${type === 'error' ? 'alert-error' : 'alert-success'}">
                    <div class="alert-icon">
                        ${type === 'error' ? 
                            '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' : 
                            '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
                        }
                    </div>
                    <div class="alert-content">${message}</div>
                    <div class="alert-close" onclick="closeAlert()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                </div>
            `;
            container.classList.remove('hidden');
            
            // 5秒后自动关闭
            setTimeout(() => {
                closeAlert();
            }, 5000);
        }
        
        // 关闭提示信息
        function closeAlert() {
            const container = document.getElementById('alert-container');
            container.classList.add('hidden');
        }

        function initGeetestCaptcha() {
            let retryCount = 0;
            const maxRetries = 3;

            function tryInit() {
                // 设置默认API地址，确保有效性
                const geeApi = "<?php echo isset($config['gee_api']) ? htmlentities(str_replace('http;//', 'http://', $config['gee_api'])) : 'https://api.geetest.com/'; ?>";
                
                // 验证API地址格式
                if (!geeApi.startsWith('http://') && !geeApi.startsWith('https://')) {
                    console.error("极验API地址格式错误");
                    showAlert('error', "系统配置错误，请联系管理员");
                    return;
                }
                
                $.ajax({
                    url: geeApi + "register",
                    type: "get",
                    dataType: "json",
                    success: function (data) {
                        if (data.gt && data.challenge) {
                            if ($("#captcha").length === 0) {
                                console.error("Captcha container not found");
                                return;
                            }

                            initGeetest({
                                gt: data.gt,
                                challenge: data.challenge,
                                offline: !data.success,
                                new_captcha: true,
                                product: "popup",
                                width: "100%"
                            }, function (captchaObj) {
                                geetestObj = captchaObj;
                                
                                $("#captcha").empty();
                                captchaObj.appendTo("#captcha");

                                captchaObj.onReady(function() {
                                    console.log("Geetest is ready");
                                });

                                captchaObj.onSuccess(function () {
                                    const phone = $('#phone').val().trim();
                                    if(!/^1[3-9]\d{9}$/.test(phone)) {
                                        showAlert('error', '请输入正确的手机号');
                                        geetestObj.reset();
                                        return;
                                    }

                                    $.ajax({
                                        url: "/user/base/verify",
                                        type: "post",
                                        data: {
                                            username: phone,
                                            geetest_challenge: geetestObj.getValidate().geetest_challenge,
                                            geetest_validate: geetestObj.getValidate().geetest_validate,
                                            geetest_seccode: geetestObj.getValidate().geetest_seccode,
                                            actionType: "changePassword"
                                        },
                                        success: function(res) {
                                            console.log("验证响应:", res);
                                            // 强化错误处理 - 修改此部分逻辑
                                            if(res.msg === 1 || (res.msg === 0 && res.error === "Array to string conversion")) {
                                                startCountdown();
                                                showAlert('success', '验证码已发送');
                                            } else {
                                                showAlert('error', res.content || '发送失败');
                                                geetestObj.reset();
                                            }
                                        },
                                        error: function(xhr) {
                                            showAlert('error', '网络错误，请稍后再试');
                                            geetestObj.reset();
                                        }
                                    });
                                });
                            });
                        } else {
                            if (retryCount < maxRetries) {
                                retryCount++;
                                setTimeout(tryInit, 1000);
                            } else {
                                showAlert('error', "极验初始化失败，请刷新页面重试");
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("极验初始化请求失败:", error);
                        if (retryCount < maxRetries) {
                            retryCount++;
                            setTimeout(tryInit, 1000);
                        } else {
                            showAlert('error', "验证服务暂时不可用，请稍后再试");
                        }
                    }
                });
            }

            tryInit();
        }

        // 页面加载完成初始化极验
        document.addEventListener('DOMContentLoaded', function() {
            initGeetestCaptcha();
        });

        function sendCode() {
            const sendBtn = document.querySelector('.send-code');
            if(sendBtn.classList.contains('disabled')) return;
            
            // 触发极验验证
            geetestObj.verify();
        }

        function startCountdown() {
            const sendBtn = document.querySelector('.send-code');
            sendBtn.classList.add('disabled');
            sendBtn.style.backgroundColor = '#e5e7eb';
            sendBtn.style.cursor = 'not-allowed';

            timer = setInterval(() => {
                sendBtn.textContent = `${countdown}秒后重试`;
                countdown--;
                
                if(countdown < 0) {
                    clearInterval(timer);
                    sendBtn.textContent = '发送验证码';
                    sendBtn.classList.remove('disabled');
                    sendBtn.style.backgroundColor = '';
                    sendBtn.style.cursor = 'pointer';
                    countdown = 60;
                }
            }, 1000);
        }

        function verifyCode() {
            const code = document.getElementById('code').value;
            const phone = document.getElementById('phone').value;
            
            if(!code) {
                showAlert('error', '请输入验证码');
                return;
            }
            
            // 验证短信验证码
            $.ajax({
                url: "/user/login/verifyCode",
                type: "post",
                data: {
                    phone: phone,
                    code: code,
                    actionType: "changePassword"
                },
                success: function(res) {
                    if(res.code === 0) {
                        verificationToken = res.token; // 保存验证令牌
                        goToStep(2);
                    } else {
                        showAlert('error', res.msg || '验证码错误');
                    }
                },
                error: function() {
                    showAlert('error', '网络错误，请稍后再试');
                }
            });
        }
        
        // 检查密码强度
        function checkPasswordStrength() {
            const password = document.getElementById('newPassword').value;
            const strengthBar = document.getElementById('strength-bar');
            const lengthHint = document.getElementById('length-hint');
            const numberHint = document.getElementById('number-hint');
            const letterHint = document.getElementById('letter-hint');
            const specialHint = document.getElementById('special-hint');
            
            // 重置所有提示
            [lengthHint, numberHint, letterHint, specialHint].forEach(hint => {
                hint.classList.remove('active');
                hint.style.color = '';
            });
            
            // 检查各项条件
            const hasLength = password.length >= 6;
            const hasNumber = /\d/.test(password);
            const hasLetter = /[a-zA-Z]/.test(password);
            const hasSpecial = /[^a-zA-Z0-9]/.test(password);
            
            // 更新提示样式
            if (hasLength) {
                lengthHint.classList.add('active');
            }
            
            if (hasNumber) {
                numberHint.classList.add('active');
            }
            
            if (hasLetter) {
                letterHint.classList.add('active');
            }
            
            if (hasSpecial) {
                specialHint.classList.add('active');
            }
            
            // 计算密码强度
            let strength = 0;
            if (hasLength) strength += 25;
            if (hasNumber) strength += 25;
            if (hasLetter) strength += 25;
            if (hasSpecial) strength += 25;
            
            // 更新强度条
            strengthBar.style.width = strength + '%';
            
            // 设置颜色
            if (strength <= 25) {
                strengthBar.style.backgroundColor = '#ef4444'; // 红色
            } else if (strength <= 50) {
                strengthBar.style.backgroundColor = '#f59e0b'; // 橙色
            } else if (strength <= 75) {
                strengthBar.style.backgroundColor = '#10b981'; // 绿色
            } else {
                strengthBar.style.backgroundColor = '#059669'; // 深绿色
            }
            
            // 检查密码匹配
            checkPasswordMatch();
        }
        
        // 检查两次密码是否一致
        function checkPasswordMatch() {
            const password = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const matchIndicator = document.getElementById('password-match');
            const mismatchIndicator = document.getElementById('password-mismatch');
            
            if (confirmPassword) {
                if (password === confirmPassword) {
                    matchIndicator.classList.remove('hidden');
                    mismatchIndicator.classList.add('hidden');
                } else {
                    matchIndicator.classList.add('hidden');
                    mismatchIndicator.classList.remove('hidden');
                }
            } else {
                matchIndicator.classList.add('hidden');
                mismatchIndicator.classList.add('hidden');
            }
        }

        function changePassword() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if(newPassword.length < 6) {
                showAlert('error', '密码长度至少6位');
                return;
            }
            
            if(newPassword !== confirmPassword) {
                showAlert('error', '两次输入的密码不一致');
                return;
            }
            
            // 提交修改密码请求
            $.ajax({
                url: "/user/base/doChangePassword",
                type: "post",
                data: {
                    token: verificationToken,
                    newPassword: newPassword
                },
                success: function(res) {
                    if(res.code === 0) {
                        goToStep(3);
                    } else {
                        showAlert('error', res.msg || '修改失败，请重试');
                    }
                },
                error: function() {
                    showAlert('error', '网络错误，请稍后再试');
                }
            });
        }

        function goToStep(stepNumber) {
            // 隐藏所有步骤
            document.querySelectorAll('.form-step').forEach(step => {
                step.classList.remove('active');
            });
            
            // 显示当前步骤
            document.getElementById(`step${stepNumber}`).classList.add('active');
            
            // 更新进度条
            const progress = document.getElementById('step-progress');
            if (stepNumber === 1) {
                progress.style.width = '0%';
            } else if (stepNumber === 2) {
                progress.style.width = '50%';
            } else if (stepNumber === 3) {
                progress.style.width = '100%';
            }
            
            // 更新步骤指示器
            for(let i = 1; i <= 3; i++) {
                const circle = document.getElementById(`step-circle-${i}`);
                const label = document.getElementById(`step-label-${i}`);
                
                if(i < stepNumber) {
                    circle.classList.remove('active');
                    circle.classList.add('completed');
                    circle.innerHTML = '✓';
                    label.classList.remove('active');
                } else if(i === stepNumber) {
                    circle.classList.add('active');
                    circle.classList.remove('completed');
                    circle.innerHTML = i;
                    label.classList.add('active');
                } else {
                    circle.classList.remove('active', 'completed');
                    circle.innerHTML = i;
                    label.classList.remove('active');
                }
            }
        }

        function goToLoginPage() {
            window.location.href = '/';
        }
    </script>
</body>
</html>