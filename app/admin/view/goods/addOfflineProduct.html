<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加离线商品</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>
<div class="layui-form" lay-filter="layuiadmin-form-useradmin" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label required">选择商品</label>
        <div class="layui-input-block">
            <select name="product_id" lay-verify="required" lay-filter="productSelect" lay-search>
                <option value="">请选择商品</option>
                {foreach $goodsList as $goods}
                    <option value="{$goods.id}">{$goods.goods_name}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label required">商品金额</label>
        <div class="layui-input-block">
            <input type="number" name="product_amount" lay-verify="required|number" lay-reqtext="商品金额不能为空" placeholder="请输入商品金额" class="layui-input">
            <tip>请输入正确的金额数值</tip>
        </div>
    </div>

    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn"><i class="layui-icon">&#xe605;</i>保存</button>
            <button class="layui-btn layui-btn-primary" type="reset">重置</button>
        </div>
    </div>
</div>
<script src="/layuiadmin/layui/layui.js"></script>
<script>
layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['form'], function () {
    var form = layui.form,
        layer = layui.layer,
        $ = layui.$;

    //监听提交
    form.on('submit(saveBtn)', function (data) {
        $.ajax({
            url: '/admin/Base/addOfflineProduct',
            type: 'POST',
            dataType: 'json',
            data: data.field,
            success: function(res) {
                if(res.code === 0) {
                    var index = layer.alert('添加成功', {
                        title: '提示'
                    }, function () {
                        layer.close(index);
                        var iframeIndex = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(iframeIndex);
                        parent.layui.table.reload('currentTableId');
                    });
                } else {
                    layer.msg('添加失败：' + res.msg);
                }
            }
        });
        return false;
    });
});
</script>
</body>
</html> 