<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$noticeContent.not_title} - {$system.sy_title}</title>
    <meta name="description" content="{$noticeContent.not_des}" />
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <script src="/static/js/jquery.min.js"></script>
    <style>
        /* 确保侧边栏导航与顶部导航样式区分 */
        .side-nav-container {
            margin-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            padding-top: 1.5rem;
        }
        
        /* 调整侧边栏导航与公告导航的间距 */
        .notice-nav-container {
            margin-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            padding-top: 1.5rem;
        }
        
        /* 富文本内容样式增强 */
        .rich-content {
            line-height: 1.8;
            color: #333;
        }
        
        /* 段落样式 */
        .rich-content p {
            margin-bottom: 1rem;
        }
        
        /* 标题样式 */
        .rich-content h1 {
            font-size: 1.8rem;
            font-weight: bold;
            margin: 1.5rem 0 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
        
        .rich-content h2 {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 1.5rem 0 1rem;
        }
        
        .rich-content h3 {
            font-size: 1.3rem;
            font-weight: bold;
            margin: 1rem 0 0.8rem;
        }
        
        /* 列表样式 */
        .rich-content ul {
            list-style-type: disc;
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .rich-content ol {
            list-style-type: decimal;
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .rich-content li {
            margin-bottom: 0.5rem;
        }
        
        /* 表格样式 */
        .rich-content table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        .rich-content table, .rich-content th, .rich-content td {
            border: 1px solid #e2e8f0;
        }
        
        .rich-content th, .rich-content td {
            padding: 0.75rem;
            text-align: left;
        }
        
        .rich-content th {
            background-color: #f8fafc;
        }
        
        /* 图片样式 */
        .rich-content img {
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
            border-radius: 0.25rem;
        }
        
        /* 链接样式 */
        .rich-content a {
            color: #3182ce;
            text-decoration: none;
        }
        
        .rich-content a:hover {
            text-decoration: underline;
        }
        
        /* 引用样式 */
        .rich-content blockquote {
            border-left: 4px solid #e2e8f0;
            padding-left: 1rem;
            color: #4a5568;
            font-style: italic;
            margin: 1rem 0;
        }
        
        /* 代码样式 */
        .rich-content pre {
            background-color: #f7fafc;
            border-radius: 0.25rem;
            padding: 1rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        .rich-content code {
            font-family: monospace;
            background-color: #f7fafc;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }
    </style>
</head>

<body class="bg-gray-100 text-gray-800">

    <!-- 引用头部 -->
    {include file='../app/user/view/ticket/header.html'}	

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row bg-white shadow-md rounded-lg overflow-hidden mb-8">
            <!-- 左侧侧边栏 - 只保留用户信息和导航 -->
            <div class="md:w-1/4 p-4 bg-gray-50">
                <div class="flex flex-col items-center">
                    <img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" alt="User Logo" class="w-24 h-24 rounded-full border-2 border-gray-300 mb-4">
                    <span class="text-lg font-semibold">{$user.us_username}</span>
                </div>
                
                <!-- 侧边栏导航 -->
                <div class="mt-4">
                    {include file='../app/user/view/ticket/index.html'}
                </div>
            </div>
            
            <!-- 右侧内容区域 -->
            <div class="md:w-3/4 p-4">
                <!-- 文章详情 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                    <div class="p-4 bg-gray-50 border-b border-gray-200">
                        <h1 class="text-xl font-bold text-gray-800">{$noticeContent.not_title}</h1>
                        <div class="flex items-center mt-2 text-sm text-gray-500">
                            <span>发布时间：{$noticeContent.time}</span>
                            <span class="mx-2">|</span>
                            <span>
                                分类：
                                {eq name="noticeContent.not_system" value="0"}
                                    帮助中心
                                {else/}
                                    系统公告
                                {/eq}
                            </span>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <!-- 文章内容 - 增加 rich-content 类支持更好的富文本显示 -->
                        <div class="rich-content prose max-w-none text-gray-800">
                            {$noticeContent.not_content|raw}
                        </div>
                        
                        <!-- 返回按钮 -->
                        <div class="mt-8 text-center">
                            <a href="{eq name='currentSection' value='help'}/user/index/notice{else/}/user/index/systemGG{/eq}" 
                               class="inline-block px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition">
                                返回列表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>