<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>添加商品</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
      <div class="layui-form-item">
      <label class="layui-form-label">选择分类</label>
      <div class="layui-input-inline">
        <select name="goods_class" id="goods_class">
            <option value="">请选择标签</option>
            {foreach $goodsclassAll as $index=>$val}
              <option value="{$val.id}">{$val.cl_name}</option>
            {/foreach}
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">商品名称</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_name" id="goods_name" placeholder="商品名称" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">永久版定价</label>
      <div class="layui-input-inline">
        <input type="number" name="permanent_price2" id="permanent_price2" placeholder="请输入永久版定价" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">是否允许提取</label>
      <div class="layui-input-inline">
        <select name="limited" id="limited">
          <option value="0">不允许</option>
          <option value="1">允许</option>
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">在线版状态</label>
      <div class="layui-input-inline">
        <select name="online_status" id="online_status">
          <option value="0">不可售</option>
          <option value="1" selected>可售</option>
        </select>
      </div>
      <div class="layui-form-mid layui-word-aux">控制时租、日租、永久版是否可售</div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">离线版状态</label>
      <div class="layui-input-inline">
        <select name="offline_status" id="offline_status">
          <option value="0">不可售</option>
          <option value="1" selected>可售</option>
        </select>
      </div>
      <div class="layui-form-mid layui-word-aux">控制离线版是否可售</div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">销量</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_xl" id="goods_xl" placeholder="默认为0" autocomplete="off" class="layui-input" value="0">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">标签词</label>
      <div class="layui-input-inline">
        <input type="text" name="tag_input" id="tag_input" placeholder="请输入标签" autocomplete="off" class="layui-input">
        <div class="selected-tags" id="selected-tags" style="margin-top: 10px;"></div>
        <input type="hidden" name="goods_key" id="goods_key" value="">
      </div>
      <div class="layui-input-inline" style="margin-left: 10px;">
        <button type="button" class="layui-btn layui-btn-primary" id="add-tag">添加标签</button>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">选择已有标签</label>
      <div class="layui-input-block">
        <div class="layui-btn-container" id="all-tags">
          {foreach $tagsList as $tag}
          <button type="button" class="layui-btn layui-btn-primary layui-btn-sm tag-option" data-tag="{$tag.tag_name}">{$tag.tag_name}</button>
          {/foreach}
        </div>
      </div>
    </div>
     <div class="layui-form-item">
      <label class="layui-form-label">商品主图</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img" id="goods_img" placeholder="请上传图片" autocomplete="off" class="layui-input" value="">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-img1">上传图片</button> 
    </div>
    
    <div class="layui-form-item">
      <label class="layui-form-label">商品图片2</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img2" id="goods_img2" placeholder="选填" autocomplete="off" class="layui-input" value="">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-img2">上传图片</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">商品图片3</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img3" id="goods_img3" placeholder="选填" autocomplete="off" class="layui-input" value="">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-img3">上传图片</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">商品图片4</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img4" id="goods_img4" placeholder="选填" autocomplete="off" class="layui-input" value="">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-img4">上传图片</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">商品视频</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_video" id="goods_video" placeholder="选填" autocomplete="off" class="layui-input" value="">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-video">上传视频</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">描述</label>
      <div class="layui-input-inline" style="width: 100%;">
        <textarea id="goods_des" name="goods_des" style="display:none;"></textarea>
      </div>
    </div>
    
    <div class="layui-form-item">
      <div class="layui-input-block">
        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        <button class="layui-btn" id="submitBtn" type="button">提交</button>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form', 'upload', 'layedit', 'layer'], function(){
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload
    ,layedit = layui.layedit
    ,layer = layui.layer;
    
    // 初始化富文本编辑器
    var editIndex = layedit.build('goods_des', {
      height: 300, // 设置编辑器高度
      tool: [
        'strong' // 加粗
        ,'italic' // 斜体
        ,'underline' // 下划线
        ,'left' // 左对齐
        ,'center' // 居中对齐
        ,'right' // 右对齐
        ,'link' // 超链接
        ,'unlink' // 清除链接
        ,'image' // 插入图片
      ]
    });

    //图片上传
    upload.render({
      elem: '#upload-img1'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });
    
    upload.render({
      elem: '#upload-img2'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });

    upload.render({
      elem: '#upload-img3'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });

    upload.render({
      elem: '#upload-img4'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });

    //视频上传
    upload.render({
      elem: '#upload-video'
      ,url: '/uploads'
      ,accept: 'video'
      ,method: 'get'
      ,acceptMime: 'video/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });
    
    // 标签管理
    var selectedTags = [];
    
    // 更新标签显示和隐藏输入
    function updateTags() {
      var tagsHtml = '';
      for(var i = 0; i < selectedTags.length; i++) {
        tagsHtml += '<span class="layui-badge layui-bg-blue" style="margin-right:5px;padding:5px;margin-bottom:5px;cursor:pointer;" data-index="' + i + '">' + 
                    selectedTags[i] + ' <i class="layui-icon layui-icon-close"></i></span>';
      }
      $('#selected-tags').html(tagsHtml);
      $('#goods_key').val(selectedTags.join(','));
    }
    
    // 刷新标签列表
    function refreshTagsList() {
      $.ajax({
        url: '/admin/base/getTagsList',
        type: 'get',
        success: function(res) {
          if(res.code === 0) {
            var tagsHtml = '';
            $.each(res.data, function(index, tag) {
              tagsHtml += '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm tag-option" data-tag="' + tag.tag_name + '">' + tag.tag_name + '</button>';
            });
            $('#all-tags').html(tagsHtml);
          } else {
            layer.msg('获取标签列表失败: ' + res.msg);
          }
        },
        error: function(xhr, status, error) {
          layer.msg('获取标签列表请求失败: ' + error);
        }
      });
    }
    
    // 添加新标签到数据库
    function addTagToDatabase(tag) {
      $.ajax({
        url: '/admin/base/addTag',
        type: 'post',
        data: {
          tag_name: tag
        },
        success: function(res) {
          if(res.code === 0) {
            layer.msg('标签添加成功');
            refreshTagsList(); // 刷新标签列表
          } else {
            layer.msg('标签添加失败: ' + res.msg);
          }
        },
        error: function(xhr, status, error) {
          layer.msg('添加标签请求失败: ' + error);
        }
      });
    }
    
    // 添加标签按钮点击事件
    $('#add-tag').on('click', function() {
      var tag = $('#tag_input').val().trim();
      console.log('添加标签:', tag);
      
      if(tag !== '' && selectedTags.indexOf(tag) === -1) {
        // 先添加到已选标签
        selectedTags.push(tag);
        updateTags();
        $('#tag_input').val(''); // 清空输入框
        
        // 然后添加到数据库
        addTagToDatabase(tag);
      } else if(tag === '') {
        layer.msg('请输入标签');
      } else {
        layer.msg('该标签已添加');
      }
    });
    
    // 点击已有标签添加
    $(document).on('click', '.tag-option', function() {
      var tag = $(this).data('tag');
      if(selectedTags.indexOf(tag) === -1) {
        selectedTags.push(tag);
        updateTags();
      } else {
        layer.msg('该标签已添加');
      }
    });
    
    // 删除标签事件
    $(document).on('click', '#selected-tags .layui-badge', function() {
      var index = $(this).data('index');
      selectedTags.splice(index, 1);
      updateTags();
    });
    
    // 回车键添加标签
    $('#tag_input').on('keypress', function(e) {
      if(e.which === 13) {
        e.preventDefault();
        var tag = $(this).val().trim();
        if(tag !== '' && selectedTags.indexOf(tag) === -1) {
          // 先添加到已选标签
          selectedTags.push(tag);
          updateTags();
          $(this).val(''); // 清空输入框
          
          // 然后添加到数据库
          addTagToDatabase(tag);
        }
      }
    });
    
    // 提交按钮点击事件
    $('#submitBtn').on('click', function() {
      // 同步富文本编辑器内容
      layedit.sync(editIndex);
      
      // 获取表单数据
      var goods_class = $('#goods_class').val();
      var goods_name = $('#goods_name').val();
      var goods_img = $('#goods_img').val();
      var goods_img2 = $('#goods_img2').val();
      var goods_img3 = $('#goods_img3').val();
      var goods_img4 = $('#goods_img4').val();
      var goods_video = $('#goods_video').val();
      var goods_money = '200'; // 这里是硬编码的值，不需要验证
      var permanent_price2 = $('#permanent_price2').val();
      var goods_xl = $('#goods_xl').val();
      var goods_key = $('#goods_key').val();
      var goods_des = $('#goods_des').val();
      var limited = $('#limited').val();
      
      // 验证必填字段
      if(!goods_class) {
        layer.msg('请选择商品分类');
        return false;
      }
      if(!goods_name) {
        layer.msg('请输入商品名称');
        return false;
      }
      if(!goods_img) {
        layer.msg('请上传商品图片');
        return false;
      }
      if(!permanent_price2) {
        layer.msg('请输入永久版定价');
        return false;
      }
      if(selectedTags.length === 0) {
        layer.msg('请添加至少一个标签');
        return false;
      }
      if(!goods_des) {
        layer.msg('请输入商品描述');
        return false;
      }
      
      // 构建提交数据
      var data = {
        goods_class: goods_class,
        goods_name: goods_name,
        goods_img: goods_img,
        goods_img2: goods_img2,
        goods_img3: goods_img3,
        goods_img4: goods_img4,
        goods_video: goods_video,
        goods_money: goods_money,
        permanent_price2: permanent_price2,
        goods_xl: goods_xl || '0',
        goods_key: goods_key,
        goods_des: goods_des,
        limited: limited || '0'
      };
      
      console.log('准备提交数据:', data);
      
      // 发送AJAX请求
      $.ajax({
        url: '/admin/base/addGoods',
        type: 'post',
        data: data,
        success: function(res) {
          console.log('服务器响应:', res);

          // 检查返回的code字段
          if (res.code === 0) {
            // 成功
            layer.msg('添加商品成功', {icon: 1});

            // 关闭弹窗并刷新父页面
            var index = parent.layer.getFrameIndex(window.name);
            parent.layui.table.reload('LAY-app-content-list2');
            parent.layer.close(index);
          } else {
            // 失败
            layer.msg(res.msg || '添加商品失败', {icon: 2});
          }
        },
        error: function(xhr, status, error) {
          console.error('提交失败:', error);
          layer.msg('添加商品失败: ' + error);
        }
      });
    });
  });
  </script>
</body>
</html>