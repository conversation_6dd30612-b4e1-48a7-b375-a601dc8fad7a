<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增永久版卡密</title>
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
    <style>
        .cdk-form-container {
            background: #f8f9fa;
            min-height: 100vh;
            padding: 15px;
            box-sizing: border-box;
        }
        .cdk-form-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 100%;
            margin: 0 auto;
        }
        .cdk-form-header {
            background: linear-gradient(135deg, #673ab7 0%, #9c27b0 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .cdk-form-header h2 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
        }
        .cdk-form-header .subtitle {
            margin-top: 5px;
            opacity: 0.9;
            font-size: 13px;
        }
        .cdk-form-body {
            padding: 25px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .layui-form-item {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
        }
        .layui-form-label {
            background: #f3e5f5;
            border: 1px solid #e0e6ed;
            border-radius: 6px;
            width: 140px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            margin-right: 15px;
            flex-shrink: 0;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
        }
        .layui-input-block {
            flex: 1;
            margin-left: 0;
        }
        .layui-input, .layui-select, select {
            border-radius: 6px;
            border: 1px solid #e0e6ed;
            width: 100%;
            height: 40px;
            line-height: 20px;
            box-sizing: border-box;
            padding: 10px 15px;
            transition: all 0.3s ease;
            vertical-align: middle;
        }
        .layui-input:focus {
            border-color: #673ab7;
            box-shadow: 0 0 0 2px rgba(103, 58, 183, 0.2);
        }

        .layui-btn {
            border-radius: 20px;
            padding: 10px 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            height: 40px;
            min-width: 100px;
            font-size: 14px;
        }
        .layui-btn:not(.layui-btn-primary) {
            background: linear-gradient(135deg, #673ab7 0%, #9c27b0 100%);
        }

        /* 按钮容器居中 */
        .button-container .layui-input-block {
            text-align: center;
        }

    </style>
</head>

<body>
<div class="cdk-form-container">
    <div class="cdk-form-card">
        <div class="cdk-form-header">
            <h2>💎 新增永久版卡密</h2>
            <div class="subtitle">生成永久有效的高级卡密，支持批量操作</div>
        </div>
        <div class="cdk-form-body">
            <form class="layui-form" id="addPermanentCdkForm">
                <!-- 商品类型选择框 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">商品类型</label>
                    <div class="layui-input-block">
                        <select name="goods_id" id="goodsType" lay-filter="goodsType" lay-search required>
                            <option value="">请选择商品类型</option>
                            {foreach $goodsList as $goods}
                            <option value="{$goods.id}">{$goods.goods_name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <!-- 代理ID输入框 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">代理ID</label>
                    <div class="layui-input-block">
                        <input type="number" name="agent_id" id="agentId" placeholder="可选，留空表示管理员生成" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <!-- 生成数量 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">生成数量</label>
                    <div class="layui-input-block">
                        <input type="number" name="num" id="generateNum" placeholder="请输入生成数量" autocomplete="off" class="layui-input" value="1" min="1" max="100" required>
                        <div class="layui-form-mid layui-word-aux">最多可生成100张永久版卡密</div>
                    </div>
                </div>

                <!-- 提交和重置按钮 -->
                <div class="layui-form-item button-container">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="submitAddPermanentCdk">立即提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" onclick="testCopyFunction()">🧪 测试复制</button>
                    </div>
                </div>

                <!-- 生成预览 -->
                <div class="layui-form-item">
                    <div id="cdkPreview" class="layui-input-block" style="color: #5FB878; font-weight: bold;"></div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
    layui.config({
        base: '/layuiadmin/' // 静态资源所在路径
    }).extend({
        index: 'lib/index' // 主入口模块
    }).use(['index', 'form'], function () {
        var $ = layui.$, form = layui.form;

        // 添加调试信息
        console.log('永久版卡密页面加载完成');
        console.log('navigator.clipboard支持:', !!navigator.clipboard);
        console.log('isSecureContext:', window.isSecureContext);

        // 监听表单提交
        form.on('submit(submitAddPermanentCdk)', function (data) {
            // 验证生成数量
            var num = parseInt(data.field.num);
            if(isNaN(num) || num < 1 || num > 100) {
                layer.msg('生成数量必须在1-100之间', {icon: 2});
                return false;
            }

            $.ajax({
                url: '/admin/CdkController/addPermanentCdk',
                type: 'POST',
                data: data.field,
                success: function (response) {
                    if (response.msg === 1) {
                        if (response.cdk_codes && response.cdk_codes.length > 0) {
                            // 批量生成的情况
                            $('#cdkPreview').html('生成成功，共 ' + response.cdk_codes.length + ' 个永久版卡密');
                            showCdkCodesModal(response.cdk_codes, '永久版卡密');
                        } else {
                            // 单个生成的情况
                            $('#cdkPreview').text('生成成功: ' + response.cdk_code);
                            showSingleCdkModal(response.cdk_code, '永久版卡密');
                        }

                        // 不自动关闭，等用户操作完弹窗后再提示
                        layer.msg('💎 永久版卡密生成成功！', { icon: 1, time: 2000 });
                    } else {
                        layer.msg('添加失败：' + response.content, { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('服务器错误，请稍后再试', { icon: 2 });
                }
            });
            return false;
        });

        // 显示批量生成的卡密弹窗
        function showCdkCodesModal(codes, type) {
            var content = '<div style="max-height: 400px; overflow-y: auto;">';
            content += '<div style="margin-bottom: 15px; text-align: center;"><strong>💎 生成的' + type + '列表</strong></div>';
            codes.forEach((code, index) => {
                content += `
                    <div style="display: flex; align-items: center; padding: 8px; border: 1px solid #e8e8e8; margin-bottom: 5px; border-radius: 6px; background: #f3e5f5;">
                        <span style="flex: 1; font-family: monospace; font-size: 13px; color: #333;">${index + 1}. ${code}</span>
                        <button onclick="window.copyToClipboard('${code}'); window.closeModalAfterCopy();" style="background: #673ab7; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">📋 复制</button>
                    </div>
                `;
            });
            content += '</div>';

            var modalIndex = layer.open({
                type: 1,
                title: '💎 生成的' + type + '列表 (' + codes.length + '个)',
                area: ['600px', '500px'],
                content: content,
                btn: ['📋 全部复制', '关闭'],
                yes: function() {
                    var allCodes = codes.join('\n');
                    window.copyToClipboard(allCodes);
                    window.closeModalAfterCopy();
                },
                btn2: function() {
                    closeCurrentPage();
                }
            });
            window.currentModalIndex = modalIndex;
        }

        // 显示单个卡密弹窗
        function showSingleCdkModal(code, type) {
            var content = `
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 24px; margin-bottom: 15px;">💎</div>
                    <div style="font-size: 18px; color: #673ab7; font-weight: bold; margin-bottom: 15px;">${type}生成成功！</div>
                    <div style="margin-bottom: 10px; color: #666;">您的卡密码：</div>
                    <div style="padding: 15px; background: #f3e5f5; border: 2px dashed #673ab7; border-radius: 8px; font-family: monospace; word-break: break-all; font-size: 16px; color: #333; margin-bottom: 15px;">
                        ${code}
                    </div>
                    <button onclick="window.copyToClipboard('${code}'); window.closeModalAfterCopy();" style="background: #673ab7; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-size: 14px;">
                        📋 复制卡密
                    </button>
                </div>
            `;

            var modalIndex = layer.open({
                type: 1,
                title: '💎 ' + type + '生成成功',
                area: ['450px', '350px'],
                content: content,
                btn: ['关闭'],
                yes: function() {
                    closeCurrentPage();
                }
            });
            window.currentModalIndex = modalIndex;
        }

        // 复制到剪贴板 - 简化版本
        window.copyToClipboard = function(text) {
            console.log('开始复制文本:', text);

            // 创建临时文本区域
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            textArea.style.opacity = "0";
            document.body.appendChild(textArea);

            try {
                // 选择文本
                textArea.focus();
                textArea.select();
                textArea.setSelectionRange(0, 99999); // 兼容移动设备

                // 执行复制
                var successful = document.execCommand('copy');
                console.log('复制结果:', successful);

                if (successful) {
                    layer.msg('📋 复制成功！', {icon: 1, time: 1500});
                } else {
                    layer.msg('📋 复制失败，请手动复制', {icon: 2, time: 3000});
                }
            } catch (err) {
                console.error('复制失败:', err);
                layer.msg('📋 复制失败，请手动复制', {icon: 2, time: 3000});
            } finally {
                // 清理临时元素
                document.body.removeChild(textArea);
            }
        };



        // 复制后关闭弹窗
        window.closeModalAfterCopy = function() {
            setTimeout(function() {
                if (window.currentModalIndex) {
                    layer.close(window.currentModalIndex);
                }
                closeCurrentPage();
            }, 1000); // 1秒后关闭
        };

        // 关闭当前页面
        function closeCurrentPage() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
            parent.layui.table.reload('LAY-app-cdk-list');
        }

        // 测试复制功能
        window.testCopyFunction = function() {
            var testText = 'TestCDK123456789';
            console.log('测试复制功能，文本:', testText);
            window.copyToClipboard(testText);
        };
    });
</script>
</body>

</html>