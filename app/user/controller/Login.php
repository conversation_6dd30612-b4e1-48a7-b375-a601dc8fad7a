<?php
/**
 * Login.php
 *
 * 用户登录控制器：处理用户登录、第三方登录及相关操作。
 *
 * @package     app\user\controller
 * @category    User Authentication
 * @version     1.0
 * @date        2024-12-08
 * <AUTHOR>
 * @license     MIT License
 * @description 实现用户登录、第三方登录及其回调逻辑。
 */

namespace app\user\controller;

use app\BaseController;
use app\admin\model\System;
use app\admin\model\User as Us;
use app\admin\model\Config;
use think\facade\View;
use think\facade\Cookie;
use app\user\model\ThirdPartyBinding;
use think\Request;
use think\facade\Db;

/**
 * 用户登录控制器
 *
 * @description 提供用户登录相关功能，包括页面展示、第三方登录入口及回调。
 */
class Login extends BaseController
{
    /**
     * 用户登录页面
     *
     * @description 展示用户登录页面，包含系统、用户、配置信息的加载逻辑。
     * @return \think\response\View 返回登录页面视图。
     */
    public function index()
    {
        // 如果用户已登录，跳转到用户中心首页
        if (!$this->user == null) {
            header("Location:/user/index/onshop");
        }

        // 渲染登录页面视图，加载用户、系统及配置信息
        return View::fetch("login/index", [
            "user" => Us::find(),
            "system" => System::find(1),
            "config" => Config::find(1)
        ]);
    }

    /**
     * 第三方登录入口
     *
     * @description 获取第三方登录的跳转URL，并执行跳转或返回错误信息。
     * @param Request $request 请求实例
     * @return \think\response\Redirect|\think\response\Json
     */
    public function thirdPartyLogin(Request $request)
    {
        $type = $request->get('type', 'weixin'); // 默认使用微信登录
        $oauthService = new \OauthService();

        // 获取第三方登录URL
        $loginData = $oauthService->getLoginUrl($type);
        if (isset($loginData['code']) && $loginData['code'] === 0) {
            return redirect($loginData['url']); // 跳转到第三方登录页面
        }

        // 如果获取失败，返回错误信息
        return json(['error' => $loginData['msg'] ?? 'Failed to retrieve login URL']);
    }

    /**
     * 第三方登录回调
     *
     * @description 处理第三方登录的回调逻辑，校验用户状态并完成登录。
     * @param Request $request 请求实例
     * @return \think\response\Json|\think\response\Redirect
     */
    public function callback(Request $request)
    {
        $code = $request->get('code');
        $state = $request->get('state');

        if ($state !== session('Oauth_state')) {
            return json(['error' => 'CSRF validation failed']);
        }

        $oauthService = new \OauthService();
        $callbackData = $oauthService->handleCallback($code);

        if (isset($callbackData['code']) && $callbackData['code'] === 0) {
            $socialUid = $callbackData['social_uid'];
            $accessToken = $callbackData['access_token'];
            $userInfo = $callbackData['user_info'] ?? [];

            // 检查是否已绑定
            $binding = ThirdPartyBinding::where('social_uid', $socialUid)->find();

            if ($binding) {
                $this->setUserLoginState($binding->user_id);

                // 记录登录日志
                Db::name('userlogo')->insert([
                    'uid' => $binding->user_id,
                    'content' => '第三方登录成功',
                    'ip' => $request->ip(),
                    'time' => date('Y-m-d H:i:s'),
                ]);

                return redirect('success');
            }

            // 存储第三方登录信息到 session，用于绑定页面使用
            session('third_party_info', [
                'social_uid' => $socialUid,
                'access_token' => $accessToken,
                'nickname' => $userInfo['nickname'] ?? '',
                'avatar' => $userInfo['avatar'] ?? '',
                'gender' => $userInfo['gender'] ?? '',
            ]);

            // 跳转到绑定手机号页面
            return redirect('/user/login/bind');
        }

        return json(['error' => $callbackData['msg'] ?? '登录回调处理失败']);
    }

    /**
     * 显示绑定手机号页面
     */
    public function bind()
    {
        return View::fetch('login/bind', [
            //"system" => System::find(1),
            "config" => Config::find(1)
        ]);
    }

    /**
     * 处理手机号绑定
     */
    public function doBind(Request $request)
    {
        $phone = $request->post('phone');
        $code = $request->post('code'); // 验证码
        $thirdPartyInfo = session('third_party_info');

        if (!$thirdPartyInfo) {
            return json(['code' => 1, 'msg' => '第三方登录信息已失效']);
        }

        // 获取短信验证开关配置
        $config = \app\admin\model\Config::find(1);
        $smsVerifySwitch = $config['sms_verify_switch'] ?? 1; // 默认开启

        // 如果短信验证开启，则验证验证码
        if ($smsVerifySwitch == 1) {
            // 验证手机验证码
            if (!$this->validateSmsCode($phone, $code)) {
                return json(['code' => 1, 'msg' => '验证码错误']);
            }
        }

        // 调用远程注册API
        $apiUrl = "http://xiuluo.zuhaom.com/phone_register.php";
        $params = [
            'type' => 'register',
            'phone' => $phone,
            'password' => $phone // 默认密码为手机号
        ];

        try {
            // 初始化 curl
            $ch = curl_init();

            // 设置 curl 选项
            curl_setopt($ch, CURLOPT_URL, $apiUrl . '?' . http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 忽略 SSL 验证
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 忽略主机验证

            // 执行请求
            curl_exec($ch);
            curl_close($ch);

            // 不验证结果,直接继续本地注册流程
            Db::startTrans();
            try {
                // 创建新用户
                $user = new Us();
                $user->us_name = $thirdPartyInfo['nickname'] ?: $phone;
                $user->us_username = $phone;
                $user->us_password = $phone; // 默认密码为手机号
                $user->us_phone = $phone;
                $user->us_email = '';
                $user->us_logo = $thirdPartyInfo['avatar'] ?: '/static/images/us.jpeg';
                $user->us_money = '0';
                $user->superior = 0;
                $user->commission = '0';
                $user->time = date('Y-m-d H:i:s');
                $user->save();

                // 创建第三方绑定记录
                $binding = new ThirdPartyBinding();
                $binding->user_id = $user->id;
                $binding->third_party = 'weixin'; // 根据实际情况设置
                $binding->social_uid = $thirdPartyInfo['social_uid'];
                $binding->access_token = $thirdPartyInfo['access_token'];
                $binding->nickname = $thirdPartyInfo['nickname'];
                $binding->faceimg = $thirdPartyInfo['avatar'];
                $binding->gender = $thirdPartyInfo['gender'];
                $binding->ip = $request->ip();
                $binding->save();

                // 记录注册日志
                Db::name("userlogo")->insert([
                    "uid" => $user->id,
                    "content" => "第三方登录绑定手机号成功",
                    "ip" => $request->ip(),
                    "time" => date("Y-m-d H:i:s")
                ]);

                Db::commit();

                // 设置登录状态
                $this->setUserLoginState($user->id);

                // 清除 session
                session('third_party_info', null);

                // 如果开启了短信验证，则清除验证码
                if ($smsVerifySwitch == 1) {
                    Cookie::delete('phoneVerify');
                }

                return json(['code' => 0, 'msg' => '绑定成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '绑定失败：' . $e->getMessage()]);
            }
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '远程注册失败：' . $e->getMessage()]);
        }
    }

    /**
     * 登录失败页面
     *
     * @description 显示第三方登录绑定错误页面。
     * @return \think\response\View 绑定错误页面视图。
     */
    public function kjloginerro()
    {
        return View::fetch('index/kjloginerro');
    }

    /**
     * 登录成功页面
     *
     * @description 显示登录成功页面。
     * @return \think\response\View 登录成功页面视图。
     */
    public function success()
    {
        return View::fetch('index/success');
    }

    /**
     * 设置用户登录状态
     *
     * @description 设置用户的Session和Cookie，标记用户为已登录。
     * @param int $userId 用户ID
     */
    private function setUserLoginState(int $userId)
    {
        // 设置 Session 和 Cookie
        session('user_id', $userId);
        Cookie::forever('id', $userId);
        Cookie::forever('login', 1);

        // 可选：查询用户信息，设置更多用户状态
        $user = Us::find($userId);
        if ($user) {
            Cookie::forever('username', $user->us_name);
        }
    }

    /**
     * 验证短信验证码
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @return bool
     */
    private function validateSmsCode($phone, $code)
    {
        // 从 Cookie 获取验证码
        $storedCode = Cookie::get("phoneVerify");

        if (empty($storedCode)) {
            return false; // 验证码不存在或已过期
        }

        // 验证码匹配检查
        if ($code !== $storedCode) {
            return false;
        }

        // 验证成功后清除验证码
        Cookie::delete('phoneVerify');

        return true;
    }

    /**
     * 修改密码页面
     */
    public function change_password() {
        // 检查Cookie中的登录状态
        $login = cookie('login');
        $username = cookie('username');
        $id = cookie('id');

        if (!$login || !$username || !$id) {
            return redirect('/user/login/index');
        }

        // 直接从数据库获取用户信息
        $user = Db::name('user')
            ->where('id', $id)
            ->where('us_username', $username)
            ->find();

        if (!$user) {
            // Cookie中的用户信息在数据库中不存在
            // 清除Cookie并重定向到登录页
            cookie('login', null);
            cookie('username', null);
            cookie('id', null);
            return redirect('/user/login/index');
        }

        $config = config('geetest');
        return view('login/change_password', [
            'user' => $user,
            'config' => $config
        ]);
    }

    /**
     * 检查用户登录状态
     * 同时检查Cookie和session
     */
    private function checkUserLogin() {
        // 首先检查session
        $sessionUser = session('user');

        // 如果session中没有用户信息，则检查Cookie
        if (!$sessionUser) {
            $cookie_login = cookie('login');
            $cookie_username = cookie('username');
            $cookie_id = cookie('id');

            // 如果Cookie中有登录信息
            if ($cookie_login && $cookie_username && $cookie_id) {
                // 从数据库中获取用户信息 - 修正字段名为"id"
                $user = Db::name('user')
                    ->where('id', $cookie_id)  // 使用正确的字段名"id"
                    ->where('us_username', $cookie_username)
                    ->find();

                if ($user) {
                    // 将用户信息存入session
                    session('user', $user);
                    return $user;
                }
            }
            return false;
        }

        return $sessionUser;
    }

    /**
     * 修改密码页面 - 别名方法
     */
    public function changePasswordPage() {
        // 检查Cookie中的登录状态
        $login = cookie('login');
        $username = cookie('username');
        $id = cookie('id');

        if (!$login || !$username || !$id) {
            return redirect('/user/login/index');
        }

        // 直接从数据库获取用户信息
        $user = Db::name('user')
            ->where('id', $id)
            ->where('us_username', $username)
            ->find();

        if (!$user) {
            // Cookie中的用户信息在数据库中不存在
            // 清除Cookie并重定向到登录页
            cookie('login', null);
            cookie('username', null);
            cookie('id', null);
            return redirect('/user/login/index');
        }

        // 从数据库获取极验配置
        $dbConfig = Db::name('config')->find(1);

        // 修正API地址中的拼写错误
        $geeApi = $dbConfig['gee_api'] ?? 'https://api.geetest.com/';
        // 修复错误的协议标识
        $geeApi = str_replace('http;//', 'http://', $geeApi);

        $config = [
            'gee_api' => $geeApi
        ];

        return view('login/change_password', [
            'user' => $user,
            'config' => $config
        ]);
    }

    /**
     * 验证短信验证码
     */
    public function verifyCode() {
        $phone = input('phone');
        $code = input('code');
        $actionType = input('actionType');

        // 验证用户身份
        $id = cookie('id');
        $cookieUsername = cookie('username');

        // 确保手机号与用户匹配
        if ($cookieUsername != $phone) {
            return json(['code' => 1, 'msg' => '用户信息不匹配，请重新登录']);
        }

        // 验证验证码 - 使用与Base控制器一致的验证方式
        $verifyCode = Cookie::get("phoneVerify");
        if (empty($verifyCode)) {
            return json(['code' => 1, 'msg' => '验证码已过期，请重新获取']);
        }

        if ($code != $verifyCode) {
            return json(['code' => 1, 'msg' => '验证码错误']);
        }

        // 验证成功，生成令牌
        $token = md5($phone . time() . rand(1000, 9999));
        cache($token, $phone, 600); // 缓存10分钟

        // 清除验证码Cookie
        Cookie::delete("phoneVerify");

        return json(['code' => 0, 'msg' => '验证成功', 'token' => $token]);
    }

    /**
     * 执行修改密码
     */
    public function doChangePassword() {
        $token = input('token');
        $newPassword = input('newPassword');

        // 验证token有效性
        $phone = cache($token);
        if (!$phone) {
            return json(['code' => 1, 'msg' => '验证已过期，请重新验证']);
        }

        // 验证用户身份
        $id = cookie('id');
        $cookieUsername = cookie('username');

        // 确保手机号与用户匹配
        if ($cookieUsername != $phone) {
            return json(['code' => 1, 'msg' => '用户信息不匹配，请重新登录']);
        }

        // 验证密码长度
        if (strlen($newPassword) < 6) {
            return json(['code' => 1, 'msg' => '密码长度至少6位']);
        }

        // 修改密码
        $user = Db::name('user')->where('us_username', $phone)->find();
        if (!$user) {
            return json(['code' => 1, 'msg' => '用户不存在']);
        }

        // 更新密码
        $result = Db::name('user')->where('us_username', $phone)
            ->update(['us_password' => $newPassword]);

        if ($result) {
            // 清除token
            cache($token, null);
            return json(['code' => 0, 'msg' => '密码修改成功']);
        } else {
            return json(['code' => 1, 'msg' => '密码修改失败，请重试']);
        }
    }

    /**
     * 检查短信验证码
     */
    private function checkSmsCode($phone, $code, $actionType) {
        // 从缓存中获取验证码
        $cacheKey = 'sms_' . $actionType . '_' . $phone;
        $cacheCode = cache($cacheKey);

        if ($cacheCode && $cacheCode == $code) {
            // 验证成功后清除掉缓存
            cache($cacheKey, null);
            return true;
        }

        return false;
    }
}
