<?php
/**
 * Verify.php
 *
 * 验证控制器：负责用户登录验证和权限校验。
 *
 * @package     app\user\controller
 * @category    User Authentication
 * @version     1.0
 * <AUTHOR>
 * @date        2024-12-08
 * @copyright   Copyright (c) 2024 Kongzue
 * @license     MIT License
 * @description 验证用户登录状态，并根据请求处理相关跳转或展示逻辑。
 */

namespace app\user\controller;

use app\BaseController;           // 引入基础控制器
use think\facade\View;            // 引入视图类
use think\facade\Cookie;          // 引入Cookie类
use think\facade\Request;         // 引入请求类
use app\admin\model\Notice;       // 引入公告模型
use app\admin\model\System;       // 引入系统设置模型
use app\admin\model\User as Us;   // 引入用户模型

/**
 * 验证控制器
 *
 * @description 验证用户身份并处理页面跳转逻辑。
 */
class Verify extends BaseController
{
    /**
     * 当前用户对象
     *
     * @var object|null $user 用户模型实例或空值
     */
    protected $user;

    /**
     * 构造函数
     *
     * @description 初始化控制器，验证用户登录状态，并处理特定请求逻辑。
     */
    public function __construct()
    {
        // 从数据库中获取当前登录用户信息
        $this->user = Us::where([
            "id" => Cookie::get("id"),                    // 用户ID
            "us_username" => Cookie::get("username")     // 用户名
        ])->find();

        // 获取请求参数
        $reqData = Request::param();

        // 如果请求参数中包含特定标识（如 "yhxy"），加载相关视图和数据
        if (isset($reqData["id"]) && $reqData["id"] == "yhxy") {
            return View::fetch("ticket/noticeList", [
                "noticeContent" => Notice::where("not_system", 1)->find(), // 获取系统公告
                "system" => System::find(1)                               // 获取系统设置
            ]);
        }

        // 如果用户未登录或登录状态无效
        if ($this->user == null || Cookie::get("login") != 1) {
            
                // PC端跳转至用户登录页
                header('Location:/user/login');
                exit();
            
        }

    }
}
