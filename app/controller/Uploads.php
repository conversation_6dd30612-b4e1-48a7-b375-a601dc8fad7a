<?php
namespace app\Controller;
use app\BaseController;
use think\facade\Request;
class Uploads extends BaseController {
    public function index() {
           $file = $_FILES['file'];
           $imgAll=["png","jpg","jpeg","gif"];
           $hz=explode(".",$file["name"])[1];
           //文件路径
           $path="/upload/".time().".".$hz;
           $paths=app()->getRootPath() . "public/".$path;
          foreach ($imgAll as $val){
              if($val==$hz){
                       if(move_uploaded_file($file['tmp_name'],$paths)){
                           return json(["code"=>0,"data"=>["src"=>$path]]);
                       };
              }
               
          }
    }
}