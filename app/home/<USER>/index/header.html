<style>
	* {
	            padding: 0;
	            margin: 0;
	            list-style: none;
	            text-decoration: none;
	        }
	
	        html {
	            color-scheme: dark;
	        }
	
	        div {
	            cursor: pointer;
	        }
	
	        /* 响应式布局 */
	        .box {
	            margin: auto;
	        }
	
	        @media screen and (min-width:1200px) {
	            .box {
	                width: 1200px;
	                margin: auto;
	            }
	        }
	
	        header {
	            width: 100%;
	            position: fixed;
	            width: 100%;
	            top: 0;
	            background-color: rgba(12, 12, 13, .52);
	            backdrop-filter: saturate(180%) blur(20px);
	            transition: all .3s linear;
	            color: #fff;
	            z-index: 9999;
	            padding: 10px;
	        }
	        header a{
	            color: #fff;
	        }
	        .header_center {
	            margin: auto;
	            max-width: 1200px;
	            display: flex;
	            justify-content: space-between;
	            align-items: center;
	        }
	
	        .hd_logo img {
	            width: 110px;
	            height: 90px;
	        }
	
	        .hd_right {
	            display: flex;
	            align-items: center;
	            font-size: 17px;
	            font-weight: 500;
	        }
	
	        .hd_right span {
	            padding: 0 20px;
	            position: relative;
	        }

	        .hd_right span>img {
	            width: 50px;
	            height: 50px;
	            border-radius: 50%;
	        }

	        .hd_right>a{
	            border-right: 1px solid rgba(255,255,255,.09);
	        }
	        .hd_right>a:nth-child(n+5){
	            border-right: 1px solid transparent;
	        }
	           .hd_right span:hover::after {
	            transition-delay:1s;
	            content: "";
	            position: absolute;
	            bottom: -12px;
	            left: 50%;
	            margin-left: -10px;
	            width: 20px;
	            height: 4px;
	            background-color: #1a89fa;
	            border-radius: 2px;
	            transition: width .2s ease-out;
	               
	           }
	           #img{
	               display: inline-block;
	               width: 30px;
	               height: 30px;
	               border-radius: 50%;
	               border: 3px solid #fff;
	               background-size: cover;
	           }
</style>

<!-- 头部 -->
<header>
    <div class="header_center">
        <div class="hd_logo">
            <!--<a href="/"><img src="{$system.sy_logo}" alt="logo"></a>-->
        </div>
        <div class="hd_right">
           <a class="" href="/"><span>首页</span></a>
           <a class="" href="/user/index/feedback"><span>工单</span></a>
           <a class="" href="/user/index/onshop"><span>在线账号</span></a>
           <a class="" href="/user/CdkExchange/index"><span>CDK兑换</span></a>
           <a class="" href="/user/index/getPurchasedComboWithVip"><span>至尊会员</span></a>
           <!--<a class="" href="/user/index/promotion"><span>推广</span></a>-->
           <a class="" href="/user/index/notice"><span>帮助</span></a>
           <a class="" href="/user/login"> 
           <img class="" src="{$user.us_logo?$user.us_logo:'/static/images/us.jpg'}" alt="" id="img"/></a>
        </div>
    </div>
    <script type="text/javascript" charset="utf-8">
    // $.get("/user/base/getUserImg",function (e){
    //         $("#img").css({backgroundImage:`url(${e})`})
    //     })
    </script>
    
</header>