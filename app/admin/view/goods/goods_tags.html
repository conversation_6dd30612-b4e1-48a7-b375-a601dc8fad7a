<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>商品标签管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>
  <div class="layui-form" lay-filter="layuiadmin-form-tags" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
      <label class="layui-form-label">商品标签</label>
      <div class="layui-input-block">
        {foreach $tagsList as $tag}
        <input type="checkbox" name="tags[{$tag.id}]" title="{$tag.tag_name}" {if in_array($tag.id, $selectedTags)}checked{/if}>
        {/foreach}
      </div>
    </div>
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认">
    </div>
  </div>
  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/'
  }).extend({
    index: 'lib/index'
  }).use(['index', 'form'], function(){
    var $ = layui.$
    ,form = layui.form;
    
    //监听提交
    form.on('submit(layuiadmin-app-form-submit)', function(data){
      var field = data.field;
      field.goods_id = getUrlParam('id');
      
      //提交修改
      $.ajax({
        url: '/admin/base/updateGoodsTags',
        type: 'POST',
        data: field,
        success: function(res){
          if(res.code == 0){
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
            parent.layui.table.reload('LAY-app-content-list2');
          }
          parent.layer.msg(res.msg);
        }
      });
    });

    function getUrlParam(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) return decodeURI(r[2]); return null;
    }
  })
  </script>
</body>
</html> 