<!DOCTYPE html>
<html>
<head>
    <title>代理状态测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        input { padding: 5px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; width: 200px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 120px; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px; }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-unknown { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>代理状态测试工具</h1>
        
        <div class="test-section">
            <h3>1. 代理状态检查</h3>
            <div class="form-group">
                <label>代理ID：</label>
                <input type="number" id="agentId" placeholder="输入代理ID" value="">
            </div>
            <button onclick="checkAgentStatus()">检查代理状态</button>
            <button onclick="simulateSessionExpiry()" class="danger">模拟会话过期</button>
            <div id="statusResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 会话状态模拟</h3>
            <button onclick="testNormalStatus()">测试正常状态</button>
            <button onclick="testSessionTimeout()">测试会话超时</button>
            <button onclick="testAccountDisabled()">测试账号禁用</button>
            <button onclick="testAccountNotFound()">测试账号不存在</button>
            <div id="simulationResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 修复说明</h3>
            <div class="result info">
                <h4>修复的问题：</h4>
                <ul>
                    <li>❌ <strong>会话超时误判：</strong>长时间无操作后，会话过期被误判为账号禁用</li>
                    <li>❌ <strong>错误信息不准确：</strong>不同错误情况显示相同的错误信息</li>
                    <li>❌ <strong>缺少异常处理：</strong>数据库查询失败时没有适当的错误处理</li>
                </ul>
                
                <h4>修复后的改进：</h4>
                <ul>
                    <li>✅ <strong>精确错误分类：</strong>区分会话过期、账号不存在、账号禁用三种情况</li>
                    <li>✅ <strong>异常处理：</strong>添加数据库查询异常捕获和处理</li>
                    <li>✅ <strong>详细日志：</strong>记录不同类型的错误和操作日志</li>
                    <li>✅ <strong>准确提示：</strong>根据实际情况显示对应的错误信息</li>
                </ul>
                
                <h4>错误类型对应：</h4>
                <ul>
                    <li><span class="status-indicator status-online"></span><strong>正常状态 (200)：</strong>代理账号正常，可以继续操作</li>
                    <li><span class="status-indicator status-unknown"></span><strong>会话过期 (401)：</strong>长时间无操作，需要重新登录</li>
                    <li><span class="status-indicator status-offline"></span><strong>账号不存在 (404)：</strong>代理账号被删除，联系管理员</li>
                    <li><span class="status-indicator status-offline"></span><strong>账号禁用 (403)：</strong>代理账号被管理员禁用</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function checkAgentStatus() {
            const agentId = document.getElementById('agentId').value;
            
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在检查代理状态...';
            
            // 如果提供了代理ID，先设置会话
            if (agentId) {
                // 模拟设置会话
                fetch('/agent/index/checkStatus', {
                    method: 'GET',
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    displayStatusResult(data, resultDiv);
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<p>请求失败：${error.message}</p>`;
                });
            } else {
                // 直接检查当前会话状态
                fetch('/agent/index/checkStatus', {
                    method: 'GET',
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    displayStatusResult(data, resultDiv);
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<p>请求失败：${error.message}</p>`;
                });
            }
        }
        
        function displayStatusResult(data, resultDiv) {
            let statusClass = 'info';
            let statusIndicator = 'status-unknown';
            
            if (data.code === 200) {
                statusClass = 'success';
                statusIndicator = 'status-online';
            } else if (data.code === 401) {
                statusClass = 'warning';
                statusIndicator = 'status-unknown';
            } else if (data.code === 403 || data.code === 404) {
                statusClass = 'error';
                statusIndicator = 'status-offline';
            }
            
            resultDiv.className = `result ${statusClass}`;
            resultDiv.innerHTML = `
                <h4><span class="status-indicator ${statusIndicator}"></span>代理状态检查结果：</h4>
                <p><strong>状态码：</strong>${data.code}</p>
                <p><strong>消息：</strong>${data.msg}</p>
                ${data.session_expired ? '<p><strong>类型：</strong>会话过期</p>' : ''}
                ${data.account_not_found ? '<p><strong>类型：</strong>账号不存在</p>' : ''}
                ${data.account_disabled ? '<p><strong>类型：</strong>账号被禁用</p>' : ''}
                ${data.agent_info ? `<p><strong>代理信息：</strong>ID=${data.agent_info.id}, 用户名=${data.agent_info.username}, 余额=${data.agent_info.balance}</p>` : ''}
            `;
        }
        
        function simulateSessionExpiry() {
            // 清除会话cookie来模拟会话过期
            document.cookie = 'PHPSESSID=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result warning';
            resultDiv.innerHTML = '<p>已清除会话Cookie，现在点击"检查代理状态"应该显示会话过期</p>';
        }
        
        function testNormalStatus() {
            const resultDiv = document.getElementById('simulationResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = `
                <h4>正常状态测试：</h4>
                <p>当代理账号正常且会话有效时，应该返回：</p>
                <ul>
                    <li>状态码：200</li>
                    <li>消息：状态正常</li>
                    <li>包含代理基本信息</li>
                </ul>
            `;
        }
        
        function testSessionTimeout() {
            const resultDiv = document.getElementById('simulationResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result warning';
            resultDiv.innerHTML = `
                <h4>会话超时测试：</h4>
                <p>当长时间无操作导致会话过期时，应该返回：</p>
                <ul>
                    <li>状态码：401</li>
                    <li>消息：会话已过期，请重新登录</li>
                    <li>session_expired: true</li>
                    <li>自动清除会话信息</li>
                </ul>
            `;
        }
        
        function testAccountDisabled() {
            const resultDiv = document.getElementById('simulationResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `
                <h4>账号禁用测试：</h4>
                <p>当代理账号被管理员禁用时，应该返回：</p>
                <ul>
                    <li>状态码：403</li>
                    <li>消息：您的代理账号已被管理员禁用，请联系管理员</li>
                    <li>account_disabled: true</li>
                    <li>记录禁用日志</li>
                </ul>
            `;
        }
        
        function testAccountNotFound() {
            const resultDiv = document.getElementById('simulationResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `
                <h4>账号不存在测试：</h4>
                <p>当代理账号被删除时，应该返回：</p>
                <ul>
                    <li>状态码：404</li>
                    <li>消息：代理账号不存在，请联系管理员</li>
                    <li>account_not_found: true</li>
                    <li>记录警告日志</li>
                </ul>
            `;
        }
    </script>
</body>
</html>
