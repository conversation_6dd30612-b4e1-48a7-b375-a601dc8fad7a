!(function (e) {
  var t = {}
  function n(r) {
    if (t[r]) return t[r].exports
    var o = (t[r] = { i: r, l: !1, exports: {} })
    return e[r].call(o.exports, o, o.exports, n), (o.l = !0), o.exports
  }
  ;(n.m = e),
    (n.c = t),
    (n.d = function (e, t, r) {
      n.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: r })
    }),
    (n.r = function (e) {
      'undefined' != typeof Symbol &&
        Symbol.toStringTag &&
        Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
        Object.defineProperty(e, '__esModule', { value: !0 })
    }),
    (n.t = function (e, t) {
      if ((1 & t && (e = n(e)), 8 & t)) return e
      if (4 & t && 'object' == typeof e && e && e.__esModule) return e
      var r = Object.create(null)
      if (
        (n.r(r),
        Object.defineProperty(r, 'default', { enumerable: !0, value: e }),
        2 & t && 'string' != typeof e)
      )
        for (var o in e)
          n.d(
            r,
            o,
            function (t) {
              return e[t]
            }.bind(null, o)
          )
      return r
    }),
    (n.n = function (e) {
      var t =
        e && e.__esModule
          ? function () {
              return e.default
            }
          : function () {
              return e
            }
      return n.d(t, 'a', t), t
    }),
    (n.o = function (e, t) {
      return Object.prototype.hasOwnProperty.call(e, t)
    }),
    (n.p = '//zuhaowan.zuhaowan.com/shanghu/www3.0/'),
    n((n.s = 'PUcC'))
})({
  '5Vks': function (e, t) {
    $(function () {
      var e = sessionStorage.getItem('navname') || 'index'
      zhw.navHighLight(e)
    })
  },
  '99HL': function (e, t) {
    function n(e, t) {
      var n = Object.keys(e)
      if (Object.getOwnPropertySymbols) {
        var r = Object.getOwnPropertySymbols(e)
        t &&
          (r = r.filter(function (t) {
            return Object.getOwnPropertyDescriptor(e, t).enumerable
          })),
          n.push.apply(n, r)
      }
      return n
    }
    function r(e) {
      for (var t = 1; t < arguments.length; t++) {
        var r = null != arguments[t] ? arguments[t] : {}
        t % 2
          ? n(Object(r), !0).forEach(function (t) {
              o(e, t, r[t])
            })
          : Object.getOwnPropertyDescriptors
          ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r))
          : n(Object(r)).forEach(function (t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t))
            })
      }
      return e
    }
    function o(e, t, n) {
      return (
        t in e
          ? Object.defineProperty(e, t, {
              value: n,
              enumerable: !0,
              configurable: !0,
              writable: !0
            })
          : (e[t] = n),
        e
      )
    }
    var a = function (e, t) {
      var n =
          arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
        o = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {}
      return new Promise(function (a, i) {
        $.ajax(
          r(
            r(
              {
                type: e,
                url: /^http(s)?|^\/\//.test(t) ? t : '' + t,
                dataType: 'json',
                timeout: 2e4,
                data: n
              },
              o
            ),
            {},
            {
              success: function (e) {
                a(e)
              },
              error: function (e) {
                i(e)
              }
            }
          )
        )
      })
    }
    ;(window.zhw = {
      default_file_path: '//zuhaowan.zuhaowan.com/shangv1/default_v2',
      txAppId: window.isFX ? '190036073' : '195234370',
      get: function (e) {
        var t =
            arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
          n =
            arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}
        return a('get', e, t, n)
      },
      post: function (e) {
        var t =
            arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
          n =
            arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}
        return a('post', e, t, n)
      },
      setCookie: function (e, t, n) {
        var r = new Date()
        r.setTime(r.getTime() + 24 * n * 60 * 60 * 1e3)
        var o = 'expires=' + r.toUTCString()
        document.cookie = e + '=' + t + '; ' + o + ';path=/'
      },
      getCookie: function (e) {
        var t,
          n = new RegExp('(^| )' + e + '=([^;]*)(;|$)')
        return (t = document.cookie.match(n)) ? unescape(t[2]) : null
      },
      removeCookie: function (e) {
        document.cookie = e + '=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/'
      },
      navHighLight: function (e) {
        $('#nav li').each(function () {
          var t = $(this)
          t.attr('data-tag') === e && t.addClass('active')
        })
      },
      getUrlValue: function () {
        var e = window.location.href.split('?'),
          t = new Array()
        if (e.length > 1)
          for (var n = e[1].split('&'), r = 0, o = n.length; r < o; r++) {
            var a = n[r].split('=')
            t[a[0]] = a[1]
          }
        return t
      },
      serializeObject: function () {
        var e, t, n, r, o
        for (
          e = this.serializeArray(), n = (t = {}).hasOwnProperty, r = 0;
          r < e.length;
          r++
        )
          (o = e[r]), n.call(t, o.name) || (t[o.name] = o.value)
        return t
      },
      loadJs: function (e, t) {
        var n = document.createElement('script')
        ;(n.type = 'text/javascript'),
          t &&
            (n.readyState
              ? (n.onreadystatechange = function () {
                  ;('loaded' != n.readyState && 'complete' != n.readyState) ||
                    ((n.onreadystatechange = null), t())
                })
              : (n.onload = function () {
                  t()
                })),
          (n.src = e),
          document.body.appendChild(n)
      }
    }),
      ($.prototype.serializeObject = zhw.serializeObject)
  },
  PUcC: function (e, t, n) {
    'use strict'
    n.r(t)
    n('99HL'), n('5Vks')
  }
})
