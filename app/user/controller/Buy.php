<?php
/**
 * Copyright (c) 2024 <PERSON><PERSON><PERSON>/<PERSON><PERSON>. All rights reserved.
 *
 * This file is part of the application developed by <PERSON><PERSON><PERSON>/<PERSON><PERSON>.
 * Unauthorized copying of this file, via any medium, is strictly prohibited.
 * Proprietary and confidential.
 * Written by <PERSON><PERSON><PERSON>/<PERSON><PERSON>, 2024.
 */

namespace app\user\controller;

use app\BaseController;
use think\facade\Request;
use app\admin\model\Goodsclass;
use app\admin\model\Goods;
use app\admin\model\Account;
use app\admin\model\Offline;
use app\admin\model\Combo;
use think\facade\Cookie;
use app\user\controller\Verify;
use app\admin\model\Order;

/**
 * 商品购买控制器
 *
 * 负责处理商品购买相关的业务逻辑，包括商品信息查询、账号状态查询等
 *
 * @package app\user\controller
 */
class Buy extends Verify
{
    /**
     * 获取商品及其套餐详细信息
     *
     * @api
     * @param int $id 商品ID，通过Request获取
     * @return \think\response\Json
     *   {
     *     "goods": object,     // 商品基本信息
     *     "comBo": array,      // 套餐信息数组
     *     "account_status": {   // 账号状态信息
     *       "offline": bool,    // 是否有可用离线账号
     *       "online": bool      // 是否有可用在线账号
     *     }
     *   }
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function index()
    {
        $id = Request::param("id");

        // 获取商品相关的套餐
        $comBo = Combo::where("co_goods", $id)->select()->toArray();

        // 获取离线账号信息并确保转换为数组
        $offlineInfo = Offline::where('product_id', $id)
            ->field('product_amount')
            ->find();

        if ($offlineInfo) {
            $offlineInfo = $offlineInfo->toArray();
        }

        // 如果没有套餐数据但有离线账号价格，创建一个默认套餐
        if (empty($comBo) && $offlineInfo) {
            $comBo = [[
                'offline_price' => $offlineInfo['product_amount'],
                //'co_name' => '标准版',
                //'co_money' => 0,
                'co_goods' => $id,
                //'id' => 0  // 添加一个默认ID
            ]];
        }
        // 如果有套餐数据且有离线账号价格，则添加到每个套餐中
        elseif ($offlineInfo) {
            foreach ($comBo as $index => $val) {
                $comBo[$index]['offline_price'] = $offlineInfo['product_amount'];
            }
        }

        // 添加调试信息
        trace([
            'offline_info' => $offlineInfo,
            'combo_data' => $comBo
        ], 'debug');


        // 打包返回数据
        $data = [
            "goods" => Goods::find($id),
            "comBo" => $comBo ?: [], // 确保返回空数组而不是null
            "account_status" => [
                "offline" => Account::where(["ac_goods" => $id, "goods_Type" => 0])->count() > 0,
                "online" => Account::where(["ac_goods" => $id, "goods_Type" => 1, "ac_states" => 1])->count() > 0
            ]
        ];

        return json($data);
    }

    /**
     * 获取所有可用账号信息（支持在线和离线账号）
     *
     * @return \think\response\Json
     */
    public function getAvailableAccounts1()
    {
        // 获取在线账号信息
        $onlineAccounts = Account::where([
            'goods_Type' => 1,
            'ac_states' => 1
        ])
        ->join('goods', 'ac_goods = goods.id')
        ->field([
            'goods.id',
            'goods.goods_name',
            'goods.goods_img',
            'count(*) as count',
            '"online" as account_type'  // 添加账号类型标识
        ])
        ->group('ac_goods')
        ->select()
        ->toArray();

        // 获取离线账号信息
        $offlineAccounts = Account::where([
            'goods_Type' => 0
        ])
        ->join('goods', 'ac_goods = goods.id')
        ->field([
            'goods.id',
            'goods.goods_name',
            'goods.goods_img',
            'count(*) as count',
            '"offline" as account_type'  // 添加账号类型标识
        ])
        ->group('ac_goods')
        ->select()
        ->toArray();

        // 合并在线和离线账号数据
        $result = array_merge($onlineAccounts, $offlineAccounts);

        // 返回标准格式的JSON响应
        return json([
            'code'   => 200,
            'msg'    => '查询成功',
            'data'   => $result
        ]);
    }

    // /**
    //  * 获取所有可用的离线账号信息
    //  *
    //  * @api
    //  * @return \think\response\Json
    //  *   {
    //  *     "code": int,         // 状态码 200表示成功
    //  *     "msg": string,       // 响应消息
    //  *     "data": [{
    //  *       "id": int,         // 商品ID
    //  *       "goods_name": string, // 商品名称
    //  *       "goods_img": string,  // 商品图片URL
    //  *       "count": int       // 可用账号数量
    //  *     }]
    //  *   }
    //  * @throws \think\db\exception\DataNotFoundException
    //  * @throws \think\db\exception\ModelNotFoundException
    //  * @throws \think\exception\DbException
    //  */
    // public function getAvailableAccounts()
    // {
    //     // 构建联表查询
    //     // goods_Type=0 表示离线账号
    //     // ac_states=1 表示账号状态可用
    //     $result = Account::where([
    //         'goods_Type' => 0
    //     ])
    //     // 通过ac_goods字段关联goods表
    //     ->join('goods', 'ac_goods = goods.id')
    //     // 指定需要查询的字段
    //     ->field([
    //         'goods.id',          // 商品ID
    //         'goods.goods_name',  // 商品名称
    //         'goods.goods_img',   // 商品图片
    //         'count(*) as count'  // 统计每个商品的可用账号数量
    //     ])
    //     // 按商品ID分组
    //     ->group('ac_goods')
    //     // 执行查询并返回结果集
    //     ->select();

    //     // 返回标准格式的JSON响应
    //     return json([
    //         'code'   => 200,
    //         'msg'    => '查询成功',
    //         'data'   => $result
    //     ]);
    // }

    /**
     * 获取当前用户已购买的永久版游戏订单信息
     *
     * @api
     * @return \think\response\Json
     *   {
     *     "code": int,         // 状态码 200表示成功
     *     "msg": string,       // 响应消息
     *     "data": [{
     *       "id": int,         // 商品ID
     *       "goods_name": string, // 商品名称
     *       "goods_img": string,  // 商品图片URL
     *       "count": int       // 可用账号数量
     *     }]
     *   }
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getPurchasedPermanentGames()
    {
        // 获取当前用户ID
        //$userId = session('user_id'); // 假设用户ID存储在会话中
        $userId = Cookie::get("id");
        // 查询用户已购买的永久版游戏订单（只查询有效订单，排除被封禁的）
        $orders = Order::where([
            'ord_uid' => $userId,
            'ord_ifpay' => 1,
            'GoodType' => 3,
            'is_permanent' => 1,
            'or_maturity' => 1  // 只查询未到期的订单，排除被封禁的
        ])->select();

        // 初始化结果数组
        $result = [];

        // 遍历订单，获取商品信息
        foreach ($orders as $order) {
            // 永久版卡密兑换订单中，ord_combo 直接存储的是商品ID
            $goodsId = $order->ord_combo;

            if ($goodsId) {
                $goods = Goods::where('id', $goodsId)->field([
                    'id',
                    'goods_name',
                    'goods_img'
                ])->find();

                if ($goods) {
                    // 统计符合条件的 Account 数据（只统计真正可用的账号）
                    $count = Account::where([
                        'ac_goods' => $goods->id,
                        'ac_states' => 1,        // 可用状态
                        'goods_Type' => 1,       // 在线账号类型
                        'modify' => 0,           // 未被修改
                        'ac_sell' => 1,          // 可售状态
                        'is_locked' => 0         // 未被锁定
                    ])
                    ->whereNull('ac_uid')        // 未被分配给用户
                    ->count();

                    // 避免重复添加相同的商品
                    $exists = false;
                    foreach ($result as $item) {
                        if ($item['id'] == $goods->id) {
                            $exists = true;
                            break;
                        }
                    }

                    if (!$exists) {
                        $result[] = [
                            'id' => $goods->id,
                            'goods_name' => $goods->goods_name,
                            'goods_img' => $goods->goods_img,
                            'count' => $count // 使用统计结果
                        ];
                    }
                }
            }
        }

        // 格式化返回的JSON数据
        return json([
            'code' => 200,
            'msg' => '查询成功',
            'data' => $result
        ]);
    }

    /**
     * 获取所有可用的在线账号信息
     *
     * @api
     * @return \think\response\Json
     *   {
     *     "code": int,         // 状态码 200表示成功
     *     "msg": string,       // 响应消息
     *     "data": [{
     *       "id": int,         // 商品ID
     *       "goods_name": string, // 商品名称
     *       "goods_img": string,  // 商品图片URL
     *       "count": int       // 可用账号数量
     *     }]
     *   }
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getAvailableOnlineAccounts()
    {
        // 构建联表查询
        // goods_Type=1 表示在线账号
        // ac_states=1 表示账号状态可用
        $result = Account::where([
            'goods_Type' => 1,
            'ac_states' => 1
        ])
        // 通过ac_goods字段关联goods表
        ->join('goods', 'ac_goods = goods.id')
        // 指定需要查询的字段
        ->field([
            'goods.id',          // 商品ID
            'goods.goods_name',  // 商品名称
            'goods.goods_img',   // 商品图片
            'count(*) as count'  // 统计每个商品的可用账号数量
        ])
        // 按商品ID分组
        ->group('ac_goods')
        // 执行查询并返回结果集
        ->select();

        // 返回标准格式的JSON响应
        return json([
            'code'   => 200,
            'msg'    => '查询成功',
            'data'   => $result
        ]);
    }
}