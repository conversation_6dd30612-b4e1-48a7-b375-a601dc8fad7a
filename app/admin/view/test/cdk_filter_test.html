<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密筛选功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 40px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .test-section h3 {
            color: #333;
            margin-top: 0;
            font-size: 1.4em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .result {
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }
        
        .feature-item h4 {
            margin-top: 0;
            color: #333;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .comparison-table th {
            background: #4facfe;
            color: white;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-unused { background: #d4edda; color: #155724; }
        .status-used { background: #f8d7da; color: #721c24; }
        .status-banned { background: #f5c6cb; color: #721c24; }
        
        .type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            background: #e2e3e5;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 卡密筛选功能测试</h1>
            <p>管理员后台 & 代理后台卡密筛选功能完整测试指南</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>1. 功能概述</h3>
                <div class="result success">
                    <h4>✅ 新增筛选功能</h4>
                    <p>为管理员后台和代理后台的卡密列表添加了完整的筛选功能，支持按状态和类型进行筛选。</p>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <h4>🎯 状态筛选</h4>
                            <ul>
                                <li>全部状态</li>
                                <li>未使用</li>
                                <li>已使用</li>
                                <li>已封禁</li>
                            </ul>
                        </div>
                        <div class="feature-item">
                            <h4>🏷️ 类型筛选</h4>
                            <ul>
                                <li>全部类型</li>
                                <li>日卡</li>
                                <li>小时卡</li>
                                <li>永久卡</li>
                                <li>离线卡</li>
                                <li>会员卡</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>2. 管理员后台筛选功能</h3>
                <div class="result info">
                    <h4>🔧 新增的筛选控件</h4>
                    <div class="code-block"><!-- 管理员后台新增的筛选表单 -->
&lt;div class="layui-inline"&gt;
    &lt;label class="layui-form-label"&gt;卡密状态&lt;/label&gt;
    &lt;div class="layui-input-inline"&gt;
        &lt;select name="status" lay-search=""&gt;
            &lt;option value=""&gt;全部状态&lt;/option&gt;
            &lt;option value="unused"&gt;未使用&lt;/option&gt;
            &lt;option value="used"&gt;已使用&lt;/option&gt;
            &lt;option value="banned"&gt;已封禁&lt;/option&gt;
        &lt;/select&gt;
    &lt;/div&gt;
&lt;/div&gt;
&lt;div class="layui-inline"&gt;
    &lt;label class="layui-form-label"&gt;卡密类型&lt;/label&gt;
    &lt;div class="layui-input-inline"&gt;
        &lt;select name="cdk_type" lay-search=""&gt;
            &lt;option value=""&gt;全部类型&lt;/option&gt;
            &lt;option value="day"&gt;日卡&lt;/option&gt;
            &lt;option value="hour"&gt;小时卡&lt;/option&gt;
            &lt;option value="permanent"&gt;永久卡&lt;/option&gt;
            &lt;option value="offline"&gt;离线卡&lt;/option&gt;
            &lt;option value="membership"&gt;会员卡&lt;/option&gt;
        &lt;/select&gt;
    &lt;/div&gt;
&lt;/div&gt;</div>
                    
                    <h4>📍 测试路径</h4>
                    <p><strong>访问地址：</strong><code>/admin/cdk/index</code></p>
                    
                    <h4>🎯 测试要点</h4>
                    <ul>
                        <li>状态筛选：选择不同状态查看筛选结果</li>
                        <li>类型筛选：选择不同类型查看筛选结果</li>
                        <li>组合筛选：同时选择状态和类型进行筛选</li>
                        <li>重置功能：点击"重置条件"清空所有筛选</li>
                    </ul>
                </div>
            </div>
            
            <div class="test-section">
                <h3>3. 代理后台筛选功能</h3>
                <div class="result info">
                    <h4>🔧 增强的筛选功能</h4>
                    <p>代理后台原有筛选功能，现在新增了"已封禁"状态筛选。</p>
                    
                    <div class="code-block"><!-- 代理后台增强的状态筛选 -->
&lt;el-radio-group v-model="filterStatus" @change="loadCards()"&gt;
    &lt;el-radio-button label=""&gt;全部&lt;/el-radio-button&gt;
    &lt;el-radio-button label="未使用"&gt;未使用&lt;/el-radio-button&gt;
    &lt;el-radio-button label="已使用"&gt;已使用&lt;/el-radio-button&gt;
    &lt;el-radio-button label="已封禁"&gt;已封禁&lt;/el-radio-button&gt;  &lt;!-- 新增 --&gt;
&lt;/el-radio-group&gt;</div>
                    
                    <h4>📍 测试路径</h4>
                    <p><strong>访问地址：</strong>代理后台 → 卡密管理</p>
                    
                    <h4>🎯 测试要点</h4>
                    <ul>
                        <li>状态筛选：测试新增的"已封禁"状态筛选</li>
                        <li>类型筛选：测试现有的卡密类型筛选</li>
                        <li>会员类型筛选：选择会员卡时的二级筛选</li>
                        <li>模糊搜索：测试关键词搜索功能</li>
                    </ul>
                </div>
            </div>
            
            <div class="test-section">
                <h3>4. 后端筛选逻辑</h3>
                <div class="result warning">
                    <h4>🔧 管理员后台筛选逻辑</h4>
                    <div class="code-block">// 状态筛选逻辑 (CdkController.php)
if (!empty($filters['status'])) {
    if ($filters['status'] === 'banned') {
        $query->where('c.is_banned', 1);
    } else {
        $query->where('c.is_banned', 0)->where('c.status', $filters['status']);
    }
}

// 卡密类型筛选逻辑
if (!empty($filters['cdk_type'])) {
    if ($filters['cdk_type'] === 'membership') {
        $query->where('c.cdk_type', 'like', 'member_%');
    } else {
        $query->where('c.cdk_type', $filters['cdk_type']);
    }
}</div>
                    
                    <h4>🔧 代理后台筛选逻辑</h4>
                    <div class="code-block">// 状态筛选逻辑 (Index.php)
if($status === '已封禁') {
    $where['is_banned'] = 1;
} else {
    $statusMap = [
        '未使用' => 'unused',
        '已使用' => 'used',
        '已过期' => 'expired'
    ];
    $where['is_banned'] = 0;
    $where['status'] = $statusMap[$status] ?? $status;
}</div>
                </div>
            </div>

            <div class="test-section">
                <h3>5. 筛选功能对比表</h3>
                <div class="result info">
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>功能</th>
                                <th>管理员后台</th>
                                <th>代理后台</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>状态筛选</strong></td>
                                <td>
                                    <span class="status-badge status-unused">未使用</span>
                                    <span class="status-badge status-used">已使用</span>
                                    <span class="status-badge status-banned">已封禁</span>
                                </td>
                                <td>
                                    <span class="status-badge status-unused">未使用</span>
                                    <span class="status-badge status-used">已使用</span>
                                    <span class="status-badge status-banned">已封禁</span>
                                </td>
                                <td>两个后台都支持完整的状态筛选</td>
                            </tr>
                            <tr>
                                <td><strong>类型筛选</strong></td>
                                <td>
                                    <span class="type-badge">日卡</span>
                                    <span class="type-badge">小时卡</span>
                                    <span class="type-badge">永久卡</span>
                                    <span class="type-badge">离线卡</span>
                                    <span class="type-badge">会员卡</span>
                                </td>
                                <td>
                                    <span class="type-badge">日卡</span>
                                    <span class="type-badge">小时卡</span>
                                    <span class="type-badge">永久卡</span>
                                    <span class="type-badge">离线卡</span>
                                    <span class="type-badge">会员卡</span>
                                </td>
                                <td>两个后台都支持完整的类型筛选</td>
                            </tr>
                            <tr>
                                <td><strong>会员类型筛选</strong></td>
                                <td>❌ 不支持</td>
                                <td>✅ 支持二级筛选</td>
                                <td>代理后台支持会员类型的二级筛选</td>
                            </tr>
                            <tr>
                                <td><strong>模糊搜索</strong></td>
                                <td>✅ 卡密码+商品名</td>
                                <td>✅ 关键词搜索</td>
                                <td>两个后台都支持模糊搜索</td>
                            </tr>
                            <tr>
                                <td><strong>代理筛选</strong></td>
                                <td>✅ 支持</td>
                                <td>❌ 不需要</td>
                                <td>管理员可筛选特定代理的卡密</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="test-section">
                <h3>6. 测试步骤</h3>
                <div class="result success">
                    <h4>🧪 管理员后台测试</h4>
                    <ol>
                        <li><strong>访问页面：</strong>登录管理员后台 → CDK卡密管理</li>
                        <li><strong>状态筛选测试：</strong>
                            <ul>
                                <li>选择"未使用" - 应只显示未使用的卡密</li>
                                <li>选择"已使用" - 应只显示已使用的卡密</li>
                                <li>选择"已封禁" - 应只显示已封禁的卡密</li>
                            </ul>
                        </li>
                        <li><strong>类型筛选测试：</strong>
                            <ul>
                                <li>选择"日卡" - 应只显示日卡类型</li>
                                <li>选择"会员卡" - 应显示所有member_开头的卡密</li>
                                <li>选择"离线卡" - 应只显示离线卡类型</li>
                            </ul>
                        </li>
                        <li><strong>组合筛选测试：</strong>同时选择状态和类型进行筛选</li>
                        <li><strong>重置测试：</strong>点击"重置条件"应清空所有筛选</li>
                    </ol>

                    <h4>🧪 代理后台测试</h4>
                    <ol>
                        <li><strong>访问页面：</strong>登录代理后台 → 卡密管理</li>
                        <li><strong>状态筛选测试：</strong>
                            <ul>
                                <li>点击"未使用"按钮 - 应只显示未使用的卡密</li>
                                <li>点击"已使用"按钮 - 应只显示已使用的卡密</li>
                                <li>点击"已封禁"按钮 - 应只显示已封禁的卡密</li>
                            </ul>
                        </li>
                        <li><strong>类型筛选测试：</strong>
                            <ul>
                                <li>选择"在线日卡" - 应只显示日卡</li>
                                <li>选择"会员卡" - 应显示会员类型下拉框</li>
                                <li>选择"离线卡密" - 应只显示离线卡</li>
                            </ul>
                        </li>
                        <li><strong>会员类型筛选：</strong>选择会员卡后，测试会员类型的二级筛选</li>
                        <li><strong>搜索测试：</strong>在搜索框输入关键词测试模糊搜索</li>
                    </ol>
                </div>
            </div>

            <div class="test-section">
                <h3>7. 预期结果</h3>
                <div class="result success">
                    <h4>✅ 功能正常的表现</h4>
                    <ul>
                        <li><strong>筛选准确：</strong>选择筛选条件后，列表只显示符合条件的卡密</li>
                        <li><strong>组合筛选：</strong>多个筛选条件可以同时生效</li>
                        <li><strong>实时更新：</strong>更改筛选条件后列表立即更新</li>
                        <li><strong>重置功能：</strong>重置后显示所有卡密</li>
                        <li><strong>分页正常：</strong>筛选后的分页功能正常</li>
                        <li><strong>状态显示：</strong>已封禁卡密在状态列正确显示"已封禁"</li>
                        <li><strong>按钮显示：</strong>已封禁卡密在操作列只显示删除按钮</li>
                    </ul>

                    <h4>🚨 需要注意的问题</h4>
                    <ul>
                        <li><strong>权限控制：</strong>代理只能看到自己生成的卡密</li>
                        <li><strong>数据一致性：</strong>筛选结果与实际数据状态一致</li>
                        <li><strong>性能表现：</strong>大量数据时筛选响应速度</li>
                        <li><strong>界面响应：</strong>筛选过程中的加载状态</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
