<?php
declare (strict_types = 1);

namespace app\agent\middleware;

use think\facade\Db;
use think\facade\Session;
use think\Request;
use think\Response;

/**
 * 代理状态检查中间件
 * 检查代理是否被禁用，如果被禁用则清除会话并返回错误信息
 */
class CheckAgentStatus
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next)
    {
        // 获取当前代理ID
        $agentId = session('agent_id');
        
        // 如果没有代理ID，说明未登录，直接跳转到登录页
        if (!$agentId) {
            if ($request->isAjax()) {
                return json([
                    'code' => 401,
                    'msg' => '请先登录',
                    'redirect' => '/agent/index/login'
                ]);
            } else {
                return redirect('/agent/index/login');
            }
        }
        
        // 查询代理状态
        try {
            $agent = Db::name('agents')->where('id', $agentId)->find();
        } catch (\Exception $e) {
            // 数据库查询失败，可能是会话超时或网络问题
            \think\facade\Log::error('代理状态查询失败: ' . $e->getMessage(), [
                'agent_id' => $agentId,
                'error' => $e->getMessage()
            ]);

            // 清除会话信息
            Session::delete('agent_id');
            Session::delete('agent_name');

            if ($request->isAjax()) {
                return json([
                    'code' => 401,
                    'msg' => '会话已过期，请重新登录',
                    'session_expired' => true,
                    'redirect' => '/agent/index/login'
                ]);
            } else {
                Session::flash('login_error', '会话已过期，请重新登录');
                return redirect('/agent/index/login');
            }
        }

        // 如果代理不存在
        if (!$agent) {
            // 清除会话信息
            Session::delete('agent_id');
            Session::delete('agent_name');

            \think\facade\Log::warning('代理账号不存在', ['agent_id' => $agentId]);

            if ($request->isAjax()) {
                return json([
                    'code' => 404,
                    'msg' => '代理账号不存在，请联系管理员',
                    'account_not_found' => true,
                    'redirect' => '/agent/index/login'
                ]);
            } else {
                Session::flash('login_error', '代理账号不存在，请联系管理员');
                return redirect('/agent/index/login');
            }
        }

        // 如果代理被禁用
        if ($agent['status'] != 1) {
            // 清除会话信息
            Session::delete('agent_id');
            Session::delete('agent_name');

            \think\facade\Log::info('代理账号被禁用', [
                'agent_id' => $agentId,
                'agent_username' => $agent['username'] ?? 'unknown',
                'status' => $agent['status']
            ]);

            if ($request->isAjax()) {
                return json([
                    'code' => 403,
                    'msg' => '您的代理账号已被管理员禁用，请联系管理员',
                    'account_disabled' => true,
                    'redirect' => '/agent/index/login'
                ]);
            } else {
                Session::flash('login_error', '您的代理账号已被管理员禁用，请联系管理员');
                return redirect('/agent/index/login');
            }
        }
        
        // 代理状态正常，继续处理请求
        return $next($request);
    }
}
