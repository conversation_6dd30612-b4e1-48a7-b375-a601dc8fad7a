<?php
namespace app\admin\controller;
use app\BaseController;
use app\admin\model\System;
use app\admin\model\Goodsclass;
use app\admin\model\Goods;
use app\admin\model\Account;
use app\admin\model\Combo;
use app\admin\model\Order;
use think\facade\Request;
use think\facade\Db;

use app\admin\model\Promotion as Pro;
use app\admin\model\Config;
class Promotion extends BaseController
{
    // 保存推广设置
    public function index(){
      return Config::where("id",1)->update(Request::param());
    }
    //全部推广记录
    public function promotionLogo(){
        $reqData=Request::param();
         $promotionLogo=Pro::where("pro_user",!0)->limit(10*($reqData['page']-1),10)->select(); 
        $list=[
            "code"=>0,
            "count"=>count(Pro::select()),
            "data"=>$promotionLogo
            ];
            return json($list);
    }
        // 删除推广记录
        public function delpromotionLogo(){
        $delPro=Pro::destroy(Request::param("id"));
        return json($delPro);
    }
        public function delGoodsClass(){
        $delGoodsClass=Goodsclass::destroy(Request::param("id"));
        return json($delGoodsClass);
    }
}