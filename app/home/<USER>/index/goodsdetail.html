<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <script src="/static/js/jquery.min.js"></script>
    <title>{$system.sy_title} - {$goodsDetail.goods_name}</title>
    <link rel="stylesheet" href="/static/core.css">
    <link rel="stylesheet" href="/static/common.css">
    <link rel="stylesheet" href="/static/public.css">
    <link rel="stylesheet" href="/static/default/core.css">
    <link rel="stylesheet" href="/static/default/index.css">
    <script src="/static/da.js"></script>
    <script src="/static/da-web-fxIndexPvUv.js"></script>
    <script src="/static/core.js"></script>
    <script src="/static/common.js"></script>
    <script src="/static/public.js"></script>
    <script src="/static/default/core.js"></script>
    <script src="/static/default/index.js"></script>
    <link rel="stylesheet" href="/static/css/font-awesome/all.min.css">
    <style>
        .header{
            -webkit-box-shadow: 0 0 0;
            box-shadow: 0 0 0;
        }
        .shi_ming_hint{
            padding: 10px 0;
        }
        .hint_con{
            width: 1200px !important;
            margin: 0 auto;
            line-height: 30px;
            padding-left: 30px;
            border-radius: 8px;
            border: 1px solid rgba(244,141,180,0.15);
            background: #FFF8F9 url(/static/images/hint.png) no-repeat;
            background-size: 15px 16px;
            background-position: 9px 50%;
            font-size: 12px;
            color: #777;
            box-sizing: border-box;
        }
        .shi_ming_hint .hint_con span{
            color: #FE2B3D;
        }

        /* 内容区域布局优化 */
        .page-container {
            background: #f5f9fa;
            min-height: calc(100vh - 300px);
            padding: 30px 0;
        }

        .content-wrapper {
            max-width: 1200px;
            margin: 0 auto;
            background: transparent;
        }

        /* 修改原有的主体内容容器样式 */
        .content-main {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
            margin-top: 20px;
            overflow: hidden;
        }

        /* 面包屑导航 */
        .breadcrumb {
            padding: 16px 0;
            color: #666;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #1a89fa;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            margin: 0 8px;
            color: #999;
        }

        /* 帮助中心样式 */
        .help_s {
            margin-top: 30px;
            padding: 20px 0;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        }

        .popular_game_t {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .t_l.font_s {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .color_2 {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .popular_game_bzli {
            padding: 15px 20px;
            border-bottom: 1px solid #f5f5f5;
        }

        .popular_game_bzli:last-child {
            border-bottom: none;
        }

        .popular_bz_font {
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
        }

        .popular_bz_fontp {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }

        .popular_bz_mt {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
        }

        /* 添加导航样式 */
        .navigation .navigation_nav a,
        .navigation .navigation_nav_s li a {
            color: #333 !important;  /* 默认深灰色 */
            text-decoration: none !important;
        }

        .navigation .navigation_nav a:hover,
        .navigation .navigation_nav_s li a:hover {
            color: #FE2B3D !important;  /* 鼠标悬停时红色 */
        }

        /* 移除之前的active样式，只在需要时添加 */
        .navigation .navigation_nav.current a,
        .navigation .navigation_nav_s li.current a {
            color: #FE2B3D !important;
        }

        /* 确保hot-tag和new-tag不影响链接颜色 */
        .navigation .navigation_nav .hot-tag,
        .navigation .navigation_nav .new-tag {
            color: #FE2B3D;
        }

        /* 富文本内容样式 */
        .rich-text-content {
            line-height: 1.6;
        }

        .rich-text-content p {
            margin-bottom: 1em;
        }

        .rich-text-content h1,
        .rich-text-content h2,
        .rich-text-content h3,
        .rich-text-content h4,
        .rich-text-content h5,
        .rich-text-content h6 {
            margin: 1.5em 0 0.5em;
            font-weight: 600;
        }

        .rich-text-content ul,
        .rich-text-content ol {
            margin: 1em 0;
            padding-left: 2em;
        }

        .rich-text-content ul {
            list-style-type: disc;
        }

        .rich-text-content ol {
            list-style-type: decimal;
        }

        .rich-text-content a {
            color: #1a89fa;
            text-decoration: none;
        }

        .rich-text-content a:hover {
            text-decoration: underline;
        }

        .rich-text-content img {
            max-width: 100%;
            height: auto;
            margin: 1em 0;
            border-radius: 4px;
        }

        .rich-text-content blockquote {
            margin: 1em 0;
            padding: 0.5em 1em;
            border-left: 4px solid #e5e7eb;
            background-color: #f9fafb;
        }

        .rich-text-content code {
            background-color: #f3f4f6;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: monospace;
        }

        .rich-text-content pre {
            background-color: #f3f4f6;
            padding: 1em;
            border-radius: 4px;
            overflow-x: auto;
            margin: 1em 0;
        }

        .rich-text-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
        }

        .rich-text-content th,
        .rich-text-content td {
            border: 1px solid #e5e7eb;
            padding: 0.5em;
            text-align: left;
        }

        .rich-text-content th {
            background-color: #f9fafb;
        }

        /* Layui 特定样式支持 */
        .rich-text-content .layui-text {
            line-height: 1.8;
        }

        .rich-text-content .layui-quote-nm {
            border-color: #e6e6e6;
            border-style: solid;
            border-width: 1px 1px 1px 5px;
            background: none;
        }

        /* 百科样式优化 */
        .baike-item {
            margin-bottom: 2.5em;
        }

        .baike-item-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin: 1.5em 0 1em;
            padding-bottom: 0.5em;
            border-bottom: 2px solid #f3f4f6;
        }

        .baike-item-main {
            color: #666;
        }

        .baike-item-main .MsoNormal {
            margin-bottom: 1em;
            line-height: 1.8;
        }

        .baike-item-main img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 1.5em 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 图片容器优化 */
        .media-wrap.image-wrap {
            margin: 1.5em 0;
        }

        .media-wrap.image-wrap img {
            display: block;
            max-width: 100%;
            height: auto;
            margin: 0 auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .baike-item-title {
                font-size: 1.25em;
            }

            .rich-text-content {
                padding: 0 15px;
            }

            .media-wrap.image-wrap {
                margin: 1em -15px;
            }

            .media-wrap.image-wrap img {
                border-radius: 0;
            }
        }

        /* 现代UI样式系统 */
        :root {
            /* 主色调 */
            --primary: #3a86ff;
            --primary-light: #e0ecff;
            --primary-dark: #2563eb;

            /* 中性色 */
            --neutral-900: #111827;
            --neutral-800: #1f2937;
            --neutral-700: #374151;
            --neutral-600: #4b5563;
            --neutral-500: #6b7280;
            --neutral-400: #9ca3af;
            --neutral-300: #d1d5db;
            --neutral-200: #e5e7eb;
            --neutral-100: #f3f4f6;
            --neutral-50: #f9fafb;

            /* 功能色 */
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;

            /* 圆角 */
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --radius-2xl: 1rem;

            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            /* 过渡 */
            --transition-fast: 150ms;
            --transition-normal: 250ms;
            --transition-slow: 350ms;
        }

        /* 帮助中心组件 */
        .help-section {
            padding: 5rem 0;
            background-color: var(--neutral-50);
        }

        .section-header {
            margin-bottom: 2.5rem;
            text-align: center;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--neutral-900);
            margin-bottom: 0.5rem;
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 50%;
            transform: translateX(-50%);
            width: 3rem;
            height: 0.25rem;
            background-color: var(--primary);
            border-radius: 9999px;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--neutral-600);
            max-width: 36rem;
            margin: 1rem auto 0;
        }

        .help-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .help-card {
            position: relative;
            background-color: white;
            border-radius: var(--radius-lg);
            padding: 1.75rem;
            box-shadow: var(--shadow-md);
            transition: transform var(--transition-normal), box-shadow var(--transition-normal);
            overflow: hidden;
            border: 1px solid var(--neutral-200);
        }

        .help-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .card-indicator {
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 3rem;
            background-color: var(--primary);
            border-radius: 0 0 var(--radius-sm) 0;
        }

        .help-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--neutral-900);
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .help-content {
            font-size: 1rem;
            color: var(--neutral-700);
            margin-bottom: 1.5rem;
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .help-meta {
            font-size: 0.875rem;
            color: var(--neutral-500);
            display: flex;
            align-items: center;
            border-top: 1px solid var(--neutral-200);
            padding-top: 1rem;
        }

        .help-meta time {
            display: flex;
            align-items: center;
        }

        .help-meta time::before {
            content: '\f017'; /* Font Awesome 时钟图标 */
            font-family: 'Font Awesome 5 Free';
            margin-right: 0.5rem;
            opacity: 0.7;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .help-section {
                padding: 3rem 1rem;
            }

            .section-title {
                font-size: 1.75rem;
            }

            .section-subtitle {
                font-size: 1rem;
            }

            .help-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 暗黑模式变量 */
        :root {
          /* 亮色模式变量 */
          --bg-primary: #ffffff;
          --bg-secondary: #f3f4f6;
          --text-primary: #111827;
          --text-secondary: #4b5563;
          --border-color: #e5e7eb;
        }

        @media (prefers-color-scheme: dark) {
          :root {
            /* 暗色模式变量 */
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --text-primary: #f3f4f6;
            --text-secondary: #9ca3af;
            --border-color: #374151;
          }
        }

        /* 应用变量 */
        body {
          background-color: var(--bg-primary);
          color: var(--text-primary);
        }

        .help-card {
          background-color: var(--bg-primary);
          border-color: var(--border-color);
        }

        .help-title {
          color: var(--text-primary);
        }

        .help-content {
          color: var(--text-secondary);
        }

        /* 防止富文本内容样式污染 */
        .rich-text-container {
          /* 重置富文本中的内联样式影响范围 */
          & [style] {
            max-width: 100% !important;
            height: auto !important;
            font-family: inherit !important;
          }

          /* 限制富文本中的特定元素 */
          & iframe, & embed, & object {
            max-width: 100% !important;
            max-height: 400px !important;
          }

          /* 移除可能有害的脚本和样式标签 */
          & script, & style {
            display: none !important;
          }

          /* 修复表格宽度问题 */
          & table[width] {
            width: 100% !important;
          }
        }

        @media (max-width: 768px) {
            /* 调整主内容区的外边距和内边距 */
            .content-main {
                margin-top: 10px;
            }

            .max-w-7xl {
                padding: 0.5rem;
            }

            /* 调整商品预览区域的布局 */
            .max-w-7xl.p-4.flex.gap-6 {
                flex-direction: column;
            }

            .flex.flex-col[style="width: 400px;"] {
                width: 100% !important;
                margin-bottom: 1rem;
            }

            /* 调整搜索框的边距 */
            .flex-1.max-w-2xl.mx-8 {
                margin-left: 1rem;
                margin-right: 1rem;
            }

            /* 确保导航菜单在移动端有正确的滚动行为 */
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }

            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .md\:hidden {
                display: block; /* 移除!important */
            }

            /* 删除下面这行，它会导致隐藏的菜单被强制显示 */
            /* .hidden.md\:hidden {
                display: flex !important;
            } */
        }

        @media (max-width: 768px) {
            .grid.grid-cols-4.gap-4.mt-6 {
                grid-template-columns: repeat(2, 1fr);
            }

            #timerComponent {
                flex-wrap: wrap;
                justify-content: center;
            }

            .mt-6.flex.justify-end.gap-4 {
                flex-direction: column;
            }

            .help-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            #mobile-menu {
                position: fixed; /* 改为fixed而不是absolute */
                top: 60px; /* 设置固定顶部距离，对应header高度 */
                left: 0;
                right: 0;
                width: 100%;
                background: white;
                z-index: 999; /* 增加z-index确保菜单在最上层 */
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                overflow-y: auto; /* 允许滚动 */
                max-height: calc(100vh - 60px); /* 最大高度为视口高度减去顶部位置 */
            }

            #mobile-menu.hidden {
                display: none !important;
            }

            #mobile-menu:not(.hidden) {
                display: block;
            }

            /* 修复header位置 */
            header.bg-white.shadow {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                z-index: 1000;
            }

            /* 为fixed header增加body padding */
            body {
                padding-top: 60px; /* 与header高度相同 */
            }
        }

        /* 修复顶部导航超出屏幕边界问题 */
        header.bg-white.shadow {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            overflow-x: hidden; /* 防止水平溢出 */
        }

        /* 调整header内部容器的宽度和内边距 */
        header .max-w-7xl {
            width: 100%;
            padding-left: 10px;
            padding-right: 10px;
            box-sizing: border-box;
        }

        /* 确保logo不会太大 */
        header .h-8.w-auto {
            height: 24px;
            width: auto;
        }

        /* 调整菜单按钮位置 */
        .flex.items-center.md\:hidden {
            margin-right: 0;
        }

        /* 确保导航下的搜索区域也不会溢出 */
        .bg-white.py-4.shadow-sm {
            overflow-x: hidden;
            width: 100%;
            box-sizing: border-box;
        }

        .bg-white.py-4.shadow-sm .max-w-7xl {
            width: 100%;
            padding-left: 10px;
            padding-right: 10px;
            box-sizing: border-box;
        }

        /* 调整搜索框区域布局 */
        .flex.flex-col.md\:flex-row.items-center.justify-between {
            width: 100%;
        }

        /* 确保搜索输入框不会溢出 */
        .w-full.md\:w-1\/2.lg\:w-2\/5 {
            width: 100%;
        }

        /* 调整移动端菜单 */
        #mobile-menu {
            width: 100%;
            left: 0;
            right: 0;
            box-sizing: border-box;
        }

        /* 修复body padding，确保内容不会被fixed header遮挡 */
        body {
            padding-top: 110px; /* 增加顶部padding，包括header和搜索区域的高度 */
            overflow-x: hidden; /* 防止整个页面水平滚动 */
        }

        /* 解决顶部导航超出WAP屏幕边界问题 */
        @media (max-width: 768px) {
            /* 强制所有容器遵循边界 */
            html, body {
                overflow-x: hidden !important;
                width: 100% !important;
                max-width: 100% !important;
                position: relative;
            }

            /* 修复顶部导航和其内部元素 */
            header.bg-white.shadow {
                width: 100% !important;
                max-width: 100vw !important;
                overflow-x: hidden !important;
                left: 0 !important;
                right: 0 !important;
                box-sizing: border-box !important;
            }

            /* 修复内部容器 */
            header .max-w-7xl,
            .bg-white.py-4.shadow-sm .max-w-7xl {
                width: 100% !important;
                max-width: 100% !important;
                padding-left: 10px !important;
                padding-right: 10px !important;
                box-sizing: border-box !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }

            /* 修复搜索区域容器 */
            .bg-white.py-4.shadow-sm {
                width: 100% !important;
                max-width: 100vw !important;
                overflow-x: hidden !important;
                left: 0 !important;
                right: 0 !important;
                box-sizing: border-box !important;
            }

            /* 修复flex容器 */
            .flex.justify-between,
            .flex.flex-col.md\:flex-row.items-center.justify-between {
                width: 100% !important;
                flex-wrap: wrap !important;
            }

            /* 确保logo图片大小合适 */
            img.h-8.w-auto,
            img.h-16.w-auto {
                max-width: 150px !important;
                height: auto !important;
            }

            /* 确保搜索框容器大小合适 */
            .w-full.md\:w-1\/2.lg\:w-2\/5 {
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* 修复搜索表单 */
            form.relative {
                width: 100% !important;
            }

            /* 修复搜索输入框 */
            input.w-full.pl-4.pr-12.py-3.border {
                width: 100% !important;
                box-sizing: border-box !important;
            }

            /* 修改页面容器间距 */
            .page-container {
                padding-top: 110px !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                overflow-x: hidden !important;
            }

            .content-wrapper {
                width: 100% !important;
                padding-left: 10px !important;
                padding-right: 10px !important;
                box-sizing: border-box !important;
            }

            /* 修复移动端菜单位置 */
            #mobile-menu {
                width: 100% !important;
                left: 0 !important;
                right: 0 !important;
                box-sizing: border-box !important;
            }
        }

        /* 移动端帮助中心优化 */
        @media (max-width: 768px) {
            /* 帮助中心容器调整 */
            .help-section {
                padding: 2rem 0.5rem !important;
                overflow-x: hidden !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            /* 调整帮助中心内容容器 */
            .help-section .container {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            /* 标题样式调整 */
            .section-title {
                font-size: 1.5rem !important;
                margin-bottom: 0.25rem !important;
            }

            .section-subtitle {
                font-size: 0.875rem !important;
                margin-bottom: 1.5rem !important;
                padding: 0 0.5rem !important;
            }

            /* 卡片网格布局调整为单列 */
            .help-grid {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
                padding: 0 0.5rem !important;
            }

            /* 卡片样式优化 */
            .help-card {
                margin-bottom: 0.75rem !important;
                padding: 1.25rem !important;
            }

            .help-title {
                font-size: 1.125rem !important;
                margin-bottom: 0.75rem !important;
            }

            .help-content {
                font-size: 0.875rem !important;
                -webkit-line-clamp: 3 !important;
                margin-bottom: 1rem !important;
            }

            .help-meta {
                padding-top: 0.75rem !important;
                font-size: 0.75rem !important;
            }

            /* 游戏介绍区域调整 */
            .game-introduction-section {
                margin: 1rem auto !important;
                padding: 0 0.5rem !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            .border-b.border-gray-200.p-6 {
                padding: 1rem !important;
            }

            .border-b.border-gray-200.p-6 h2 {
                font-size: 1.25rem !important;
            }

            .p-6 {
                padding: 1rem !important;
            }

            /* 备案信息调整 */
            div[style="text-align: center; padding: 20px; color: #141313;"] {
                padding: 1rem 0 !important;
                font-size: 0.75rem !important;
            }

            /* 修复卡片高度不一致问题 */
            .help-card {
                min-height: auto !important;
                height: auto !important;
            }

            /* 优化卡片内容布局 */
            .help-content.rich-text-container {
                max-height: 4.5em !important;
                overflow: hidden !important;
            }

            .help-card.expanded .help-content.rich-text-container {
                max-height: none !important;
            }

            /* 改进展开按钮样式 */
            .expand-btn {
                display: block !important;
                width: 100% !important;
                text-align: center !important;
                padding: 0.5rem 0 !important;
                font-size: 0.75rem !important;
                color: var(--primary) !important;
                background-color: transparent !important;
                border: none !important;
                position: relative !important;
            }

            .expand-btn::after {
                content: '展开更多' !important;
                display: inline-block !important;
            }

            .help-card.expanded .expand-btn::after {
                content: '收起内容' !important;
            }

            /* 加强入场动画效果 */
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .help-card {
                animation: fadeInUp 0.5s ease forwards;
                animation-delay: calc(var(--index) * 0.1s);
                opacity: 0;
            }

            /* 响应式布局下的指示器调整 */
            .card-indicator {
                height: 2rem !important;
                width: 3px !important;
            }

            /* 优化时间显示 */
            .help-meta time {
                font-size: 0.75rem !important;
                opacity: 0.7 !important;
            }
        }

        /* 会员开通模态框WAP适配优化 */
        @media (max-width: 768px) {
            /* 模态框大小调整 */
            #membershipModal .bg-white.rounded-lg.shadow-xl {
                width: 95% !important;
                max-width: 95% !important;
                max-height: 85vh !important;
                overflow-y: auto !important;
                margin: 0 auto !important;
            }

            /* 模态框头部调整 */
            #membershipModal .relative.bg-gradient-to-r {
                padding: 0.75rem 1rem !important;
            }

            #membershipModal .text-xl.font-bold.text-white {
                font-size: 1rem !important;
            }

            #membershipModal .absolute.top-4.right-4 {
                top: 0.75rem !important;
                right: 0.75rem !important;
            }

            /* 模态框内容区域 */
            #membershipModal .p-6 {
                padding: 1rem !important;
            }

            /* 时长会员选项调整为单列 */
            #durationMemberOptions {
                grid-template-columns: 1fr !important;
            }

            /* 会员类型按钮调整 */
            #membershipModal .flex.mb-6 button {
                padding: 0.5rem !important;
                font-size: 0.875rem !important;
            }

            /* 会员特权说明调整为单列 */
            #membershipModal .grid.grid-cols-2.gap-4 {
                grid-template-columns: 1fr !important;
            }

            /* 优化特权图标大小 */
            #membershipModal .bg-pink-100.p-2.rounded-full.mr-3 {
                padding: 0.5rem !important;
                margin-right: 0.5rem !important;
            }

            #membershipModal .w-5.h-5 {
                width: 1rem !important;
                height: 1rem !important;
            }

            /* 至尊会员选项内容优化 */
            #vipMemberOptions .border-2.border-purple-600 {
                padding: 1rem !important;
            }

            #vipMemberOptions h4 {
                font-size: 1.125rem !important;
            }

            #vipMemberOptions .text-purple-600.text-3xl {
                font-size: 1.75rem !important;
            }

            #vipMemberOptions .mt-6.bg-white.p-4 {
                padding: 0.75rem !important;
                margin-top: 1rem !important;
            }

            #vipMemberOptions .text-left.text-gray-600.space-y-2 {
                margin-top: 0.5rem !important;
                space-y: 0.375rem !important;
            }

            /* 特权项目间距调整 */
            #vipMemberOptions .flex.items-start {
                margin-bottom: 0.5rem !important;
            }

            /* 购买按钮调整 */
            #purchaseMembership {
                padding: 0.75rem 1.5rem !important;
                font-size: 0.875rem !important;
            }

            /* 服务协议说明调整 */
            #membershipModal .text-gray-500.text-xs.mt-2 {
                margin-top: 0.5rem !important;
                font-size: 0.7rem !important;
            }

            /* 确保列表项目在小屏幕上正确排布 */
            #vipMemberOptions .flex.items-start span {
                font-size: 0.75rem !important;
                line-height: 1.2 !important;
            }

            /* 卡片内部内容调整 */
            .duration-member-option h4 {
                font-size: 0.875rem !important;
            }

            .duration-member-option .text-pink-500.text-xl {
                font-size: 1.25rem !important;
            }

            .duration-member-option .text-gray-500.text-sm {
                font-size: 0.75rem !important;
            }

            /* 允许模态框内容滚动 */
            #membershipModal .bg-white.rounded-lg.shadow-xl > div:last-child {
                max-height: 70vh !important;
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                padding-bottom: 1rem !important;
            }

            /* 优化关闭按钮触摸区域 */
            #closeMembershipModal {
                padding: 10px !important;
                margin: -10px !important;
            }

            /* 防止模态框内容因为手指滚动而触发背景滚动 */
            #membershipModal.fixed.inset-0 {
                position: fixed !important;
                top: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                left: 0 !important;
                z-index: 50 !important;
                overscroll-behavior: contain !important;
            }

            /* 优化按钮触摸区域 */
            #membershipModal button {
                min-height: 44px !important; /* 确保触摸区域足够大 */
            }

            /* 防止选择项目溢出 */
            .duration-member-option {
                width: 100% !important;
                box-sizing: border-box !important;
            }

            /* 模态框出现时防止主体内容滚动 */
            body.modal-open {
                overflow: hidden !important;
                position: fixed !important;
                width: 100% !important;
            }
        }
    </style>
</head>

<body class="bg-gray-50 font-sans text-gray-800">
    <!-- 顶部导航 -->
    <header class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <a href="/">
                <img class="h-8 w-auto" src="{$system.sy_logo}" alt="{$system.sy_title}">
              </a>
            </div>
          </div>

          <!-- 导航菜单 -->
          <nav class="hidden md:flex space-x-8">
            <a href="/" class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
            <a href="/user/index/feedback" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">工单管理</a>
            <a href="/user/CdkExchange/index" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">卡密兑换</a>
            <a href="/user/index/notice" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">帮助中心</a>
            <a href="/home/<USER>" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">全部游戏</a>
            <a href="/user/index/onshop" class="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">用户中心</a>
          </nav>

          <!-- 用户登录状态 -->
          <div class="hidden md:flex items-center">
            {if condition="isset($isLoggedIn) && $isLoggedIn && isset($user) && !empty($user) && isset($user.us_username)"}
            <a href="/user/index/onshop" class="flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
              <span class="mr-2 h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                <i class="fas fa-user"></i>
              </span>
              <span>{$user.us_username}</span>
            </a>
            {else /}
            <a href="/user/login" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
              登录
            </a>
            {/if}
          </div>

          <!-- 移动端菜单按钮 -->
          <div class="flex items-center md:hidden">
            <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
              <span class="sr-only">打开菜单</span>
              <i class="fas fa-bars"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 移动端菜单 -->
      <div class="md:hidden hidden" id="mobile-menu">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50">首页</a>
          <a href="/user/index/feedback" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">工单管理</a>
          <a href="/user/CdkExchange/index" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">卡密兑换</a>
          <a href="/user/index/notice" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">帮助中心</a>
          <a href="/home/<USER>" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">全部游戏</a>
          <a href="/user/index/onshop" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">用户中心</a>

          <!-- 移动端用户状态 -->
          {if condition="isset($isLoggedIn) && $isLoggedIn"}
          <div class="pt-4 pb-3 border-t border-gray-200">
            <div class="flex items-center px-5">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                  <i class="fas fa-user text-gray-500"></i>
                </div>
              </div>
              <div class="ml-3">
                <div class="text-base font-medium text-gray-800">
                  {if condition="isset($user) && !empty($user) && isset($user.us_username)"}
                    {$user.us_username}
                  {else /}
                    游客
                  {/if}
                </div>
              </div>
            </div>
            <div class="mt-3 px-2 space-y-1">
              <a href="/user/index/onshop" class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">用户中心</a>
            </div>
          </div>
          {else /}
          <div class="pt-4 pb-3 border-t border-gray-200">
            <a href="/user/login" class="block px-3 py-2 rounded-md text-base font-medium text-blue-600 hover:bg-gray-50">
              登录
            </a>
          </div>
          {/if}
        </div>
      </div>
    </header>

    <!-- 提示信息 -->
    <!-- <div class="bg-rose-50 py-2">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="hint_con">
          根据相关法律法规的要求，我们<span>严禁向未成年人提供游戏账号租售服务</span>，如果您是未成年人，请退出。
        </div>
      </div>
    </div> -->

    <!-- 搜索区域 -->
    <div class="bg-white py-4 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row items-center justify-between">
          <a href="/" class="mb-4 md:mb-0">
            <img src="{$system.sy_logo}" alt="{$system.sy_title}" class="h-16 w-auto">
          </a>
          <div class="w-full md:w-1/2 lg:w-2/5">
            <form name="search" action="/home/<USER>" method="get" class="relative">
              <input id="keyWords" type="text" name="keyWords" value="" placeholder="输入关键词为您查找" class="w-full pl-4 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <button id="search" type="submit" class="absolute right-0 top-0 h-full px-4 flex items-center justify-center text-gray-400 hover:text-blue-600">
                <i class="fas fa-search text-lg"></i>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <div class="page-container">
        <div class="content-wrapper">
            <!-- 添加面包屑导航 -->
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span>/</span>
                <a href="#game-list">游戏列表</a>
                <span>/</span>
                {$goodsDetail.goods_name}
            </div>

            <!-- 将原有主体内容包裹在新的容器中 -->
            <div class="content-main">
                <div class="max-w-7xl mx-auto p-4 flex gap-6">
                    <!-- Left Section -->
                    <div class="flex flex-col" style="width: 400px;">
                        <!-- Preview Section -->
                        <div class="relative flex flex-col">
                            <!-- Main Preview -->
                            <div class="relative flex items-center justify-center mb-4">
                                <div id="mediaContainer" class="w-full h-64 overflow-hidden rounded border border-pink-500" style="max-width: 400px; max-height: 300px;">
                                    <img id="previewImage" src="{$goodsDetail.goods_img|default='/static/images/default.png'}" alt="Preview" class="w-full h-full object-cover">
                                    {if condition="!empty($goodsDetail.goods_video)"}
                                    <video id="previewVideo" class="hidden w-full h-full object-cover" controls playsinline>
                                        <source src="{$goodsDetail.goods_video}" type="video/mp4">
                                        <source src="{$goodsDetail.goods_video}" type="video/webm">
                                        您的浏览器不支持视频播放。
                                    </video>
                                    {/if}
                                </div>
                            </div>

                            <!-- Thumbnails -->
                            <div class="flex gap-2 overflow-x-auto">
                                {if condition="$goodsDetail.goods_video"}
                                <div class="thumbnail cursor-pointer w-16 h-16 rounded border-2 border-transparent hover:border-pink-500 relative"
                                     data-type="video"
                                     data-src="{$goodsDetail.goods_video}">
                                    <img src="{$goodsDetail.goods_img}" class="w-full h-full object-cover" alt="Video thumbnail">
                                    <div class="absolute inset-0 bg-black/50 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                {/if}

                                {if condition="$goodsDetail.goods_img"}
                                <div class="thumbnail cursor-pointer w-16 h-16 rounded border-2 border-pink-500 hover:border-pink-500"
                                     data-type="image"
                                     data-src="{$goodsDetail.goods_img}">
                                    <img src="{$goodsDetail.goods_img}" class="w-full h-full object-cover" alt="Image 1">
                                </div>
                                {/if}

                                {if condition="$goodsDetail.goods_img2"}
                                <div class="thumbnail cursor-pointer w-16 h-16 rounded border-2 border-transparent hover:border-pink-500"
                                     data-type="image"
                                     data-src="{$goodsDetail.goods_img2}">
                                    <img src="{$goodsDetail.goods_img2}" class="w-full h-full object-cover" alt="Image 2">
                                </div>
                                {/if}

                                {if condition="$goodsDetail.goods_img3"}
                                <div class="thumbnail cursor-pointer w-16 h-16 rounded border-2 border-transparent hover:border-pink-500"
                                     data-type="image"
                                     data-src="{$goodsDetail.goods_img3}">
                                    <img src="{$goodsDetail.goods_img3}" class="w-full h-full object-cover" alt="Image 3">
                                </div>
                                {/if}

                                {if condition="$goodsDetail.goods_img4"}
                                <div class="thumbnail cursor-pointer w-16 h-16 rounded border-2 border-transparent hover:border-pink-500"
                                     data-type="image"
                                     data-src="{$goodsDetail.goods_img4}">
                                    <img src="{$goodsDetail.goods_img4}" class="w-full h-full object-cover" alt="Image 4">
                                </div>
                                {/if}
                            </div>
                        </div>
                        <!-- Game Label -->
                        <div class="bg-gray-100 mt-3 p-2 rounded flex items-center justify-center w-fit">
                            <span class="text-gray-600 text-sm">{$goodsDetail.goods_name}</span>
                        </div>

                        <!-- Game Introduction Section -->
                        <!-- <div class="mt-6 bg-gray-100 p-4 rounded w-full">
                            <h2 class="text-lg font-medium text-gray-800">游戏介绍</h2>
                            <div class="mt-2 text-gray-600 rich-text-content">
                                {$goodsDetail.goods_des|raw}
                            </div>
                        </div> -->
                    </div>

                    <!-- Right Section -->
                    <div class="flex-1 bg-white rounded shadow p-6">
                        <div>
                            <h1 class="text-lg font-medium text-gray-800">{$goodsDetail.goods_name}</h1>
                        </div>

                        <!-- Store Section -->
                        <div class="mt-4 flex items-center justify-between">

                        </div>
                        <!-- 会员提示区域 -->
                <div class="relative bg-gradient-to-r from-pink-50 to-pink-100 border-2 border-pink-200 p-4 rounded-lg mb-4 shadow-md transform hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <!-- VIP图标 -->
                            <svg class="w-8 h-8 text-yellow-400 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                            <div>
                                <span class="text-pink-600 font-medium">
                                    <span class="mr-2">🎉</span>
                                    永久会员特权，永久免费使用租号猫所有功能！
                                    <span class="inline-block animate-bounce ml-1">⭐</span>
                                </span>
                            </div>
                        </div>
                        <button id="openMembershipModal" class="relative group">
                            <span class="absolute -inset-0.5 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg blur opacity-75 group-hover:opacity-100 transition duration-200"></span>
                            <span class="relative inline-block px-4 py-2 bg-pink-500 text-white rounded-lg font-medium hover:bg-pink-600 transform hover:scale-105 transition-all duration-200">
                                去开通
                            </span>
                        </button>
                    </div>
                    <!-- 右上角装饰 -->
                    <div class="absolute -top-2 -right-2">
                        <span class="flex h-4 w-4">
                            <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-pink-400 opacity-75"></span>
                            <span class="relative inline-flex rounded-full h-4 w-4 bg-pink-500"></span>
                        </span>
                    </div>
                </div>

                        <!-- Pricing Section -->
                        <div class="grid grid-cols-4 gap-4 mt-6">
                            {if condition="$accountStatus.online"}
                                {if condition="$combo"}
                                <div class="text-center border-2 border-pink-500 rounded p-4">
                                    <p class="text-gray-500 text-sm">时租</p>
                                    <p class="text-pink-500 font-bold text-lg mt-2">¥{$combo.hour_price}</p>
                                </div>
                                <div class="text-center border rounded p-4">
                                    <p class="text-gray-500 text-sm">日租(24小时)</p>
                                    <p class="text-gray-800 font-bold text-lg mt-2">¥{$combo.day_price}</p>
                                </div>
                                <!-- 永久版选项 -->
                                <div class="text-center border rounded p-4">
                                    <p class="text-gray-500 text-sm">永久版</p>
                                    <p class="text-gray-800 font-bold text-lg mt-2">¥{$goodsDetail.permanent_price2}</p>
                                </div>
                                {else}
                                <div class="text-center border-2 border-gray-300 rounded p-4 col-span-4">
                                    <p class="text-gray-500 text-sm">暂无套餐</p>
                                    <p class="text-gray-800 font-bold text-lg mt-2">未设置价格</p>
                                </div>
                                {/if}
                            {/if}
                            <!-- 离线版选项 -->
                            {if condition="$accountStatus.offline"}
                            <div class="text-center border rounded p-4">
                                <p class="text-gray-500 text-sm">离线版</p>
                                <p class="text-gray-800 font-bold text-lg mt-2">¥{$offlinePrice}</p>
                            </div>
                            {/if}
                        </div>

                        <!-- Additional Info -->
                        <div class="mt-6 text-sm text-gray-600">
                            <p>租用规则: 一小时起租，租用时长内独享账号，租用到期后游戏账号可能被顶下线！ </p>
                        </div>

<!--                        <div class="mt-6 text-sm text-gray-600">
                            <p>租用规则:一小时起租，租用时长内独享账号，租用到期后游戏账号可能被顶下线！ <br><br>
                                <span style="color: red; font-weight: bold;">Tips: 请勿浏览或使用外挂辅助等扰乱游戏的工具，发现使用永久封禁您在我站的所有游戏！</span></p>
                        </div>-->

                        <!-- Action Buttons -->
                        <div class="mt-6 flex justify-end gap-4">
                            <!-- Timer Component -->
                            <div id="timerComponent" class="flex items-center gap-2" style="display: {$accountStatus.online ? 'flex' : 'none'}">
                                <button class="px-3 py-1 border border-gray-300 text-gray-500 rounded">1小时</button>
                                <button class="px-3 py-1 border border-gray-300 text-gray-500 rounded">2小时</button>
                                <button class="px-3 py-1 border border-gray-300 text-gray-500 rounded">3小时</button>
                                <button class="px-3 py-1 border border-gray-300 text-gray-500 rounded">4小时</button>
                                <button class="px-3 py-1 border border-gray-300 text-gray-500 rounded">5小时</button>
                                <div class="flex items-center">
                                    <button id="decrement" class="px-3 py-1 bg-gray-300 text-gray-600 rounded">-</button>
                                    <span id="timerValue" class="px-3">7</span>
                                    <button id="increment" class="px-3 py-1 bg-gray-300 text-gray-600 rounded">+</button>
                                </div>
                            </div>
                            <!-- Rent Now Button -->
                            <button class="bg-pink-500 text-white px-10 py-5 rounded font-medium hover:bg-pink-600">
                                立即租用
                            </button>
                        </div>

                        <!-- Tips -->
                        <div class="mt-6 text-xs text-yellow-500">
                            <p>Tips: 请勿浏览或使用外挂辅助等扰乱游戏的工具，发现使用永久封禁您在我站的所有游戏！</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加游戏介绍区域 -->
            <div class="game-introduction-section" style="margin: 30px auto; max-width: 1200px;">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <!-- 标题区域 -->
                    <div class="border-b border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-800">游戏介绍</h2>
                    </div>

                    <!-- 内容区域 -->
                    <div class="p-6">
                        <div class="rich-text-content prose max-w-none">
                            {$goodsDetail.goods_des|raw}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加帮助中心部分 -->
            <section class="help-section">
                <div class="container">
                    <header class="section-header">
                        <div class="title-group">
                            <h2 class="section-title">帮助中心</h2>
                            <p class="section-subtitle">解决您的问题，提供专业支持</p>
                        </div>
                    </header>

                    <div class="help-grid">
                        {volist name="announcementList" id="announcement" offset="0" length="4"}
                        <article class="help-card">
                            <span class="card-indicator"></span>
                            <h3 class="help-title">{$announcement.not_title}</h3>
                            <div class="help-content rich-text-container">
                                {$announcement.not_content|raw}
                            </div>
                            <button type="button" class="expand-btn" aria-label="展开内容"></button>
                            <footer class="help-meta">
                                <time datetime="{$announcement.time}">{$announcement.time}</time>
                            </footer>
                        </article>
                        {/volist}
                    </div>
                </div>
            </section>

            <!-- 添加备案号 -->
            <div style="text-align: center; padding: 20px; color: #141313;">
                备案号：蜀ICP备2024116123
            </div>
        </div>
    </div>

    <!-- 会员购买拟态框 -->
    <div id="membershipModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl overflow-hidden transform transition-all">
            <!-- 模态框头部 -->
            <div class="relative bg-gradient-to-r from-pink-500 to-purple-600 px-6 py-4">
                <h3 class="text-xl font-bold text-white">会员特权开通</h3>
                <button id="closeMembershipModal" class="absolute top-4 right-4 text-white hover:text-gray-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6">
                <div class="text-center mb-6">
                    <p class="text-gray-600">选择适合您的会员方案，享受更多特权</p>
                </div>

                <!-- 会员类型选择 -->
                <div class="flex mb-6">
                    <button id="durationMemberBtn" class="flex-1 py-2 px-4 bg-pink-500 text-white font-medium rounded-l focus:outline-none">
                        时长会员
                    </button>
                    <button id="vipMemberBtn" class="flex-1 py-2 px-4 bg-gray-200 text-gray-700 font-medium rounded-r focus:outline-none">
                        至尊会员
                    </button>
                </div>

                <!-- 时长会员选项 -->
                <div id="durationMemberOptions" class="grid grid-cols-3 gap-4 mb-6">
                    {foreach $membershipPricing as $membership}
                        {if condition="$membership.membership_type != '至尊会员'"}
                            <div class="border {if condition="$membership.membership_type == '年费会员'"}border-pink-500 bg-pink-50{else}border-gray-200{/if} rounded-lg p-4 text-center cursor-pointer hover:border-pink-500 duration-member-option" data-duration="{$membership.validity_period}" data-price="{$membership.price}">
                                <h4 class="font-medium text-gray-800">{$membership.membership_type}</h4>
                                <p class="text-pink-500 text-xl font-bold mt-2">¥{$membership.price}</p>
                                <p class="text-gray-500 text-sm mt-1">有效期{$membership.validity_period}天</p>
                            </div>
                        {/if}
                    {/foreach}
                </div>

                <!-- 至尊会员选项 -->
                <div id="vipMemberOptions" class="hidden mb-6">
                    {foreach $membershipPricing as $membership}
                        {if condition="$membership.membership_type == '至尊会员'"}
                            <div class="border-2 border-purple-600 rounded-lg p-6 text-center bg-gradient-to-b from-purple-50 to-white relative overflow-hidden">
                                <h4 class="font-bold text-purple-800 text-xl mb-2">{$membership.membership_type}</h4>
                                <p class="text-purple-600 text-3xl font-bold mt-4">¥{$membership.price}</p>
                                <p class="text-gray-600 mt-4">一次付费，终身享受</p>

                                <div class="mt-6 bg-white p-4 rounded-lg shadow-sm">
                                    <h5 class="font-medium text-gray-800 mb-2">专属特权</h5>
                                    <ul class="text-left text-gray-600 space-y-2">
                                        <li class="flex items-start">
                                            <svg class="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>所有游戏账号无限制使用</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>专属客服7×24小时服务</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>新游戏优先体验权</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>平台所有功能永久免费使用</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        {/if}
                    {/foreach}
                </div>

                <!-- 会员特权说明 -->
                <div class="bg-gray-50 p-4 rounded-lg mb-6">
                    <h4 class="font-medium text-gray-800 mb-2">会员特权</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-start">
                            <div class="bg-pink-100 p-2 rounded-full mr-3">
                                <svg class="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">租号时长翻倍</h5>
                                <p class="text-gray-500 text-sm">同等价格，使用时间延长一倍</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-pink-100 p-2 rounded-full mr-3">
                                <svg class="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">专属优惠折扣</h5>
                                <p class="text-gray-500 text-sm">所有游戏账号享受8折优惠</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-pink-100 p-2 rounded-full mr-3">
                                <svg class="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">账号安全保障</h5>
                                <p class="text-gray-500 text-sm">专属安全保障，无需担心账号问题</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-pink-100 p-2 rounded-full mr-3">
                                <svg class="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">优先使用权</h5>
                                <p class="text-gray-500 text-sm">热门游戏账号优先使用权</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 购买按钮 -->
                <div class="text-center">
                    <button id="purchaseMembership" class="bg-pink-500 hover:bg-pink-600 text-white font-medium py-3 px-8 rounded-lg shadow-md transition duration-300 transform hover:scale-105">
                        立即开通
                    </button>
                    <p class="text-gray-500 text-xs mt-2">点击立即开通，表示您同意<a href="#" class="text-pink-500">《会员服务协议》</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建提示框函数
        function createAlert(color, message, time) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed top-4 right-4 z-50 p-4 rounded shadow-lg ${color === 'red' ? 'bg-red-500' : 'bg-green-500'} text-white`;
            alertDiv.textContent = message;

            document.body.appendChild(alertDiv);

            if (time !== 0) {
                setTimeout(() => {
                    alertDiv.remove();
                }, time || 3000);
            } else {
                const closeBtn = document.createElement('button');
                closeBtn.className = 'ml-4 text-white';
                closeBtn.innerHTML = '&times;';
                closeBtn.addEventListener('click', () => alertDiv.remove());
                alertDiv.appendChild(closeBtn);
            }
        }

        // 创建登录提示模态框函数
        function showLoginModal() {
            // 创建模态框容器
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            modal.id = 'loginPromptModal';

            // 模态框内容
            modal.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl w-full max-w-md p-6 transform transition-all">
                    <div class="text-center mb-4">
                        <svg class="w-12 h-12 text-yellow-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mt-2">您尚未登录</h3>
                        <p class="text-gray-500 mt-1">请先登录后再进行此操作</p>
                    </div>
                    <div class="flex justify-center space-x-4">
                        <button id="closeLoginModal" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                            关闭
                        </button>
                        <button id="goToLogin" class="px-4 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors">
                            前往登录
                        </button>
                    </div>
                </div>
            `;

            // 添加到body
            document.body.appendChild(modal);

            // 添加关闭事件
            document.getElementById('closeLoginModal').addEventListener('click', function() {
                document.getElementById('loginPromptModal').remove();
            });

            // 添加跳转事件
            document.getElementById('goToLogin').addEventListener('click', function() {
                window.location.href = '/user/login';
            });
        }

        // ✅ 登录状态检查函数 - 与后端逻辑保持一致
        function checkLogin() {
            function getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                return null;
            }

            const userId = getCookie("id");
            const username = getCookie("username");
            const loginStatus = getCookie("login");

            // 与后端逻辑完全一致：三个Cookie都必须存在且login为'1'
            return (userId && username && loginStatus === '1');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 获取所有套餐选项并设置套餐选择功能
            const packageOptions = document.querySelectorAll('.grid.grid-cols-4.gap-4.mt-6 > div');
            let selectedPackage = packageOptions.length > 0 ? packageOptions[0] : null; // 默认选中第一个套餐

            // 为套餐选项添加点击事件，并处理计时器显示/隐藏
            packageOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除所有套餐的选中样式
                    packageOptions.forEach(p => {
                        p.classList.remove('border-pink-500');
                        p.classList.add('border-gray-300');
                        if (p.classList.contains('border-2')) {
                            p.classList.remove('border-2');
                            p.classList.add('border');
                        }
                    });

                    // 添加当前套餐的选中样式
                    this.classList.remove('border-gray-300');
                    this.classList.add('border-2', 'border-pink-500');
                    selectedPackage = this;

                    // 根据套餐类型显示或隐藏计时器
                    const packageText = this.querySelector('p').textContent;
                    const timerComponent = document.getElementById('timerComponent');

                    if (packageText.includes('永久版') || packageText.includes('离线版')) {
                        // 对于永久版和离线版，隐藏计时器
                        if (timerComponent) timerComponent.style.display = 'none';
                    } else {
                        // 对于时租和日租，显示计时器
                        if (timerComponent) timerComponent.style.display = 'flex';
                    }

                    console.log('套餐已选择:', packageText);
                });
            });

            // 添加定时器功能
            const timerButtons = document.querySelectorAll('#timerComponent button');
            const timerValueElement = document.getElementById('timerValue');
            const incrementBtn = document.getElementById('increment');
            const decrementBtn = document.getElementById('decrement');

            // 设置默认时长按钮点击效果
            timerButtons.forEach((btn, index) => {
                if (index < 5) { // 只处理前5个按钮（1-5小时）
                    btn.addEventListener('click', function() {
                        timerValueElement.textContent = (index + 1).toString();
                    });
                }
            });

            // 增加时长按钮
            if (incrementBtn) {
                incrementBtn.addEventListener('click', function() {
                    let value = parseInt(timerValueElement.textContent);
                    if (!isNaN(value) && value < 24) {
                        timerValueElement.textContent = (value + 1).toString();
                    }
                });
            }

            // 减少时长按钮
            if (decrementBtn) {
                decrementBtn.addEventListener('click', function() {
                    let value = parseInt(timerValueElement.textContent);
                    if (!isNaN(value) && value > 1) {
                        timerValueElement.textContent = (value - 1).toString();
                    }
                });
            }

            // 修改会员模态框打开事件
            const openMembershipModal = document.getElementById('openMembershipModal');
            const membershipModal = document.getElementById('membershipModal');
            if(openMembershipModal && membershipModal) {
                // 移除原有的事件监听器
                const newOpenMembershipModal = openMembershipModal.cloneNode(true);
                openMembershipModal.parentNode.replaceChild(newOpenMembershipModal, openMembershipModal);

                newOpenMembershipModal.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    if(!checkLogin()) {
                        showLoginModal();
                        return;
                    }

                    membershipModal.classList.remove('hidden');
                    document.body.classList.add('modal-open');
                    document.body.style.overflow = 'hidden';
                });
            }

            // 设置会员相关变量和功能
            const durationMemberBtn = document.getElementById('durationMemberBtn');
            const vipMemberBtn = document.getElementById('vipMemberBtn');
            const durationMemberOptions = document.getElementById('durationMemberOptions');
            const vipMemberOptions = document.getElementById('vipMemberOptions');
            const purchaseMembership = document.getElementById('purchaseMembership');

            // 当前选择的会员类型和选项
            let currentMemberType = 'duration'; // 'duration' 或 'vip'
            let selectedDurationOption = null;
            let selectedPrice = 0;

            // 初始化时长会员选项
            const durationOptions = document.querySelectorAll('.duration-member-option');
            if (durationOptions.length > 0) {
                // 默认选中年度会员或第一个选项
                let defaultOption = durationOptions[2] || durationOptions[0];
                defaultOption.classList.add('selected');
                selectedDurationOption = defaultOption;
                selectedPrice = parseFloat(defaultOption.getAttribute('data-price') || 0);
            }

            // 切换会员类型
            if(durationMemberBtn) {
                durationMemberBtn.addEventListener('click', function() {
                    currentMemberType = 'duration';
                    durationMemberBtn.classList.remove('bg-gray-200', 'text-gray-700');
                    durationMemberBtn.classList.add('bg-pink-500', 'text-white');
                    if(vipMemberBtn) {
                        vipMemberBtn.classList.remove('bg-pink-500', 'text-white');
                        vipMemberBtn.classList.add('bg-gray-200', 'text-gray-700');
                    }
                    if(durationMemberOptions) durationMemberOptions.classList.remove('hidden');
                    if(vipMemberOptions) vipMemberOptions.classList.add('hidden');
                    selectedPrice = getSelectedDurationPrice();
                });
            }

            if(vipMemberBtn) {
                vipMemberBtn.addEventListener('click', function() {
                    currentMemberType = 'vip';
                    vipMemberBtn.classList.remove('bg-gray-200', 'text-gray-700');
                    vipMemberBtn.classList.add('bg-pink-500', 'text-white');
                    if(durationMemberBtn) {
                        durationMemberBtn.classList.remove('bg-pink-500', 'text-white');
                        durationMemberBtn.classList.add('bg-gray-200', 'text-gray-700');
                    }
                    if(vipMemberOptions) vipMemberOptions.classList.remove('hidden');
                    if(durationMemberOptions) durationMemberOptions.classList.add('hidden');

                    // 从数据属性获取至尊会员价格
                    const vipOption = document.querySelector('#vipMemberOptions .border-purple-600');
                    if (vipOption) {
                        const priceElement = vipOption.querySelector('.text-purple-600');
                        if (priceElement) {
                            const priceText = priceElement.textContent;
                            selectedPrice = parseFloat(priceText.replace('¥', '')) || 0;
                        }
                    }
                });
            }

            // 选择时长会员选项
            durationOptions.forEach(option => {
                option.addEventListener('click', function() {
                    durationOptions.forEach(opt => {
                        opt.classList.remove('border-pink-500', 'bg-pink-50');
                        opt.classList.add('border-gray-200');
                    });
                    this.classList.remove('border-gray-200');
                    this.classList.add('border-pink-500', 'bg-pink-50');
                    selectedDurationOption = this;
                    selectedPrice = parseFloat(this.getAttribute('data-price') || 0);
                });
            });

            // 获取选中的时长会员价格
            function getSelectedDurationPrice() {
                if (!selectedDurationOption) {
                    // 默认选中第一个选项
                    const firstOption = document.querySelector('.duration-member-option');
                    if (firstOption) {
                        return parseFloat(firstOption.getAttribute('data-price') || 0);
                    }
                    return 0;
                }

                return parseFloat(selectedDurationOption.getAttribute('data-price') || 0);
            }

            // 修改购买会员按钮事件
            if(purchaseMembership) {
                // 移除原有的事件监听器
                const newPurchaseMembership = purchaseMembership.cloneNode(true);
                purchaseMembership.parentNode.replaceChild(newPurchaseMembership, purchaseMembership);

                newPurchaseMembership.addEventListener('click', function() {
                    if(!checkLogin()) {
                        membershipModal.classList.add('hidden');
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        showLoginModal();
                        return;
                    }

                    // 会员购买逻辑
                    const memberType = currentMemberType;
                    const price = selectedPrice;

                    let memberName = '';
                    let duration = 0;

                    if (memberType === 'duration') {
                        if (selectedDurationOption) {
                            const titleElement = selectedDurationOption.querySelector('h4');
                            memberName = titleElement ? titleElement.textContent : '会员';

                            if (memberName.includes('月')) {
                                duration = 30;
                            } else if (memberName.includes('季')) {
                                duration = 90;
                            } else if (memberName.includes('年')) {
                                duration = 365;
                            }
                        }
                    } else {
                        memberName = '至尊会员';
                        duration = -1; // 表示永久
                    }

                    console.log(`购买会员: ${memberName}, 价格: ${price}, 时长: ${duration}天`);

                    // 发送AJAX请求
                    $.ajax({
                        url: "/user/index/purchaseMembership",
                        type: "post",
                        dataType: "json",
                        data: {
                            memberName: encodeURIComponent(memberName),
                            ord_type: "member"
                        },
                        success: function(res) {
                            if (res.code === 0 && res.src) {
                                // 显示成功提示
                                createAlert("green", "订单创建成功，3秒后跳转...", 3000);
                                // 3秒后跳转
                                setTimeout(function() {
                                    window.location.href = res.src;
                                }, 3000);
                            } else {
                                // 显示错误提示
                                createAlert("red", res.msg || "请联系管理员", 0);
                            }
                        },
                        error: function() {
                            createAlert("red", "网络错误，请重试", 0);
                        }
                    });
                });
            }

            // 修改立即租用按钮点击事件
            const rentButton = document.querySelector('.bg-pink-500.text-white.px-10.py-5');
            if(rentButton) {
                // 移除原有的事件监听器
                const newRentButton = rentButton.cloneNode(true);
                rentButton.parentNode.replaceChild(newRentButton, rentButton);

                newRentButton.addEventListener('click', function() {
                    if(!checkLogin()) {
                        showLoginModal();
                        return;
                    }

                    // 获取选中的套餐
                    const packageText = selectedPackage ? selectedPackage.querySelector('p').textContent : '未选择套餐';

                    // 获取时长
                    const timerValue = timerValueElement ? timerValueElement.textContent : '未选择时间';

                    console.log(`立即租用按钮被点击，选择的套餐: ${packageText}，计时器数值: ${timerValue}`);

                    // 判断套餐类型并发送AJAX请求
                    if (packageText.includes('时租')) {
                        const goodsId = '{$goodsDetail.id}';
                        const duration = timerValue;
                        const unit = 'hour';

                        $.ajax({
                            url: "/user/index/order",
                            type: "post",
                            dataType: "json",
                            data: {
                                goodsId: goodsId,
                                duration: duration,
                                unit: unit,
                                yhjId: null
                            },
                            success: function(res) {
                                if (res.src) {
                                    window.location.href = res.src;
                                } else {
                                    createAlert("red", res.msg || "购买失败，请重试", 0);
                                }
                            },
                            error: function() {
                                createAlert("red", "网络错误，请重试", 0);
                            }
                        });
                    } else if (packageText.includes('日租')) {
                        const goodsId = '{$goodsDetail.id}';
                        const duration = timerValue;
                        const unit = 'day';

                        $.ajax({
                            url: "/user/index/order",
                            type: "post",
                            dataType: "json",
                            data: {
                                goodsId: goodsId,
                                duration: duration,
                                unit: unit,
                                yhjId: null
                            },
                            success: function(res) {
                                if (res.src) {
                                    window.location.href = res.src;
                                } else {
                                    createAlert("red", res.msg || "购买失败，请重试", 0);
                                }
                            },
                            error: function() {
                                createAlert("red", "网络错误，请重试", 0);
                            }
                        });
                    } else if (packageText.includes('离线版')) {
                        const goodsId = '{$goodsDetail.id}';
                        const goodsName = '{$goodsDetail.goods_name} - 离线版';

                        $.ajax({
                            url: "/user/index/offlineOrder",
                            type: "post",
                            dataType: "json",
                            data: {
                                goodsId: goodsId,
                                goods_name: goodsName
                            },
                            success: function(res) {
                                if (res.src) {
                                    window.location.href = res.src;
                                } else {
                                    createAlert("red", res.msg || "购买失败，请重试", 0);
                                }
                            },
                            error: function() {
                                createAlert("red", "网络错误，请重试", 0);
                            }
                        });
                    } else if (packageText.includes('永久版')) {
                        const goodsId = '{$goodsDetail.id}';
                        const goodsName = '{$goodsDetail.goods_name} - 永久版';

                        $.ajax({
                            url: "/user/index/createGamePermanentOrder",
                            type: "post",
                            dataType: "json",
                            data: {
                                goodsId: goodsId,
                                goods_name: goodsName
                            },
                            success: function(res) {
                                if (res.src) {
                                    window.location.href = res.src;
                                } else {
                                    createAlert("red", res.msg || "购买失败，请重试", 0);
                                }
                            },
                            error: function() {
                                createAlert("red", "网络错误，请重试", 0);
                            }
                        });
                    } else {
                        createAlert("red", "请先选择套餐", 0);
                    }
                });
            }
        });

        // 添加缩略图点击功能
        document.addEventListener('DOMContentLoaded', function() {
            // 获取所有缩略图
            const thumbnails = document.querySelectorAll('.thumbnail');
            // 获取预览容器
            const previewImage = document.getElementById('previewImage');
            const previewVideo = document.getElementById('previewVideo');
            const mediaContainer = document.getElementById('mediaContainer');

            // 检查是否有视频
            const hasVideo = document.querySelector('.thumbnail[data-type="video"]') !== null;

            // 如果有视频，默认自动播放视频
            if(hasVideo && previewVideo) {
                // 获取视频缩略图
                const videoThumb = document.querySelector('.thumbnail[data-type="video"]');
                const videoSrc = videoThumb.getAttribute('data-src');

                // 设置视频源并播放
                previewVideo.src = videoSrc;
                previewVideo.classList.remove('hidden');
                previewImage.classList.add('hidden');

                // 为视频添加自动播放属性
                previewVideo.setAttribute('autoplay', '');
                previewVideo.muted = true; // 大多数浏览器要求自动播放的视频必须静音
                previewVideo.play().catch(e => console.log('自动播放失败:', e));

                // 更新缩略图选中状态
                thumbnails.forEach(t => {
                    t.classList.remove('border-pink-500');
                    t.classList.add('border-transparent');
                });
                videoThumb.classList.remove('border-transparent');
                videoThumb.classList.add('border-pink-500');
            }

            // 为每个缩略图添加点击事件
            thumbnails.forEach(thumbnail => {
                thumbnail.addEventListener('click', function() {
                    // 移除所有缩略图的选中样式
                    thumbnails.forEach(t => {
                        t.classList.remove('border-pink-500');
                        t.classList.add('border-transparent');
                    });

                    // 添加当前缩略图的选中样式
                    this.classList.remove('border-transparent');
                    this.classList.add('border-pink-500');

                    // 获取数据类型和源
                    const type = this.getAttribute('data-type');
                    const src = this.getAttribute('data-src');

                    // 根据类型切换显示内容
                    if(type === 'image') {
                        previewImage.src = src;
                        previewImage.classList.remove('hidden');
                        if(previewVideo) {
                            previewVideo.classList.add('hidden');
                            previewVideo.pause();
                        }
                    } else if(type === 'video' && previewVideo) {
                        previewVideo.src = src;
                        previewVideo.classList.remove('hidden');
                        previewImage.classList.add('hidden');
                        previewVideo.play().catch(e => console.log('播放失败:', e));
                    }
                });
            });
        });

        // 处理会员模态框关闭按钮
        const closeMembershipModal = document.getElementById('closeMembershipModal');
        const membershipModal = document.getElementById('membershipModal');

        if(closeMembershipModal && membershipModal) {
            // 移除原有的事件监听器(如果有)
            const newCloseMembershipModal = closeMembershipModal.cloneNode(true);
            closeMembershipModal.parentNode.replaceChild(newCloseMembershipModal, closeMembershipModal);

            // 添加新的事件监听器
            newCloseMembershipModal.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                membershipModal.classList.add('hidden');
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
            });

            // 点击模态框外部关闭
            membershipModal.addEventListener('click', function(e) {
                if(e.target === membershipModal) {
                    membershipModal.classList.add('hidden');
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                }
            });

            // 添加ESC键关闭
            document.addEventListener('keydown', function(e) {
                if(e.key === 'Escape' && !membershipModal.classList.contains('hidden')) {
                    membershipModal.classList.add('hidden');
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                }
            });
        }

        // 修复登录状态判断
        function updateLoginDisplay() {
            // 检查登录状态
            const isLoggedIn = checkLogin();

            // 获取cookie中的用户信息
            const username = getCookie("username");
            const userId = getCookie("id");

            // 获取登录按钮和用户信息显示区域
            const loginButtonDesktop = document.querySelector('.hidden.md\\:flex.items-center a[href="/user/login"]');
            const userInfoDesktop = document.querySelector('.hidden.md\\:flex.items-center a[href="/user/index/onshop"]');

            // 获取移动端登录按钮和用户信息
            const mobileLoginSection = document.querySelector('#mobile-menu .pt-4.pb-3.border-t:not(:has(.ml-3))');
            const mobileUserSection = document.querySelector('#mobile-menu .pt-4.pb-3.border-t:has(.ml-3)');

            if (isLoggedIn && username && userId) {
                // 用户已登录，显示用户信息，隐藏登录按钮
                if (loginButtonDesktop && userInfoDesktop) {
                    loginButtonDesktop.parentElement.classList.add('hidden');
                    userInfoDesktop.parentElement.classList.remove('hidden');

                    // 更新用户名显示
                    const usernameElement = userInfoDesktop.querySelector('span:last-child');
                    if (usernameElement) {
                        usernameElement.textContent = username;
                    }
                }

                // 更新移动端显示
                if (mobileLoginSection && mobileUserSection) {
                    mobileLoginSection.classList.add('hidden');
                    mobileUserSection.classList.remove('hidden');

                    // 更新移动端用户名
                    const mobileUsernameElement = mobileUserSection.querySelector('.text-base.font-medium');
                    if (mobileUsernameElement) {
                        mobileUsernameElement.textContent = username;
                    }
                }
            } else {
                // 用户未登录，显示登录按钮，隐藏用户信息
                if (loginButtonDesktop && userInfoDesktop) {
                    loginButtonDesktop.parentElement.classList.remove('hidden');
                    userInfoDesktop.parentElement.classList.add('hidden');
                }

                // 更新移动端显示
                if (mobileLoginSection && mobileUserSection) {
                    mobileLoginSection.classList.remove('hidden');
                    mobileUserSection.classList.add('hidden');
                }
            }
        }

        // 获取cookie函数
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return decodeURIComponent(parts.pop().split(';').shift());
            return null;
        }

        // ✅ 统一的登录状态检查函数 - 与后端逻辑完全一致
        function checkLogin() {
            const userId = getCookie("id");
            const username = getCookie("username");
            const loginStatus = getCookie("login");

            // 与后端逻辑完全一致：三个Cookie都必须存在且login为'1'
            return (userId && username && loginStatus === '1');
        }

        // 页面加载时立即执行
        updateLoginDisplay();
    </script>


</html>
