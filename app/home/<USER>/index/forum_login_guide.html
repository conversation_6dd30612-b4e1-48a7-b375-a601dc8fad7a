<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>登录提示 - {$system.web_name}</title>
    <style>
        :root {
            --primary-color: #4a6cf7;
            --primary-dark: #3a56d4;
            --secondary-color: #00c9a7;
            --text-color: #1d2a4d;
            --light-text: #4a5568;
            --lighter-text: #718096;
            --background: #f8fafc;
            --card-bg: #fff;
            --border-color: #e2e8f0;
            --shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
            --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: var(--background);
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            touch-action: none;
            -webkit-overflow-scrolling: none;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNDQwIiBoZWlnaHQ9IjgxMCIgdmlld0JveD0iMCAwIDE0NDAgODEwIiBmaWxsPSJub25lIj48ZyBvcGFjaXR5PSIwLjA1Ij48cGF0aCBkPSJNLTc4LjU5NjUgODEwTDE0NDAuNDEgODEwTDE0NDAuNDEgLTU3Mi42MDRMLTc4LjU5NjUgLTU3Mi42MDRMLTc4LjU5NjUgODEwWiIgZmlsbD0idXJsKCNwYWludDBfbGluZWFyXzYyXzE4NSkiLz48L2c+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzYyXzE4NSIgeDE9IjY4MC45MDciIHkxPSI4MTAiIHgyPSI2ODAuOTA3IiB5Mj0iLTU3Mi42MDQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj48c3RvcCBzdG9wLWNvbG9yPSIjNEE2Q0Y3Ii8+PHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjMDBDOUE3Ii8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PC9zdmc+') no-repeat center center;
            background-size: cover;
            opacity: 0.8;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            flex: 1;
            width: 100%;
        }

        .header {
            display: none;
        }

        .main-content {
            padding: 80px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
            min-height: calc(100vh - 180px);
        }

        .login-guide-card {
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: var(--shadow);
            padding: 50px;
            width: 100%;
            max-width: 700px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transform: translateY(0);
            transition: transform 0.3s ease;
            margin: 0 auto;
        }

        .login-guide-card:hover {
            transform: translateY(-5px);
        }

        .login-guide-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: var(--gradient);
        }

        .guide-icon {
            width: 100px;
            height: 100px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, rgba(74, 108, 247, 0.1), rgba(0, 201, 167, 0.1));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .guide-icon::after {
            content: '';
            position: absolute;
            width: 90%;
            height: 90%;
            border-radius: 50%;
            border: 2px dashed rgba(74, 108, 247, 0.3);
            animation: spin 30s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .guide-icon svg {
            width: 50px;
            height: 50px;
            fill: var(--primary-color);
        }

        h1 {
            font-size: 28px;
            margin-bottom: 20px;
            color: var(--text-color);
            font-weight: 600;
        }

        .guide-description {
            color: var(--light-text);
            margin-bottom: 30px;
            font-size: 17px;
            line-height: 1.7;
            max-width: 80%;
            margin-left: auto;
            margin-right: auto;
        }

        .btn {
            display: inline-block;
            padding: 14px 32px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: var(--transition);
            margin: 0 10px;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
            z-index: -1;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(74, 108, 247, 0.3);
        }

        .btn-outline {
            background-color: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline:hover {
            background-color: rgba(74, 108, 247, 0.1);
            color: var(--primary-dark);
            box-shadow: 0 10px 20px rgba(74, 108, 247, 0.15);
        }

        .action-buttons {
            margin-top: 40px;
        }

        .footer {
            display: none;
        }

        .divider {
            margin: 40px auto;
            border: none;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--border-color), transparent);
            width: 80%;
        }

        .benefits {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 30px;
        }

        .benefit-item {
            flex: 1;
            min-width: 180px;
            max-width: 200px;
            padding: 25px 15px;
            text-align: center;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            transition: var(--transition);
            border: 1px solid rgba(226, 232, 240, 0.7);
        }

        .benefit-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.05);
            background-color: rgba(255, 255, 255, 0.9);
        }

        .benefit-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            background: linear-gradient(135deg, rgba(74, 108, 247, 0.1), rgba(0, 201, 167, 0.1));
            border-radius: 12px;
        }

        .benefit-icon svg {
            width: 30px;
            height: 30px;
            fill: var(--primary-color);
        }

        .benefit-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-color);
            font-size: 18px;
        }

        .benefit-desc {
            font-size: 14px;
            color: var(--light-text);
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .login-guide-card {
                padding: 40px 25px;
                margin: 0 15px;
            }
            
            .benefits {
                flex-direction: column;
                align-items: center;
            }
            
            .benefit-item {
                margin: 0;
                width: 100%;
                max-width: 280px;
            }

            .guide-description {
                max-width: 100%;
            }

            .main-content {
                padding: 50px 0;
            }

            .action-buttons {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .btn {
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container header-content">
            <a href="{:url('home/index/index')}" class="logo">
                {if condition="!empty($system.web_logo)"}
                    <img src="{$system.web_logo|get_file_url}" alt="{$system.web_name}">
                {else}
                    {$system.web_name}
                {/if}
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="login-guide-card">
                <div class="guide-icon">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/>
                    </svg>
                </div>
                
                <h1>需要登录后才能访问论坛内容</h1>
                
                <p class="guide-description">
                    您正在尝试访问需要登录的论坛内容。登录后即可查看完整的论坛帖子、参与讨论并获取更多专属功能。
                </p>
                
                <div class="benefits">
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                            </svg>
                        </div>
                        <div class="benefit-title">参与讨论</div>
                        <div class="benefit-desc">发表评论，分享您的观点</div>
                    </div>
                    
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                            </svg>
                        </div>
                        <div class="benefit-title">收藏内容</div>
                        <div class="benefit-desc">保存喜欢的帖子随时查看</div>
                    </div>
                    
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                            </svg>
                        </div>
                        <div class="benefit-title">个人中心</div>
                        <div class="benefit-desc">管理您的个人资料和内容</div>
                    </div>
                </div>
                
                <hr class="divider">
                
                <div class="action-buttons">
                    <a href="/user/login" class="btn">立即登录</a>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; {$Think.date.year} {$system.web_name} - {$system.web_copyright}</p>
        </div>
    </footer>

    <script>
        // 记录目标URL，登录成功后可以跳转回来
        const targetUrl = "{$targetUrl|default=''}";
        if (targetUrl) {
            // 将目标URL存储到本地存储中
            localStorage.setItem('forumRedirectUrl', targetUrl);
            
            // 修改登录按钮链接，添加回调URL参数
            const loginBtn = document.querySelector('.btn');
            if (loginBtn) {
                const currentHref = loginBtn.getAttribute('href');
                loginBtn.setAttribute('href', currentHref + '?redirect=' + encodeURIComponent(window.location.href));
            }
        }
    </script>
</body>
</html>