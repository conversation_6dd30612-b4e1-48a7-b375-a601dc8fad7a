<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑卡密账号</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
    <style>
        .layui-form {
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .layui-form-item:last-child {
            margin-bottom: 0;
            text-align: center;
        }
        .layui-input-block {
            margin-right: 30px;
        }
        .layui-textarea {
            min-height: 100px;
        }
    </style>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-card">
            <div class="layui-card-header"><i class="layui-icon layui-icon-edit"></i> 编辑卡密账号</div>
            <div class="layui-card-body">
                <form class="layui-form" lay-filter="accountForm">
                    <input type="hidden" name="id" value="{$account.id}">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">选择商品</label>
                        <div class="layui-input-block">
                            <select name="ac_goods" lay-verify="required" lay-search>
                                <option value="">请选择商品</option>
                                {foreach $goodsAll as $goods}
                                <option value="{$goods.id}" {if $goods.id eq $account.ac_goods}selected{/if}>{$goods.goods_name}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">账号类型</label>
                        <div class="layui-input-block">
                            <select name="goods_Type" lay-verify="required">
                                <option value="0" {if $account.goods_Type eq 0}selected{/if}>离线账号</option>
                                <option value="1" {if $account.goods_Type eq 1}selected{/if}>在线账号</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">账号</label>
                        <div class="layui-input-block">
                            <input type="text" name="ac_name" value="{$account.ac_name}" lay-verify="required" placeholder="请输入账号" class="layui-input" autocomplete="off">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">密码</label>
                        <div class="layui-input-block">
                            <input type="text" name="ac_password" value="{$account.ac_password}" lay-verify="required" placeholder="请输入密码" class="layui-input" autocomplete="off">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="ac_states" value="1" title="正常" {if $account.ac_states eq 1}checked{/if}>
                            <input type="radio" name="ac_states" value="0" title="不可用" {if $account.ac_states eq 0}checked{/if}>
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="remarks" placeholder="请输入备注信息" class="layui-textarea">{$account.remarks}</textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">到期时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="exit_time" id="exit_time" lay-verify="required" placeholder="请选择到期时间" class="layui-input" readonly>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button type="submit" class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">
                                <i class="layui-icon layui-icon-save"></i> 保存修改
                            </button>
                            <button type="reset" class="layui-btn layui-btn-primary">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
layui.use(['form', 'jquery', 'laydate'], function () {
    var form = layui.form,
        layer = layui.layer,
        $ = layui.jquery,
        laydate = layui.laydate;

    // 初始化日期时间选择器
    laydate.render({
        elem: '#exit_time',
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm',
        value: new Date({$account.exit_time * 1000}),
        done: function(value, date, endDate) {
            // 将选择的日期时间转换为时间戳（秒）
            $('#exit_time').data('timestamp', Math.floor(new Date(value).getTime() / 1000));
        }
    });

    // 监听提交
    form.on('submit(saveBtn)', function (data) {
        // 将 exit_time 的值替换为时间戳（秒）
        data.field.exit_time = $('#exit_time').data('timestamp') || Math.floor(new Date($('#exit_time').val()).getTime() / 1000);

        // 禁用提交按钮，防止重复提交
        var submitBtn = $(this).find('button[lay-submit]');
        submitBtn.addClass('layui-btn-disabled').prop('disabled', true);
        
        // 显示加载层
        var loadIndex = layer.load(1, {shade: [0.1,'#fff']});
        
        $.ajax({
            url: '/admin/base/updateAccount',
            type: 'POST',
            dataType: 'json',
            data: data.field,
            success: function(res) {
                if (res.code === 0) {
                    layer.msg('保存成功', {
                        icon: 1,
                        time: 1500
                    }, function() {
                        // 关闭当前弹窗并刷新父页面表格
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layui.table.reload('accountTable');
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(res.msg || '保存失败', {
                        icon: 2,
                        time: 2000
                    });
                }
            },
            error: function(xhr, textStatus, error) {
                layer.msg('网络错误，请稍后重试', {
                    icon: 2,
                    time: 2000
                });
                console.error('请求错误:', error);
            },
            complete: function() {
                // 关闭加载层
                layer.close(loadIndex);
                // 恢复提交按钮
                submitBtn.removeClass('layui-btn-disabled').prop('disabled', false);
            }
        });
        return false;
    });
});
</script>
</body>
</html> 