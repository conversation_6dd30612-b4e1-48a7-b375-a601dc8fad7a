

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
  <meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
        <!--<div class="layui-form-item">-->
        <!--  <div class="layui-inline">-->
        <!--    <label class="layui-form-label">账号</label>-->
        <!--    <div class="layui-input-inline">-->
        <!--      <input type="text" name="ac_name" placeholder="请输入账号" autocomplete="off" class="layui-input">-->
        <!--    </div>-->
        <!--  </div>-->
        <!--  <div class="layui-inline">-->
        <!--    <label class="layui-form-label">是否出售</label>-->
        <!--    <div class="layui-input-inline">-->
        <!--      <select name="ac_sell">-->
        <!--        <option value="">请选择标签</option>-->
        <!--            <option value="0">已出售</option>-->
        <!--            <option value="1">未出售</option>-->
        <!--      </select>-->
        <!--    </div>-->
        <!--  </div>-->
        <!--  <div class="layui-inline">-->
        <!--    <label class="layui-form-label">商品分类</label>-->
        <!--    <div class="layui-input-inline">-->
        <!--      <select name="ac_goods">-->
        <!--        <option value="">请选择标签</option>-->
        <!--      </select>-->
        <!--    </div>-->
        <!--  </div>-->
        <!--  <div class="layui-inline">-->
        <!--    <button class="layui-btn layuiadmin-btn-comm" data-type="reload" lay-submit lay-filter="LAY-app-contcomm-search">-->
        <!--      <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>-->
        <!--    </button>-->
        <!--  </div>-->
        <!--</div>-->
      </div>
      <div class="layui-card-body">
        <div style="padding-bottom: 10px;">
          <button class="layui-btn layuiadmin-btn-comm" data-type="batchdel">删除</button>
        </div>
        <table id="LAY-app-content-list2" lay-filter="LAY-app-content-list2"></table>  
        <script type="text/html" id="table-content-com">
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
        </script>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'promotionLogo', 'table'], function(){
    var $ = layui.$
    ,form = layui.form
    ,table = layui.table;
    
    
    //监听搜索
    form.on('submit(LAY-app-contcomm-search)', function(data){
      var field = data.field;
      field.search=true
    //   //执行重载
      table.reload('LAY-app-content-list2', {
        where: field,
      });
    });
    
    //点击事件
    var active = {
      batchdel: function(){
        var checkStatus = table.checkStatus('LAY-app-content-list2')
        ,checkData = checkStatus.data; //得到选中的数据

        if(checkData.length === 0){
          return layer.msg('请选择数据');
        }
      
        layer.confirm('确定删除吗？', function(index) {
            var arr=[]
            checkData.forEach(function(val,index){
                arr.push(val.id)
            })
		    layui.$.ajax({
                url: '/admin/promotion/delpromotionLogo',
                method: 'post',
                data: {id:arr},
                success: function(res){
                layer.msg('删除成功');
                }
            });
          table.reload('LAY-app-content-list2');
          layer.msg('已删除');
        });
      },
      add: function(){
            layer.open({
              type: 2
              ,title: '添加账号'
              ,content: '/admin/index/addAccount'
              ,maxmin: true
              ,area: ['550px', '550px']
              ,btn: ['确定', '取消']
              ,yes: function(index, layero){
                //点击确认触发 iframe 内容中的按钮提交
                var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
                submit.click();
              }
            }); 
          }
    }  

    $('.layui-btn.layuiadmin-btn-comm').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });
  });
  </script>
</body>
</html>

