<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>租号平台管理系统</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
  <style>
    body {
      background: #f5f7fa;
      overflow: hidden;
    }
    .login-container {
      display: flex;
      height: 100vh;
      width: 100vw;
    }
    .login-banner {
      flex: 1;
      background: linear-gradient(135deg, #3a78ff 0%, #6d5ce7 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      position: relative;
      overflow: hidden;
    }
    .login-banner::before {
      content: '';
      position: absolute;
      width: 120%;
      height: 120%;
      background: url('/layuiadmin/style/res/gaming-pattern.svg') repeat;
      opacity: 0.05;
      transform: rotate(10deg);
      animation: backgroundMove 40s linear infinite;
    }
    .login-banner h1 {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
      position: relative;
      z-index: 2;
    }
    .login-banner p {
      font-size: 1.2rem;
      max-width: 60%;
      text-align: center;
      margin-bottom: 2rem;
      line-height: 1.6;
      position: relative;
      z-index: 2;
    }
    .login-banner .features {
      display: flex;
      gap: 2rem;
      position: relative;
      z-index: 2;
    }
    .login-banner .feature {
      text-align: center;
      padding: 1rem;
    }
    .login-banner .feature i {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      display: block;
    }
    .login-form {
      width: 400px;
      padding: 3rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      background: #fff;
      box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
    }
    .login-form-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    .login-form-header h2 {
      font-size: 1.8rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 0.5rem;
    }
    .login-form-header p {
      color: #888;
      font-size: 0.9rem;
    }
    .layui-input {
      border-radius: 4px;
      padding: 12px 15px;
      height: auto;
      font-size: 0.95rem;
      border: 1px solid #e0e0e0;
      box-shadow: none;
      transition: all 0.3s;
    }
    .layui-input:focus {
      border-color: #3a78ff;
      box-shadow: 0 0 0 2px rgba(58, 120, 255, 0.2);
    }
    .layui-form-label {
      display: none;
    }
    .layui-form-item {
      margin-bottom: 20px;
      position: relative;
    }
    .login-icon {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #bbb;
      font-size: 1.2rem;
      z-index: 2;
    }
    .has-icon .layui-input {
      padding-left: 45px;
    }
    /* ✅ 已移除验证码相关样式 */
    /* .captcha-container {
      position: relative;
      display: flex;
      align-items: center;
    }
    .captcha-input {
      flex: 1;
    }
    .captcha-img {
      width: 120px;
      height: 42px;
      margin-left: 10px;
      border-radius: 4px;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
    }
    .captcha-img:hover {
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    } */
    .login-btn {
      height: 48px;
      font-size: 1.1rem;
      border-radius: 4px;
      font-weight: 500;
      background: linear-gradient(135deg, #3a78ff 0%, #6d5ce7 100%);
      margin-top: 1rem;
      box-shadow: 0 4px 12px rgba(106, 115, 235, 0.4);
      transition: all 0.3s;
    }
    .login-btn:hover {
      box-shadow: 0 6px 20px rgba(106, 115, 235, 0.6);
      transform: translateY(-1px);
    }
    .footer {
      text-align: center;
      margin-top: 3rem;
      color: #888;
      font-size: 0.85rem;
    }
    .footer a {
      color: #3a78ff;
      text-decoration: none;
    }
    .layui-form-checked[lay-skin=primary] i {
      border-color: #3a78ff !important;
      background-color: #3a78ff;
    }
    .layui-form-checkbox[lay-skin=primary]:hover i {
      border-color: #3a78ff;
    }
    .shake {
      animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
    }
    @keyframes shake {
      10%, 90% { transform: translate3d(-1px, 0, 0); }
      20%, 80% { transform: translate3d(2px, 0, 0); }
      30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
      40%, 60% { transform: translate3d(3px, 0, 0); }
    }
    @keyframes backgroundMove {
      0% { transform: rotate(10deg) translateY(0); }
      50% { transform: rotate(5deg) translateY(-10px); }
      100% { transform: rotate(10deg) translateY(0); }
    }
    @media (max-width: 992px) {
      .login-banner {
        display: none;
      }
      .login-form {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-banner">
      <h1>租号平台管理系统</h1>
      <p>高效管理您的游戏账号租赁服务，提供全面的数据分析、用户管理和订单追踪</p>
      <div class="features">
        <div class="feature">
          <i class="layui-icon layui-icon-chart"></i>
          <span>数据分析</span>
        </div>
        <div class="feature">
          <i class="layui-icon layui-icon-user"></i>
          <span>用户管理</span>
        </div>
        <div class="feature">
          <i class="layui-icon layui-icon-template-1"></i>
          <span>订单追踪</span>
        </div>
      </div>
    </div>
    <div class="login-form">
      <div class="login-form-header">
        <h2>管理员登录</h2>
        <p>请输入您的账号和密码进行登录</p>
      </div>
      <form class="layui-form" action="">
        <div class="layui-form-item has-icon">
          <i class="layui-icon layui-icon-username login-icon"></i>
          <input type="text" name="username" required lay-verify="required" placeholder="请输入用户名" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-form-item has-icon">
          <i class="layui-icon layui-icon-password login-icon"></i>
          <input type="password" name="password" required lay-verify="required" placeholder="请输入密码" autocomplete="off" class="layui-input">
        </div>
        <!-- ✅ 已移除验证码输入框 -->
        <!-- <div class="layui-form-item">
          <div class="captcha-container">
            <div class="captcha-input has-icon">
              <i class="layui-icon layui-icon-vercode login-icon"></i>
              <input type="text" name="captcha" required lay-verify="required" placeholder="请输入验证码" autocomplete="off" class="layui-input">
            </div>
            <img src="{:captcha_src()}" class="captcha-img" onclick="this.src='{:captcha_src()}?t='+ new Date().getTime();" alt="验证码">
          </div>
        </div> -->
        <div class="layui-form-item">
          <input type="checkbox" name="remember" lay-skin="primary" title="记住密码">
        </div>
        <div class="layui-form-item">
          <button class="layui-btn login-btn layui-btn-fluid" lay-submit lay-filter="LAY-user-login-submit">登 录</button>
        </div>
      </form>
      <div class="footer">
        <p>© 2025 <a href="./" target="_blank">逻界</a> - 账号租赁管理系统</p>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
    layui.config({
      base: '../../layuiadmin/'
    }).extend({
      index: 'lib/index'
    }).use(['index', 'form', 'layer'], function(){
      var $ = layui.$,
          form = layui.form,
          layer = layui.layer;

      form.render();

      // 登录按钮点击动效
      $('.login-btn').on('mousedown', function(){
        $(this).css('transform', 'translateY(2px)');
      }).on('mouseup mouseleave', function(){
        $(this).css('transform', '');
      });

      // 登录提交事件
      form.on('submit(LAY-user-login-submit)', function(obj){
        var field = obj.field;

        // 显示loading
        var loadIndex = layer.load(2);

        $.ajax({
          url: "/admin/base/login",
          type: "post",
          dataType: "json",
          data: {
            data: field
          },
          success: function(res) {
            layer.close(loadIndex);

            if(typeof res === 'string' && res.indexOf('成功') !== -1) {
              // 登录成功，显示成功提示
              layer.msg('登录成功，正在跳转...', {
                icon: 1,
                time: 1500
              }, function(){
                window.location.href = "/admin";
              });
            } else {
              // 登录失败，抖动效果
              $('.login-form').addClass('shake');
              setTimeout(function(){
                $('.login-form').removeClass('shake');
              }, 500);

              // ✅ 已移除验证码刷新逻辑
              // $('.captcha-img').attr('src', '{:captcha_src()}?t=' + new Date().getTime());
              // $('input[name="captcha"]').val('');

              // 显示错误信息
              layer.msg(res, {
                icon: 2,
                anim: 6
              });
            }
          },
          error: function() {
            layer.close(loadIndex);
            layer.msg('服务器连接失败，请稍后再试', {
              icon: 2
            });
          }
        });

        return false;
      });
    });
  </script>
</body>
</html>