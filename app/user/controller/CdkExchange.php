<?php
/**
 * © 2024 空祖版权所有
 *
 * 本代码受《中华人民共和国著作权法》等相关法律保护。
 * 作者：空祖 (Kongzue)
 * 创建日期：2024-11-25
 * 描述：用于处理卡密兑换逻辑的控制器，包含卡密激活、查询商品等功能。
 * 如需使用、复制或修改本代码，请联系作者并获得授权。
 */

namespace app\user\controller;

use think\facade\View;
use think\facade\Request;
use think\facade\Db;
use think\facade\Log;
use app\admin\model\Cdk;
use app\BaseController;
use think\facade\Cookie;
use think\exception\ValidateException;
use think\response\Json;
use Exception;
use DateTime;
use DateTimeZone;

class CdkExchange extends BaseController
{
    /**
     * 显示卡密激活页面
     *
     * @return string|\think\response\View
     */
    public function index()
    {
        try {
            $system = Db::name('system')->where('id', 1)->find();
            if (empty($system)) {
                return '系统配置不存在';
            }

            // 获取当前用户信息
            $userId = Cookie::get('id');
            if (!$userId) {
                return redirect('/user/login/index');
            }

            $user = Db::name('user')->where('id', $userId)->find();
            if (empty($user)) {
                return redirect('/user/login/index');
            }

            return View::fetch('cdk/exchange', [
                'system' => $system,
                'user' => $user
            ]);
        } catch (Exception $e) {
            Log::error('获取系统配置失败', ['error' => $e->getMessage()]);
            return $e->getMessage();
        }
    }

    /**
     * 卡密兑换接口
     *
     * @return \think\response\Json
     */
    public function exchange(): Json
    {
        try {
            $cdkCode = trim(Request::param('cdk_code', ''));

            // 验证卡密
            if (empty($cdkCode)) {
                return json(['code' => 1, 'message' => '卡密不能为空']);
            }
            if (!preg_match('/^[A-Za-z0-9\-]{10,}$/', $cdkCode)) {
                return json(['code' => 1, 'message' => '卡密格式不正确']);
            }

            // 查询卡密
            $cdkCard = Cdk::where('cdk_code', $cdkCode)->findOrEmpty();
            if ($cdkCard->isEmpty()) {
                Log::warning('无效的卡密', ['cdk_code' => $cdkCode]);
                return json(['code' => 1, 'message' => '无效的卡密']);
            }

            // 检查卡密状态
            if ($cdkCard->status === '已使用' || $cdkCard->status === 'used') {
                return json(['code' => 1, 'message' => '卡密已激活']);
            }

            // 检查卡密是否被封禁
            if ($cdkCard->is_banned == 1) {
                Log::warning('尝试使用被封禁的卡密', [
                    'cdk_code' => $cdkCode,
                    'banned_at' => $cdkCard->banned_at,
                    'banned_reason' => $cdkCard->banned_reason
                ]);
                return json(['code' => 1, 'message' => '该卡密已被封禁，无法使用']);
            }

            // 检查用户登录状态
            $userId = Cookie::get('id');
            if (empty($userId) || !is_numeric($userId) || $userId <= 0) {
                Log::warning('未登录或无效的用户 ID 尝试激活卡密', ['user_id' => $userId, 'cdk_code' => $cdkCode]);
                return json(['code' => 4, 'message' => '用户未登录或认证失败']);
            }

            // 验证用户是否存在
            $user = Db::name('user')->where('id', $userId)->find();
            if (empty($user)) {
                Log::warning('用户不存在或认证失败', ['user_id' => $userId]);
                return json(['code' => 4, 'message' => '用户认证失败']);
            }

            // 根据卡密类型处理不同的激活逻辑
            if (strpos($cdkCard->cdk_type, 'member_') === 0) {
                // 会员卡密激活
                return $this->activateMembershipCard($cdkCard, $userId, $user);
            } elseif ($cdkCard->cdk_type === 'permanent') {
                // 永久版卡密激活
                return $this->activatePermanentCard($cdkCard, $userId, $user);
            } elseif ($cdkCard->cdk_type === 'offline') {
                // 离线卡密激活
                return $this->activateOfflineCard($cdkCard, $userId, $user);
            } else {
                // 普通卡密激活（原有逻辑）
                return $this->activateRegularCard($cdkCard, $userId, $user);
            }
        } catch (Exception $e) {
            Log::error('卡密兑换异常', ['error' => $e->getMessage()]);
            return json(['code' => 500, 'message' => '服务器错误，请稍后再试', 'error' => $e->getMessage()]);
        }
    }

    /**
     * 激活会员卡密
     *
     * @param Cdk $cdkCard 卡密对象
     * @param int $userId 用户ID
     * @param array $user 用户信息
     * @return \think\response\Json
     */
    private function activateMembershipCard(Cdk $cdkCard, int $userId, array $user): Json
    {
        try {
            // 解析会员类型
            $membershipType = str_replace('member_', '', $cdkCard->cdk_type);

            // 查询会员类型信息 - 优先使用shop字段（会员类型ID），如果为0则使用类型名称查询
            if ($cdkCard->shop && $cdkCard->shop > 0) {
                $membershipInfo = Db::name('membership_pricing')
                    ->where('id', $cdkCard->shop)
                    ->find();
            } else {
                // 兼容旧数据，使用类型名称查询
                $membershipInfo = Db::name('membership_pricing')
                    ->where('membership_type', $membershipType)
                    ->find();
            }

            if (empty($membershipInfo)) {
                Log::error('未找到会员类型信息', ['membership_type' => $membershipType]);
                return json(['code' => 5, 'message' => '未知的会员类型']);
            }

            // 计算到期时间 - 修复时效期单位问题
            $now = new DateTime('now', new DateTimeZone('Asia/Shanghai'));
            $validityPeriodDays = (int)$cdkCard->expiry_date2; // 这个字段存储的是天数

            // 限制有效期最大值为36500天（100年），防止日期计算溢出
            $validityPeriodDays = min(max($validityPeriodDays, 1), 36500);

            $now->modify("+{$validityPeriodDays} day"); // 会员卡密以天为单位
            $expiryDate = $now->format('Y-m-d H:i:s');

            Log::info('会员卡密时效期计算', [
                'cdk_code' => $cdkCard->cdk_code,
                'validity_period_days' => $validityPeriodDays,
                'expiry_date' => $expiryDate
            ]);

            // 开始事务
            Db::startTrans();
            try {
                // 更新用户会员信息
                $updateResult = Db::name('user')->where('id', $userId)->update([
                    'membership_level' => $membershipInfo['membership_type'],
                    'exit_time' => $expiryDate
                ]);

                if ($updateResult === false) {
                    throw new Exception('更新用户会员信息失败');
                }

                // 更新卡密状态
                $cdkCard->status = 'used';
                $cdkCard->assigned_user = $userId;
                $cdkCard->expiry_date = $expiryDate;
                if (!$cdkCard->save()) {
                    throw new Exception('更新卡密信息失败');
                }

                // 创建会员订单记录
                // $orderData = [
                //     'ord_bbh' => time() . mt_rand(10000, 99999),
                //     'ord_type' => '会员卡密兑换',
                //     'ord_type2' => 2, // 会员订单类型
                //     'ord_money' => $membershipInfo['price'] ?? 0,
                //     'ord_name' => $membershipInfo['membership_type'] . '会员',
                //     'ord_uid' => $userId,
                //     'ord_ifpay' => 1,
                //     'ord_combo' => $membershipInfo['id'] ?? null,
                //     'or_maturity' => 1,
                //     'time' => date('Y-m-d H:i:s'),
                //     'expiry_date' => $expiryDate
                // ];

                // $insertResult = Db::name('order')->insert($orderData);
                // if (!$insertResult) {
                //     throw new Exception('创建会员订单记录失败');
                // }

                Db::commit();

                return json([
                    'code' => 0,
                    'message' => '会员激活成功',
                    'membership_type' => $membershipInfo['membership_type'],
                    'expiry_date' => $expiryDate
                ]);
            } catch (Exception $e) {
                Db::rollback();
                Log::error('会员卡密激活失败', ['error' => $e->getMessage()]);
                return json(['code' => 7, 'message' => '会员激活失败，请稍后再试', 'error' => $e->getMessage()]);
            }
        } catch (Exception $e) {
            Log::error('会员卡密激活异常', ['error' => $e->getMessage()]);
            return json(['code' => 500, 'message' => '服务器错误，请稍后再试', 'error' => $e->getMessage()]);
        }
    }

    /**
     * 激活离线卡密
     *
     * @param Cdk $cdkCard 卡密对象
     * @param int $userId 用户ID
     * @param array $user 用户信息
     * @return \think\response\Json
     */
    private function activateOfflineCard(Cdk $cdkCard, int $userId, array $user): Json
    {
        try {
            Log::info('开始激活离线卡密', [
                'cdk_code' => (string)$cdkCard->cdk_code,
                'user_id' => (int)$userId,
                'shop_id' => (int)$cdkCard->shop
            ]);

            // 查询商品信息
            $goods = Db::name('goods')->where('id', $cdkCard->shop)->find();
            if (empty($goods)) {
                Log::error('未找到商品信息', ['shop_id' => $cdkCard->shop]);
                return json(['code' => 6, 'message' => '未知的商品类型']);
            }

            Log::info('找到商品信息', [
                'goods_id' => $goods['id'] ?? 'unknown',
                'goods_name' => $goods['goods_name'] ?? 'unknown'
            ]);

            // 查询离线价格信息
            Log::info('准备查询离线价格信息', ['product_id' => (int)$cdkCard->shop]);

            try {
                $offlineInfo = Db::name('offline')->where('product_id', $cdkCard->shop)->find();
                Log::info('离线价格查询结果', [
                    'product_id' => (int)$cdkCard->shop,
                    'product_amount' => $offlineInfo['product_amount'] ?? 'not_found',
                    'is_empty' => empty($offlineInfo)
                ]);

                if (empty($offlineInfo)) {
                    Log::error('未找到离线价格信息', [
                        'product_id' => (int)$cdkCard->shop,
                        'goods_name' => $goods['goods_name'] ?? 'unknown'
                    ]);
                    return json(['code' => 6, 'message' => '该商品(' . ($goods['goods_name'] ?? 'ID:' . $cdkCard->shop) . ')未设置离线价格，请联系管理员']);
                }

                Log::info('找到离线价格信息', [
                    'product_id' => (int)$offlineInfo['product_id'],
                    'product_amount' => (float)$offlineInfo['product_amount']
                ]);
            } catch (Exception $e) {
                Log::error('查询离线价格信息时发生异常', [
                    'error' => $e->getMessage(),
                    'product_id' => (int)$cdkCard->shop
                ]);
                throw $e;
            }

            // 开始事务
            Db::startTrans();
            try {
                Log::info('开始查找可用的离线账号');

                // 查找可用的离线账号
                $availableAccount = Db::name('account')
                    ->where('ac_goods', $cdkCard->shop)
                    ->where('ac_sell', 1)
                    ->where('ac_states', 1)
                    ->where('goods_Type', 0) // 离线账号
                    ->lock(true)
                    ->find();

                if (empty($availableAccount)) {
                    Log::warning('没有可用的离线账号', ['cdk_code' => $cdkCard->cdk_code, 'shop' => $cdkCard->shop]);
                    Db::rollback();
                    return json(['code' => 6, 'message' => '没有可用的离线账号，请联系管理员']);
                }

                Log::info('找到可用的离线账号', [
                    'account_id' => $availableAccount['id'],
                    'account_name' => $availableAccount['ac_name']
                ]);

                // 更新卡密状态
                $cdkCard->status = 'used';
                $cdkCard->assigned_user = $userId;
                $cdkCard->expiry_date = null; // 离线卡密永久有效，不设置到期时间
                $cdkCard->account_id = $availableAccount['id']; // 关联分配的账号ID
                $saveResult = $cdkCard->save();

                Log::info('卡密状态更新结果', ['save_result' => $saveResult]);

                if (!$saveResult) {
                    throw new Exception('更新卡密信息失败');
                }

                // 更新离线账号状态为已分配（但保持可用状态）
                $updateAccount = Db::name('account')->where('id', $availableAccount['id'])->update([
                    'ac_states' => 1, // 离线账号永远保持可用状态
                    'ac_uid' => $userId,
                    'exit_time' => null, // 离线账号永久有效
                    'token_limit' => 0,
                ]);

                if ($updateAccount === false) {
                    throw new Exception('更新离线账号状态失败');
                }

                Log::info('离线账号分配成功', [
                    'account_id' => $availableAccount['id'],
                    'user_id' => $userId
                ]);

                // 创建离线卡密订单记录
                $orderData = [
                    'ord_bbh' => time() . mt_rand(10000, 99999),
                    'ord_type' => '离线卡密兑换',
                    'ord_money' => $offlineInfo['product_amount'] ?? 0,
                    'ord_name' => $goods['goods_name'] . '(离线版)',
                    'GoodType' => 0, // 离线游戏类型
                    'ord_uid' => $userId,
                    'ord_combo' => $cdkCard->shop, // 修复：确保shop字段存储的是正确的商品ID
                    'ord_aid' => $availableAccount['id'], // 关联分配的账号ID
                    'ord_ifpay' => 1,
                    'or_maturity' => 1,  // 设置订单为有效状态
                    'time' => date('Y-m-d H:i:s'),
                    'goods_id' => $cdkCard->shop, // 新增：明确存储商品ID
                ];

                Log::info('准备插入订单数据', [
                    'ord_bbh' => $orderData['ord_bbh'],
                    'ord_type' => $orderData['ord_type'],
                    'ord_money' => $orderData['ord_money'],
                    'ord_name' => $orderData['ord_name'],
                    'ord_uid' => $orderData['ord_uid']
                ]);

                $insertResult = Db::name('order')->insert($orderData);

                Log::info('订单插入结果', ['insert_result' => $insertResult]);

                if (!$insertResult) {
                    throw new Exception('创建离线订单记录失败');
                }

                Db::commit();

                return json([
                    'code' => 0,
                    'message' => '离线卡密激活成功',
                    'goods_name' => $goods['goods_name'],
                    'offline_price' => $offlineInfo['product_amount'],
                    'account_name' => $availableAccount['ac_name'],
                    'account_password' => $availableAccount['ac_password'],
                    'instructions' => '您已成功激活离线游戏权限，该权限永久有效。账号信息已分配，请妥善保管。',
                    'is_offline' => true
                ]);
            } catch (Exception $e) {
                Db::rollback();
                Log::error('离线卡密激活失败', [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                    'cdk_code' => $cdkCard->cdk_code,
                    'user_id' => $userId,
                    'goods_id' => $cdkCard->shop
                ]);
                return json(['code' => 7, 'message' => '离线卡密激活失败：' . $e->getMessage()]);
            }
        } catch (Exception $e) {
            Log::error('离线卡密激活异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'cdk_code' => $cdkCard->cdk_code ?? 'unknown',
                'user_id' => $userId ?? 'unknown',
                'shop_id' => $cdkCard->shop ?? 'unknown'
            ]);
            return json(['code' => 500, 'message' => '服务器错误：' . $e->getMessage()]);
        }
    }

    /**
     * 激活永久版卡密
     *
     * @param CdkCard $cdkCard 卡密对象
     * @param int $userId 用户ID
     * @param array $user 用户信息
     * @return \think\response\Json
     */
    private function activatePermanentCard(Cdk $cdkCard, int $userId, array $user): Json
    {
        try {
            // 查询商品信息
            $goods = Db::name('goods')->where('id', $cdkCard->shop)->find();
            if (empty($goods)) {
                Log::error('未找到商品信息', ['shop_id' => $cdkCard->shop]);
                return json(['code' => 6, 'message' => '未知的商品类型']);
            }

            // 开始事务
            Db::startTrans();
            try {
                // 更新卡密状态
                $cdkCard->status = 'used';
                $cdkCard->assigned_user = $userId;
                if (!$cdkCard->save()) {
                    throw new Exception('更新卡密信息失败');
                }

                // 创建永久版订单记录
                $orderData = [
                    'ord_bbh' => time() . mt_rand(10000, 99999),
                    'ord_type' => '永久版卡密兑换',
                    'ord_type2' => 1, // 永久版订单类型
                    'ord_money' => $goods['goods_price'] ?? 0,
                    'ord_name' => $goods['goods_name'] . '(永久版)',
                    'is_permanent' => 1,
                    'GoodType' => 3,
                    'ord_uid' => $userId,
                    'ord_combo' => $cdkCard['shop'] ?? null, // 修复：确保shop字段存储的是正确的商品ID
                    'ord_ifpay' => 1,
                    'or_maturity' => 1,  // 设置订单为有效状态
                    'time' => date('Y-m-d H:i:s'),
                    'goods_id' => $cdkCard['shop'] ?? null, // 新增：明确存储商品ID
                ];

                $insertResult = Db::name('order')->insert($orderData);
                if (!$insertResult) {
                    throw new Exception('创建永久版订单记录失败');
                }

                Db::commit();

                return json([
                    'code' => 0,
                    'message' => '永久版激活成功',
                    'goods_name' => $goods['goods_name'],
                    'instructions' => '您已成功激活永久版权限，请前往用户中心查看详情并提取账号。'
                ]);
            } catch (Exception $e) {
                Db::rollback();
                Log::error('永久版卡密激活失败', ['error' => $e->getMessage()]);
                return json(['code' => 7, 'message' => '永久版激活失败，请稍后再试', 'error' => $e->getMessage()]);
            }
        } catch (Exception $e) {
            Log::error('永久版卡密激活异常', ['error' => $e->getMessage()]);
            return json(['code' => 500, 'message' => '服务器错误，请稍后再试', 'error' => $e->getMessage()]);
        }
    }

    /**
     * 激活普通卡密（原有逻辑）
     *
     * @param Cdk $cdkCard 卡密对象
     * @param int $userId 用户ID
     * @param array $user 用户信息
     * @return \think\response\Json
     */
    private function activateRegularCard(Cdk $cdkCard, int $userId, array $user): Json
    {
        // 计算到期时间
        $expiryDate = $this->calculateExpiryDate($cdkCard->cdk_type, $cdkCard->expiry_date2);
        if (!$expiryDate) {
            Log::error('未知的卡密类型', ['cdk_type' => $cdkCard->cdk_type]);
            return json(['code' => 5, 'message' => '未知的卡密类型']);
        }

        // 开始事务
        Db::startTrans();
        try {
            // 查找可用账号
            $availableAccount = Db::name('account')
                ->where('ac_goods', $cdkCard->shop)
                ->where('ac_sell', 1)
                ->where('ac_states', 1)
                ->where('goods_Type', 1)
                ->lock(true)
                ->find();

            if (empty($availableAccount)) {
                Log::warning('没有可用的账号', ['cdk_code' => $cdkCard->cdk_code, 'shop' => $cdkCard->shop]);
                Db::rollback();
                return json(['code' => 6, 'message' => '没有可用的账号']);
            }

            // 激活卡密并更新账号
            $this->activateCard($cdkCard, $availableAccount, $userId, $expiryDate);

            // 生成订单
            $orderResult = $this->createOrder($cdkCard, $availableAccount, $userId);
            if ($orderResult['code'] !== 0) {
                Db::rollback();
                return json($orderResult);
            }

            Db::commit();

            return json([
                'code' => 0,
                'message' => '激活成功',
                'expiry_date' => $expiryDate,
                'account' => $availableAccount['ac_name'],
                'password' => '受到规则限制：在用户中心查看密码',
                'token' => '受到规则限制：在用户中心查看令牌',
            ]);
        } catch (Exception $e) {
            Db::rollback();
            Log::error('激活失败', ['cdk_code' => $cdkCard->cdk_code, 'error' => $e->getMessage()]);
            return json(['code' => 7, 'message' => '激活失败，请稍后再试', 'error' => $e->getMessage()]);
        }
    }

    /**
     * 计算到期时间
     *
     * 根据卡密类型和动态时间参数，计算相应的到期时间。
     *
     * @param string $cdkType 卡密类型（year, month, day, hour）
     * @param int|null $expiry_date2 动态时间参数（例如：4 years, 6 months），默认为 1
     * @return string|null 返回计算出的时间，未知类型返回 null。
     */
    private function calculateExpiryDate(string $cdkType, ?int $expiry_date2 = 1): ?string
    {
        $now = new DateTime('now', new DateTimeZone('Asia/Shanghai'));
        $expiry_date2 = max((int)$expiry_date2, 1);

        $modifiers = [
            'year' => "+{$expiry_date2} year",
            'month' => "+{$expiry_date2} month",
            'day' => "+{$expiry_date2} day",
            'hour' => "+{$expiry_date2} hour",
        ];

        if (!isset($modifiers[$cdkType])) {
            return null;
        }

        $now->modify($modifiers[$cdkType]);
        return $now->format('Y-m-d H:i:s');
    }


    /**
     * 激活卡密并更新账号信息
     *
     * @param Cdk $cdkCard
     * @param array $availableAccount
     * @param int $userId
     * @param string $expiryDate
     * @return void
     * @throws Exception
     */
    private function activateCard(Cdk $cdkCard, array $availableAccount, int $userId, string $expiryDate): void
    {
        // 更新卡密信息
        $cdkCard->status = 'used';
        $cdkCard->assigned_user = $userId;
        $cdkCard->expiry_date = $expiryDate;
        $cdkCard->account_id = $availableAccount['id'];
        if (!$cdkCard->save()) {
            throw new Exception('更新卡密信息失败');
        }

        // 根据id查询ac_name
        $acName = Db::name('account')->where('id', $availableAccount['id'])->value('ac_name');

        // ✅ 使用统一的账号分配方法
        $assignResult = \app\controller\Api::assignAccount(
            $acName,
            $userId,
            strtotime($expiryDate)
        );

        if (!$assignResult) {
            throw new Exception('更新账号状态失败');
        }

        Log::info('卡密激活成功', ['cdk_code' => $cdkCard->cdk_code]);
    }

    /**
     * 创建订单
     *
     * @param Cdk $cdkCard
     * @param array $availableAccount
     * @param int $userId
     * @return array
     */
    private function createOrder(Cdk $cdkCard, array $availableAccount, int $userId): array
    {
        try {
            $platformOrderId = time() . mt_rand(10000, 99999);
            $paymentType = 'CDK兑换';
            $orderType2 = 1;

            // 获取套餐和商品信息
            $combo = Db::name('combo')->where('co_goods', $cdkCard->shop)->find();
            if (!$combo) {
                throw new Exception('未找到对应的套餐信息');
            }

            $goods = Db::name('goods')->where('id', $cdkCard->shop)->find();
            if (!$goods) {
                throw new Exception('未找到对应的商品信息');
            }

            $orderAmount = $combo['co_money'] ?? null;
            $packageName = $goods['goods_name'] ?? null;
            $orderData = [
                'ord_ybh'     => null,
                'ord_bbh'     => $platformOrderId,
                'ord_type'    => $paymentType,
                'ord_type2'   => $orderType2,
                'ord_money'   => $orderAmount,
                'ord_name'    => $packageName,
                'ord_uid'     => $userId,
                'ord_ifpay'   => 1,
                'ord_combo'   => $combo['id'] ?? null,
                'or_maturity' => 1,
                'GoodType'    => 1,
                'ord_aid'     => $availableAccount['id'],
                'time'        => date('Y-m-d H:i:s'),
            ];

            $insertResult = Db::name('order')->insert($orderData);
            if (!$insertResult) {
                Log::warning('订单插入未成功', ['order_data' => $orderData]);
                return ['code' => 8, 'message' => '订单记录失败，请稍后再试'];
            }

            return ['code' => 0, 'message' => '订单记录成功'];
        } catch (Exception $e) {
            Log::error('订单插入失败', [
                'error' => $e->getMessage(),
                'order_data' => $orderData ?? [],
                'stack_trace' => $e->getTraceAsString(),
            ]);
            return [
                'code' => 8,
                'message' => '订单记录失败，请稍后再试',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 获取所有可用商品的名称和ID，屏蔽名称为"至尊会员"的商品
     *
     * @return \think\response\Json
     */
    public function getGoodsNames(): Json
    {
        try {
            $goods = Db::name('goods')
                ->field('id, goods_name')
                ->where('goods_name', '<>', '至尊会员')
                ->select();

            if ($goods->isEmpty()) {
                return json(['code' => 1, 'message' => '暂无可用商品']);
            }

            return json(['code' => 0, 'message' => '查询成功', 'goods' => $goods]);
        } catch (Exception $e) {
            Log::error('获取商品信息失败', ['error' => $e->getMessage()]);
            return json(['code' => 2, 'message' => '获取商品信息失败，请稍后再试']);
        }
    }
}
