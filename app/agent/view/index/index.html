<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>代理商后台系统</title>
  <!-- 使用可靠的CDN链接 -->
  <link rel="stylesheet" href="/static/css/index.css" />
  <link rel="stylesheet" href="/static/css/all.min.css">
  <link href="/static/css/nprogress.css" rel="stylesheet">
  <script src="/static/js/echarts.min.js"></script>
  <!-- 代理后台Session处理脚本 -->
  <script src="/static/agent/js/session-handler.js"></script>
  <style>
    :root {
      --primary-color: #409EFF;
      --success-color: #67C23A;
      --warning-color: #E6A23C;
      --danger-color: #F56C6C;
      --info-color: #909399;
      --border-radius: 8px;
      --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: "PingFang SC", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
      background-color: #f6f8fa;
      color: #333;
    }

    [v-cloak] { display: none; }

    .app-layout {
      display: flex;
      min-height: 100vh;
    }

    .app-sidebar {
      width: 220px;
      background: #001529;
      color: white;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      transition: all 0.3s;
      z-index: 1000;
    }

    .sidebar-logo {
      padding: 20px;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      height: 64px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar-logo i {
      font-size: 24px;
      margin-right: 12px;
      color: var(--primary-color);
    }

    .app-main {
      margin-left: 220px;
      flex: 1;
      padding: 20px;
      transition: all 0.3s;
    }

    .app-header {
      background: white;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 60px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      position: sticky;
      top: 0;
      z-index: 999;
    }

    .welcome-section {
      background: linear-gradient(135deg, #409EFF, #3654FD);
      border-radius: var(--border-radius);
      padding: 24px;
      margin-bottom: 24px;
      color: white;
      box-shadow: 0 10px 20px rgba(54, 84, 253, 0.15);
    }

    .stat-cards {
      margin-bottom: 24px;
    }

    .custom-card {
      border-radius: var(--border-radius);
      overflow: hidden;
      transition: all 0.3s;
      cursor: pointer;
    }

    .custom-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .card-header-icon {
      font-size: 20px;
      margin-right: 8px;
    }

    .card-actions {
      display: flex;
      gap: 12px;
      margin-top: 16px;
    }

    .search-form {
      padding: 16px;
      margin-bottom: 20px;
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
    }

    .table-container {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      overflow: hidden;
    }

    .el-header, .el-footer, .el-main {
      padding: 0;
    }

    .toast-message {
      z-index: 9999;
    }

    .global-loading {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 9999;
    }

    .global-loading .el-loading-spinner {
      text-align: center;
    }

    .global-loading .circular {
      height: 42px;
      width: 42px;
      animation: loading-rotate 2s linear infinite;
    }

    .global-loading .path {
      animation: loading-dash 1.5s ease-in-out infinite;
      stroke-dasharray: 90, 150;
      stroke-dashoffset: 0;
      stroke-width: 2;
      stroke: #409EFF;
      stroke-linecap: round;
    }

    @keyframes loading-rotate {
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes loading-dash {
      0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
      }
      50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -40px;
      }
      100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -120px;
      }
    }

    @media (max-width: 768px) {
      .app-sidebar {
        width: 60px;
      }
      .app-main {
        margin-left: 60px;
      }
      .sidebar-logo span {
        display: none;
      }
    }

    .timeline-content {
      padding: 4px 0;
    }

    .card-title {
      margin-bottom: 6px;
      display: flex;
      align-items: center;
    }

    .card-detail {
      font-size: 13px;
      color: #606266;
    }

    .card-name {
      font-weight: 500;
      margin-bottom: 2px;
    }

    .card-code {
      color: #909399;
      font-family: monospace;
    }

    .empty-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 40px 0;
      color: #909399;
    }

    .card-type-prefix {
      color: #909399;
      font-size: 0.9em;
      margin-right: 4px;
    }
  </style>
</head>
<body>
  <div id="app" v-cloak>
    <!-- 全局加载状态 -->
    <div v-if="loading" class="global-loading">
      <div class="el-loading-spinner">
        <svg class="circular" viewBox="25 25 50 50">
          <circle class="path" cx="50" cy="50" r="20" fill="none"/>
        </svg>
        <p class="el-loading-text">加载中...</p>
      </div>
    </div>

    <!-- 主布局 -->
    <div class="app-layout">
      <!-- 侧边栏 -->
      <aside class="app-sidebar">
        <div class="sidebar-logo">
          <i class="fas fa-chart-pie"></i>
          <span>代理商后台</span>
        </div>

        <el-menu
          :default-active="currentPage"
          class="sidebar-menu"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#409EFF"
          @select="changePage">
          <el-menu-item index="dashboard">
            <i class="fas fa-home"></i>
            <span>控制台</span>
          </el-menu-item>
          <el-menu-item index="cards">
            <i class="fas fa-credit-card"></i>
            <span>卡密管理</span>
          </el-menu-item>
        </el-menu>
      </aside>

      <!-- 主内容区 -->
      <div class="app-main">
        <header class="app-header">
          <div>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>代理商后台</el-breadcrumb-item>
              <el-breadcrumb-item>{{ pageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="user-info">
            <el-dropdown>
              <span class="el-dropdown-link" style="display: flex; align-items: center; cursor: pointer;">
                <el-avatar :size="32" :src="userAvatar"></el-avatar>
                <span style="margin-left: 8px">{{ userName }}</span>
                <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="logout"><i class="fas fa-sign-out-alt"></i> 退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </header>

        <!-- 控制台页面 -->
        <div v-if="currentPage === 'dashboard'">
          <div class="welcome-section">
            <h2 style="font-size: 24px; margin-bottom: 8px;">欢迎回来，{{ userName }}</h2>
            <p>今天是 {{ currentDate }}，以下是您的账户概览</p>
            <div class="card-actions">
              <el-button type="primary" @click="changePage('cards')">
                <i class="fas fa-plus-circle" style="margin-right: 5px;"></i> 生成卡密
              </el-button>
            </div>
          </div>

          <!-- 统计卡片 -->
          <el-row :gutter="20" class="stat-cards">
            <el-col :xs="24" :sm="12" :md="6" v-for="(stat, index) in stats" :key="index">
              <el-card class="custom-card" shadow="hover" @click="handleStatClick(stat)">
                <div style="display: flex; align-items: center; margin-bottom: 16px;">
                  <i :class="stat.icon" style="font-size: 20px; margin-right: 8px;" :style="{color: statColors[stat.color]}"></i>
                  <span>{{ stat.title }}</span>
                </div>
                <div style="font-size: 24px; font-weight: bold; color: #333;">
                  {{ stat.value }}
                </div>
              </el-card>
            </el-col>
          </el-row>


        </div>

        <!-- 卡密管理页面 -->
        <div v-if="currentPage === 'cards'">
          <div class="card-actions" style="margin-bottom: 20px;">
            <el-button type="primary" @click="showModal('gameCard')">
              <i class="fas fa-gamepad" style="margin-right: 5px;"></i> 添加在线卡密
            </el-button>
            <el-button type="warning" @click="showModal('offlineCard')">
              <i class="fas fa-download" style="margin-right: 5px;"></i> 添加离线卡密
            </el-button>
            <el-button type="success" @click="showModal('memberCard')">
              <i class="fas fa-user-tag" style="margin-right: 5px;"></i> 添加会员卡密
            </el-button>
            <el-button type="danger" @click="openBatchBanModal()">
              <i class="fas fa-ban" style="margin-right: 5px;"></i> 批量封禁
            </el-button>
          </div>

          <!-- 搜索筛选 -->
          <div class="search-form">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="8">
                <el-radio-group v-model="filterStatus" @change="loadCards()">
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button label="未使用">未使用</el-radio-button>
                  <el-radio-button label="已使用">已使用</el-radio-button>
                  <el-radio-button label="已封禁">已封禁</el-radio-button>
                </el-radio-group>
              </el-col>
              <el-col :xs="24" :sm="8">
                <el-select v-model="filterType" placeholder="卡密类型" style="width: 100%" @change="loadCards()">
                  <el-option label="全部类型" value=""></el-option>
                  <el-option label="在线日卡" value="day"></el-option>
                  <el-option label="在线时卡" value="hour"></el-option>
                  <el-option label="在线永久卡" value="permanent"></el-option>
                  <el-option label="离线卡密" value="offline"></el-option>
                  <el-option label="会员卡" value="member"></el-option>
                </el-select>
              </el-col>
              <el-col :xs="24" :sm="8" v-if="filterType === 'member'">
                <el-select v-model="filterMemberType" placeholder="会员类型" style="width: 100%" @change="loadCards()">
                  <el-option label="全部会员" value=""></el-option>
                  <el-option
                    v-for="item in membershipTypes"
                    :key="item.id"
                    :label="item.membership_type"
                    :value="item.membership_type">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :xs="24" :sm="8" :md="6">
                <el-input
                  v-model="searchKeyword"
                  placeholder="模糊搜索..."
                  @input="debounceSearch"
                  clearable>
                  <template #prefix>
                    <i class="fas fa-search" style="color: #909399;"></i>
                  </template>
                </el-input>
              </el-col>
            </el-row>
          </div>

          <!-- 批量操作按钮 -->
          <div style="margin-bottom: 16px; display: flex; gap: 12px; align-items: center;">
            <el-button
              type="danger"
              :disabled="!selectedCards || selectedCards.length === 0"
              @click="batchBanSelected">
              <i class="fas fa-ban" style="margin-right: 6px;"></i>
              批量封禁 ({{ selectedCards ? selectedCards.length : 0 }})
            </el-button>
            <el-button
              type="primary"
              plain
              @click="openBatchBanModal">
              <i class="fas fa-edit" style="margin-right: 6px;"></i>
              手动输入批量封禁
            </el-button>
            <span v-if="selectedCards && selectedCards.length > 0" style="color: #909399; font-size: 14px;">
              已选择 {{ selectedCards.length }} 张卡密
            </span>
          </div>

          <!-- 卡密列表 -->
          <div class="table-container">
            <el-table
              :data="cardsList"
              style="width: 100%"
              v-loading="tableLoading"
              border
              @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55"
                :selectable="checkSelectable">
              </el-table-column>
              <el-table-column prop="cdk_code" label="卡密码">
                <template #default="scope">
                  <div style="display: flex; align-items: center;">
                    <i class="fas fa-key" style="margin-right: 8px; color: #409EFF;"></i>
                    <span>{{ scope.row.cdk_code }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="cdk_type" label="类型">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.is_member_card ? 'warning' : (scope.row.cdk_type === 'offline' ? 'info' : (scope.row.cdk_type === 'permanent' ? 'danger' : 'primary'))"
                    effect="light">
                    {{ scope.row.is_member_card ? '会员卡' : (scope.row.cdk_type === 'offline' ? '离线卡' : (scope.row.cdk_type === 'day' ? '日卡' : (scope.row.cdk_type === 'hour' ? '小时卡' : '永久卡'))) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="商品/会员">
                <template #default="scope">
                  <span v-if="scope.row.is_member_card">
                    <span class="card-type-prefix">[{{ scope.row.membership_type_name }}]</span> 会员卡
                  </span>
                  <span v-else-if="scope.row.cdk_type === 'offline'">
                    <span class="card-type-prefix">[离线卡]</span> {{ scope.row.goods_name }}
                  </span>
                  <span v-else-if="scope.row.cdk_type === 'day'">
                    <span class="card-type-prefix">[日卡]</span> {{ scope.row.goods_name }}
                  </span>
                  <span v-else-if="scope.row.cdk_type === 'hour'">
                    <span class="card-type-prefix">[小时卡]</span> {{ scope.row.goods_name }}
                  </span>
                  <span v-else-if="scope.row.cdk_type === 'permanent'">
                    <span class="card-type-prefix">[永久卡]</span> {{ scope.row.goods_name }}
                  </span>
                  <span v-else>
                    {{ scope.row.goods_name || '未知类型' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态">
                <template #default="scope">
                  <el-tag
                    :type="(scope.row.is_banned == 1 || scope.row.is_banned === '1') ? 'danger' : (scope.row.status === '未使用' ? 'success' : 'info')"
                    effect="dark">
                    {{ (scope.row.is_banned == 1 || scope.row.is_banned === '1') ? '已封禁' : scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="时间单位">
                <template #default="scope">
                  <span v-if="scope.row.is_member_card">{{ scope.row.expiry_date2 }}月</span>
                  <span v-else-if="scope.row.cdk_type === 'offline'">
                    <el-tag type="info" effect="dark" size="small">离线永久</el-tag>
                  </span>
                  <span v-else-if="scope.row.cdk_type === 'day'">{{ scope.row.expiry_date2 }}天</span>
                  <span v-else-if="scope.row.cdk_type === 'hour'">{{ scope.row.expiry_date2 }}小时</span>
                  <span v-else-if="scope.row.cdk_type === 'permanent'">
                    <el-tag type="success" effect="dark" size="small">永久</el-tag>
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="生成时间"></el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="scope">
                  <div style="display: flex; gap: 4px; align-items: center; justify-content: center;">
                    <template v-if="!(scope.row.is_banned == 1 || scope.row.is_banned === '1')">
                      <el-button
                        type="danger"
                        size="small"
                        @click="editCard(scope.row)">
                        封禁
                      </el-button>
                    </template>
                    <el-button
                      type="danger"
                      size="small"
                      plain
                      @click="deleteCard(scope.row)">
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div style="padding: 20px; text-align: center;">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="pagination.total"
                :current-page="pagination.current"
                :page-size="15"
                @current-change="loadCards">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 游戏卡密生成对话框 -->
    <el-dialog
      title="生成游戏卡密"
      v-model="showGameCardModal"
      width="500px">
      <el-form :model="gameCardForm" label-width="100px">
        <el-form-item label="选择商品" required>
          <el-select
            v-model="gameCardForm.goods_id"
            placeholder="选择商品类型或输入搜索"
            style="width: 100%"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteSearchGoods"
            :loading="goodsLoading"
            default-first-option
            @focus="showAllGoods">
            <el-option
              v-for="item in filteredGoodsList"
              :key="item.id"
              :label="item.goods_name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="卡密类型" required>
          <el-select v-model="gameCardForm.cdk_type" placeholder="选择卡密类型" style="width: 100%">
            <el-option label="日卡" value="day"></el-option>
            <el-option label="小时卡" value="hour"></el-option>
            <el-option label="永久卡" value="permanent"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生成数量">
          <el-input-number v-model="gameCardForm.num" :min="1" :max="100"></el-input-number>
        </el-form-item>
        <el-form-item :label="`时长(${timeUnitMap[gameCardForm.cdk_type]})`" v-if="gameCardForm.cdk_type && gameCardForm.cdk_type !== 'permanent'">
          <el-input-number v-model="gameCardForm.expiry_date2" :min="1"></el-input-number>
        </el-form-item>
        <el-form-item v-if="gameCardForm.cdk_type === 'permanent'" style="color: #67C23A; font-size: 14px;">
          <i class="el-icon-check"></i> 永久卡密无需设置时长，购买后永久有效
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeModal('gameCard')">取消</el-button>
          <el-button type="primary" @click="generateGameCards" :loading="submitLoading">生成</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 离线卡密生成对话框 -->
    <el-dialog
      title="生成离线卡密"
      v-model="showOfflineCardModal"
      width="500px">
      <el-form :model="offlineCardForm" label-width="100px">
        <el-form-item label="选择商品" required>
          <el-select
            v-model="offlineCardForm.goods_id"
            placeholder="选择离线游戏商品"
            style="width: 100%"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteSearchOfflineGoods"
            :loading="offlineGoodsLoading"
            default-first-option
            @focus="showAllOfflineGoods">
            <el-option
              v-for="item in filteredOfflineGoodsList"
              :key="item.id"
              :label="`${item.goods_name} (¥${item.product_amount})`"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生成数量">
          <el-input-number v-model="offlineCardForm.num" :min="1" :max="100"></el-input-number>
        </el-form-item>
        <el-form-item style="color: #67C23A; font-size: 14px;">
          <i class="el-icon-check"></i> 离线卡密激活后永久有效，无需设置时长
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeModal('offlineCard')">取消</el-button>
          <el-button type="primary" @click="generateOfflineCards" :loading="submitLoading">生成</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 会员卡密生成对话框 -->
    <el-dialog
      title="生成会员卡密"
      v-model="showMemberCardModal"
      width="500px">
      <el-form :model="memberCardForm" label-width="100px">
        <el-form-item label="会员类型" required>
          <el-select
            v-model="memberCardForm.membership_type"
            placeholder="选择会员类型或输入搜索"
            style="width: 100%"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteSearchMembership"
            :loading="membershipLoading"
            default-first-option
            @focus="showAllMembershipTypes">
            <el-option
              v-for="item in filteredMembershipTypes"
              :key="item.id"
              :label="`${item.membership_type} (${item.validity_period}天 - ¥${item.price})`"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生成数量">
          <el-input-number v-model="memberCardForm.num" :min="1" :max="100"></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeModal('memberCard')">取消</el-button>
          <el-button type="primary" @click="generateMemberCards" :loading="submitLoading">生成</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 封禁卡密对话框 -->
    <el-dialog
      title="封禁卡密"
      v-model="showBanCardModal"
      width="500px">
      <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
        <h4 style="margin: 0 0 10px 0; color: #333;">📋 卡密信息</h4>
        <p><strong>卡密码：</strong>{{ banCardForm.cdk_code }}</p>
        <p><strong>类型：</strong>{{ banCardForm.cdk_type }}</p>
        <p><strong>状态：</strong>{{ banCardForm.status }}</p>
      </div>

      <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
        <strong>⚠️ 警告：</strong>封禁操作不可逆！您只能封禁自己生成的卡密！
        <div v-if="banCardForm.status === '已使用'" style="margin-top: 5px;">
          该卡密已使用，封禁后将扣除用户对应权益！
        </div>
      </div>

      <el-form :model="banCardForm" label-width="100px">
        <el-form-item label="封禁原因" required>
          <el-input
            type="textarea"
            v-model="banCardForm.reason"
            placeholder="请输入封禁原因（必填）"
            :rows="3">
          </el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeBanModal()">取消</el-button>
          <el-button type="danger" @click="confirmBanCard" :loading="submitLoading">确认封禁</el-button>
        </span>
      </template>
    </el-dialog>
  </div>

  <!-- 首先加载Vue -->
  <script src="/static/js/vue.global.prod.js"></script>
  <!-- 加载NProgress -->
  <script src="/static/js/nprogress.js"></script>
  <!-- 然后加载Element Plus -->
  <script src="/static/js/index.full.js"></script>
  <!-- 最后加载中文语言包 -->
  <script src="/static/js/zh-cn.min.js"></script>
  <!-- 加载axios -->
  <script src="/static/js/axios.min.js"></script>

  <script>
    NProgress.start();

    const { createApp, ref, reactive, computed, watch, onMounted } = Vue;

    const app = createApp({
      setup() {
        // 引入Element Plus组件
        const { ElMessage, ElMessageBox } = ElementPlus;

        // 状态
        const loading = ref(true);
        const tableLoading = ref(false);
        const submitLoading = ref(false);
        const currentPage = ref('dashboard');
        const userName = ref('代理商');
        const userAvatar = ref('https://api.dicebear.com/7.x/avataaars/svg?seed=Felix');
        const currentDate = new Date().toLocaleDateString('zh-CN', {year: 'numeric', month: 'long', day: 'numeric', weekday: 'long'});
        const timeRange = ref('7d');

        // 统计数据
        const stats = reactive([
          {
            title: '账户余额',
            value: '{$agent.balance}',
            icon: 'fas fa-wallet',
            color: 'primary',
          },
          {
            title: '折扣比例',
            value: '{$agent.discount_rate}%',
            icon: 'fas fa-percent',
            color: 'success',
          },
          {
            title: '卡密总数',
            value: '{$data.cdk_count}',
            icon: 'fas fa-key',
            color: 'warning',
          },
          {
            title: '已使用',
            value: '{$data.used_count}',
            icon: 'fas fa-check-circle',
            color: 'danger',
          }
        ]);

        // 颜色映射
        const statColors = {
          primary: '#409EFF',
          success: '#67C23A',
          warning: '#E6A23C',
          danger: '#F56C6C',
          info: '#909399'
        };

        // 卡密管理相关
        const goodsList = ref([]);
        const offlineGoodsList = ref([]);
        const membershipTypes = ref([]);
        const cardsList = ref([]);
        const pagination = reactive({
          current: 1,
          total: 0
        });
        const filterStatus = ref('');
        const filterType = ref('');
        const searchKeyword = ref('');
        let searchTimer = null;

        // 下拉框搜索相关
        const goodsLoading = ref(false);
        const offlineGoodsLoading = ref(false);
        const membershipLoading = ref(false);
        const filteredGoodsList = ref([]);
        const filteredOfflineGoodsList = ref([]);
        const filteredMembershipTypes = ref([]);

        // 模态框显示状态
        const showGameCardModal = ref(false);
        const showOfflineCardModal = ref(false);
        const showMemberCardModal = ref(false);
        const showBanCardModal = ref(false);

        // 表单数据
        const gameCardForm = reactive({
          goods_id: '',
          cdk_type: '',
          num: 1,
          expiry_date2: 1
        });

        const offlineCardForm = reactive({
          goods_id: '',
          num: 1
        });

        const memberCardForm = reactive({
          membership_type: '',
          num: 1
        });

        const banCardForm = reactive({
          cdk_code: '',
          cdk_type: '',
          status: '',
          reason: ''
        });

        const timeUnitMap = {
          year: '年',
          month: '月',
          day: '天',
          hour: '小时',
          permanent: '永久'
        };



        // 计算属性
        const pageTitle = computed(() => {
          return currentPage.value === 'dashboard' ? '控制台' : '卡密管理';
        });

        // 在状态变量部分添加
        const filterMemberType = ref('');

        // 批量选择相关
        const selectedCards = ref([]);

        // 方法
        function changePage(page) {
          NProgress.start();
          loading.value = true;
          currentPage.value = page;

          if(page === 'cards') {
            loadCards().then(() => {
              loading.value = false;
              NProgress.done();
            });
          } else {
            setTimeout(() => {
              loading.value = false;
              NProgress.done();
            }, 500);
          }
        }

        function showModal(type) {
          if(type === 'gameCard') {
            showGameCardModal.value = true;
          } else if(type === 'offlineCard') {
            showOfflineCardModal.value = true;
          } else if(type === 'memberCard') {
            showMemberCardModal.value = true;
          }
        }

        function closeModal(type) {
          if(type === 'gameCard') {
            showGameCardModal.value = false;
            Object.assign(gameCardForm, {
              goods_id: '',
              cdk_type: '',
              num: 1,
              expiry_date2: 1
            });
          } else if(type === 'offlineCard') {
            showOfflineCardModal.value = false;
            Object.assign(offlineCardForm, {
              goods_id: '',
              num: 1
            });
          } else if(type === 'memberCard') {
            showMemberCardModal.value = false;
            Object.assign(memberCardForm, {
              membership_type: '',
              num: 1
            });
          }
        }

        // 通用的请求处理函数，检查代理状态和Session过期
        async function handleApiResponse(response) {
          const data = await response.json();

          // 优先使用Session处理器检查
          if (window.AgentSessionHandler && window.AgentSessionHandler.handleSessionExpired(data)) {
            throw new Error('Session expired');
          }

          // 兼容旧的代理禁用检查
          if (data.code === 403 && data.disabled) {
            // 代理被禁用
            ElMessage.error({
              message: data.msg,
              duration: 0,
              showClose: true
            });

            ElMessageBox.alert(data.msg, '账号已被禁用', {
              confirmButtonText: '重新登录',
              type: 'error',
              showClose: false,
              closeOnClickModal: false,
              closeOnPressEscape: false
            }).then(() => {
              window.location.href = '/agent/index/login';
            });

            throw new Error('Agent disabled');
          }

          return data;
        }

        async function loadCards(page = 1) {
          tableLoading.value = true;
          try {
            let url = `/agent/index/cdklist?page=${page}&status=${filterStatus.value}&type=${filterType.value}&keyword=${searchKeyword.value}`;

            // 如果筛选的是会员卡且选了会员类型
            if (filterType.value === 'member' && filterMemberType.value) {
              url += `&member_type=${filterMemberType.value}`;
            }

            const res = await fetch(url);
            const data = await handleApiResponse(res);
            if(data.code === 1) {
              cardsList.value = data.data.list;
              pagination.current = data.data.pagination.current;
              pagination.total = data.data.pagination.total;
            }
          } catch(e) {
            if (e.message !== 'Agent disabled') {
              console.error(e);
              ElMessage.error('当前账户已被封禁，请联系管理员获得支持');
            }
          } finally {
            tableLoading.value = false;
          }
        }

        function debounceSearch() {
          if(searchTimer) clearTimeout(searchTimer);
          searchTimer = setTimeout(() => {
            loadCards();
          }, 300);
        }

        // 远程搜索商品
        function remoteSearchGoods(query) {
          if (query !== null && query !== undefined) {
            goodsLoading.value = true;
            setTimeout(() => {
              filteredGoodsList.value = goodsList.value.filter(item => {
                return item.goods_name.toLowerCase().includes(query.toLowerCase());
              });
              goodsLoading.value = false;
            }, 200);
          } else {
            filteredGoodsList.value = goodsList.value;
          }
        }

        // 远程搜索离线商品
        function remoteSearchOfflineGoods(query) {
          if (query !== null && query !== undefined) {
            offlineGoodsLoading.value = true;
            setTimeout(() => {
              filteredOfflineGoodsList.value = offlineGoodsList.value.filter(item => {
                return item.goods_name.toLowerCase().includes(query.toLowerCase());
              });
              offlineGoodsLoading.value = false;
            }, 200);
          } else {
            filteredOfflineGoodsList.value = offlineGoodsList.value;
          }
        }

        // 远程搜索会员类型
        function remoteSearchMembership(query) {
          if (query !== null && query !== undefined) {
            membershipLoading.value = true;
            setTimeout(() => {
              filteredMembershipTypes.value = membershipTypes.value.filter(item => {
                return item.membership_type.toLowerCase().includes(query.toLowerCase());
              });
              membershipLoading.value = false;
            }, 200);
          } else {
            filteredMembershipTypes.value = membershipTypes.value;
          }
        }

        // 显示所有商品选项
        function showAllGoods() {
          filteredGoodsList.value = goodsList.value;
        }

        // 显示所有离线商品选项
        function showAllOfflineGoods() {
          filteredOfflineGoodsList.value = offlineGoodsList.value;
        }

        // 显示所有会员类型选项
        function showAllMembershipTypes() {
          filteredMembershipTypes.value = membershipTypes.value;
        }

        async function generateOfflineCards() {
          if(!offlineCardForm.goods_id) {
            ElMessage.warning('请选择离线游戏商品');
            return;
          }

          submitLoading.value = true;
          try {
            const res = await fetch('/agent/index/generateOfflineCard', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(offlineCardForm)
            });
            const data = await handleApiResponse(res);
            if(data.code === 1) {
              ElMessage.success('离线卡密生成成功');
              closeModal('offlineCard');
              loadCards();

              // 显示生成的卡密
              if (data.cdk_codes && data.cdk_codes.length > 0) {
                showCdkCodesModal(data.cdk_codes, '离线卡密');
              } else if (data.cdk_code) {
                showSingleCdkModal(data.cdk_code, '离线卡密');
              }
            } else {
              ElMessage.error(data.msg || '生成失败');
            }
          } catch(e) {
            if (e.message !== 'Agent disabled') {
              console.error(e);
              ElMessage.error('生成失败，请稍后重试');
            }
          } finally {
            submitLoading.value = false;
          }
        }

        async function generateGameCards() {
          if(!gameCardForm.goods_id || !gameCardForm.cdk_type) {
            ElMessage.warning('请选择商品和卡密类型');
            return;
          }

          submitLoading.value = true;
          try {
            const res = await fetch('/agent/index/generateGameCard', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(gameCardForm)
            });

            const data = await handleApiResponse(res);
            if(data.code === 1) {
              // 显示卡密弹窗
              if (data.cdk_codes && data.cdk_codes.length > 0) {
                // 批量生成的情况
                showCdkCodesModal(data.cdk_codes, '游戏卡密');
              } else if (data.cdk_code) {
                // 单个生成的情况
                showSingleCdkModal(data.cdk_code, '游戏卡密');
              }

              ElMessage.success(data.msg);
              closeModal('gameCard');
              await loadCards(pagination.current);
            } else {
              ElMessage.error(data.msg);
            }
          } catch(e) {
            if (e.message !== 'Agent disabled') {
              console.error('当前账户已被封禁，请联系管理员获得支持', e);
              ElMessage.error('当前账户已被封禁，请联系管理员获得支持');
            }
          } finally {
            submitLoading.value = false;
          }
        }

        async function generateMemberCards() {
          if(!memberCardForm.membership_type) {
            ElMessage.warning('请选择会员类型');
            return;
          }

          submitLoading.value = true;
          try {
            const res = await fetch('/agent/index/generateMemberCard', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(memberCardForm)
            });

            const data = await handleApiResponse(res);
            if(data.code === 1) {
              // 显示卡密弹窗
              if (data.cdk_codes && data.cdk_codes.length > 0) {
                // 批量生成的情况
                showCdkCodesModal(data.cdk_codes, '会员卡密');
              } else if (data.cdk_code) {
                // 单个生成的情况
                showSingleCdkModal(data.cdk_code, '会员卡密');
              }

              ElMessage.success(data.msg);
              closeModal('memberCard');
              await loadCards(pagination.current);
            } else {
              ElMessage.error(data.msg);
            }
          } catch(e) {
            if (e.message !== 'Session expired' && e.message !== 'Agent disabled') {
              console.error('当前账户已被封禁，请联系管理员获得支持', e);
              ElMessage.error('当前账户已被封禁，请联系管理员获得支持');
            }
          } finally {
            submitLoading.value = false;
          }
        }

        function handleStatClick(stat) {
          if(stat.title === '卡密总数' || stat.title === '已使用') {
            changePage('cards');
            if(stat.title === '已使用') {
              filterStatus.value = '已使用';
            } else {
              filterStatus.value = '';
            }
          }
        }

        async function loadGoodsList() {
          try {
            goodsLoading.value = true;
            const res = await fetch('/agent/index/goodsList');
            const data = await handleApiResponse(res);
            if(data.code === 1) {
              goodsList.value = data.data;
              filteredGoodsList.value = data.data; // 初始化过滤列表
            }
          } catch(e) {
            if (e.message !== 'Session expired' && e.message !== 'Agent disabled') {
              console.error('加载商品列表失败:', e);
            }
          } finally {
            goodsLoading.value = false;
          }
        }

        async function loadOfflineGoodsList() {
          try {
            offlineGoodsLoading.value = true;
            const res = await fetch('/agent/index/offlineGoodsList');
            const data = await handleApiResponse(res);
            if(data.code === 1) {
              offlineGoodsList.value = data.data;
              filteredOfflineGoodsList.value = data.data;
            }
          } catch(e) {
            if (e.message !== 'Session expired' && e.message !== 'Agent disabled') {
              console.error(e);
            }
          } finally {
            offlineGoodsLoading.value = false;
          }
        }

        async function loadMembershipTypes() {
          try {
            membershipLoading.value = true;
            const res = await fetch('/agent/index/membershipTypes');
            const data = await handleApiResponse(res);
            if(data.code === 1) {
              membershipTypes.value = data.data;
              filteredMembershipTypes.value = data.data; // 初始化过滤列表
            }
          } catch(e) {
            if (e.message !== 'Session expired' && e.message !== 'Agent disabled') {
              console.error('加载会员类型列表失败:', e);
            }
          } finally {
            membershipLoading.value = false;
          }
        }

        // 监听状态变化
        watch(filterStatus, () => {
          if(currentPage.value === 'cards') {
            loadCards(1);
          }
        });

        watch(filterType, () => {
          if(currentPage.value === 'cards') {
            loadCards(1);
          }
        });

        // 添加对会员类型变化的监听
        watch(filterMemberType, () => {
          if(currentPage.value === 'cards') {
            loadCards(1);
          }
        });



        // 编辑卡密（封禁功能）- 直接弹出确认对话框
        async function editCard(card) {
          try {
            const { value: reason } = await ElMessageBox.prompt(
              `<div style="margin-bottom: 15px; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #409eff;">
                <h4 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">📋 卡密信息</h4>
                <p style="margin: 4px 0; color: #666;"><strong>卡密码：</strong>${card.cdk_code}</p>
                <p style="margin: 4px 0; color: #666;"><strong>类型：</strong>${card.cdk_type}</p>
                <p style="margin: 4px 0; color: #666;"><strong>状态：</strong>${card.status}</p>
              </div>
              <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 12px; border-radius: 4px; margin-bottom: 15px;">
                <strong>⚠️ 警告：</strong>封禁操作不可逆！您只能封禁自己生成的卡密！
                ${card.status === '已使用' ? '<br><strong>该卡密已使用，封禁后将扣除用户对应权益！</strong>' : ''}
              </div>`,
              '🚫 确认封禁卡密',
              {
                confirmButtonText: '确认封禁',
                cancelButtonText: '取消',
                inputPlaceholder: '请输入封禁原因（选填）',
                inputType: 'textarea',
                // 移除必填验证，允许空原因
                dangerouslyUseHTMLString: true,
                type: 'warning'
              }
            );

            // 封禁原因可以为空，不需要验证
            submitLoading.value = true;
            const res = await fetch('/agent/Index/banCdk', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: new URLSearchParams({
                cdk_code: card.cdk_code,
                reason: reason ? reason.trim() : '' // 如果没有输入原因，传空字符串
              })
            });

              const data = await handleApiResponse(res);
              if (data.code === 1) {
                ElMessage.success(data.msg);
                loadCards(pagination.current);
              } else {
                ElMessage.error(data.msg);
              }
          } catch (error) {
            if (error !== 'cancel' && error.message !== 'Agent disabled') {
              console.error('封禁失败:', error);
              ElMessage.error('封禁失败，请稍后重试');
            }
          } finally {
            submitLoading.value = false;
          }
        }

        // 关闭封禁弹窗
        function closeBanModal() {
          showBanCardModal.value = false;
          Object.assign(banCardForm, {
            cdk_code: '',
            cdk_type: '',
            status: '',
            reason: ''
          });
        }

        // 批量选择相关方法
        function handleSelectionChange(selection) {
          selectedCards.value = selection;
        }

        // 检查是否可选择（只能选择未封禁的卡密）
        function checkSelectable(row) {
          return !(row.is_banned == 1 || row.is_banned === '1');
        }

        // 批量封禁选中的卡密
        async function batchBanSelected() {
          if (selectedCards.value.length === 0) {
            ElMessage.warning('请先选择要封禁的卡密');
            return;
          }

          if (selectedCards.value.length > 50) {
            ElMessage.error('单次最多只能封禁50张卡密');
            return;
          }

          try {
            const { value: reason } = await ElMessageBox.prompt(
              `<div style="margin-bottom: 15px; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #e6a23c;">
                <h4 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">📋 批量封禁确认</h4>
                <p style="margin: 4px 0; color: #666;"><strong>选中数量：</strong>${selectedCards.value.length} 张卡密</p>
                <p style="margin: 4px 0; color: #666;"><strong>操作类型：</strong>批量封禁</p>
              </div>
              <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 12px; border-radius: 4px; margin-bottom: 15px;">
                <strong>⚠️ 警告：</strong>封禁操作不可逆！您只能封禁自己生成的卡密！
                <br><strong>如果卡密已使用，封禁后将扣除用户对应权益！</strong>
              </div>`,
              '🚫 批量封禁卡密',
              {
                confirmButtonText: '确认封禁',
                cancelButtonText: '取消',
                inputPlaceholder: '请输入封禁原因（选填）',
                inputType: 'textarea',
                dangerouslyUseHTMLString: true,
                type: 'warning'
              }
            );

            // 构建卡密码列表
            const cdkCodes = selectedCards.value.map(card => card.cdk_code).join('\n');

            submitLoading.value = true;
            const res = await fetch('/agent/Index/batchBanCdk', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: new URLSearchParams({
                cdk_codes: cdkCodes,
                reason: reason ? reason.trim() : ''
              })
            });

            const data = await handleApiResponse(res);
            if (data.code === 1) {
              ElMessage.success(data.msg);
              // 清空选择
              selectedCards.value = [];
              // 刷新列表
              loadCards(pagination.current);

              // 显示详细结果
              if (data.data && data.data.details) {
                showBatchResult(data.data);
              }
            } else {
              ElMessage.error(data.msg);
            }

          } catch (error) {
            if (error !== 'cancel' && error.message !== 'Agent disabled') {
              console.error('批量封禁失败:', error);
              ElMessage.error('批量封禁失败，请稍后重试');
            }
          } finally {
            submitLoading.value = false;
          }
        }

        // 显示批量操作结果
        function showBatchResult(result) {
          const successCount = result.success || 0;
          const failedCount = result.failed || 0;
          const details = result.details || [];

          let content = `<div style="margin-bottom: 15px;">
            <h4 style="margin: 0 0 10px 0;">📊 批量封禁结果</h4>
            <p><span style="color: #67C23A;">✅ 成功：${successCount} 张</span></p>
            <p><span style="color: #F56C6C;">❌ 失败：${failedCount} 张</span></p>
          </div>`;

          if (details.length > 0) {
            content += '<div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">';
            details.forEach(item => {
              const icon = item.status === 'success' ? '✅' : '❌';
              const color = item.status === 'success' ? '#67C23A' : '#F56C6C';
              content += `<p style="margin: 4px 0; color: ${color};">${icon} ${item.cdk_code}: ${item.message}</p>`;
            });
            content += '</div>';
          }

          ElMessageBox.alert(content, '批量封禁结果', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定'
          });
        }

        // 打开批量封禁弹窗
        function openBatchBanModal() {
          // 创建一个新窗口打开批量封禁页面
          const batchBanWindow = window.open(
            '/agent/Index/batchBanCdk',
            'batchBanCdk',
            'width=1000,height=700,scrollbars=yes,resizable=yes,status=yes,toolbar=no,menubar=no,location=no'
          );

          if (batchBanWindow) {
            batchBanWindow.focus();

            // 监听窗口关闭事件，刷新当前页面的卡密列表
            const checkClosed = setInterval(() => {
              if (batchBanWindow.closed) {
                clearInterval(checkClosed);
                // 刷新卡密列表
                if (currentPage.value === 'cards') {
                  loadCards(pagination.current);
                }
              }
            }, 1000);
          } else {
            ElMessage.error('无法打开批量封禁窗口，请检查浏览器弹窗设置');
          }
        }

        // 确认封禁卡密
        async function confirmBanCard() {
          // 封禁原因不是必填的，可以为空

          try {
            await ElMessageBox.confirm(
              `确定要封禁卡密 "${banCardForm.cdk_code}" 吗？此操作不可逆！`,
              '确认封禁',
              {
                confirmButtonText: '确认封禁',
                cancelButtonText: '取消',
                type: 'warning',
              }
            );

            submitLoading.value = true;
            const res = await fetch('/agent/Index/banCdk', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: new URLSearchParams({
                cdk_code: banCardForm.cdk_code,
                reason: banCardForm.reason
              })
            });

            const data = await handleApiResponse(res);
            if (data.code === 1) {
              ElMessage.success(data.msg);
              closeBanModal();
              loadCards(pagination.current);
            } else {
              ElMessage.error(data.msg);
            }
          } catch (error) {
            if (error !== 'cancel' && error.message !== 'Agent disabled') {
              console.error('封禁失败:', error);
              ElMessage.error('封禁失败，请稍后重试');
            }
          } finally {
            submitLoading.value = false;
          }
        }

        // 删除卡密
        async function deleteCard(card) {
          try {
            await ElMessageBox.confirm(
              `确定要删除卡密 "${card.cdk_code}" 吗？此操作不可逆！`,
              '确认删除',
              {
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
                type: 'warning',
              }
            );

            submitLoading.value = true;
            const res = await fetch('/agent/Index/deleteCdk', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: new URLSearchParams({
                cdk_code: card.cdk_code
              })
            });

            const data = await handleApiResponse(res);
            if (data.code === 1) {
              ElMessage.success(data.msg);
              loadCards(pagination.current);
            } else {
              ElMessage.error(data.msg);
            }
          } catch (error) {
            if (error !== 'cancel' && error.message !== 'Agent disabled') {
              console.error('删除失败:', error);
              ElMessage.error('删除失败，请稍后重试');
            }
          } finally {
            submitLoading.value = false;
          }
        }

        // 在方法部分添加退出登录函数
        async function logout() {
          try {
            const res = await fetch('/agent/index/logout');
            const data = await handleApiResponse(res);
            if (data.code === 1) {
              ElMessage.success(data.msg || '退出成功');
              setTimeout(() => {
                window.location.href = '/agent/index/login';
              }, 500);
            } else {
              ElMessage.error(data.msg || '退出失败');
            }
          } catch (e) {
            if (e.message !== 'Session expired' && e.message !== 'Agent disabled') {
              console.error('退出失败:', e);
              ElMessage.error('退出失败，请重试');
            }
          }
        }

        // 代理状态检查函数
        async function checkAgentStatus() {
          try {
            const res = await fetch('/agent/index/checkStatus', {
              method: 'GET',
              headers: {
                'X-Requested-With': 'XMLHttpRequest'
              }
            });

            const data = await handleApiResponse(res);
            return data.code === 200;
          } catch (e) {
            if (e.message !== 'Session expired' && e.message !== 'Agent disabled') {
              console.error('状态检查失败:', e);
            }
            return false; // Session过期或代理被禁用时返回false
          }
        }

        onMounted(() => {
          // 加载数据
          loadGoodsList();
          loadOfflineGoodsList();
          loadMembershipTypes();

          // 启动状态检查
          checkAgentStatus();

          // 每30秒检查一次代理状态
          setInterval(checkAgentStatus, 30000);

          // 模拟加载完成
          setTimeout(() => {
            loading.value = false;
            NProgress.done();
          }, 800);
        });

        return {
          loading,
          tableLoading,
          submitLoading,
          currentPage,
          userName,
          userAvatar,
          currentDate,
          stats,
          statColors,
          goodsList,
          offlineGoodsList,
          membershipTypes,
          cardsList,
          pagination,
          filterStatus,
          filterType,
          searchKeyword,
          showGameCardModal,
          showOfflineCardModal,
          showMemberCardModal,
          showBanCardModal,
          gameCardForm,
          offlineCardForm,
          memberCardForm,
          banCardForm,
          timeUnitMap,
          pageTitle,
          changePage,
          showModal,
          closeModal,
          loadCards,
          debounceSearch,
          generateGameCards,
          generateOfflineCards,
          generateMemberCards,
          handleStatClick,
          loadGoodsList,
          loadOfflineGoodsList,
          loadMembershipTypes,
          filterMemberType,
          logout,
          // 下拉框搜索相关
          goodsLoading,
          offlineGoodsLoading,
          membershipLoading,
          filteredGoodsList,
          filteredOfflineGoodsList,
          filteredMembershipTypes,
          remoteSearchGoods,
          remoteSearchOfflineGoods,
          remoteSearchMembership,
          showAllGoods,
          showAllOfflineGoods,
          showAllMembershipTypes,
          editCard,
          deleteCard,
          closeBanModal,
          confirmBanCard,
          // 批量选择相关
          selectedCards,
          handleSelectionChange,
          checkSelectable,
          batchBanSelected,
          showBatchResult,
          openBatchBanModal
        };
      }
    });

    // 获取Element Plus组件和消息通知
    const { ElMessage, ElMessageBox } = ElementPlus;

    // 卡密弹窗显示函数
    function showCdkCodesModal(codes, type) {
      const content = `
        <div style="max-height: 400px; overflow-y: auto;">
          <div style="margin-bottom: 15px; text-align: center;">
            <strong>🎯 生成的${type}列表</strong>
          </div>
          ${codes.map((code, index) => `
            <div style="display: flex; align-items: center; padding: 8px; border: 1px solid #e8e8e8; margin-bottom: 5px; border-radius: 6px; background: #f0f9ff;">
              <span style="flex: 1; font-family: monospace; font-size: 13px; color: #333;">${index + 1}. ${code}</span>
              <button onclick="copyToClipboard('${code}');" style="background: #1890ff; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">📋 复制</button>
            </div>
          `).join('')}
          <div style="text-align: center; margin-top: 15px;">
            <button onclick="copyAllCodes(['${codes.join("','")}']);" style="background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; margin-right: 10px;">📋 复制全部</button>
            <button onclick="closeCdkModal();" style="background: #666; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">关闭</button>
          </div>
        </div>
      `;

      // 创建弹窗
      const modal = document.createElement('div');
      modal.id = 'cdkModal';
      modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); z-index: 9999; display: flex;
        align-items: center; justify-content: center;
      `;

      const modalContent = document.createElement('div');
      modalContent.style.cssText = `
        background: white; padding: 20px; border-radius: 8px;
        max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;
      `;
      modalContent.innerHTML = content;

      modal.appendChild(modalContent);
      document.body.appendChild(modal);
    }

    function showSingleCdkModal(code, type) {
      const content = `
        <div style="text-align: center; padding: 20px;">
          <div style="font-size: 24px; margin-bottom: 15px;">🎯</div>
          <div style="font-size: 18px; color: #1890ff; font-weight: bold; margin-bottom: 15px;">${type}生成成功！</div>
          <div style="margin-bottom: 10px; color: #666;">您的卡密码：</div>
          <div style="padding: 15px; background: #f0f9ff; border: 2px dashed #1890ff; border-radius: 8px; font-family: monospace; word-break: break-all; font-size: 16px; color: #333; margin-bottom: 15px;">
            ${code}
          </div>
          <div style="text-align: center;">
            <button onclick="copyToClipboard('${code}');" style="background: #1890ff; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-size: 14px; margin-right: 10px;">📋 复制卡密</button>
            <button onclick="closeCdkModal();" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-size: 14px;">关闭</button>
          </div>
        </div>
      `;

      // 创建弹窗
      const modal = document.createElement('div');
      modal.id = 'cdkModal';
      modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); z-index: 9999; display: flex;
        align-items: center; justify-content: center;
      `;

      const modalContent = document.createElement('div');
      modalContent.style.cssText = `
        background: white; padding: 20px; border-radius: 8px;
        max-width: 500px; width: 90%;
      `;
      modalContent.innerHTML = content;

      modal.appendChild(modalContent);
      document.body.appendChild(modal);
    }

    // 复制到剪贴板函数
    function copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          ElMessage.success('复制成功！');
        }).catch(() => {
          fallbackCopyTextToClipboard(text);
        });
      } else {
        fallbackCopyTextToClipboard(text);
      }
    }

    function fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        ElMessage.success('复制成功！');
      } catch (err) {
        ElMessage.error('复制失败，请手动复制');
      }

      document.body.removeChild(textArea);
    }

    // 复制全部卡密
    function copyAllCodes(codes) {
      const allCodes = codes.join('\n');
      copyToClipboard(allCodes);
    }

    // 关闭弹窗
    function closeCdkModal() {
      const modal = document.getElementById('cdkModal');
      if (modal) {
        document.body.removeChild(modal);
      }
    }

    // 将函数添加到全局作用域
    window.showCdkCodesModal = showCdkCodesModal;
    window.showSingleCdkModal = showSingleCdkModal;
    window.copyToClipboard = copyToClipboard;
    window.copyAllCodes = copyAllCodes;
    window.closeCdkModal = closeCdkModal;

    // 挂载应用并设置中文语言包
    app.use(ElementPlus, {
      locale: ElementPlusLocaleZhCn
    }).mount('#app');
  </script>
</body>
</html>