<?php

namespace app\admin\controller;

use app\BaseController;
use app\admin\model\System;
use app\admin\model\Config;
use think\facade\View;
use app\admin\model\Goods;
use app\admin\model\Account;
use app\admin\model\Notice;
use app\admin\model\Banner;
use app\admin\model\Offline;
use app\admin\model\User as Us;
use app\admin\model\Workorder;
use app\admin\model\Withdraw;
use app\admin\model\Coupons;
use think\facade\Request;
use think\facade\Db;
use think\facade\Cookie;
use think\facade\Log;
use think\exception\ValidateException;
// 引入 ThinkPHP 的日志门面

/**
 * 管理员主控制器
 *
 * 本控制器负责处理后台管理系统中的各项功能，包括用户管理、订单管理、
 * 系统配置、轮播图管理、公告管理、优惠券管理、工单管理等。
 *
 * @package app\admin\controller
 */
class Index extends BaseController
{
    /**
     * 初始化方法
     *
     * 在控制器实例化时自动调用，用于验证管理员登录状态。
     * 如果管理员未登录或登录状态无效，则重定向到登录页面。
     *
     * @return void
     */
    public function initialize()
{
    // 获取当前登录的管理员用户名和登录状态
    $currentAdminName = Cookie::get("adname");
    $isLogin = Cookie::get("islogin");

    // 检查登录状态
    if (empty($currentAdminName) || $isLogin != 1) {
        // 未登录，重定向到登录页面
        header("Location:/admin/login");
        exit;
    }

    // 验证用户名是否存在于管理员表中
    $admin = Db::name("admin")->where("username", $currentAdminName)->find();
    if (!$admin) {
        // 管理员不存在，清除Cookie并重定向
        Cookie::delete('adname');
        Cookie::delete('islogin');
        Cookie::delete('logintime');
        header("Location:/admin/login");
        exit;
    }
}

     /**
 * 删除邮箱接口方法
 *
 * 此方法用于通过外部API删除一个邮箱账户。
 * 无论远程API调用是否成功，都会在本地数据库中删除该邮箱账户记录。
 *
 * @return \think\response\Json 返回JSON格式的响应数据
 */
public function deleteMailbox()
{
    // 获取请求中的 username 参数（优先从 POST 中获取，未找到则从 GET 中获取）
    $username = input('post.username', input('get.username', ''));

    // 验证 username 是否为空
    if (empty($username)) {
        // 如果 username 参数为空，返回错误提示信息
        return json(['code' => -1, 'msg' => '用户名不能为空']);
    }

    try {
        // 开始数据库事务
        Db::startTrans();

        // 先从数据库中删除用户记录
        $deleteResult = Db::name('userlist')->where('username', $username)->delete();

        // 获取数据库中的配置参数
        $config = Config::find(1);

        // 构造API请求的URL地址
        $url = "http://" . $config['email_api'] . "/plugin?action=a&name=mail_sys&s=delete_mailbox";

        // 构造POST数据
        $postData = http_build_query([
            'username' => $username . '@zuhaom.com', // 邮箱地址
        ]);

        // 初始化cURL
        $ch = curl_init();

        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_ENCODING, "gzip, deflate");

        // 设置HTTP头信息
        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0",
            "Accept: */*",
            "Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Content-Type: application/x-www-form-urlencoded; charset=UTF-8",
            "x-http-token: " . $config['email_tk'],
            "X-Requested-With: XMLHttpRequest",
            "Origin: http://*************:38664",
            "Connection: keep-alive",
            "Referer: http://*************:38664/",
            "Cookie: " . $config['email_ck'],
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // 执行cURL请求 - 但忽略任何错误，因为我们已经删除了数据库记录
        $response = curl_exec($ch);

        // 记录远程API调用的结果，但不影响我们的操作结果
        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            Log::error('远程邮箱API调用失败: ' . $error_msg . '，但数据库记录已删除');
        }

        // 关闭cURL会话
        curl_close($ch);

        // 提交事务
        Db::commit();

        // 返回成功响应数据，无论远程API是否成功
        return json([
            'status' => true,
            'code' => 200,
            'msg' => '邮箱账号删除成功'
        ]);

    } catch (\Exception $e) {
        // 发生异常时回滚事务
        Db::rollback();

        // 记录错误日志
        Log::error('删除邮箱账号失败: ' . $e->getMessage());

        // 返回错误信息
        return json([
            'status' => false,
            'code' => -1,
            'msg' => '删除邮箱账号失败: ' . $e->getMessage()
        ]);
    }
}


    public function addMailbox()
{
    // 获取请求中的 username 参数（优先从 POST 中获取，未找到则从 GET 中获取）
    $username = input('post.username', input('get.username', ''));

    // 验证 username 是否为空
    if (empty($username)) {
        // 如果 username 参数为空，返回错误提示信息
        return json(['error' => 'Username is required.']);
    }

    // 获取数据库中的配置参数
    $config = Config::find(1);

    // 构造API请求的URL地址
    $url = "http://" . $config['email_api'] . "/plugin?action=a&name=mail_sys&s=add_mailbox";

    // 构造POST数据
    $postData = http_build_query([
        'quota' => '1 GB', // 配额为1GB
        'username' => $username . '@zuhaom.com', // 邮箱地址
        'password' => 'qAZ12345.', // 默认密码
        'full_name' => $username, // 用户全名
        'is_admin' => 0, // 是否管理员（0 表示普通用户）
    ]);

    // 初始化cURL
    $ch = curl_init();

    // 设置cURL选项
    curl_setopt($ch, CURLOPT_URL, $url); // 设置请求的URL
    curl_setopt($ch, CURLOPT_POST, true); // 设置请求方法为POST
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData); // 设置POST数据
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回响应而不是直接输出
    curl_setopt($ch, CURLOPT_ENCODING, "gzip, deflate"); // 支持的压缩方式

    // 设置HTTP头信息
    $headers = [
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0",
        "Accept: */*",
        "Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
        "Content-Type: application/x-www-form-urlencoded; charset=UTF-8",
        "x-http-token: " . $config['email_tk'], // 从配置中获取的Token
        "X-Requested-With: XMLHttpRequest",
        "Origin: http://*************:38664",
        "Connection: keep-alive",
        "Referer: http://*************:38664/",
        "Cookie: " . $config['email_ck'], // 从配置中获取的Cookie
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers); // 设置HTTP头

    // 执行cURL请求
    $response = curl_exec($ch);

    // 检查是否有错误发生
    if (curl_errno($ch)) {
        // 获取cURL错误信息
        $error_msg = curl_error($ch);
        curl_close($ch); // 关闭cURL会话
        // 返回错误信息
        return json(['error' => 'cURL Error: ' . $error_msg], 500, [], JSON_PRETTY_PRINT);
    }

    // 关闭cURL会话
    curl_close($ch);

    // 解析API响应的JSON数据
    $jsonData = json_decode($response, true);

    // 检查JSON解析是否成功
    if (json_last_error() !== JSON_ERROR_NONE) {
        // 返回JSON解析错误信息
        return json(['error' => 'JSON Decode Error: ' . json_last_error_msg()], JSON_PRETTY_PRINT);
    }

    if($jsonData['status']==true)
    {

        $datas = array(
            'code' => '200',
            'msg' =>$jsonData['msg']
            );
    }else
    {
       $datas = array(
            'code' => '-1',
            'msg' =>$jsonData['msg']
            );
    }

    // 返回成功响应数据
    return json($datas);
}


    /**
     * 获取邮箱用户列表并同步到数据库
     *
     * 通过调用外部邮箱API获取所有邮箱用户信息，并将新用户同步到本地数据库。
     * 仅插入尚未存在于数据库中的用户记录。
     *
     * 支持的请求方法：GET、POST
     *
     * @return \think\Response 返回同步结果的JSON响应
     */
    public function getEmailUserList()
    {
        // 获取当前请求方法
        $method = Request::method();

        // 支持 GET 和 POST 请求
        if (in_array($method, ['GET', 'POST'])) {

            // 获取配置参数
            $config = Config::find(1);
            $url = "http://" . $config['email_api'] . "/plugin?action=a&name=mail_sys&s=get_all_user";

            // 初始化 cURL 会话
            $ch = curl_init();

            // 设置 cURL 选项
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true); // 设置请求方法为 POST
            curl_setopt($ch, CURLOPT_POSTFIELDS, ''); // 设置请求体为空
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回响应而不是输出
            curl_setopt($ch, CURLOPT_ENCODING, "gzip, deflate"); // 启用压缩响应

            // 设置请求头
            $headers = [
                "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0",
                "Accept: */*",
                "Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
                "Accept-Encoding: gzip, deflate",
                "Referer: http://" . $config['email_api'],
                "x-http-token: " . $config['email_tk'],
                "X-Requested-With: XMLHttpRequest",
                "Origin: http://" . $config['email_api'],
                "Connection: keep-alive",
                "Cookie: " . $config['email_ck'],
                "Priority: u=0",
                "Content-Length: 0",
                "Pragma: no-cache",
                "Cache-Control: no-cache"
            ];
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            // 执行 cURL 请求
            $response = curl_exec($ch);

            // 检查cURL是否有错误
            if ($response === false) {
                $error = curl_error($ch);
                curl_close($ch);
                return json(['error' => 'cURL Error: ' . $error], 500);
            }

            // 关闭 cURL 会话
            curl_close($ch);

            // 尝试解析 JSON 响应
            $responseData = json_decode($response, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                return json(['error' => 'JSON Decode Error: ' . json_last_error_msg(),'data'=>$response], 500);
            }

            // 检查新的响应结构
            if (!isset($responseData['data']) || !is_array($responseData['data'])) {
                return json(['error' => 'Invalid data format received from API.'], 400);
            }

            // 提取data数组
            $userData = $responseData['data'];

            // 开始数据库操作
            try {
                // 开启事务
                Db::startTrans();

                // 获取所有现有的用户名
                $existingUsernames = Db::name('userlist')->column('username');

                // 转换为键值对数组，便于快速查找
                $existingUsernames = array_flip($existingUsernames);

                $newUsers = [];

                foreach ($userData as $user) {
                    // 验证必要字段是否存在
                    if (
                        !isset(
                            $user['full_name'],
                            $user['username'],
                            $user['quota'],
                            $user['created'],
                            $user['modified'],
                            $user['active'],
                            $user['is_admin']
                        )
                    ) {
                        // 可以选择记录日志或跳过该记录
                        continue;
                    }

                    $username = $user['username'];

                    // 检查用户是否已存在
                    if (isset($existingUsernames[$username])) {
                        // 用户已存在，忽略
                        continue;
                    }

                    // 准备新用户数据
                    $newUsers[] = [
                        'full_name' => $user['full_name'],
                        'username' => $username,
                        'quota' => $user['quota'],
                        'created' => $user['created'],
                        'modified' => $user['modified'],
                        'active' => $user['active'],
                        'is_admin' => $user['is_admin'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                }

                // 如果有新用户，执行批量插入
                if (!empty($newUsers)) {
                    Db::name('userlist')->insertAll($newUsers);
                }

                // 提交事务
                Db::commit();

                // 返回原始响应数据
                return json($responseData);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json(['error' => 'Database Error: ' . $e->getMessage()], 500);
            }
        }
    }

        /**
         * 获取表中存在的用户列表
         *
         * 该方法用于获取数据库中已存在的用户列表，支持分页查询。
         * 适用于前端表格展示，如使用 Layui 表格组件。
         *
         * 支持的请求方法：GET
         *
         * @return \think\Response 返回符合 Layui 表格的数据格式的JSON响应
         */
        public function getExistingUsers()
    {
        // 获取当前请求方法
        $method = Request::method();

        // 支持 GET 请求
        if ($method === 'GET') {
            // 获取分页参数（可选）
            $page = Request::get('page', 1);
            $limit = Request::get('limit', 10); // Layui 默认每页 10 条

            try {
                // 获取总记录数
                $total = Db::name('userlist')->count();

                // 获取当前页的数据
                $users = Db::name('userlist')
                    ->page($page, $limit)
                    ->order('id', 'desc') // 按 id 降序排列
                    ->select()
                    ->toArray();

                // 返回符合 Layui 表格的数据格式
                return json([
                    'code'  => 0,          // 0 表示成功
                    'msg'   => '',
                    'count' => $total,     // 总记录数
                    'data'  => $users,     // 数据列表
                ]);
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('Database Error: ' . $e->getMessage());
                return json(['code' => 1, 'msg' => 'Database Error: ' . $e->getMessage()], 500);
            }
        } else {
            // 不支持的请求方法
            return json(['code' => 1, 'msg' => 'Unsupported request method.'], 405);
        }
    }

        /**
         * 获取邮件列表
         *
         * 根据提供的用户名，通过调用外部邮箱API获取该用户的邮件列表。
         *
         * 支持的请求方法：POST、GET
         *
         * @return \think\Response 返回邮件列表数据的JSON响应
         */
        public function getEmaiList()
    {
        // 获取请求中的 username 参数（优先从 POST 中获取，未找到则从 GET 中获取）
        $username = input('post.username', input('get.username', ''));

        // 验证 username 是否为空
        if (empty($username)) {
            return json(['error' => 'Username is required.']);
        }

        // 获取配置参数
        $config = Config::find(1);

        // 请求的URL
        $url = "http://" . $config['email_api'] . "/plugin?action=a&name=mail_sys&s=get_mails";

        // POST数据
        $postData = http_build_query([
            'p' => 1,
            'username' => $username,
        ]);

        // 初始化cURL
        $ch = curl_init();

        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true); // 设置请求方法为 POST
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData); // 设置请求体
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回响应而不是输出
        curl_setopt($ch, CURLOPT_ENCODING, "gzip, deflate"); // 支持压缩

        // 设置HTTP头
        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0",
            "Accept: */*",
            "Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Content-Type: application/x-www-form-urlencoded; charset=UTF-8",
            "x-http-token: " . $config['email_tk'],
            "X-Requested-With: XMLHttpRequest",
            "Origin: http://" . $config['email_api'],
            "Connection: keep-alive",
            "Referer: http://" . $config['email_api'],
            "Cookie: " . $config['email_ck']
        ];


        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // 执行cURL请求
        $response = curl_exec($ch);

        // 检查是否有错误发生
        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            curl_close($ch);
            return json(['error' => 'cURL Error: ' . $error_msg], 500, [], JSON_PRETTY_PRINT);
        }

        // 关闭cURL会话
        curl_close($ch);

        // 解析JSON响应
        $jsonData = json_decode($response, true);

        // 检查JSON解析是否成功
        if (json_last_error() !== JSON_ERROR_NONE) {
            return json(['error' => 'JSON Decode Error: ' . json_last_error_msg()], JSON_PRETTY_PRINT);
        }

        // 格式化并返回JSON数据
        return json($jsonData['data']);
    }

    /**
     * 更新用户的Steam账号
     *
     * 该方法用于更新数据库中指定用户的Steam账号。
     * 支持 POST 请求，接收用户ID和新的Steam账号并保存至数据库。
     *
     * 支持的请求方法：POST
     *
     * @return \think\Response 返回JSON响应，指示操作结果
     */
    public function updateUser()
    {
        // 检查请求方法是否为 POST
        if (Request::isPost()) {
            // 获取POST请求中的数据
            $data = Request::post();

            // 验证必要的字段
            if (!isset($data['id'])) {
                return json(['code' => 1, 'msg' => '用户ID缺失'], 400);
            }

            if (!isset($data['steam_user'])) {
                return json(['code' => 1, 'msg' => 'Steam账号缺失'], 400);
            }

            // 提取用户ID和新的Steam账号
            $userId = intval($data['id']);
            $newSteamUser = trim($data['steam_user']);

            try {
                // 检查用户是否存在
                $user = Db::name('userlist')->where('id', $userId)->find();
                if (!$user) {
                    return json(['code' => 1, 'msg' => '用户不存在'], 404);
                }

                // 更新Steam账号和修改时间
                $updateData = [
                    'steam_user' => $newSteamUser,
                    'modified'   => date('Y-m-d H:i:s'), // 更新时间
                ];

                Db::name('userlist')->where('id', $userId)->update($updateData);

                // 返回成功响应
                return json(['code' => 0, 'msg' => 'Steam账号更新成功']);
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('Update Steam User Error: ' . $e->getMessage());
                return json(['code' => 1, 'msg' => '更新失败: ' . $e->getMessage()], 500);
            }
        } else {
            // 不支持的请求方法
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }
    }


    /**
         * 后台主页
         *
         * 渲染并返回后台管理系统的首页视图。
         *
         * @return \think\View 返回后台首页视图
         */
        public function index()
    {
        // 获取管理员信息
        $admin = Db::name("admin")->find(1);
        return View::fetch('/index', ["admin" => $admin]);
    }

        /**
         * 邮箱用户列表页面
         *
         * 渲染并返回邮箱用户列表的视图。
         *
         * @return \think\View 返回邮箱用户列表视图
         */
        public function EmailUserList()
    {
        return View::fetch('/email/userlist');
    }

        /**
         * 邮件列表页面
         *
         * 渲染并返回邮件列表的视图。
         *
         * @return \think\View 返回邮件列表视图
         */
        public function EmaiList()
    {
        return View::fetch('/email/emaillist');
    }

        /**
         * 控制台主页
         *
         * 获取并返回控制台所需的统计数据
         *
         * @return \think\View
         */
        public function console()
    {
        // 先获取系统信息
        $system = Db::name('system')->where('id', 1)->find();

        // 如果系统信息不存在，创建默认值避免报错
        if (!$system) {
            $system = [
                'sy_title' => '租号系统',
                'version' => '1.0.0',
                'server_info' => 'PHP/'.PHP_VERSION,
                'last_update' => date('Y-m-d H:i:s')
            ];
        }

        // 获取关键业务数据
        $data = [
            // 账号相关统计
            'accounts' => [
                'total' => Db::name('account')->count(), // 总账号数
                'online' => Db::name('account')->where('goods_Type', 1)->count(), // 在线账号
                'rented' => Db::name('account')->where('ac_states', 0)->count(), // 已租用账号
            ],

            // 用户相关统计
            'users' => [
                'total' => Db::name('user')->count(), // 总用户数
                'vip' => Db::name('user')->where('membership_level', '<>', '基础会员')->count(), // VIP用户数
                'today' => Db::name('user')->whereDay('time')->count(), // 今日新增
            ],

            // 工单统计
            'workorders' => [
                'pending' => Db::name('workorder')->where('work_state', 0)->count(), // 待处理工单
                'total' => Db::name('workorder')->count(), // 总工单数
            ],

            // 系统信息
            'system' => $system,  // 使用已获取的系统信息

            // 最近的监控日志
            'monitor_logs' => Db::table('monitor_logs')
                ->order('timestamp', 'desc')
                ->limit(5)
                ->select()
                ->toArray(),

            // 最近的用户操作日志
            'user_logs' => Db::name('userlogo')
                ->alias('l')
                ->join('user u', 'l.uid = u.id', 'LEFT') // 使用LEFT JOIN避免无匹配数据时报错
                ->field('l.*, u.us_name as username')
                ->order('l.time', 'desc')
                ->limit(5)
                ->select()
                ->toArray(),

            // 年度流水数据（示例数据，实际应从订单表统计）
            'yearly_data' => [
                'current_year' => $this->getYearlyData(date('Y')),
                'last_year' => $this->getYearlyData(date('Y')-1)
            ]
        ];

        return View::fetch("/home/<USER>", [
            'data' => $data,
            'system' => $system  // 同时传递系统信息到视图
        ]);
    }

    /**
     * 获取指定年份的月度流水数据
     *
     * @param int $year 年份
     * @return array 月度数据数组
     */
    private function getYearlyData($year)
    {
        $result = [];

        // 尝试从订单表获取每月流水数据
        try {
            for ($month = 1; $month <= 12; $month++) {
                // 构建月份的开始和结束日期
                $startDate = sprintf('%d-%02d-01', $year, $month);
                $endDate = date('Y-m-t', strtotime($startDate));

                // 查询该月的订单总金额
                $monthlyAmount = Db::name('order')
                    ->where('or_state', 1) // 假设状态1表示已支付订单
                    ->whereTime('or_time', 'between', [$startDate, $endDate])
                    ->sum('or_price');

                $result[] = round($monthlyAmount, 2);
            }
        } catch (\Exception $e) {
            // 如果查询失败，使用示例数据
            Log::error('获取年度流水数据失败: ' . $e->getMessage());
            $result = [
                rand(500, 1000), rand(600, 1100), rand(700, 1200),
                rand(800, 1300), rand(900, 1400), rand(1000, 1500),
                rand(900, 1400), rand(800, 1300), rand(700, 1200),
                rand(600, 1100), rand(500, 1000), rand(400, 900)
            ];
        }

        return $result;
    }

        /**
         * 网站设置页面
         *
         * 渲染并返回网站设置的视图，包含系统配置息。
         *
         * @return \think\View 返回网站设置视图
         */
        public function config()
    {
        $system = System::find(1);
        $data = [
            "system" => $system
        ];
        return View::fetch('set/system/website', $data);
    }

        /**
         * 系统配置页面
         *
         * 渲染并返回系统配置的视图，包含配置信息。
         *
         * @return \think\View 返回系统配置视图
         */
        public function systems()
    {
        $config = Config::find(1);
        return View::fetch('set/system/system', ["config" => $config]);
    }

        /**
         * 轮播图配置页面
         *
         * 渲染并返回轮播图配置的视图，包含所有轮播图信息。
         *
         * @return \think\View 返回轮播图配置视图
         */
        public function banner()
    {
        $banner = Banner::select();
        return View::fetch('set/banner/banner', ["banner" => $banner]);
    }

        /**
         * 编辑轮播图配置页面
         *
         * 根据提供的轮播图ID，渲染并返回编辑轮播图的视图。
         *
         * @return \think\View 返回编辑轮播图视图
         */
        public function upBanner()
    {
        // 获取轮播图ID参数
        $data = Request::param("id");
        // 查找对应的轮播图记录
        $banner = Banner::find($data);
        return View::fetch('set/banner/upBanner', ["banner" => $banner]);
    }

        /**
         * 添加轮播图配置页面
         *
         * 渲染并返回添加新轮播图的视图。
         *
         * @return \think\View 返回添加轮播图视图
         */
        public function addBanner()
    {
        return View::fetch('set/banner/addBanner');
    }

        /**
         * 商品分类管理页面
         *
         * 渲染并返回商品分类管理的视图，包含所有商品分类信息。
         *
         * @return \think\View 返回商品分类管理视图
         */
        public function goodsclass()
    {
        return View::fetch('goods/goodsclass');
    }

        /**
         * 编辑商品分类页面
         *
         * 根据提供的分类ID，渲染并返回编辑商品分类的视图
         *
         * @return \think\View 返回编辑商品分类视图
         */
        public function upgoodsClass()
    {
        // 获取请求参数
        $data = Request::param();
        return View::fetch('goods/upgoodsclass', ["goodsclass" => $data]);
    }

        /**
         * 套餐管理页面
         *
         * 渲染并返回套餐管理的视图，包含所有商品信息。
         *
         * @return \think\View 返回套餐管理视图
         */
        public function combo()
    {
        $goodsAll = Goods::select();
        return View::fetch('goods/combo', ["goodsAll" => $goodsAll]);
    }

        /**
         * 添加套餐页面
         *
         * 根据请求参数type决定显示普通套餐还是离线套餐添加页面
         *
         * @return \think\View
         */
        public function addCombo()
    {
        $type = input('type', 'normal');

        // 获取所有商品分类
        $goodsclassAll = Goodsclass::select();

        // 获取所有商品
        $goodsAll = Goods::select();

        return View::fetch('goods/addCombo', [
            'goodsclassAll' => $goodsclassAll,
            'goodsAll' => $goodsAll,
            'type' => $type
        ]);
    }

        /**
         * 编辑套餐页面
         *
         * @return \think\View
         */
        public function upCombo()
    {
        $id = input('id');

        // 获取商品套餐信息和对应的离线价格
        $combo = Db::name('combo')
            ->alias('c')
            ->join('goods g', 'c.co_goods = g.id')
            ->leftJoin('offline o', 'c.co_goods = o.product_id')
            ->field('c.*, g.goods_name, o.product_amount')
            ->where('c.id', $id)
            ->find();

        // 获取所有商品列表供选择
        $goodsAll = Db::name('goods')->select();

        return View::fetch('goods/upCombo', [
            'combo' => $combo,
            'goodsAll' => $goodsAll
        ]);
    }

        /**
         * 全部商品管理页面
         *
         * 渲染并返回全部商品管理的视图，包含所有商品分类信息。
         *
         * @return \think\View 返回全部商品管理视图
         */
        public function goods()
    {
        $goodsclassAll = Goodsclass::select();
        return View::fetch('goods/goods', ["goodsclassAll" => $goodsclassAll]);
    }

        /**
         * 添加商品页面
         *
         * 渲染并返回添加新商品的视图，包含所有商品分类信息。
         *
         * @return \think\View 返回添加商品视图
         */
        public function addGoods()
    {
        try {
            // 获取商品分类
            $goodsclassAll = Goodsclass::select();

            // 获取所有标签
            $tagsList = Db::name('game_tags')
                ->field('id, name as tag_name')
                ->select()
                ->toArray();

            // 明确指定模板文件路径
            return View::fetch('goods/addGoods', [
                'goodsclassAll' => $goodsclassAll,
                'tagsList' => $tagsList
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('加载添加商品页面失败: ' . $e->getMessage());

            // 返回错误视图或消息
            return view('error/index', ['message' => '加载页面时发生错误，请联系管理员']);
        }
    }

        /**
         * 修改商品页面
         *
         * 根据提供的商品ID，渲染并返回修改商品的视图，包含当前商品和所有商品分类信息。
         *
         * @return \think\View 返回修改商品视图
         */
        public function upGoods()
    {
        // 获取商品ID参数
        $data = Request::param("id");
        // 查找对应的商品记录
        $goodsAll = Goods::find($data);
        // 获取所有商品分类信息
        $goodsclassAll = Goodsclass::select();

        // 获取所有标签
        $tagsList = Db::name('game_tags')->select();

        // 获取商品已选标签
        $selectedTags = Db::name('goods_tags')
            ->where('goods_id', $data)
            ->column('goods_id');
        //return json($goodsAll);
        return View::fetch('goods/upgoods', [
            "goodsAll" => $goodsAll,
            "goodsclassAll" => $goodsclassAll,
            "tagsList" => $tagsList,
            "selectedTags" => $selectedTags
        ]);
    }

        /**
         * 全部卡密账号管理页面
         *
         * 渲染并返回全部卡密账号管理的视图，包含所有商品信息。
         *
         * @return \think\View 返回全部卡密账号管理视图
         */
        public function account()
    {
        $goodsAll = Goods::select();
        return View::fetch('account/account', ["goodsAll" => $goodsAll]);
    }

        /**
         * 添加卡密账号页面
         *
         * 渲染并返回添加新卡密账号的视图，包含所有商品信息。
         *
         * @return \think\View 返回添加卡密账号视图
         */
        public function addAccount()
    {
        $goodsAll = Goods::select();
        return View::fetch('account/addAccount', ["goodsAll" => $goodsAll]);
    }

        /**
         * 全部用户管理页面
         *
         * 渲染并返回全部用户管理的视图。
         *
         * @return \think\View 返回全部用户管理视图
         */
        public function user()
    {
        return View::fetch('user/user');
    }

        /**
         * 添加用户页面
         *
         * 渲染并返回添加新用户的视图。
         *
         * @return \think\View 返回添加用户视图
         */
        public function addUser()
    {
        return View::fetch('user/addUser');
    }

        /**
         * 修改用户页面
         *
         * 根据提供的用户ID，渲染并返回修改用户的视图，包含当前用户信息。
         *
         * @return \think\View 返回修改用户视图
         */
        public function upUser()
    {
        // 获取用户ID参数
        $data = Request::param("id");
        // 查找对应的用户记录
        $user = Us::find($data);
        return View::fetch('user/upUser', ["user" => $user]);
    }

        /**
         * 全部订单管理页面
         *
         * 渲染并返回全部订单管理的视图。
         *
         * @return \think\View 返回全部订单管理视图
         */
        public function order()
    {
        return View::fetch('order/index');
    }

        /**
         * 推广设置页面
         *
         * 渲染并返回推广设置的视图，包含相关配置信息。
         *
         * @return \think\View 返回推广设置视图
         */
        public function promotion()
    {
        $config = Config::find(1);
        return View::fetch('promotion/promotion', ["config" => $config]);
    }

        /**
         * 推广记录页面
         *
         * 渲染并返回推广记录的视图。
         *
         * @return \think\View 返回推广记录视图
         */
        public function promotionLogo()
    {
        return View::fetch('promotion/promotionLogo');
    }

        /**
         * 公告管理页面
         *
         * 渲染并返回公告管理的视图。
         *
         * @return \think\View 返回公告管理视图
         */
        public function notice()
    {
        return View::fetch('notice/notice');
    }

        /**
         * 添加公告页面
         *
         * 渲染并返回添加新公告的视图。
         *
         * @return \think\View 返回添加公告视图
         */
        public function addNotice()
    {
        return View::fetch('notice/addNotice');
    }

        /**
         * 编辑公告页面
         *
         * 根据提供的公告ID，渲染并返回编辑公告的视图，包含当前公告内容。
         *
         * @return \think\View 返回编辑公告视图
         */
        public function upNotice()
    {
        // 获取公告ID参数
        $data = Notice::find(Request::param("id"));
        return View::fetch('notice/upNotice', [
            "notice" => $data->getData(),
            "not_content" => $data["not_content"]
        ]);
    }

        /**
         * 优惠券管理页面
         *
         * 渲染并返回优惠券管理的视图。
         *
         * @return \think\View 返回优惠券管理视图
         */
        public function coupons()
    {
        return View::fetch('coupons/coupons');
    }

        /**
         * 添加优惠券页面
         *
         * 渲染并返回添加新优惠券的视图。
         *
         * @return \think\View 返回添加优惠券视图
         */
        public function addCoupons()
    {
        return View::fetch('coupons/addCoupons');
    }

        /**
         * 编辑优惠券页面
         *
         * 根据提供的优惠券ID，渲染并返回编辑优惠券的视图，包含当前优惠券信息。
         *
         * @return \think\View 返回编辑优惠券视图
         */
        public function upCoupons()
    {
        // 获取优惠券ID参数
        $data = Coupons::find(Request::param("id"));
        return View::fetch('coupons/upCoupons', ["coupons" => $data]);
    }

        /**
         * 全部工单管理页面
         *
         * 渲染并返回全部工单管理的视图。
         *
         * @return \think\View 返回全部工单管理视图
         */
        public function workorder()
    {
        return View::fetch('workorder/workorder');
    }

        /**
         * 编辑工单页面
         *
         * 根据提供的工单ID，渲染并返回编辑工单的视图，包含当前工单信息。
         *
         * @return \think\View 返回编辑工单视图
         */
        public function upWorkorder()
    {
        try {
            // 获取工单ID参数
            $id = Request::param("id");

            if (empty($id)) {
                throw new \Exception('工单ID不能为空');
            }

            // 查找工单数据
            $data = Workorder::find($id);

            if (!$data) {
                throw new \Exception('工单不存在，ID: ' . $id);
            }

            return View::fetch('workorder/upworkorder', ["workorder" => $data]);

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('编辑工单页面错误: ' . $e->getMessage(), [
                'id' => Request::param("id"),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 返回错误页面或重定向
            return View::fetch('error/404', [
                'message' => '工单不存在或已被删除',
                'error' => $e->getMessage()
            ]);
        }
    }

        /**
         * 全部提现申请管理页面
         *
         * 渲染并返回全部提现申请管理的视图。
         *
         * @return \think\View 返回全部提现申请管理视图
         */
        public function withdraw()
    {
        return View::fetch('workorder/withdraw');
    }

        /**
         * 编辑提现申请页面
         *
         * 根据提供的提现申请ID，渲染并返回编辑提现申请的视图，包含当前提现申请信息。
         *
         * @return \think\View 返回编辑提现申请视图
         */
        public function upWithdraw()
    {
        // 获取提现申请ID参数
        $data = Withdraw::find(Request::param("id"));
        // 设置提现类型为支付宝（示例，实际应根据业务逻辑设置）
        $data["wi_type"] = "支付宝";
        return View::fetch('workorder/upWithdraw', ["withdraw" => $data]);
    }

        /**
         * 用户登录日志管理页面
         *
         * 渲染并返回用户登录日志管理的视图。
         *
         * @return \think\View 返回用户登录日志管理视图
         */
        public function Userlogin()
    {
        return View::fetch('user/userlogo');
    }

        /**
         * 修改管理员密码页面
         *
         * 渲染并返回修改管理员密码的视图，包含当前管理员信息。
         *
         * @return \think\View 返回修改管理员密码视图
         */
        public function password()
    {
        // 获取管理员信息
        $admin = Db::name("admin")->find(1);
        return View::fetch('set/user/password', ["admin" => $admin]);
    }

    /**
     * 令牌池列表页面
     *
     * 渲染并返回令牌池列表的视图。
     *
     * @return \think\View 返回令牌池列表视图
     */
    public function tokenPool()
    {
        return View::fetch('/email/tokenpool');
    }

    /**
     * 令牌添加页面
     *
     * 渲染并返回令牌添加页面的视图。
     *
     * @return \think\View 返回令牌池列表视图
     */
    public function token_add()
    {
        return View::fetch('/token/add');
    }

    /**
     * 获取Steam令牌池列表数据，支持搜索功能
     *
     * @return \think\Response 返回符合Layui表格的数据格式的JSON响应
     */
    public function getSteamAccountData()
    {
        // 获取分页参数
        $page = Request::get('page', 1);
        $limit = Request::get('limit', 10);

        // 获取搜索关键词
        $searchValue = Request::get('searchValue', '');

        try {
            // 构建查询条件
            $query = Db::name('steamaccountdata');

            // 如果有搜索关键词，添加模糊查询条件
            if (!empty($searchValue)) {
                $query->where(function($q) use ($searchValue) {
                    // 对account_name和bz字段进行模糊查询
                    $q->whereLike('account_name', '%'.$searchValue.'%')
                      ->whereOr('bz', 'like', '%'.$searchValue.'%');
                });
            }

            // 获取总记录数
            $total = $query->count();

            // 获取当前页的数据
            $accounts = $query->page($page, $limit)
                ->order('id', 'desc')
                ->select()
                ->toArray();

            // 返回符合Layui表格的数据格式
            return json([
                'code'  => 0,
                'msg'   => '',
                'count' => $total,
                'data'  => $accounts
            ]);

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取Steam账号数据失败: ' . $e->getMessage());

            // 返回错误信息
            return json([
                'code' => 1,
                'msg' => '获取数据失败: ' . $e->getMessage(),
                'count' => 0,
                'data' => []
            ], 500);
        }
    }

    /**
     * 删除令牌
     *
     * 根据提供的ID从steamaccountdata表中删除应的令牌记录
     *
     * 支持的请求方法：POST
     *
     * @return \think\Response 返回JSON格式的删除操作结果
     */
    public function token_delete()
    {
        // 检查是否为POST请求
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        // 获取要删除的记录ID
        $id = input('post.id');

        // 验证ID是否存在
        if (empty($id)) {
            return json(['code' => 1, 'msg' => 'ID参数缺失']);
        }

        try {
            // 查找记录是否存在
            $token = Db::name('steamaccountdata')->where('id', $id)->find();
            if (!$token) {
                return json(['code' => 1, 'msg' => '令牌记录不存在']);
            }

            // 删除记录
            $result = Db::name('steamaccountdata')->where('id', $id)->delete();

            if ($result) {
                return json(['code' => 0, 'msg' => '删除成功']);
            } else {
                return json(['code' => 1, 'msg' => '删除失败']);
            }

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('删除令牌失败: ' . $e->getMessage());

            // 返回错误信息
            return json(['code' => 1, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 添加Steam令牌到数据库
     *
     * 接收JSON格式的令牌数据，解析并存储到steamaccountdata表中
     * 支持的请求方法：POST
     *
     * @return \think\Response 返回JSON格式的操作结果
     */
    public function addtoken()
    {


        // 检查是否为POST请求
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        // 获取POST数据
        $tokenData = input('post.tokenData');

        // 验证数据是否为空
        if (empty($tokenData)) {
            return json(['code' => 1, 'msg' => '令牌数据不能为空']);
        }

        try {
            // 解析JSON数据
            $data = json_decode($tokenData, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Steam令牌JSON解析失败: ' . json_last_error_msg());
                return json(['code' => 1, 'msg' => 'JSON解析错误: ' . json_last_error_msg()]);
            }

            // 准备要插入的数据
            $insertData = [
                'shared_secret' => $data['shared_secret'] ?? '',
                'serial_number' => $data['serial_number'] ?? '',
                'revocation_code' => $data['revocation_code'] ?? '',
                'uri' => $data['uri'] ?? '',
                'server_time' => $data['server_time'] ?? 0,
                'account_name' => $data['account_name'] ?? '',
                'token_gid' => $data['token_gid'] ?? '',
                'identity_secret' => $data['identity_secret'] ?? '',
                'secret_1' => $data['secret_1'] ?? '',
                'status' => $data['status'] ?? 1,
                'device_id' => $data['device_id'] ?? '',
                'fully_enrolled' => $data['fully_enrolled'] ? 1 : 0,
                'SteamID' => $data['Session']['SteamID'] ?? '',
                'AccessToken' => $data['Session']['AccessToken'] ?? '',
                'RefreshToken' => $data['Session']['RefreshToken'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 开始数据库操作
            Db::startTrans();

            // 检查账号是否已存在
            $exists = Db::name('steamaccountdata')
                ->where('account_name', $insertData['account_name'])
                ->find();

            if ($exists) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $insertData['account_name'].'该Steam账号已存在']);
            }

            // 插入数据
            $result = Db::name('steamaccountdata')->insert($insertData);

            if (!$result) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '数据插入失败']);
            }

            // 提交事务
            Db::commit();

            // 返回成功信息
            return json([
                'code' => 0,
                'msg' => '令牌添加成功'
            ]);

        } catch (\Exception $e) {
            // 发生异常时回滚事务
            Db::rollback();

            // 记录错误日志
            Log::error('添加Steam令牌失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // 返回错误信息
            return json([
                'code' => 1,
                'msg' => '添加失败，请检查日志或联系管理员',
                'erro' =>$e->getMessage()
            ]);
        } catch (\Throwable $t) {
            // 捕获所有其他类型的错误
            Db::rollback();

            // 记录错误日志
            Log::error('添加Steam令牌发生严重错误: ' . $t->getMessage() . "\n" . $t->getTraceAsString());

            // 返回错误信息
            return json([
                'code' => 1,
                'msg' => '系统错误，请联系管理员',
                'erro' =>$t->getMessage()
            ]);
        }
    }

    /**
     * 编辑卡密账号页面
     *
     * 渲染并返回编辑卡密账号的视图，包含账号信息和所有商品信息
     *
     * @param int $id 要编辑的账号ID
     * @return \think\View 返回编辑卡密账号视图
     */
    public function editAccount($id)
    {
        // 获取所有商品信息
        $goodsAll = Goods::select();
        // 获取要编辑的账号信息
        $account = Account::find($id);

        // 确保remarks字段存在且不为null
        if ($account && !isset($account['remarks'])) {
            $account['remarks'] = '';
        }

        return View::fetch('account/editAccount', [
            "goodsAll" => $goodsAll,  // 传递所有商品信息，用于商品下拉选择
            "account" => $account     // 传递当前账号信息
        ]);
    }

    /**
     * 获取第三方绑定数据列表
     *
     * 该方法用于获取数据库中third_party_bindings表的所有数据，支持分页查询。
     * 适用于前端表格展示，使用 Layui 表格组件。
     *
     * @return \think\Response 返回符合 Layui 表格的数据格式的JSON响应
     */
    public function getThirdPartyBindings()
    {
        // 获取分页参数
        $page = input('page', 1);
        $limit = input('limit', 15);

        try {
            // 使用完整表名进行查询
            $tableName = 'third_party_bindings';

            // 获取总记录数
            $total = Db::table($tableName)->count();

            // 获取当前页的数据，并关联用户表获取用户名
            $bindings = Db::table($tableName)
                ->alias('t')
                ->join('user u', 't.user_id = u.id', 'LEFT')  // LEFT JOIN 用户表
                ->field([
                    't.*',  // 保留绑定表的所有字段
                    'u.us_username'  // 添加用户名字段
                ])
                ->page($page, $limit)
                ->order('t.id', 'desc')
                ->select()
                ->toArray();

            // 处理返回的数据，将 user_id 替换为 us_username
            foreach ($bindings as &$binding) {
                $binding['user_id'] = $binding['us_username'] ?? '未知用户';
                unset($binding['us_username']); // 删除多余的字段
            }

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $total,
                'data' => $bindings
            ]);

        } catch (\Exception $e) {
            Log::error('获取第三方绑定数据失败: ' . $e->getMessage());

            return json([
                'code' => 1,
                'msg' => '获取数据失败: ' . $e->getMessage(),
                'count' => 0,
                'data' => []
            ]);
        }
    }

    /**
     * 第三方绑定列表页面
     *
     * 渲染并返回第三方账号绑定列表的视图。
     *
     * @return \think\View 返回第三方绑定列表视图
     */
    public function thirdPartyBindings()
    {
        return View::fetch('user/third_party_bindings');
    }

    /**
     * 离线商品价格定义列表页面
     *
     * 渲染并返回离线商品价格定义列表的视图。
     *
     * @return \think\View 返回离线商品价格定义列表视图
     */
    public function offline()
    {
        // 获取所有离线商品价格定义，并关联商品表获取商品名称
        $offlineProducts = Offline::alias('o')
            ->join('goods g', 'o.product_id = g.id', 'LEFT')
            ->field([
                'o.*',  // 保留离线商品表的所有字段
                'g.goods_name'  // 添加商品名称字段
            ])
            ->select();

        // 渲染视图并传递数据
        return View::fetch('goods/offline', [
            "offlineProducts" => $offlineProducts
        ]);
    }

    /**
     * 添加离线商品页面
     *
     * @return \think\View
     */
    public function addOfflineProduct()
    {
        // 查询所有商品
        $goodsList = Goods::select();

        // 渲染视图并传递商品数据
        return View::fetch('goods/addOfflineProduct', [
            'goodsList' => $goodsList
        ]);
    }

    /**
     * 更新令牌备注
     *
     * 该方法用于更新数据库中指定令牌的备注信息。
     * 支持 POST 请求，接收令牌ID和新的备注信息并保存至数据库。
     *
     * @return \think\Response 返回JSON响应，指示操作结果
     */
    public function updateAccountRemark()
    {
        // 检查请求方法是否为 POST
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        // 获取POST请求中的数据
        $data = Request::post();

        // 验证必要的字段
        if (!isset($data['id'])) {
            return json(['code' => 1, 'msg' => '令牌ID缺失'], 400);
        }

        if (!isset($data['bz'])) {
            return json(['code' => 1, 'msg' => '备注信息缺失'], 400);
        }

        // 提取令牌ID和新的备注信息
        $tokenId = intval($data['id']);
        $newRemark = trim($data['bz']);

        try {
            // 检查令牌是否存在
            $token = Db::name('steamaccountdata')->where('id', $tokenId)->find();
            if (!$token) {
                return json(['code' => 1, 'msg' => '令牌不存在'], 404);
            }

            // 更新备注信息
            Db::name('steamaccountdata')->where('id', $tokenId)->update(['bz' => $newRemark]);

            // 返回成功响应
            return json(['code' => 0, 'msg' => '备注更新成功']);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('更新令牌备注失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '更新失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 更新离线商品价格
     *
     * @return \think\Response 返回JSON响应，指示操作结果
     */
    public function updateOfflineProductPrice()
    {
        // 检查请求方法是否为 POST
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        // 获取POST请求中的数据
        $data = Request::post();

        // 验证必要的字段
        if (!isset($data['id']) || !isset($data['product_amount'])) {
            return json(['code' => 1, 'msg' => '参数缺失'], 400);
        }

        // 提取商品ID和新的价格
        $productId = intval($data['id']);
        $newAmount = floatval($data['product_amount']);

        try {
            // 检查商品是否存在
            $product = Offline::find($productId);
            if (!$product) {
                return json(['code' => 1, 'msg' => '商品不存在'], 404);
            }

            // 更新商品价格
            $product->product_amount = $newAmount;
            $product->save();

            // 返回成功响应
            return json(['code' => 0, 'msg' => '价格更新成功']);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('更新商品价格失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '更新失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取系统日志数据
     *
     * @return \think\Response 返回符合 Layui 表格的数据格式的JSON响应
     */
    public function getSystemLogs()
    {
        // 获取当前请求方法
        $method = Request::method();

        // 仅支持 GET 和 POST 请求
        if ($method === 'GET' || $method === 'POST') {
            // 获取分页参数
            $page = Request::param('page', 1);
            $limit = Request::param('limit', 10);

            // 获取搜索参数 - 支持GET和POST方式
            $searchAccount = Request::param('search_account', '');        // 账号名称搜索
            $searchAction = Request::param('search_action', '');          // 操作类型搜索
            $searchStatus = Request::param('search_status', '');          // 操作状态搜索
            $searchStartTime = Request::param('search_start_time', '');   // 开始时间
            $searchEndTime = Request::param('search_end_time', '');       // 结束时间

            try {
                // 记录搜索参数用于调试
                Log::info('系统日志搜索参数', [
                    'account' => $searchAccount,
                    'action' => $searchAction,
                    'status' => $searchStatus,
                    'start_time' => $searchStartTime,
                    'end_time' => $searchEndTime
                ]);

                // 构建查询条件
                $query = Db::table('monitor_logs');

                // 账号名称模糊搜索
                if (!empty($searchAccount)) {
                    $query->where('account_name', 'like', '%' . $searchAccount . '%');
                }

                // 操作类型筛选 - 使用精确匹配
                if (!empty($searchAction)) {
                    $query->where('action', '=', $searchAction);
                }

                // 操作状态筛选
                if (!empty($searchStatus)) {
                    $query->where('status', $searchStatus);
                }

                // 时间范围筛选
                if (!empty($searchStartTime)) {
                    $query->where('timestamp', '>=', $searchStartTime);
                }
                if (!empty($searchEndTime)) {
                    $query->where('timestamp', '<=', $searchEndTime . ' 23:59:59');
                }

                // 获取总记录数（应用搜索条件）
                $total = $query->count();

                // 获取数据列表（应用搜索条件和分页）
                $logs = $query->page($page, $limit)
                    ->order('id', 'desc')  // 按 id 降序排列
                    ->select()
                    ->toArray();

                // 返回符合 Layui 表格的数据格式
                return json([
                    'code'  => 0,          // 0 表示成功
                    'msg'   => '',         // 消息
                    'count' => $total,     // 总记录数
                    'data'  => $logs,      // 数据列表
                    'search_info' => [     // 搜索信息
                        'account' => $searchAccount,
                        'action' => $searchAction,
                        'status' => $searchStatus,
                        'start_time' => $searchStartTime,
                        'end_time' => $searchEndTime,
                        'has_search' => !empty($searchAccount) || !empty($searchAction) || !empty($searchStatus) || !empty($searchStartTime) || !empty($searchEndTime)
                    ]
                ]);

            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('获取系统日志数据失败: ' . $e->getMessage());

                // 返回错误信息
                return json([
                    'code' => 1,
                    'msg' => '获取数据失败: ' . $e->getMessage(),
                    'count' => 0,
                    'data' => []
                ], 500);
            }
        } else {
            // 不支持的请求方法
            return json([
                'code' => 1,
                'msg' => '不支持的请求方法',
                'count' => 0,
                'data' => []
            ], 405);
        }
    }

    /**
     * 导出系统日志数据
     *
     * @return \think\Response 返回CSV文件下载
     */
    public function exportSystemLogs()
    {
        // 获取当前请求方法
        $method = Request::method();

        // 仅支持 GET 请求
        if ($method === 'GET') {
            // 获取搜索参数
            $searchAccount = Request::get('search_account', '');
            $searchAction = Request::get('search_action', '');
            $searchStatus = Request::get('search_status', '');
            $searchStartTime = Request::get('search_start_time', '');
            $searchEndTime = Request::get('search_end_time', '');

            try {
                // 构建查询条件
                $query = Db::table('monitor_logs');

                // 应用搜索条件
                if (!empty($searchAccount)) {
                    $query->where('account_name', 'like', '%' . $searchAccount . '%');
                }
                if (!empty($searchAction)) {
                    $query->where('action', '=', $searchAction);
                }
                if (!empty($searchStatus)) {
                    $query->where('status', $searchStatus);
                }
                if (!empty($searchStartTime)) {
                    $query->where('timestamp', '>=', $searchStartTime);
                }
                if (!empty($searchEndTime)) {
                    $query->where('timestamp', '<=', $searchEndTime . ' 23:59:59');
                }

                // 获取所有数据（限制最大导出数量）
                $logs = $query->order('id', 'desc')
                    ->limit(10000) // 限制最大导出10000条
                    ->select()
                    ->toArray();

                // 生成CSV内容
                $csvContent = "\xEF\xBB\xBF"; // UTF-8 BOM
                $csvContent .= "ID,账号名称,操作类型,方法类型,操作时间,操作状态,错误信息,改密前密码,改密后密码\n";

                foreach ($logs as $log) {
                    $csvContent .= sprintf(
                        "%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                        $log['id'] ?? '',
                        $log['account_name'] ?? '',
                        $log['action'] ?? '',
                        $log['monitor_type'] ?? '',
                        $log['timestamp'] ?? '',
                        $log['status'] ?? '',
                        str_replace(['"', "\n", "\r"], ['""', ' ', ' '], $log['error_message'] ?? ''),
                        $log['old_password'] ?? '',
                        $log['new_password'] ?? ''
                    );
                }

                // 生成文件名
                $filename = 'system_logs_' . date('Y-m-d_H-i-s') . '.csv';

                // 返回CSV文件
                return response($csvContent, 200, [
                    'Content-Type' => 'text/csv; charset=utf-8',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                    'Cache-Control' => 'max-age=0'
                ]);

            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('导出系统日志失败: ' . $e->getMessage());

                // 返回错误页面
                return json([
                    'code' => 1,
                    'msg' => '导出失败: ' . $e->getMessage()
                ], 500);
            }
        } else {
            return json([
                'code' => 1,
                'msg' => '不支持的请求方法'
            ], 405);
        }
    }

    /**
     * 删除单条系统日志
     *
     * @return \think\Response 返回JSON格式的操作结果
     */
    public function deleteSystemLog()
    {
        // 检查是否为POST请求
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        $id = Request::post('id');

        // 验证参数
        if (empty($id) || !is_numeric($id)) {
            return json(['code' => 1, 'msg' => '无效的日志ID']);
        }

        try {
            // 检查日志是否存在
            $log = Db::table('monitor_logs')->where('id', $id)->find();
            if (!$log) {
                return json(['code' => 1, 'msg' => '日志记录不存在']);
            }

            // 删除日志记录
            $result = Db::table('monitor_logs')->where('id', $id)->delete();

            if ($result) {
                return json(['code' => 0, 'msg' => '删除成功']);
            } else {
                return json(['code' => 1, 'msg' => '删除失败']);
            }

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('删除系统日志失败: ' . $e->getMessage());

            return json(['code' => 1, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 批量删除系统日志
     *
     * @return \think\Response 返回JSON格式的操作结果
     */
    public function batchDeleteSystemLogs()
    {
        // 检查是否为POST请求
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        $ids = Request::post('ids');

        // 验证参数
        if (empty($ids)) {
            return json(['code' => 1, 'msg' => '请选择要删除的日志记录']);
        }

        // 处理ID字符串
        $idArray = explode(',', $ids);
        $idArray = array_filter($idArray, function($id) {
            return is_numeric(trim($id));
        });

        if (empty($idArray)) {
            return json(['code' => 1, 'msg' => '无效的日志ID']);
        }

        try {
            // 批量删除日志记录
            $result = Db::table('monitor_logs')->whereIn('id', $idArray)->delete();

            if ($result) {
                return json(['code' => 0, 'msg' => '批量删除成功，共删除 ' . $result . ' 条记录']);
            } else {
                return json(['code' => 1, 'msg' => '没有记录被删除']);
            }

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('批量删除系统日志失败: ' . $e->getMessage());

            return json(['code' => 1, 'msg' => '批量删除失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取系统日志详情
     *
     * @return \think\Response 返回JSON格式的日志详情
     */
    public function getSystemLogDetail()
    {
        // 检查是否为GET请求
        if (!Request::isGet()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        $id = Request::get('id');

        // 验证参数
        if (empty($id) || !is_numeric($id)) {
            return json(['code' => 1, 'msg' => '无效的日志ID']);
        }

        try {
            // 获取日志详情
            $log = Db::table('monitor_logs')->where('id', $id)->find();

            if ($log) {
                return json(['code' => 0, 'msg' => '获取成功', 'data' => $log]);
            } else {
                return json(['code' => 1, 'msg' => '日志记录不存在']);
            }

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取系统日志详情失败: ' . $e->getMessage());

            return json(['code' => 1, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 清理所有系统日志
     *
     * @return \think\Response 返回JSON格式的操作结果
     */
    public function clearAllSystemLogs()
    {
        // 检查是否为POST请求
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        try {
            // 获取删除前的记录总数
            $totalCount = Db::table('monitor_logs')->count();

            // 清空所有日志记录
            $result = Db::table('monitor_logs')->delete(true);

            if ($result !== false) {
                // 记录管理员操作日志
                Log::info('管理员清理了所有系统日志', [
                    'deleted_count' => $totalCount,
                    'admin_ip' => Request::ip(),
                    'user_agent' => Request::header('User-Agent'),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);

                return json([
                    'code' => 0,
                    'msg' => '系统日志清理成功',
                    'deleted_count' => $totalCount
                ]);
            } else {
                return json(['code' => 1, 'msg' => '清理失败，请重试']);
            }

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('清理系统日志失败: ' . $e->getMessage());

            return json([
                'code' => 1,
                'msg' => '清理失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 系统日志管理页面
     *
     * @return \think\View 返回系统日志管理视图
     */
    public function systemLogs()
    {
        return View::fetch('system/system_logs');
    }

    /**
     * 代理列表页面
     */
    public function agents()
    {
        return View::fetch('agent/list');
    }

    /**
     * 获取代理列表数据
     */
    public function getAgentList()
    {
        $page = input('page', 1);
        $limit = input('limit', 15);

        try {
            $total = Db::name('agents')->count();

            $agents = Db::name('agents')
                ->page($page, $limit)
                ->order('id', 'desc')
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $total,
                'data' => $agents
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '获取数据失败: ' . $e->getMessage(),
                'count' => 0,
                'data' => []
            ]);
        }
    }

    /**
     * 添加/编辑代理
     */
    public function saveAgent()
    {
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $data = input('post.');
        $id = isset($data['id']) ? intval($data['id']) : 0;

        // 处理状态字段 - 开关按钮未选中时为空，需要转换为0
        if (!isset($data['status']) || $data['status'] === '') {
            $data['status'] = 0;
        } else {
            $data['status'] = 1;
        }

        // 验证数据
        try {
            validate([
                'username' => 'require|length:3,50',
                'password' => 'requireIf:id,0|length:6,50',
                'discount_rate' => 'require|float|between:0,100',
                'status' => 'in:0,1',
                'balance'=>'require|float|egt:0',  // 修改为只验证大于等于0，移除上限
            ])->check($data);
        } catch (ValidateException $e) {
            return json(['code' => 1, 'msg' => $e->getError()]);
        }

        // 检查用户名是否已存在（编辑时排除自己）
        $existingAgent = Db::name('agents')->where('username', $data['username']);
        if ($id) {
            $existingAgent = $existingAgent->where('id', '<>', $id);
        }
        if ($existingAgent->find()) {
            return json(['code' => 1, 'msg' => '代理账号已存在']);
        }

        // 处理数据
        $saveData = [
            'username' => $data['username'],
            'discount_rate' => $data['discount_rate'],
            'status' => $data['status'],
            'balance' => $data['balance']
        ];

        // 密码处理 - 只有在密码字段不为空且不为空字符串时才更新密码
        if (!empty($data['password']) && trim($data['password']) !== '') {
            $saveData['password'] = md5($data['password']); // 使用MD5加密密码
        }

        try {
            if ($id) {
                // 编辑
                $saveData['updated_at'] = date('Y-m-d H:i:s');
                Db::name('agents')->where('id', $id)->update($saveData);
            } else {
                // 添加
                $saveData['created_at'] = date('Y-m-d H:i:s');
                $saveData['updated_at'] = date('Y-m-d H:i:s');
                Db::name('agents')->insert($saveData);
            }
            return json(['code' => 0, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取代理信息
     */
    public function getAgentInfo()
    {
        $id = input('id/d');
        $info = Db::name('agents')->where('id', $id)->find();

        // 编辑时不返回密码字段，避免密码被意外修改
        if ($info && isset($info['password'])) {
            unset($info['password']);
        }

        return json(['code' => 0, 'data' => $info]);
    }

    /**
     * 删除代理
     */
    public function deleteAgent()
    {
        $id = input('id/d');

        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        try {
            // 检查代理是否存在
            $agent = Db::name('agents')->where('id', $id)->find();
            if (!$agent) {
                return json(['code' => 1, 'msg' => '代理不存在']);
            }

            // 检查代理是否有关联的卡密记录
            // 使用更安全的方式检查表是否存在
            $cdkCount = 0;
            $checkCdkRelation = true; // 控制是否检查卡密关联

            if ($checkCdkRelation) {
                try {
                    // 首先检查表是否存在
                    $tables = Db::query("SHOW TABLES LIKE 'tk_cdk_cards'");
                    if (empty($tables)) {
                        // 表不存在，跳过检查
                        \think\facade\Log::info('tk_cdk_cards 表不存在，跳过卡密关联检查');
                    } else {
                        // 表存在，进行查询
                        $cdkCount = Db::name('cdk_cards')->where('agent_id', $id)->count();
                    }
                } catch (\Exception $tableException) {
                    // 查询失败，记录日志但继续
                    \think\facade\Log::warning('卡密表查询失败: ' . $tableException->getMessage());
                }
            }

            if ($cdkCount > 0) {
                return json(['code' => 1, 'msg' => "该代理还有 {$cdkCount} 条关联的卡密记录，无法删除"]);
            }

            // 删除代理
            $result = Db::name('agents')->where('id', $id)->delete();

            return json([
                'code' => $result ? 0 : 1,
                'msg' => $result ? '删除成功' : '删除失败'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 修改代理状态
     */
    public function changeAgentStatus()
    {
        $id = input('id/d');
        $status = input('status/d');

        $result = Db::name('agents')->where('id', $id)->update(['status' => $status, 'updated_at' => date('Y-m-d H:i:s')]);

        return json([
            'code' => $result ? 0 : 1,
            'msg' => $result ? '状态更新成功' : '状态更新失败'
        ]);
    }

    /**
     * 代理表单页面
     *
     * 渲染并返回代理添加/编辑的表单视图
     *
     * @return \think\View 返回代理表单视图
     */
    public function agentForm()
    {
        return View::fetch('agent/form');
    }

    /**
     * 会员价格管理页面
     */
    public function membershipPricing()
    {
        // 获取所有会员价格信息
        $pricing = Db::name('membership_pricing')->select();

        // 渲染并返回会员价格管理的视图
        return View::fetch('membership/pricing', ["pricing" => $pricing]);
    }

    // 添加新的会员价格
    public function addMembershipPricing()
    {
        $data = Request::param();
        $result = Db::name('membership_pricing')->insert($data);
        return json(['code' => $result ? 0 : 1, 'msg' => $result ? '添加成功' : '添加失败']);
    }

    // 删除现有的会员价格
    public function deleteMembershipPricing()
    {
        $id = Request::param('id');
        $result = Db::name('membership_pricing')->where('id', $id)->delete();
        return json(['code' => $result ? 0 : 1, 'msg' => $result ? '删除成功' : '删除失败']);
    }

    // 更新现有的会员价格
    public function updateMembershipPricing()
    {
        $data = Request::param();
        $result = Db::name('membership_pricing')->where('id', $data['id'])->update($data);
        return json(['code' => $result ? 0 : 1, 'msg' => $result ? '更新成功' : '更新失败']);
    }

    // 获取所有的会员价格信息
    public function getMembershipPricing()
    {
        $pricing = Db::name('membership_pricing')->select();
        return json(['code' => 0, 'data' => $pricing]);
    }

    /**
     * 会员价格管理页面视图
     *
     * 渲染并返回会员价格管理页面的视图。
     *
     * @return \think\View 返回会员价格管理视图
     */
    public function membershipPricingView()
    {
        return View::fetch('membership/pricing');
    }

    public function goodsTags()
    {
        $goods_id = input('id');

        try {
            // 获取商品的标签关键字
            $goods = Db::name('goods')
                ->where('id', $goods_id)
                ->field('id, goods_name, goods_key')
                ->find();

            // 获取所有可用的标签列表
            $tagsList = Db::name('game_tags')
                ->field('id, name as tag_name')
                ->select()
                ->toArray();

            // 从goods_key字段获取已选标签
            $selectedTags = [];
            if ($goods && !empty($goods['goods_key'])) {
                $selectedTags = explode(',', $goods['goods_key']);
            }
            //return json($selectedTags);
            // 渲染视图并传递数据
            return View::fetch('goods/goods_tags', [
                'goods' => $goods,
                'tagsList' => $tagsList,
                'selectedTags' => $selectedTags
            ]);

        } catch (\Exception $e) {
            // 增强的错误处理
            Log::error('获取商品标签失败: ' . $e->getMessage() . "\n" .
                      '错误文件: ' . $e->getFile() . "\n" .
                      '错误行号: ' . $e->getLine() . "\n" .
                      '堆栈跟踪: ' . $e->getTraceAsString());

            return json([
                'code' => 1,
                'msg' => '获取标签失败',
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTrace()
                ]
            ]);
        }
    }

    /**
     * 从文件导入Steam令牌数据
     *
     * 接收客户端上传的JSON格式令牌数据，解析并存储到steamaccountdata表中
     * 支持的请求方法：POST
     *
     * @return \think\Response 返回JSON格式的操作结果
     */
    public function importTokenFile()
    {
        // 检查是否为POST请求
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        // 获取POST数据
        $tokenData = input('post.tokenData');

        // 验证数据是否为空
        if (empty($tokenData)) {
            return json(['code' => 1, 'msg' => '令牌数据不能为空']);
        }

        try {
            // 解析JSON数据
            $data = json_decode($tokenData, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Steam令牌JSON解析失败: ' . json_last_error_msg());
                return json(['code' => 1, 'msg' => 'JSON解析错误: ' . json_last_error_msg()]);
            }

            // 准备要插入的数据
            $insertData = [
                'shared_secret' => $data['shared_secret'] ?? '',
                'serial_number' => $data['serial_number'] ?? '',
                'revocation_code' => $data['revocation_code'] ?? '',
                'uri' => $data['uri'] ?? '',
                'server_time' => $data['server_time'] ?? 0,
                'account_name' => $data['account_name'] ?? '',
                'token_gid' => $data['token_gid'] ?? '',
                'identity_secret' => $data['identity_secret'] ?? '',
                'secret_1' => $data['secret_1'] ?? '',
                'status' => $data['status'] ?? 1,
                'device_id' => $data['device_id'] ?? '',
                'fully_enrolled' => isset($data['fully_enrolled']) && $data['fully_enrolled'] ? 1 : 0,
                'SteamID' => isset($data['Session']) && isset($data['Session']['SteamID']) ? $data['Session']['SteamID'] : '',
                'AccessToken' => isset($data['Session']) && isset($data['Session']['AccessToken']) ? $data['Session']['AccessToken'] : '',
                'RefreshToken' => isset($data['Session']) && isset($data['Session']['RefreshToken']) ? $data['Session']['RefreshToken'] : '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 开始数据库操作
            Db::startTrans();

            // 检查账号是否已存在
            $exists = Db::name('steamaccountdata')
                ->where('account_name', $insertData['account_name'])
                ->find();

            if ($exists) {
                Db::rollback();
                return json([
                    'code' => 1,
                    'msg' => '该Steam账号 ' . $insertData['account_name'] . ' 已存在',
                    'account' => $exists
                ]);
            }

            // 插入数据
            $result = Db::name('steamaccountdata')->insert($insertData);

            if (!$result) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '数据插入失败']);
            }

            // 提交事务
            Db::commit();

            // 返回成功信息
            return json([
                'code' => 0,
                'msg' => '令牌导入成功'
            ]);

        } catch (\Exception $e) {
            // 发生异常时回滚事务
            Db::rollback();

            // 记录错误日志
            Log::error('导入Steam令牌失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // 返回错误信息
            return json([
                'code' => 1,
                'msg' => '导入失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 批量导入Steam令牌数据
     *
     * 接收多个令牌数据文件，解析并批量存储到steamaccountdata表中
     * 支持的请求方法：POST
     *
     * @return \think\Response 返回JSON格式的操作结果
     */
    public function batchImportTokens()
    {
        // 检查是否为POST请求
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '不支持的请求方法'], 405);
        }

        // 获取POST数据
        $tokenDataArray = input('post.tokenDataArray');

        // 验证数据是否为空
        if (empty($tokenDataArray) || !is_array($tokenDataArray)) {
            return json(['code' => 1, 'msg' => '令牌数据不能为空']);
        }

        $results = [
            'total' => count($tokenDataArray),
            'success' => 0,
            'fail' => 0,
            'existAccounts' => []
        ];

        // 逐个处理令牌数据
        foreach ($tokenDataArray as $tokenData) {
            try {
                // 解析JSON数据
                $data = json_decode($tokenData, true);

                // 检查JSON解析是否成功
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('批量导入: Steam令牌JSON解析失败: ' . json_last_error_msg());
                    $results['fail']++;
                    continue; // 跳过此条数据，继续下一条
                }

                // 准备要插入的数据
                $insertData = [
                    'shared_secret' => $data['shared_secret'] ?? '',
                    'serial_number' => $data['serial_number'] ?? '',
                    'revocation_code' => $data['revocation_code'] ?? '',
                    'uri' => $data['uri'] ?? '',
                    'server_time' => $data['server_time'] ?? 0,
                    'account_name' => $data['account_name'] ?? '',
                    'token_gid' => $data['token_gid'] ?? '',
                    'identity_secret' => $data['identity_secret'] ?? '',
                    'secret_1' => $data['secret_1'] ?? '',
                    'status' => $data['status'] ?? 1,
                    'device_id' => $data['device_id'] ?? '',
                    'fully_enrolled' => isset($data['fully_enrolled']) && $data['fully_enrolled'] ? 1 : 0,
                    'SteamID' => isset($data['Session']) && isset($data['Session']['SteamID']) ? $data['Session']['SteamID'] : '',
                    'AccessToken' => isset($data['Session']) && isset($data['Session']['AccessToken']) ? $data['Session']['AccessToken'] : '',
                    'RefreshToken' => isset($data['Session']) && isset($data['Session']['RefreshToken']) ? $data['Session']['RefreshToken'] : '',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // 检查账号是否已存在
                $exists = Db::name('steamaccountdata')
                    ->where('account_name', $insertData['account_name'])
                    ->find();

                if ($exists) {
                    // 记录已存在的账号
                    $results['fail']++;
                    $results['existAccounts'][] = $insertData['account_name'];
                    continue; // 跳过此条数据，继续下一条
                }

                // 插入数据
                $result = Db::name('steamaccountdata')->insert($insertData);

                if ($result) {
                    $results['success']++;
                } else {
                    $results['fail']++;
                }

            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('批量导入: Steam令牌导入失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                $results['fail']++;
            }
        }

        // 返回处理结果
        return json([
            'code' => 0,
            'msg' => "批量导入完成：成功 {$results['success']} 个，失败 {$results['fail']} 个",
            'data' => $results
        ]);
    }
}
