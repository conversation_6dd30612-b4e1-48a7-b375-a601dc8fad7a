<html>
<head>
<title>PHPMailer FAQ</title>
</head>
<body>
<h2>PHPMailer FAQ</h2>
<ul>
    <li><strong>Q: I am concerned that using include files will take up too much
    processing time on my computer. How can I make it run faster?</strong><br>
    <strong>A:</strong> PHP by itself is fairly fast, but it recompiles scripts every time they are run, which takes up valuable
    computer resources. You can bypass this by using an opcode cache which compiles
    PHP code and store it in memory to reduce overhead immensely. <a href="http://www.php.net/apc/">APC
    (Alternative PHP Cache)</a> is a free opcode cache extension in the PECL library.</li>
    <li><strong>Q: Which mailer gives me the best performance?</strong><br>
    <strong>A:</strong> On a single machine the <strong>sendmail (or Qmail)</strong> is fastest overall.
    Next fastest is mail() to give you the best performance. Both do not have the overhead of SMTP.
    If you do not have a local mail server (as is typical on Windows), SMTP is your only option.</li>
    <li><strong>Q: When I try to attach a file with on my server I get a
    "Could not find {file} on filesystem error". Why is this?</strong><br>
    <strong>A:</strong> If you are using a Unix machine this is probably because the user
    running your web server does not have read access to the directory in question. If you are using Windows,
    then the problem is probably that you have used single backslashes to denote directories (\).
    A single backslash has a special meaning to PHP so these are not
    valid. Instead use double backslashes ("\\") or a single forward
    slash ("/").</li>
</ul>
</body>
</html>
