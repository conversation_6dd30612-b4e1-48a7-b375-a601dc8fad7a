<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索的商品不存在</title>
    <link rel="stylesheet" href="http://cdn.shifen.host/zuhao/kuaijie_login/style.css">
    <script>
        let countdown = 3;

        // 页面跳转逻辑和倒计时
        function updateCountdown() {
            const countdownElement = document.getElementById("countdown");
            countdown -= 1;
            if (countdownElement) {
                countdownElement.textContent = countdown;
            }
            if (countdown === 0) {
                window.location.href = "/";
            }
        }

        document.addEventListener("DOMContentLoaded", () => {
            setInterval(updateCountdown, 1000);
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="icon-success">✔</div>
        <div class="message">搜索的商品不存在！</div>
        <div class="sub-message">页面将在 <span id="countdown">3</span> 秒后自动跳转...</div>
        <div class="progress-bar">
            <div class="progress" id="progress"></div>
        </div>
    </div>
</body>
</html>
