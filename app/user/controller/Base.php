<?php
namespace app\user\controller;

use app\BaseController;
use app\admin\model\System;
use think\facade\View;
use app\admin\model\User as Us;
use think\facade\Request;
use think\facade\Cookie;
use think\facade\Session;
use think\facade\Db;
use app\admin\model\Config;
use app\controller\Email;
use app\controller\SmsController;
use app\admin\model\Order;
use app\admin\model\Combo;
use app\admin\model\Account;
use app\admin\model\Promotion;
use app\user\model\Coupons;
use app\admin\model\Couponslogo;
use app\admin\model\Workorder;
use app\admin\model\Withdraw;
use app\model\CdkCard;
use think\facade\Cache;

class Base extends BaseController
{
    /**
     * 获取系统数据
     *
     * @return \think\response\Json
     */
    public function systemData(){
        return json(System::find(1));
    }

    /**
     * 用户登录
     *
     * 支持账号密码登录
     *
     * @return array|\think\response\Json
     */
    public function login()
    {
        $reqData = Request::param();

        if (empty($reqData["username"])) {
            return json(["msg" => 0, "content" => "请输入账号"]);
        }

        if (empty($reqData["password"])) {
            return json(["msg" => 0, "content" => "请输入密码"]);
        }

        // 查找用户
        $user = Us::where('us_username', $reqData["username"])->find();
        if (!$user) {
            return json(["msg" => 0, "content" => "账号不存在"]);
        }

        // 验证密码
        if ($user['us_password'] !== $reqData["password"]) {
            return json(["msg" => 0, "content" => "密码错误"]);
        }

        // 登录成功,设置安全的Cookie (24小时过期)
        $userExpire = 86400; // 24小时
        Cookie::set("id", $user["id"], $userExpire);
        Cookie::set("login", 1, $userExpire);
        Cookie::set("username", $user["us_username"], $userExpire);

        // 记录登录日志
        Db::name("userlogo")->insert([
            "uid" => $user["id"],
            "content" => "登录成功",
            "ip" => $_SERVER['REMOTE_ADDR'],
            "time" => date("Y-m-d H:i:s")
        ]);

        return json(["msg" => 1, "content" => "登录成功"]);
    }

    /**
     * 用户注册
     *
     * 支持通过手机号注册
     *
     */
    public function register()
    {
        try {
            $reqData = Request::param();

            // 参数验证
            if (empty($reqData["username"])) {
                return json(["msg" => 0, "content" => "请输入手机号"]);
            }

            if (empty($reqData["password"])) {
                return json(["msg" => 0, "content" => "请输入密码"]);
            }

            // 获取短信验证开关配置
            $config = \app\admin\model\Config::find(1);
            $smsVerifySwitch = $config['sms_verify_switch'] ?? 1; // 默认开启

            // 如果短信验证开启，则验证验证码
            if ($smsVerifySwitch == 1) {
                if (empty($reqData["verify_code"])) {
                    return json(["msg" => 0, "content" => "请输入验证码"]);
                }

                // 验证验证码
                $verifyCode = Cookie::get("phoneVerify");
                if (empty($verifyCode)) {
                    return json(["msg" => 0, "content" => "验证码已过期，请重新获取"]);
                }
                if ($reqData["verify_code"] != $verifyCode) {
                    return json(["msg" => 0, "content" => "验证码错误"]);
                }
            }

            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $reqData["username"])) {
                return json(["msg" => 0, "content" => "请输入正确的11位手机号"]);
            }

            // 检查账号是否已存在
            if (Us::where('us_username', $reqData["username"])->find()) {
                return json(["msg" => 0, "content" => "该账号已被注册"]);
            }

            // 密码验证
            if (strlen($reqData["password"]) < 6) {
                return json(["msg" => 0, "content" => "密码长度不能小于6位"]);
            }

            // 频率限制检查
            $ip = Request::ip();
            $key = "register_limit:" . $ip;
            $count = Cache::get($key);

            if ($count && $count >= 5) {
                return json(["msg" => 0, "content" => "注册太频繁,请稍后再试"]);
            }

            // 调用远程注册API
            $apiUrl = "https://xiuluo.zuhaom.com/phone_register.php";
            $params = [
                'type' => 'register',
                'phone' => $reqData["username"],
                'password' => $reqData["password"]
            ];

            try {
                // 初始化 curl
                $ch = curl_init();

                // 设置 curl 选项
                curl_setopt($ch, CURLOPT_URL, $apiUrl . '?' . http_build_query($params));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 忽略 SSL 验证
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 忽略主机验证

                // 执行请求
                curl_exec($ch);
                curl_close($ch);

                // 不验证结果,直接继续本地注册流程
                // 开启事务
                Db::startTrans();
                try {
                    // 创建新用户
                    $data = [
                        "us_name" => $reqData["username"],
                        "us_username" => $reqData["username"],
                        "us_password" => $reqData["password"],
                        "us_email" => '',
                        "us_phone" => $reqData["username"],
                        "us_logo" => "/static/images/us.jpeg",
                        "us_money" => 0,
                        "superior" => $reqData["uid"] ?? 0,
                        "commission" => 0,
                        "us_des" => "这个家伙真懒、什么都不说",
                        "time" => date("Y-m-d H:i:s"),
                    ];

                    $user = Us::create($data);

                    // 处理推广关系
                    if(isset($reqData["uid"])){
                        Promotion::create([
                            "pro_user" => $reqData["uid"],
                            "pro_subuser" => $user["id"],
                            "time" => date("Y-m-d H:i:s"),
                        ]);
                    }

                    // 记录注册日志
                    Db::name("userlogo")->insert([
                        "uid" => $user["id"],
                        "content" => "注册成功",
                        "ip" => $ip,
                        "time" => date("Y-m-d H:i:s")
                    ]);

                    // 更新注册频率限制
                    Cache::set($key, ($count ? $count + 1 : 1), 600);

                    // 提交事务
                    Db::commit();

                    // 清除验证码
                    Cookie::delete('phoneVerify');

                    return json(["msg" => 1, "content" => "注册成功"]);

                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    throw $e;
                }
            } catch (\Exception $e) {
                trace('远程注册异常: ' . $e->getMessage(), 'error');
                return json(["msg" => 0, "content" => "注册失败，请稍后重试"]);
            }

        } catch (\Exception $e) {
            trace('注册异常: ' . $e->getMessage(), 'error');
            return json(["msg" => 0, "content" => "注册失败，请稍后重试"]);
        }
    }

    /**
     * 验证方法
     *
     * 支持通过手机号获取验证码
     *
     * @return \think\response\Json
     */
    public function verify() {
        // 获取请求参数
        $username = Request::param("username");
        $geetest_challenge = Request::param("geetest_challenge");
        $geetest_validate = Request::param("geetest_validate");
        $geetest_seccode = Request::param("geetest_seccode");
        $actionType = Request::param("actionType"); // 获取请求类型

        // 参数校验
        if (empty($username)) {
            return json(["msg" => 0, "content" => "手机号不能为空"]);
        }

        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $username)) {
            return json(["msg" => 0, "content" => "请输入正确的11位手机号"]);
        }

        // 参数完整性校验
        if (empty($geetest_challenge) || empty($geetest_validate) || empty($geetest_seccode)) {
            return json(["msg" => 0, "content" => "验证参数不完整"]);
        }

        // 根据请求类型进行不同的验证
        if ($actionType === "register" || $actionType === "bind") {
            if ($actionType === "register") {
                // 验证手机号是否已注册
                $user = Us::where('us_username', $username)->find();
                if ($user) {
                    return json(["msg" => 0, "content" => "该手机号已注册"]);
                }
            }
            // bind操作不需要检查是否已注册，因为我们就是要绑定一个已经登录的第三方账号
        }

        // 请求验证服务
        $postData = http_build_query([
            'username' => $username,
            'geetest_challenge' => $geetest_challenge,
            'geetest_validate' => $geetest_validate,
            'geetest_seccode' => $geetest_seccode
        ]);

        $url = "https://gee.zuhaom.com/validate";

        try {
            // 设置请求上下文
            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => "Content-Type: application/x-www-form-urlencoded\r\n"
                        . "Content-Length: " . strlen($postData) . "\r\n",
                    'content' => $postData,
                ]
            ]);

            // 生成随机验证码，设置10分钟过期
            $randstr = $this->GetRandStr(4);
            Cookie::set("phoneVerify", $randstr, 600); // 10分钟过期

            // 发起请求
            $response = file_get_contents($url, false, $context);
            $result = json_decode($response, true);

            // 验证响应结果
            if ($result['result'] !== 'success') {
                return json(["msg" => 0, "content" => "数据验证失败"]);
            }

            // 获取短信验证开关配置
            $config = \app\admin\model\Config::find(1);
            $smsVerifySwitch = $config['sms_verify_switch'] ?? 1; // 默认开启

            // 如果需要短信验证
            if ($smsVerifySwitch == 1) {
                // 使用 SmsController 发送短信
                $smsController = new SmsController();
                $smsResponse = $smsController->sendLoginCode($username, $randstr);

                if ($smsResponse['status'] !== 'success') {
                    return json(["msg" => 0, "content" => "短信发送失败: " . $smsResponse['message']]);
                }

                // 返回成功响应，并标记需要短信验证
                return json(["msg" => 1, "content" => "获取成功", "sms_required" => true]);
            } else {
                // 短信验证关闭，直接返回成功
                return json(["msg" => 1, "content" => "验证成功", "sms_required" => false]);
            }

        } catch (\Exception $e) {
            // 捕获异常并返回错误信息
            return json(["msg" => 0, "content" => "请求失败，请稍后再试", "error" => $e->getMessage()]);
        }
    }

    /**
     * 生成随机数
     *
     * @param int $length
     * @return string
     */
    public function GetRandStr($length){
        $str = '0123456789';
        $len = strlen($str) - 1;
        $randstr = '';
        for($i = 0; $i < $length; $i++){
            $num = mt_rand(0, $len);
            $randstr .= $str[$num];
        }
        return $randstr;
    }

/**
     * 获取用户卡密
     *
     * @return \think\response\Json 返回处理后的数据或空数组
     */
    public function usOrder()
    {
        // 验证用户身份
        $userId = Cookie::get("id");
        if (!$userId) {
            return json(['msg' => 0, 'content' => '用户未登录']);
        }

        $id = Request::param("id");
        $order = Order::where("id", $id)->find();
        if (!$order) {
            return json(['error' => '订单不存在']);
        }

        // 确认订单属于当前用户
        if ($order["ord_uid"] != $userId) {
            return json(['msg' => 0, 'content' => '无权访问此订单']);
        }

        // 获取所有订单和账号
        $data = [
            "ord_name" => $order["ord_name"],
            "ord_uid" => $order["ord_uid"],
            "ord_ifpay" => $order["ord_ifpay"],
            "ord_combo" => $order["ord_combo"],
            "or_maturity" => $order["or_maturity"],
        ];

        $order2 = Order::where($data)->select()->toArray(); // 转换为数组
        $accounts = Account::where([
            "ac_uid" => $order["ord_uid"],
            "id" => $order["ord_aid"]
        ])->select()->toArray();
        return json($accounts);

        // 调试输出
        // trace('订单数据：' . json_encode($order2));
        // trace('账号数据：' . json_encode($accounts));

        if (empty($accounts)) {
            return json([]);
        }

        $system = System::find(1);
        foreach ($accounts as $key => &$account) {  // 使用引用
            if (isset($order2[$key])) {
                $currentOrder = $order2[$key];

                try {
                    if ($currentOrder["ord_type"] === 'CDK兑换') {
                        $cdkData = CdkCard::where("assigned_user", $currentOrder["ord_uid"])->find();
                        $time = $cdkData ? $cdkData["expiry_date"] : null;
                    } else {
                        $apiUrl = $system["sy_url"] . "/api/outDate?id={$currentOrder['id']}";
                        $time = @file_get_contents($apiUrl);
                    }

                    if ($time) {
                        $account["time"] = is_numeric($time) ? $this->timer($time) : $time;
                    } else {
                        $account["time"] = "时间获取失败";
                    }
                } catch (\Exception $e) {
                    $account["time"] = "处理异常：" . $e->getMessage();
                }
            } else {
                $account["time"] = "无对应订单";
            }
        }

        return json($accounts);
    }

    private function timer($timestamp)
    {
        if (!is_numeric($timestamp)) {
            return "时间格式错误";
        }

        $t = $timestamp - time();
        if ($t <= 0) {
            return "已过期";
        }

        $day = floor($t / 86400);
        $hour = floor(($t % 86400) / 3600);
        $minute = floor(($t % 3600) / 60);
        $second = $t % 60;

        return "{$day}天{$hour}时{$minute}分{$second}秒";
    }

//   获取令牌
   public function getAccountToken()
{
    // 验证用户身份
    $userId = Cookie::get("id");
    if (!$userId) {
        return json(['msg' => 0, 'content' => '用户未登录']);
    }

    // 检查令牌开关是否开启
    $config = Config::find(1);
    if (!$config || $config['token_switch'] != 1) {
        return json(['msg' => 0, 'content' => '令牌功能已关闭']);
    }

    // 获取订单ID
    $id = Request::param("id");
    if (!$id) {
        return json(['msg' => 0, 'content' => '订单ID不能为空']);
    }

    // 查询订单信息
    $order = Order::find($id);
    if (!$order) {
        return json(['msg' => 0, 'content' => '订单不存在']);
    }

    // 查询账号信息
    $account = Account::find($order['ord_aid']);
    if (!$account) {
        return json(['msg' => 0, 'content' => '账号不存在']);
    }

    // 验证账号所有权
    if ($account['ac_uid'] != $userId) {
        return json(['msg' => 0, 'content' => '无权访问此账号']);
    }

    // 使用北京时间进行到期时间判断
    date_default_timezone_set('Asia/Shanghai');
    $currentTime = time();
    if (isset($account['exit_time']) && !empty($account['exit_time']) && $account['exit_time'] < $currentTime) {
        return json(['msg' => 0, 'content' => '账号已过期']);
    }

    // 查询账号令牌信息
    $accountName = $account['ac_name'];
    $tokenInfo = Db::name('steamaccountdata')->where('account_name', $accountName)->find();
    if (!$tokenInfo || empty($tokenInfo['shared_secret'])) {
        return json(['msg' => 0, 'content' => '当前账号没有添加令牌信息，无法提取，请联系管理员']);
    }

    // 请求令牌API
    try {

        $apiUrl = $config['token_api'] . "api/v1/steam_secret";
        $postData = json_encode(['shared_secret' => $tokenInfo['shared_secret']]);

        $options = [
            'http' => [
                'header'  => "Content-Type: application/json\r\n" .
                             "Content-Length: " . strlen($postData) . "\r\n",
                'method'  => 'POST',
                'content' => $postData,
            ],
        ];

        $context  = stream_context_create($options);
        $token = file_get_contents($apiUrl, false, $context);

        if (!$token) {
            throw new \Exception('获取令牌失败');
        }

        // 解析 JSON 响应
        $responseData = json_decode($token, true);

        // 检查解析是否成功以及是否存在 'secret' 字段
        if (isset($responseData['secret'])) {
            $token = $responseData['secret'];
        } else {
            throw new \Exception('获取令牌失败，响应中没有 secret 字段');
        }

        // 记录令牌获取日志
        Db::name("userlogo")->insert([
            "uid" => $userId,
            "content" => "获取在线账号令牌成功",
            "ip" => Request::ip(),
            "time" => date("Y-m-d H:i:s")
        ]);

        return json([
            'msg' => 1,
            'content' => '获取成功',
            'data' => ['token' => $token]
        ]);
    } catch (\Exception $e) {
        return json([
            'msg' => 0,
            'content' => '获取令牌失败：' . $e->getMessage()
        ]);
    }
}



//   获取VIP令牌
public function getVIipAccountToken()
{
    // 检查令牌开关是否开启
    $config = Config::find(1);
    if (!$config || $config['token_switch'] != 1) {
        return json(['msg' => 0, 'content' => '令牌功能已关闭']);
    }

    // 获取系统设置的令牌限制次数
    $system = Config::find(1);
    if (!$system) {
        return json(['msg' => 0, 'content' => '系统配置错误']);
    }
    $tokenLimit = $system['sy_token_limit'];

    // 获取账号ID
    $id = Request::param("id");
    if (!$id) {
        return json(['msg' => 0, 'content' => '账号ID不能为空']);
    }

    // 查询账号信息
    $account = Account::find($id);
    if (!$account) {
        return json(['msg' => 0, 'content' => '账号不存在']);
    }

    // 验证账号所有权
    $userId = Cookie::get("id");
    if ($account['ac_uid'] != $userId) {
        return json(['msg' => 0, 'content' => '无权访问此账号']);
    }

    // 使用北京时间进行到期时间判断
    date_default_timezone_set('Asia/Shanghai');
    $currentTime = time();
    if (isset($account['exit_time']) && !empty($account['exit_time']) && $account['exit_time'] < $currentTime) {
        return json(['msg' => 0, 'content' => '账号已过期']);
    }

    // 检查令牌提取次数限制
    if ($account['token_limit'] >= $tokenLimit) {
        return json(['msg' => 0, 'content' => '已达到令牌提取次数限制']);
    }

    // 查询账号令牌信息
    $accountName = $account['ac_name'];
    $tokenInfo = Db::name('steamaccountdata')->where('account_name', $accountName)->find();
    if (!$tokenInfo || empty($tokenInfo['shared_secret'])) {
        return json(['msg' => 0, 'content' => '当前账号没有添加令牌信息，无法提取，请联系管理员']);
    }

    // 请求令牌API
    try {
        $apiUrl = $config['token_api'] . "api/v1/steam_secret";
        $postData = json_encode(['shared_secret' => $tokenInfo['shared_secret']]);

        $options = [
            'http' => [
                'header'  => "Content-Type: application/json\r\n" .
                             "Content-Length: " . strlen($postData) . "\r\n",
                'method'  => 'POST',
                'content' => $postData,
            ],
        ];

        $context  = stream_context_create($options);
        $token = file_get_contents($apiUrl, false, $context);

        if (!$token) {
            throw new \Exception('获取令牌失败');
        }

        // 解析 JSON 响应
        $responseData = json_decode($token, true);

        // 检查解析是否成功以及是否存在 'secret' 字段
        if (isset($responseData['secret'])) {
            $token = $responseData['secret'];
        } else {
            throw new \Exception('获取令牌失败，响应中没有 secret 字段');
        }

        // 更新账号的令牌提取次数
        $account->token_limit = $account->token_limit + 1;
        $account->save();

        // 记录令牌获取日志
        Db::name("userlogo")->insert([
            "uid" => $userId,
            "content" => "获取VIP账号令牌成功",
            "ip" => Request::ip(),
            "time" => date("Y-m-d H:i:s")
        ]);

        return json([
            'msg' => 1,
            'content' => '获取成功',
            'data' => ['token' => $token]
        ]);
    } catch (\Exception $e) {
        return json([
            'msg' => 0,
            'content' => '获取令牌失败：' . $e->getMessage()
        ]);
    }
}


       //当前用户所有可用的优惠卷
   public function getUserCouponsAll(){
        $coAll=Coupons::select();
        $ulAll=Couponslogo::where("co_user",Cookie::get("id"))->select();//获取的优惠卷记录 co_id
           $coArr=[];//当前用户领取未使用过的优惠卷
           foreach ($ulAll as $index=>$val){
               array_push($coArr,Coupons::where(["id"=>$val["co_id"],"coupons_use"=>0])->find());
           }
          $arr=[];
          foreach($coArr as $index=>$val){
              if($val!=null){
                    array_push($arr,[
                  "id"=>$val["id"],//优惠卷id
                  "coupons_time"=>$val["coupons_time"],//优惠卷对应套餐天数
                   "coupon_value"=>$val["coupon_type"]==1?$val["coupon_value"]."元":(10-$val["coupon_value"]/10)."折",//优惠金额、几折
                   "coupons_typeuser"=>$val["coupons_typeuser"]==0?"新用户":"新老用户",//优惠用户类型
                  "coupons_date"=>$val["coupons_date"],//有效期
                  "time"=>$val["time"],//有效期
                  ]);
              }
          }
          $data=[
              "Unused"=>$arr,//领取了但未使用的优惠卷
              "Used"=>1,//已经使用的优惠卷
              "expired"=>1,//未使用的优惠卷
              ];
              return json($data);
   }

   //获取所有优惠卷
   public function couponsList(){
       $Coupons=Coupons::where("co_receive",0)->select();
       $arr=[];
       foreach ($Coupons as $index=>$val){
           array_push($arr,[
               "id"=>$val["id"],//优惠卷id
               "coupons_time"=>$val["coupons_time"],//优惠卷对应套餐天数
               "coupon_value"=>$val["coupon_type"]==1?$val["coupon_value"]."元":(10-$val["coupon_value"]/10)."折",//优惠额、几折
              "coupons_typeuser"=>$val["coupons_typeuser"]==0?"新用户":"新老用户",//优惠用户类型
               "coupons_date"=>$val["coupons_date"],//有效期
               ]);
       }
       return json($arr);
   }
   //用户领取优惠卷
   public function  getCoupons(){
       $userId=Cookie::get("id");
       $id=Request::param();
       //查询领取过的优惠卷
       if(isset($id["getuserlist"])){
           $coAll=Coupons::select();
           $ulAll=Couponslogo::where("co_user",$userId)->select();//获取的优惠卷记录 co_id

           $coArr=[];//使用过的优惠卷
           foreach ($ulAll as $index=>$val){
               array_push($coArr,Coupons::where(["id"=>$ulAll[$index]["co_id"]])->find());
           }
          $arr=[];
          foreach($coArr as $index=>$val){
            //   全部未使用的优惠卷
              if($coArr[$index]["coupons_use"]==0){
                  array_push($arr,[
                  "id"=>$val["id"],//优惠卷id
                  "coupons_time"=>$val["coupons_time"],//优惠卷对应套餐天数
                   "coupon_value"=>$val["coupon_type"]==1?$val["coupon_value"]."元":(10-$val["coupon_value"]/10)."折",//优惠金额、几折
                   "coupons_typeuser"=>$val["coupons_typeuser"]==0?"新用户":"新老用户",//优惠用户类型
                  "coupons_date"=>$val["coupons_date"],//有效期
                //   "time"=>date("Y-m-d H:i:s", (strtotime($val["time"])+$val["coupons_date"]*24*3600)),//有效期
                  "time"=>$this->timer(date("Y-m-d H:i:s", (strtotime($val["time"])+$val["coupons_date"]*24*3600))),//有效期
                  ]);
              }
          }
          $arr2=[];
          foreach($coArr as $index=>$val){
            //   已使用的优惠卷
              if($coArr[$index]["coupons_use"]==1){
                  array_push($arr2,[
                  "id"=>$val["id"],//优惠卷id
                  "coupons_time"=>$val["coupons_time"],//优惠卷对应套餐天数
                   "coupon_value"=>$val["coupon_type"]==1?$val["coupon_value"]."元":(10-$val["coupon_value"]/10)."折",//优惠金额、几折
                   "coupons_typeuser"=>$val["coupons_typeuser"]==0?"新用户":"新老用户",//优惠用户类型
                  "coupons_date"=>$val["coupons_date"],//有效期
                  "time"=>$val["time"],//有效期
                  ]);
              }
          }
          $arr3=[];
          foreach($coArr as $index=>$val){
            //   已过期的优惠卷
              if($coArr[$index]["coupons_use"]==3){
                  array_push($arr3,[
                  "id"=>$val["id"],//优惠卷id
                  "coupons_time"=>$val["coupons_time"],//优惠卷对应套餐天数
                   "coupon_value"=>$val["coupon_type"]==1?$val["coupon_value"]."元":(10-$val["coupon_value"]/10)."折",//优惠金额、几折
                   "coupons_typeuser"=>$val["coupons_typeuser"]==0?"新用户":"新老用户",//优惠用户类型
                  "coupons_date"=>$val["coupons_date"],//有效期
                  "time"=>$val["time"],//有效期
                  ]);
              }
          }
          $data=[
              "Unused"=>$arr,//领取了但未使用的优惠卷
              "Used"=>$arr2,//已经使用的优惠卷
              "expired"=>$arr3,//已过期的优惠卷
              ];
              return json($data);
       }
        $uid=Couponslogo::where("co_id",$id["id"])->find();//优惠卷领取日志记录
        if($uid){
            return "当前优惠卷已领取";
        }

       $Coupons=Coupons::find($id["id"]);//当前选中的优惠卷
    //   查询是否用过优惠卷
        if($Coupons["coupons_typeuser"]==0){
            if(Couponslogo::where("co_user",$userId)->find()!=null){
                return "已领取过";
            }
        }
    //自己领取的所有优惠卷
    $coArr=[];
    $coupAll=Couponslogo::where("co_user",$userId)->select();
    foreach ($coupAll as $val){
        array_push($coArr,Coupons::where("id",$val["co_id"])->find());
    }
    $arrA=[]; //领取了还未使用的优惠卷的天数
    foreach ($coArr as $val){
        if($val["coupons_use"]==0){
            array_push($arrA,$val["coupons_time"]);
        }
    }
    foreach ($coArr as $val){
        foreach ($arrA as $va){
            if($val["coupons_time"]==$va){
                return "已领取过其他优惠卷，使用后再来领取";
            }
        }
    }
       Coupons::where("id",$id["id"])->update(["co_receive"=>1,"time"=>date("Y-m-d H:i:s")]);
       Couponslogo::create(["co_id"=>$id["id"],"co_user"=>$userId,"time"=>date("Y-m-d H:i:s")]);
       return json("领取成功");
   }

   //前台获取当前套餐优惠卷
   public function getUserCoupons(){
       $id=Request::param();//当前套餐id
       //使用优惠卷码
       if(isset($id["comboId"]) && isset($id["yhjm"])){
            		 $coID=Combo::find($id["comboId"])["co_money"];//当前套餐的价格
            		 $tc=Combo::find($id["comboId"]);//当前套餐
            		 $coupID=Coupons::where("coupon_code",$id["yhjm"])->find();//优惠卷的类型
            		 if(!$coupID){
            		     return json(["state"=>"error","content"=>"该优惠卷不存在"]);
            		 }
            		 if($tc["co_date"]!=$coupID["coupons_time"]){
            		     return json(["id"=>$coupID["id"],"state"=>0,"type"=>$coupID["coupons_time"]]);
            		 }

            		 if($coupID["coupon_type"]==1){
            		     return json(["id"=>$coupID["id"],"money"=>$coID-$coupID["coupon_value"],"zk"=>$coupID["coupon_value"]]);
            		 }
            		 if($coupID["coupon_type"]==2){
            		     return json(["id"=>$coupID["id"],"money"=>$coID-($coID*$coupID["coupon_value"]/100),"zk"=>$coID*$coupID["coupon_value"]/100]);
            		 }
       }
       //判断当前优惠了多少，优惠后的价格
       if(isset($id["comboId"]) && isset($id["yhjId"])){
            		 $coID=Combo::find($id["comboId"])["co_money"];//当前套餐的价格
            		 $coupID=Coupons::find($id["yhjId"]);//优惠卷的类型
            		 if($coupID["coupon_type"]==1){
            		     return json(["money"=>$coID-$coupID["coupon_value"],"zk"=>$coupID["coupon_value"]]);
            		 }
            		 if($coupID["coupon_type"]==2){
            		     return json(["money"=>$coID-($coID*$coupID["coupon_value"]/100),"zk"=>$coID*$coupID["coupon_value"]/100]);
            		 }
       }

      $coID=Combo::find($id)["co_date"];//当前套餐的天数
       $data=$this->getUserCouponsAll()["Unused"];
      $arr=[];
      foreach ($data as $index=>$val){
          if($data[$index]["coupons_time"]==$coID){
              array_push($arr,$val);
          }
      }
      return json($arr);
   }
   public function workorder(){
       $data=Request::param();
        // 前台查看工单详情
        if(isset($data["id"])){
            $list=Workorder::where("id",$data["id"])->find();
            return json($list);
        }
       $data["work_user"]=Cookie::get("id");
       $data["work_state"]=0;
       $data["time"]=date("Y-m-d H:i:s");
       Workorder::create($data);
       return json(1);
   }
   //佣金转余额
//   public function workorderMoney(){
//       $idList=new Workorder();
//       return json($idList->index($id="10"));
//   }
   //用户提现+佣金划入余额
   public function txMoney(){
       $reqData=Request::param();
       $uid=Cookie::get("id");
      $u1=Us::find($uid);
       //划转
       if(isset($reqData["hz"])){
          if(($u1["commission"]-$reqData["hz"])<=0){
              return json("佣金额不足");
          }
          $data=[
              "us_money"=>$u1["us_money"]+$reqData["hz"],
              "commission"=>$u1["commission"]-$reqData["hz"],
              ];
          Us::where("id",$uid)->update($data);
          return json("转出成功");
       }
       //提现
       if(isset($reqData["zfbusername"]) && isset($reqData["zfbname"])){
            if($u1["commission"]<=0){
               return json("佣金余额不足");
             }
           $data=[
               "wi_uid"=>$uid,
               "wi_money"=>$u1["commission"],
               "wi_state"=>0,
               "wi_type"=>"alipay",
               "wi_username"=>$reqData["zfbusername"],
               "wi_content"=>$reqData["zfbname"],
               "time"=>date("Y-m-d H:i:s"),
               ];
           Withdraw::create($data);
           Us::where("id",$uid)->update(["commission"=>0]);
           return json("提交成功");
       }
       //提现记录
       if(isset($reqData["txLogo"])){
           $tx=Withdraw::where("wi_uid",$uid)->order('id', 'desc')->select();
           foreach ($tx as $index=>$val){
               if($val["wi_state"]==0){
                   $tx[$index]["wi_state"]="正在处理";
               }
               if($val["wi_state"]==1){
                   $tx[$index]["wi_state"]="已转帐";
               }
            //   目前只支持支付宝提现
               $tx[$index]["wi_type"]="支付宝";
           }
           return json($tx);
       }
   }
   public function upUser(){
        $reqData=Request::param('input');
        $date=[
            "id"=>Cookie::get("id"),
            $reqData["input"][0]=>$reqData["input"][1]
            ];
        Us::update($date);
        return json(["state"=>1,"content"=>"修改成功"]);
    }

    /**
     * 找回密码
     *
     * 支持通过手机号验证找回密码
     *
     * @return \think\response\Json
     */
    public function resetPassword()
    {
        try {
            $reqData = Request::param();

            // 参数验证
            if (empty($reqData["username"])) {
                return json(["msg" => 0, "content" => "请输入手机号"]);
            }

            if (empty($reqData["verify_code"])) {
                return json(["msg" => 0, "content" => "请输入验证码"]);
            }

            if (empty($reqData["new_password"])) {
                return json(["msg" => 0, "content" => "请输入新密码"]);
            }

            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $reqData["username"])) {
                return json(["msg" => 0, "content" => "请输入正确的11位手机号"]);
            }

            // 验证账号是否存在
            $user = Us::where('us_username', $reqData["username"])->find();
            if (!$user) {
                return json(["msg" => 0, "content" => "该账号不存在"]);
            }

            // 验证验证码
            $verifyCode = Cookie::get("phoneVerify");
            if (empty($verifyCode)) {
                return json(["msg" => 0, "content" => "验证码已过期，请重新获取"]);
            }
            if ($reqData["verify_code"] != $verifyCode) {
                return json(["msg" => 0, "content" => "验证码错误"]);
            }

            // 密码验证
            if (strlen($reqData["new_password"]) < 6) {
                return json(["msg" => 0, "content" => "新密码长度不能小于6位"]);
            }

            // 更新密码
            $user->us_password = $reqData["new_password"];
            $user->save();

            // 清除验证码
            Cookie::delete('phoneVerify');

            // 记录密码重置日志
            Db::name("userlogo")->insert([
                "uid" => $user["id"],
                "content" => "密码重置成功",
                "ip" => Request::ip(),
                "time" => date("Y-m-d H:i:s")
            ]);

            return json(["msg" => 1, "content" => "密码重置成功"]);

        } catch (\Exception $e) {
            // 记录错误日志
            trace('密码重置异常: ' . $e->getMessage(), 'error');
            return json(["msg" => 0, "content" => "密码重置失败，请稍后重试"]);
        }
    }

    /**
     * 获取离线账号令牌
     *
     * @return \think\response\Json
     */
    public function getOfflineToken()
    {
        // 检查令牌开关是否开启
        $config = Config::find(1);
        if (!$config || $config['token_switch'] != 1) {
            return json(['msg' => 0, 'content' => '令牌功能已关闭']);
        }

        // 获取系统设置的令牌限制次数
        $system = Config::find(1);
        if (!$system) {
            return json(['msg' => 0, 'content' => '系统配置错误']);
        }
        $tokenLimit = $system['sy_token_limit'];

        // 获取账号ID
        $accountId = Request::param("id");
        if (!$accountId) {
            return json(['msg' => 0, 'content' => '账号ID不能为空']);
        }

        // 查询账号信息
        $account = Account::find($accountId);
        if (!$account) {
            return json(['msg' => 0, 'content' => '账号不存在']);
        }

        // 验证是否为离线账号
        if ($account['goods_type'] != 0) {
            return json(['msg' => 0, 'content' => '当前账号不是离线账号']);
        }

        // 检查令牌提取次数限制
        if ($account['token_limit'] >= $tokenLimit) {
            return json(['msg' => 0, 'content' => '已达到令牌提取次数限制']);
        }

        // 验证账号所有权
         $userId = Cookie::get("id");
        // if ($account['ac_uid'] != $userId) {
        //     return json(['msg' => 0, 'content' => '无权访问此账号']);
        // }

        // 查询账号令牌信息
        $tokenInfo = Db::name('steamaccountdata')
            ->where('account_name', $account['ac_name'])
            ->find();

        if (!$tokenInfo || empty($tokenInfo['shared_secret'])) {
            return json(['msg' => 0, 'content' => '当前账号没有添加令牌信息，无法提取，请联系管理员']);
        }

        // 请求令牌API
        try {
            $apiUrl = $config['token_api'] . "api/v1/steam_secret";
        $postData = json_encode(['shared_secret' => $tokenInfo['shared_secret']]);

        $options = [
            'http' => [
                'header'  => "Content-Type: application/json\r\n" .
                             "Content-Length: " . strlen($postData) . "\r\n",
                'method'  => 'POST',
                'content' => $postData,
            ],
        ];

        $context  = stream_context_create($options);
        $token = file_get_contents($apiUrl, false, $context);

        if (!$token) {
            throw new \Exception('获取令牌失败');
        }

        // 解析 JSON 响应
        $responseData = json_decode($token, true);

        // 检查解析是否成功以及是否存在 'secret' 字段
        if (isset($responseData['secret'])) {
            $token = $responseData['secret'];
        } else {
            throw new \Exception('获取令牌失败，响应中没有 secret 字段');
        }

            // 更新账号的令牌提取次数
            $account->token_limit = $account->token_limit + 1;
            $account->save();

            // 记录令牌获取日志
            Db::name("userlogo")->insert([
                "uid" => $userId,
                "content" => "获取离线账号令牌成功",
                "ip" => Request::ip(),
                "time" => date("Y-m-d H:i:s")
            ]);

            return json([
                'msg' => 1,
                'content' => '获取成功',
                'data' => ['token' => $token]
            ]);

        } catch (\Exception $e) {
            // 记录错误日志
            trace('获取离线令牌失败: ' . $e->getMessage(), 'error');

            return json([
                'msg' => 0,
                'content' => '获取令牌失败：' . $e->getMessage()
            ]);
        }
    }


    /**
     * 获取用户订单数据
     *
     * @return \think\response\Json
     */
    public function uss()
    {
        // 验证用户身份
        $userId = Cookie::get("id");
        if (!$userId) {
            return json(['msg' => 0, 'content' => '用户未登录']);
        }

        // 获取订单ID
        $orderId = Request::param("id");
        $order = Order::where("id", $orderId)->find();
        if (!$order) {
            return json(['msg' => 0, 'content' => '订单不存在']);
        }

        // 确认订单属于当前用户
        if ($order["ord_uid"] != $userId) {
            return json(['msg' => 0, 'content' => '无权访问此订单']);
        }

        // 获取该用户所有相同套餐的已付款订单
        $orders = Order::where([
            "ord_uid" => $order["ord_uid"],
            "ord_ifpay" => 1,
            "ord_combo" => $order["ord_combo"],
            'GoodType' => $order['GoodType']
        ])->select();

        // 获取订单对应的账号信息
        $accountsMap = []; // 用于去重的关联数组
        $hasAvailable = false;
        foreach ($orders as $order) {
            $account = Account::where("id", $order["ord_aid"])->find();
            if ($account) {
                $accountId = $account['id'];
                if (!isset($accountsMap[$accountId])) { // 如果该账号还未加入数组
                    $accountsMap[$accountId] = [
                        'id' => $accountId,
                        'ac_name' => $account['ac_name'],
                        'ac_state' => $account['ac_states'] == 1 ? '可用' : '已占用',
                        'exit_time' => $account['ac_states'] == 1 ? '' : date('Y-m-d H:i:s', $account['exit_time'])
                    ];
                    if ($account['ac_states'] == 1) {
                        $hasAvailable = true;
                    }
                }
            }
        }
        $accounts = array_values($accountsMap); // 将关联数组的值作为索引数组

        // 如果所有账号都被占用,检查当前订单账号状态
        if (!$hasAvailable) {
            $currentAccount = Account::where("id", $order["ord_aid"])->find();
            if ($currentAccount && $currentAccount['ac_states'] == 1) {  // 只有当当前账号状态为可用时才添加
                $accounts[] = [
                    'id' => $currentAccount['id'],
                    'ac_name' => $currentAccount['ac_name'],
                    'ac_state' => '可用',
                    'exit_time' => ''
                ];
            }
        }

        return json([
            'msg' => 1,
            'content' => '获取成功',
            'accounts' => $accounts
        ]);
    }

    public function switchAccount()
    {
        // 获取请求参数
        $accountId = Request::param("accountId");
        $orderId = Request::param("orderId");

        // 参数验证
        if (!$accountId || !$orderId) {
            return json(['msg' => 0, 'content' => '参数缺失']);
        }

        // 验证用户是否登录
        $userId = Cookie::get("id");
        if (!$userId) {
            return json(['msg' => 0, 'content' => '用户未登录']);
        }

        // 查询订单信息
        $order = Order::find($orderId);
        if (!$order) {
            return json(['msg' => 0, 'content' => '订单不存在']);
        }

        // 验证订单所有权
        if ($order['ord_uid'] != $userId) {
            return json(['msg' => 0, 'content' => '无权访问此订单']);
        }

        // 检查订单是否已到期
        if ($order['or_maturity'] == 0) {
            return json(['msg' => 0, 'content' => '订单已到期，无法切换账号']);
        }

        // 获取当前租用账号ID
        $currentAccountId = $order['ord_aid'];

        // 查询要切换的账号信息
        $switchAccount = Account::find($accountId);
        if (!$switchAccount) {
            return json(['msg' => 0, 'content' => '切换账号不存在']);
        }

        if ($switchAccount['goods_Type'] != 1) {
            return json(['msg' => 0, 'content' => '切换账号类型错误']);
        }

        if ($switchAccount['ac_states'] != 1) {
            return json(['msg' => 0, 'content' => '切换账号已被占用']);
        }

        // 查询当前租用账号信息
        $currentAccount = Account::find($currentAccountId);
        if (!$currentAccount) {
            return json(['msg' => 0, 'content' => '当前租用账号不存在']);
        }

        // 检查账号到期时间
        if (isset($currentAccount['exit_time']) && $currentAccount['exit_time'] < time()) {
            return json(['msg' => 0, 'content' => '账号已到期，无法切换']);
        }

        // 保存当前租赁账号的到期时间
        $exitTime = $currentAccount['exit_time'];

        // ✅ 使用统一的账号回收方法回收当前账号
        \app\controller\Api::recycleAccount($currentAccount['ac_name']);

        // 插入切换账号订单
        $newOrder = $order->toArray();
        unset($newOrder['id']); // 移除原订单ID
        $newOrder['or_maturity'] = 0;
        $newOrder['ord_name'] = $order['ord_name'] . '--切换账号订单';
        $newOrderId = Db::name('order')->insertGetId($newOrder);

        // ✅ 使用统一的账号分配方法分配新账号
        \app\controller\Api::assignAccount(
            $switchAccount['ac_name'],
            $userId,
            $exitTime
        );

        // 更新订单信息
        $order->ord_aid = $accountId;
        $order->save();

        return json([
            'msg' => 1,
            'content' => '切换成功',
            'data' => [
                'previous_account' => [
                    'id' => $currentAccountId,
                    'ac_name' => $currentAccount['ac_name'],
                    'exit_time' => date('Y-m-d H:i:s', $exitTime)
                ],
                'current_account' => [
                    'id' => $accountId,
                    'ac_name' => $switchAccount['ac_name']
                ]
            ]
        ]);
    }

    /**
     * 获取永久版用户历史账号数据
     *
     * @return \think\response\Json
     */
    public function getPermanentHistoryAccounts()
    {
        // 验证用户身份
        $userId = Cookie::get("id");
        if (!$userId) {
            return json(['msg' => 0, 'content' => '用户未登录']);
        }

        // 获取商品ID
        $goodsId = Request::param("goodsId");
        if (!$goodsId) {
            return json(['msg' => 0, 'content' => '商品ID缺失']);
        }

        // 验证用户是否有该商品的永久版权限
        $hasPermission = Order::where([
            'ord_uid' => $userId,
            'is_permanent' => 1,
            'GoodType' => 3,
            'ord_ifpay' => 1,
            'or_maturity' => 1,
            'ord_combo' => $goodsId
        ])->find();

        if (!$hasPermission) {
            return json(['msg' => 0, 'content' => '您没有该商品的永久版权限']);
        }

        // 查询用户历史提取的该商品账号（只查询永久版提取记录）
        $extractOrders = Order::where([
            'ord_uid' => $userId,
            'is_permanent' => 2, // 提取订单标识
            'ord_ifpay' => 1,
            'ord_combo' => $goodsId,
            'ord_type' => 'permanent_extract' // 只查询永久版提取
        ])->select();

        $accountsMap = [];

        // 遍历提取订单获取账号信息
        foreach ($extractOrders as $extractOrder) {
            if ($extractOrder["ord_aid"]) {
                $account = Account::where("id", $extractOrder["ord_aid"])->find();
                if ($account) {
                    // 检查账号当前状态
                    $isCurrentlyUsed = ($account['ac_uid'] == $userId && $account['ac_vip'] == 2);
                    $isAvailable = ($account['ac_states'] == 1 && $account['ac_sell'] == 1 && !$account['ac_uid']);

                    // 确定账号状态
                    $accountState = '未知';
                    if ($isCurrentlyUsed) {
                        $accountState = '当前使用中';
                    } elseif ($isAvailable) {
                        $accountState = '可用';
                    } else {
                        $accountState = '被占用';
                    }

                    $accountsMap[$account['ac_name']] = [
                        'id' => $account['id'],
                        'ac_name' => $account['ac_name'],
                        'ac_state' => $accountState,
                        'is_current' => $isCurrentlyUsed,
                        'can_switch' => $isAvailable,
                        'exit_time' => $account['exit_time'] ? date('Y-m-d H:i:s', $account['exit_time']) : '',
                        'last_extract_time' => $extractOrder['time']
                    ];
                }
            }
        }

        return json([
            'msg' => 1,
            'content' => '获取成功',
            'accounts' => array_values($accountsMap)
        ]);
    }

    /**
     * 永久版账号切换
     *
     * @return \think\response\Json
     */
    public function switchPermanentAccount()
    {
        // 获取请求参数
        $accountId = Request::param("accountId");
        $currentAccountId = Request::param("currentAccountId");
        $goodsId = Request::param("goodsId");

        // 参数验证
        if (!$accountId || !$currentAccountId || !$goodsId) {
            return json(['msg' => 0, 'content' => '参数缺失']);
        }

        // 验证用户是否登录
        $userId = Cookie::get("id");
        if (!$userId) {
            return json(['msg' => 0, 'content' => '用户未登录']);
        }

        // 验证用户是否有该商品的永久版权限
        $hasPermission = Order::where([
            'ord_uid' => $userId,
            'is_permanent' => 1,
            'GoodType' => 3,
            'ord_ifpay' => 1,
            'or_maturity' => 1,
            'ord_combo' => $goodsId
        ])->find();

        if (!$hasPermission) {
            return json(['msg' => 0, 'content' => '您没有该商品的永久版权限']);
        }

        // 查询当前使用的账号
        $currentAccount = Account::find($currentAccountId);
        if (!$currentAccount) {
            return json(['msg' => 0, 'content' => '当前账号不存在']);
        }

        // 验证当前账号所有权
        if ($currentAccount['ac_uid'] != $userId || $currentAccount['ac_vip'] != 2) {
            return json(['msg' => 0, 'content' => '当前账号验证失败']);
        }

        // 查询要切换的目标账号
        $targetAccount = Account::find($accountId);
        if (!$targetAccount) {
            return json(['msg' => 0, 'content' => '目标账号不存在']);
        }

        // 验证目标账号是否可用
        if ($targetAccount['ac_states'] != 1 || $targetAccount['ac_sell'] != 1 || $targetAccount['ac_uid']) {
            return json(['msg' => 0, 'content' => '目标账号当前不可用']);
        }

        // 验证目标账号是否属于同一商品
        if ($targetAccount['ac_goods'] != $goodsId) {
            return json(['msg' => 0, 'content' => '目标账号商品不匹配']);
        }

        // 保存当前账号的剩余时间
        $remainingTime = $currentAccount['exit_time'] - time();
        if ($remainingTime <= 0) {
            return json(['msg' => 0, 'content' => '当前账号已过期，无法切换']);
        }

        // 获取配置信息
        $config = Config::find(1);

        try {
            // 开启事务
            Db::startTrans();

            // 1. 回收当前账号
            \app\controller\Api::recycleAccount($currentAccount['ac_name']);

            // 2. 分配目标账号
            $newExitTime = time() + $remainingTime;
            \app\controller\Api::assignAccount(
                $targetAccount['ac_name'],
                $userId,
                $newExitTime,
                [
                    'ac_sell' => 0,
                    'ac_vip' => 2  // 永久版标识
                ]
            );

            // 3. 创建切换记录订单
            $orderData = [
                'ord_bbh' => date('YmdHis') . rand(1000, 9999),
                'ord_type' => 'permanent_switch',
                'ord_type2' => 1,
                'ord_name' => '永久版账号切换 - ' . $targetAccount['ac_name'],
                'ord_uid' => $userId,
                'ord_ifpay' => 1,
                'GoodType' => 2, // 永久版账号切换
                'is_permanent' => 3, // 永久版切换订单标识
                'ord_aid' => $targetAccount['id'],
                'time' => date('Y-m-d H:i:s'),
                'account_status' => 1,
                'expiry_date' => date('Y-m-d H:i:s', $newExitTime),
                'or_maturity' => 1,
                'ord_combo' => $goodsId, // 修复：存储商品ID用于永久版权限验证
                'ord_money' => 0, // 切换不收费
                'previous_account_id' => $currentAccountId,
                'goods_id' => $goodsId, // 新增：明确存储商品ID
            ];

            Order::create($orderData);

            // 4. 记录操作日志
            Db::name('userlogo')->insert([
                'uid' => $userId,
                'content' => "永久版账号切换: {$currentAccount['ac_name']} → {$targetAccount['ac_name']}",
                'ip' => Request::ip(),
                'time' => date('Y-m-d H:i:s')
            ]);

            // 提交事务
            Db::commit();

            return json([
                'msg' => 1,
                'content' => '切换成功',
                'data' => [
                    'previous_account' => [
                        'id' => $currentAccountId,
                        'ac_name' => $currentAccount['ac_name']
                    ],
                    'current_account' => [
                        'id' => $accountId,
                        'ac_name' => $targetAccount['ac_name'],
                        'exit_time' => date('Y-m-d H:i:s', $newExitTime)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json(['msg' => 0, 'content' => '切换失败：' . $e->getMessage()]);
        }
    }

    /**
     * 修改密码页面
     */
    public function changePasswordPage() {
        // 使用Cookie和session双重验证用户登录状态
        $user = $this->checkUserLogin();
        if (!$user) {
            return redirect('/user/login/index');
        }

        $config = config('geetest');
        return view('login/change_password', [
            'user' => $user,
            'config' => $config
        ]);
    }

    /**
     * 检查用户登录状态
     * 同时检查Cookie和session
     */
    private function checkUserLogin() {
        // 首先检查session
        $sessionUser = session('user');

        // 如果session中没有用户信息，则检查Cookie
        if (!$sessionUser) {
            $cookie_login = cookie('login');
            $cookie_username = cookie('username');
            $cookie_id = cookie('id');

            // 如果Cookie中有登录信息
            if ($cookie_login && $cookie_username && $cookie_id) {
                // 从数据库中获取用户信息
                $user = Db::name('user')
                    ->where('us_id', $cookie_id)
                    ->where('us_username', $cookie_username)
                    ->find();

                if ($user) {
                    // 将用户信息存入session
                    session('user', $user);
                    return $user;
                }
            }
            return false;
        }

        return $sessionUser;
    }

    /**
     * 验证短信验证码
     */
    public function verifyCode() {
        $phone = input('phone');
        $code = input('code');
        $actionType = input('actionType');

        // 验证用户身份
        $userId = cookie('id');
        $cookieUsername = cookie('username');

        // 确保手机号与用户匹配
        if ($cookieUsername != $phone) {
            return json(['code' => 1, 'msg' => '用户信息不匹配，请重新登录']);
        }

        // 验证短信验证码
        $verifyResult = $this->checkSmsCode($phone, $code, $actionType);

        if ($verifyResult) {
            // 生成一个临时令牌，用于后续修改密码操作
            $token = md5($phone . time() . rand(1000, 9999));
            cache($token, $phone, 600); // 缓存10分钟

            return json(['code' => 0, 'msg' => '验证成功', 'token' => $token]);
        } else {
            return json(['code' => 1, 'msg' => '验证码错误或已过期']);
        }
    }

    /**
     * 执行修改密码
     */
    public function doChangePassword() {
        $token = input('token');
        $newPassword = input('newPassword');

        // 验证token有效性
        $phone = cache($token);
        if (!$phone) {
            return json(['code' => 1, 'msg' => '验证已过期，请重新验证']);
        }

        // 验证用户身份
        $userId = cookie('id');
        $cookieUsername = cookie('username');

        // 确保手机号与用户匹配
        if ($cookieUsername != $phone) {
            return json(['code' => 1, 'msg' => '用户信息不匹配，请重新登录']);
        }

        // 验证密码长度
        if (strlen($newPassword) < 6) {
            return json(['code' => 1, 'msg' => '密码长度至少6位']);
        }

        // 修改密码
        $user = Db::name('user')->where('us_username', $phone)->find();
        if (!$user) {
            return json(['code' => 1, 'msg' => '用户不存在']);
        }

        // 更新密码
        $result = Db::name('user')->where('us_username', $phone)
            ->update(['us_password' => $newPassword]);

        if ($result) {
            // 清除token
            cache($token, null);
            return json(['code' => 0, 'msg' => '密码修改成功']);
        } else {
            return json(['code' => 1, 'msg' => '密码修改失败，请重试']);
        }
    }

    /**
     * 检查短信验证码
     */
    private function checkSmsCode($phone, $code, $actionType) {
        // 从缓存中获取验证码
        $cacheKey = 'sms_' . $actionType . '_' . $phone;
        $cacheCode = cache($cacheKey);

        if ($cacheCode && $cacheCode == $code) {
            // 验证成功后清除缓存
            cache($cacheKey, null);
            return true;
        }

        return false;
    }

    /**
     * 取消离线账号租用
     *
     * @return \think\response\Json
     */
    public function cancelOfflineLease()
    {
        // 验证用户身份
        $userId = Cookie::get("id");
        if (!$userId) {
            return json(['msg' => 0, 'content' => '用户未登录']);
        }

        // 获取参数
        $accountId = Request::param("vip_id");
        $orderNumber = Request::param("ord_bbh");

        if (!$accountId || !$orderNumber) {
            return json(['msg' => 0, 'content' => '参数不完整']);
        }

        // 查询订单信息
        $order = Order::where([
            'ord_bbh' => $orderNumber,
            'ord_uid' => $userId,
            'GoodType' => 0,  // 确保是离线账号订单
            'is_permanent' => 2  // 确保是会员提取账号订单
        ])->find();

        if (!$order) {
            return json(['msg' => 0, 'content' => '订单不存在或无权操作']);
        }

        // 开启事务
        Db::startTrans();
        try {
            // 更新订单状态
            $order->account_status = 2;  // 2表示已取消
            $order->or_maturity = 0;     // 设置为已到期
            $order->pending_reason = '用户主动取消租用';
            $order->expiry_date = date('Y-m-d H:i:s');  // 设置到期时间为当前时间
            $order->save();

            // 记录操作日志
            Db::name("userlogo")->insert([
                "uid" => $userId,
                "content" => "取消离线账号租用，订单号：{$orderNumber}",
                "ip" => Request::ip(),
                "time" => date("Y-m-d H:i:s")
            ]);

            // 提交事务
            Db::commit();
            return json(['msg' => 1, 'content' => '取消租用成功']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json(['msg' => 0, 'content' => '取消租用失败：' . $e->getMessage()]);
        }
    }

}