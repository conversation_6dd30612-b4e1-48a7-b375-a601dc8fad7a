/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */ ;
layui.define(["table", "form"], function(t) {
	var e = layui.$,
        $ = layui.$,
		i = layui.table,
		n = layui.form;
	i.render({
			elem: "#LAY-app-content-list2",
			url: "/admin/promotion/promotionLogo",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 100,
						title: "ID",
						sort: !0
					},
                    {
						field: "pro_user",
						title: "推广用户ID",
						minWidth: 100
					},
					{
						field: "pro_subuser",
						minWidth: 100,
						title: "下级用户ID",
					},
					{
						field: "time",
						title: "添加时间",
						minWidth: 80,
						align: "center",
					},
					{
						title: "操作",
						width: 150,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-com"
					}
				]
			],
			page: !0,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list2)",
			function(t) {
				t.data;
				"del" === t.event ? layer.confirm("确定删除此账号？",
					function(e) {
					    console.log(t.data)
						t.del(), layer.close(e)
						layui.$.ajax({
                                    url: '/admin/promotion/delpromotionLogo',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "编辑商品",
					content: "/admin/promotion/uppromotionLogo?id="+t.data.id,
					area: ["500px", "500px"],
					btn: ["确定", "取消"],
					yes: function(t, e) {
						var n = window["layui-layer-iframe" + t],
							l = "layuiadmin-app-form-submit",
							a = e.find("iframe")
							.contents()
							.find("#" + l);
						n.layui.form.on("submit(" + l + ")",
							function(e) {
							    console.log(e.field)
								    $.ajax({
                                        url: '/admin/promotion/uppromotionLogo',
                                        method: 'post',
                                        data: e.field,
                                        success: function(res){
                                        }
                                    });
								e.field;
								layer.msg('保存成功');
								i.reload("LAY-app-content-list2"),
									layer.close(t)
							}), a.trigger("click")
					},
					success: function(t, e) {
                            
					}
				})

			}),
		t("promotionLogo", {})
});