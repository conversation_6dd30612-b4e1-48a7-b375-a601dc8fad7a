<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>绑定手机号</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <style>
        .btn-primary {
            background: linear-gradient(to right, #4481eb, #04befe);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(50, 50, 93, .1);
        }
        .input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        .input-group input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        .input-group input:focus {
            border-color: #4481eb;
            box-shadow: 0 0 0 2px rgba(68, 129, 235, 0.2);
        }
        .send-code {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            background: #f3f4f6;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .send-code:hover {
            background: #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full bg-white rounded-xl shadow-lg p-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900">绑定手机号</h2>
                <p class="mt-2 text-sm text-gray-600">绑定手机号完成注册</p>
            </div>

            <form id="bindForm" class="mt-8 space-y-6">
                <div class="input-group">
                    <input type="tel" id="phone" name="phone" required 
                           pattern="^1[3-9]\d{9}$"
                           placeholder="请输入手机号"
                           class="appearance-none">
                </div>

                <div id="captcha"></div>

                <div class="input-group">
                    <input type="text" id="code" name="code" required
                           placeholder="请输入验证码"
                           class="appearance-none">
                    <span class="send-code" onclick="sendCode()">发送验证码</span>
                </div>

                <button type="submit" 
                        class="btn-primary w-full py-3 px-4 border border-transparent rounded-lg
                               text-white font-medium focus:outline-none">
                    立即绑定
                </button>
            </form>
        </div>
    </div>

    <!-- 添加 jQuery -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <!-- 更换极验CDN地址 -->
    <script src="https://static.geetest.com/static/js/gt.0.4.9.js"></script> <!-- 引入极验 JS SDK -->
    <script>
        let countdown = 60;
        let timer = null;
        let geetestObj;

        function initGeetestCaptcha() {
            let retryCount = 0;
            const maxRetries = 3;

            function tryInit() {
                $.ajax({
                    url: "{$config.gee_api}register",
                    type: "get",
                    dataType: "json",
                    success: function (data) {
                        if (data.gt && data.challenge) {
                            if ($("#captcha").length === 0) {
                                console.error("Captcha container not found");
                                return;
                            }

                            initGeetest({
                                gt: data.gt,
                                challenge: data.challenge,
                                offline: !data.success,
                                new_captcha: true,
                                product: "popup",
                                width: "100%"
                            }, function (captchaObj) {
                                geetestObj = captchaObj;
                                
                                $("#captcha").empty();
                                captchaObj.appendTo("#captcha");

                                captchaObj.onReady(function() {
                                    console.log("Geetest is ready");
                                });

                                captchaObj.onSuccess(function () {
                                    const phone = $('#phone').val().trim();
                                    if(!/^1[3-9]\d{9}$/.test(phone)) {
                                        alert('请输入正确的手机号');
                                        geetestObj.reset();
                                        return;
                                    }

                                    $.ajax({
                                        url: "/user/base/verify",
                                        type: "post",
                                        data: {
                                            username: phone,
                                            geetest_challenge: geetestObj.getValidate().geetest_challenge,
                                            geetest_validate: geetestObj.getValidate().geetest_validate,
                                            geetest_seccode: geetestObj.getValidate().geetest_seccode,
                                            actionType: "bind"
                                        },
                                        success: function(res) {
                                            if(res.msg === 1) {
                                                if(res.sms_required) {
                                                    // 需要短信验证
                                                    startCountdown();
                                                    alert('验证码已发送，请注意查收');
                                                } else {
                                                    // 不需要短信验证，提示用户可以直接提交
                                                    alert('验证成功，请点击"立即绑定"按钮完成绑定');
                                                    // 如果不需要验证码，可以隐藏验证码输入框
                                                    $('#code').parent().hide();
                                                }
                                            } else {
                                                alert(res.content || '发送失败');
                                                geetestObj.reset();
                                            }
                                        }
                                    });
                                });
                            });
                        } else {
                            if (retryCount < maxRetries) {
                                retryCount++;
                                setTimeout(tryInit, 1000);
                            } else {
                                alert("极验初始化失败，请刷新页面重试");
                            }
                        }
                    },
                    error: function () {
                        if (retryCount < maxRetries) {
                            retryCount++;
                            setTimeout(tryInit, 1000);
                        } else {
                            alert("极验请求失败，请检查网络连接");
                        }
                    }
                });
            }

            tryInit();
        }

        // 页面加载完成初始化极验
        document.addEventListener('DOMContentLoaded', function() {
            initGeetestCaptcha();
        });

        function sendCode() {
            const sendBtn = document.querySelector('.send-code');
            if(sendBtn.classList.contains('disabled')) return;
            
            // 触发极验验证
            geetestObj.verify();
        }

        function startCountdown() {
            const sendBtn = document.querySelector('.send-code');
            sendBtn.classList.add('disabled');
            sendBtn.style.backgroundColor = '#e5e7eb';
            sendBtn.style.cursor = 'not-allowed';

            timer = setInterval(() => {
                sendBtn.textContent = `${countdown}秒后重试`;
                countdown--;
                
                if(countdown < 0) {
                    clearInterval(timer);
                    sendBtn.textContent = '发送验证码';
                    sendBtn.classList.remove('disabled');
                    sendBtn.style.backgroundColor = '';
                    sendBtn.style.cursor = 'pointer';
                    countdown = 60;
                }
            }, 1000);
        }

        document.getElementById('bindForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                phone: document.getElementById('phone').value,
                code: document.getElementById('code').value
            };

            fetch('/user/login/doBind', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(res => res.json())
            .then(data => {
                if(data.code === 0) {
                    alert('绑定成功');
                    window.location.href = '/'; // 跳转到首页
                } else {
                    alert(data.msg);
                }
            });
        });
    </script>
</body>
</html> 