<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>{$system.sy_title}</title>
	<link href="/static/css/tailwind.min.css" rel="stylesheet">
	<script src="/static/js/jquery.min.js"></script>
	<style>
		/* 卡片动画效果 */
		@keyframes slideUp {
			from { transform: translateY(20px); opacity: 0; }
			to { transform: translateY(0); opacity: 1; }
		}
		
		.animate-slide-up {
			animation: slideUp 0.5s ease-out forwards;
		}
		
		/* 渐变背景动画 */
		@keyframes gradientFlow {
			0% { background-position: 0% 50%; }
			50% { background-position: 100% 50%; }
			100% { background-position: 0% 50%; }
		}
		
		.gradient-animate {
			background-size: 200% 200%;
			animation: gradientFlow 5s ease infinite;
		}
		
		/* 数字滚动动画 */
		@keyframes countUp {
			from { transform: translateY(100%); opacity: 0; }
			to { transform: translateY(0); opacity: 1; }
		}
		
		.animate-count {
			animation: countUp 0.5s ease-out forwards;
		}
		
		/* 自定义滚动条 */
		.custom-scrollbar::-webkit-scrollbar {
			width: 6px;
			height: 6px;
		}
		
		.custom-scrollbar::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 3px;
		}
		
		.custom-scrollbar::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 3px;
		}
		
		.custom-scrollbar::-webkit-scrollbar-thumb:hover {
			background: #a8a8a8;
		}
	</style>
</head>
<body class="bg-gray-50 text-gray-800">

	<!-- 引用头部 -->
	{include file='../app/user/view/ticket/header.html'}

	<!-- Main Content -->
	<main class="container mx-auto px-4 py-8">
		<div class="flex flex-col lg:flex-row gap-6">
			<!-- 左侧用户信息面板 -->
			<div class="lg:w-1/4 space-y-6">
				<!-- 用户信息卡片 -->
				<div class="bg-white rounded-2xl shadow-sm overflow-hidden animate-slide-up">
					<div class="h-32 bg-gradient-to-r from-blue-500 to-indigo-600 gradient-animate relative">
						<div class="absolute -bottom-16 left-1/2 transform -translate-x-1/2">
							<div class="relative">
								<img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" 
									 alt="用户头像" 
									 class="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg">
								<div class="absolute bottom-2 right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
							</div>
						</div>
					</div>
					<div class="pt-20 pb-6 px-6 text-center">
						<h3 class="text-xl font-bold text-gray-800">{$user.us_username}</h3>
						<p class="text-sm text-gray-500 mt-1">ID: {$user.id}</p>
					</div>
				</div>

				<!-- 导航菜单 -->
				<div class="bg-white rounded-2xl shadow-sm overflow-hidden">
					<div class="p-4">
						{include file='../app/user/view/ticket/index.html'}
					</div>
				</div>
			</div>

			<!-- 右侧内容区 -->
			<div class="lg:w-3/4 space-y-6">
				<!-- 账户统计卡片 -->
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- 余额卡片 -->
					<div class="bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl p-6 text-white shadow-lg hover:shadow-xl transition-shadow duration-300 animate-slide-up" style="animation-delay: 0.2s">
						<div class="flex items-center justify-between mb-4">
							<h3 class="text-lg font-semibold opacity-90">账户余额</h3>
							<svg class="w-8 h-8 opacity-75" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
							</svg>
						</div>
						<div class="text-3xl font-bold mb-2 animate-count">¥{$user.us_money|default='0'}</div>
						<div class="flex justify-between items-center">
							<button id="cz" class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-300">
								充值
							</button>
							<a href="/user/index/onshop" class="text-white/80 hover:text-white transition-colors duration-300">
								查看详情 →
							</a>
						</div>
					</div>

					<!-- 消费统计卡片 -->
					<div class="bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl p-6 text-white shadow-lg hover:shadow-xl transition-shadow duration-300 animate-slide-up" style="animation-delay: 0.4s">
						<div class="flex items-center justify-between mb-4">
							<h3 class="text-lg font-semibold opacity-90">消费统计</h3>
							<svg class="w-8 h-8 opacity-75" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
							</svg>
						</div>
						<div class="text-3xl font-bold mb-2 animate-count">¥{$Consumed|default='0'}</div>
						<div class="text-white/80">累计消费金额</div>
					</div>
				</div>

				<!-- 消费记录表格 -->
				<div class="bg-white rounded-2xl shadow-sm overflow-hidden animate-slide-up" style="animation-delay: 0.6s">
					<div class="p-6 border-b border-gray-100">
						<h3 class="text-xl font-bold text-gray-800">消费记录</h3>
						<p class="text-sm text-gray-500 mt-1">显示全部订单记录</p>
					</div>
					<div class="overflow-x-auto custom-scrollbar">
						<table class="w-full">
							<thead class="bg-gray-50">
								<tr>
									<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">订单号</th>
									<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">充值名称</th>
									<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">状态</th>
									<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">充值金额</th>
									<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">更新时间</th>
									<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">操作</th>
								</tr>
							</thead>
							<tbody class="divide-y divide-gray-100">
								{foreach $order as $val}
								<tr class="hover:bg-gray-50 transition-colors">
									<td class="px-6 py-4 text-sm text-gray-600 font-mono">{$val.ord_bbh}</td>
									<td class="px-6 py-4 text-sm text-gray-800">{$val.ord_name|default='-'}</td>
									<td class="px-6 py-4">
										<span class="px-3 py-1 rounded-full text-xs font-medium 
											{$val.ord_ifpay == '已支付' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
											{$val.ord_ifpay|default='未支付'}
										</span>
									</td>
									<td class="px-6 py-4 text-sm font-medium text-indigo-600">¥{$val.ord_money|default='0'}</td>
									<td class="px-6 py-4 text-sm text-gray-500">{$val.time|default='-'}</td>
									<td class="px-6 py-4">
										<button onclick="window.location.href='/user/index/onshop'" 
												class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-300">
											提取商品
										</button>
									</td>
								</tr>
								{/foreach}
							</tbody>
						</table>
						
						<!-- 无订单时的提示 -->
						{if empty($order)}
						<div class="py-10 text-center">
							<svg class="w-16 h-16 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
							</svg>
							<p class="mt-4 text-gray-500">暂无消费记录</p>
						</div>
						{/if}
					</div>
				</div>
			</div>
		</div>
	</main>

	<!-- Recharge Modal -->
	<div class="main-box1 fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center hidden z-50">
		<div class="bg-white rounded-2xl shadow-xl p-6 w-full max-w-md mx-auto transform transition-all duration-300 scale-95">
			<div class="flex items-center justify-between mb-6">
				<h2 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
					余额充值
				</h2>
				<button class="clos p-1 hover:bg-gray-100 rounded-full transition-colors">
					<svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
				</button>
			</div>
			
			<p class="text-gray-600 mb-6">请输入您要充值的金额数量，充值完成后金额将进入钱包供您在站内消费</p>
			
			<div class="space-y-4">
				<div>
					<label for="czMoney" class="block text-sm font-medium text-gray-700 mb-1">充值金额</label>
					<div class="relative rounded-lg shadow-sm">
						<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<span class="text-gray-500 sm:text-sm">¥</span>
						</div>
						<input type="number" 
							   id="czMoney" 
							   class="block w-full pl-8 pr-12 py-3 border-gray-200 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
							   placeholder="请输入充值金额">
					</div>
				</div>
				
				<button class="button w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-4 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:scale-[1.02]">
					立即充值
				</button>
			</div>
		</div>
	</div>

	<script src="/static/js/alert.js"></script>
	<script>
		$(document).ready(function() {
			// 充值弹窗显示/隐藏
			$("#cz").on("click", function() {
				$(".main-box1").removeClass('hidden').find('.transform').addClass('scale-100');
			});
			
			$(".clos").on("click", function() {
				const modal = $(".main-box1");
				modal.find('.transform').removeClass('scale-100').addClass('scale-95');
				setTimeout(() => {
					modal.addClass('hidden');
				}, 200);
			});

			// 充值操作
			$(".button").on("click", function() {
				const amount = $("#czMoney").val();
				if (!/^[1-9]\d*(\.\d{1,2})?$/.test(amount)) {
					createAlert("red", "请输入有效的充值金额", 0);
					return false;
				}
				
				$.post("/user/index/order", { 
					czMoney: amount 
				}, function(e) {
					window.location.href = e.src;
				});
			});
		});
	</script>
</body>
<!-- 引用页脚 -->
{include file='../app/user/view/ticket/footer.html'}
</html>
