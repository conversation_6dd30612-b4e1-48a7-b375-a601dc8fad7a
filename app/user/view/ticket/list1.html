<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>{$system.sy_title}</title>
	<link href="/static/css/tailwind.min.css" rel="stylesheet">
	<script src="/static/js/jquery.min.js"></script>
	<style>
		/* 动画效果 */
		@keyframes slideIn {
			from { transform: translateY(-10px); opacity: 0; }
			to { transform: translateY(0); opacity: 1; }
		}

		@keyframes fadeIn {
			from { opacity: 0; }
			to { opacity: 1; }
		}

		.animate-slide-in {
			animation: slideIn 0.3s ease-out;
		}

		.animate-fade-in {
			animation: fadeIn 0.3s ease-out;
		}

		/* 自定义滚动条 */
		.custom-scrollbar::-webkit-scrollbar {
			width: 6px;
			height: 6px;
		}

		.custom-scrollbar::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 3px;
		}

		.custom-scrollbar::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 3px;
		}

		.custom-scrollbar::-webkit-scrollbar-thumb:hover {
			background: #a8a8a8;
		}

		/* 卡片悬浮效果 */
		.hover-card {
			transition: all 0.3s ease;
		}

		.hover-card:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 16px rgba(0,0,0,0.1);
		}

		/* 按钮动画效果 */
		.btn-animate {
			transition: all 0.2s ease;
		}

		.btn-animate:hover {
			transform: translateY(-1px);
			box-shadow: 0 4px 8px rgba(0,0,0,0.1);
		}

		.btn-animate:active {
			transform: translateY(0);
		}

		/* 加载动画优化 */
		.loader {
			border-top-color: #6366f1;
			border-width: 3px;
			transition: all 0.3s ease;
		}

		/* 表格样式优化 */
		.modern-table {
			border-spacing: 0;
			width: 100%;
		}

		.modern-table th {
			background: linear-gradient(to right, #f9fafb, #f3f4f6);
			font-weight: 600;
			text-transform: uppercase;
			font-size: 0.75rem;
			letter-spacing: 0.05em;
			color: #4b5563;
			padding: 1rem;
			text-align: left;
			border-bottom: 2px solid #e5e7eb;
		}

		.modern-table td {
			padding: 1rem;
			border-bottom: 1px solid #e5e7eb;
			transition: all 0.2s ease;
		}

		.modern-table tr:hover td {
			background-color: #f9fafb;
		}

		/* 状态标签样式 */
		.status-tag {
			padding: 0.25rem 0.75rem;
			border-radius: 9999px;
			font-size: 0.75rem;
			font-weight: 500;
			display: inline-flex;
			align-items: center;
			gap: 0.25rem;
		}

		.status-tag.success {
			background-color: #dcfce7;
			color: #16a34a;
		}

		.status-tag.warning {
			background-color: #fef3c7;
			color: #d97706;
		}

		.status-tag.error {
			background-color: #fee2e2;
			color: #dc2626;
		}

		/* 添加数字格式化和溢出处理的样式 */
		.truncate {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		/* 调整数据统计卡片的最小宽度 */
		.grid > div {
			min-width: 0; /* 确保flex子项可以正确收缩 */
		}

		/* 确保金额显示时不会超出容器 */
		.text-2xl {
			font-size: 1.5rem; /* 稍微减小字体大小 */
			line-height: 1.2;
		}

		/* 当数字较长时自动调整字体大小 */
		@media screen and (max-width: 1280px) {
			.text-2xl {
				font-size: 1.25rem;
			}
		}

		/* VIP标签的高级样式 */
		.vip-badge {
			display: inline-flex;
			align-items: center;
			padding: 0.5rem 1rem;
			font-size: 0.875rem;
			font-weight: 600;
			color: #fff;
			background: linear-gradient(135deg, #B8860B 0%, #FFD700 50%, #DAA520 100%);
			border-radius: 9999px;
			box-shadow: 0 2px 10px rgba(218, 165, 32, 0.3),
						0 0 2px rgba(218, 165, 32, 0.3),
						inset 0 1px 1px rgba(255, 255, 255, 0.4);
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
			position: relative;
			transition: all 0.3s ease;
		}

		/* 添加光泽效果 */
		.vip-badge::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 50%;
			background: linear-gradient(180deg, 
						rgba(255, 255, 255, 0.3) 0%, 
						rgba(255, 255, 255, 0) 100%);
			border-radius: 9999px 9999px 0 0;
		}

		/* 悬浮效果 */
		.vip-badge:hover {
			transform: translateY(-1px);
			box-shadow: 0 4px 15px rgba(218, 165, 32, 0.4),
						0 0 4px rgba(218, 165, 32, 0.4),
						inset 0 1px 1px rgba(255, 255, 255, 0.4);
		}

		/* 适配深色模式 */
		@media (prefers-color-scheme: dark) {
			.vip-badge {
				background: linear-gradient(135deg, #FFD700 0%, #DAA520 50%, #B8860B 100%);
				box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3),
							0 0 2px rgba(255, 215, 0, 0.3),
							inset 0 1px 1px rgba(255, 255, 255, 0.2);
			}
		}

		/* 添加闪光动画效果 */
		@keyframes shine {
			0% {
				background-position: -100% 0;
			}
			100% {
				background-position: 200% 0;
			}
		}

		.vip-badge::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(90deg, 
						transparent 0%, 
						rgba(255, 255, 255, 0.2) 50%, 
						transparent 100%);
			background-size: 200% 100%;
			border-radius: 9999px;
			animation: shine 3s infinite linear;
			pointer-events: none;
		}

		/* 模态框动画 */
		@keyframes modalFadeIn {
			from { opacity: 0; transform: scale(0.95); }
			to { opacity: 1; transform: scale(1); }
		}

		@keyframes modalFadeOut {
			from { opacity: 1; transform: scale(1); }
			to { opacity: 0; transform: scale(0.95); }
		}

		/* 表格行动画 */
		@keyframes rowFadeIn {
			from { opacity: 0; transform: translateY(-10px); }
			to { opacity: 1; transform: translateY(0); }
		}

		/* 表格样式 */
		#modalBody tr {
			animation: rowFadeIn 0.3s ease-out forwards;
			animation-delay: calc(var(--row-index) * 0.1s);
		}

		#modalBody tr:hover {
			background-color: rgba(249, 250, 251, 0.8);
			transition: background-color 0.2s ease;
		}

		#modalBody td {
			padding: 1rem 1.5rem;
			color: #374151;
			font-size: 0.95rem;
		}

		/* 空状态样式 */
		#modalBody tr.empty-state td {
			padding: 3rem 1.5rem;
			text-align: center;
			color: #6B7280;
		}

		/* 模态框阴影效果 */
		.account-modal-shadow {
			box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
						0 8px 10px -6px rgba(0, 0, 0, 0.1);
		}

		/* 账号卡片样式 */
		.account-card {
			@apply bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300;
			border: 1px solid rgba(0,0,0,0.1);
		}

		.account-card:hover {
			@apply shadow-lg transform -translate-y-1;
		}

		/* 状态标签样式 */
		.status-badge {
			@apply px-2 py-1 rounded-full text-xs font-medium;
		}

		.status-badge.available {
			@apply bg-green-100 text-green-600;
		}

		.status-badge.occupied {
			@apply bg-red-100 text-red-600;
		}

		/* 切换按钮样式 */
		.switch-btn {
			@apply w-full px-4 py-2 rounded-lg font-medium transition-all duration-300;
		}

		.switch-btn.active {
			@apply bg-blue-500 text-white hover:bg-blue-600;
			box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
		}

		.switch-btn.disabled {
			@apply bg-gray-100 text-gray-400 cursor-not-allowed;
		}

		/* 卡片动画 */
		@keyframes cardFadeIn {
			from { 
				opacity: 0; 
				transform: translateY(20px);
			}
			to { 
				opacity: 1; 
				transform: translateY(0);
			}
		}

		.account-card {
			animation: cardFadeIn 0.5s ease-out forwards;
			animation-delay: calc(var(--card-index) * 0.1s);
		}

		/* 闪光效果 */
		@keyframes shine {
			from {
				background-position: 200% center;
			}
		}

		.shine-effect {
			background: linear-gradient(
				90deg,
				transparent,
				rgba(255,255,255,0.2),
				transparent
			);
			background-size: 200% 100%;
			animation: shine 2s infinite linear;
		}
	</style>
</head>
<body class="bg-gray-50 text-gray-800">

	<!-- 引用头部 -->
	{include file='../app/user/view/ticket/header.html'}

	<!-- 主内容区 -->
	<main class="container mx-auto px-4 py-8">
		<div class="flex flex-col lg:flex-row gap-6">
			<!-- 左侧用户信息面板 -->
			<div class="lg:w-1/4 space-y-6">
				<!-- 用户信息卡片 -->
				<div class="bg-white rounded-2xl shadow-sm hover-card overflow-hidden">
					<!-- 顶部背景装饰 -->
					<div class="h-24 bg-gradient-to-r from-indigo-500 to-purple-600 relative">
						<div class="absolute -bottom-12 left-1/2 transform -translate-x-1/2">
							<div class="relative">
								<img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" 
									 alt="用户头像" 
									 class="w-24 h-24 rounded-full object-cover border-4 border-white shadow-md">
								<div class="absolute bottom-2 right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
				</div>
						</div>
					</div>

					<!-- 用户信息 -->
					<div class="pt-14 pb-6 px-6">
						<div class="text-center">
							<h3 class="text-lg font-semibold text-gray-800">{$user.us_username}</h3>
							<p class="text-sm text-gray-500 mt-1">ID: {$user.id}</p>
							
							<!-- VIP标签 -->
							<div class="mt-3">
								<span class="vip-badge">
									<svg class="w-4 h-4 mr-1.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" 
											  fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
									</svg>
									VIP会员
								</span>
							</div>
						</div>

						<!-- 账户数据统计 -->
						<div class="grid grid-cols-2 gap-4 mt-6">
							<div class="text-center p-4 rounded-xl bg-gradient-to-br from-indigo-50 to-indigo-100">
								<p class="text-sm font-medium text-indigo-600 mb-1">账号数量</p>
								<p class="text-2xl font-bold text-indigo-700 truncate">{:count($order)}</p>
							</div>
							<div class="text-center p-4 rounded-xl bg-gradient-to-br from-pink-50 to-pink-100">
								<p class="text-sm font-medium text-pink-600 mb-1">账户余额</p>
								<p class="text-2xl font-bold text-pink-700 truncate" title="¥{$user.us_money|default='0.00'}">
									¥{$user.us_money|default='0.00'|number_format=2}
								</p>
							</div>
						</div>
					</div>
				</div>

				<!-- 导航菜单 -->
				<div class="bg-white rounded-2xl shadow-sm hover-card overflow-hidden">
					<div class="p-4">
				{include file='../app/user/view/ticket/index.html'}
			</div>
				</div>
			</div>

			<!-- 右侧订单列表 -->
			<div class="lg:w-3/4">
				<div class="bg-white rounded-xl shadow-sm hover-card overflow-hidden">
					<!-- 标题栏 -->
					<div class="p-6 border-b border-gray-100">
						<div class="flex items-center">
							<h2 class="text-xl font-semibold text-gray-800">订单记录</h2>
							<!-- 如果需要添加其他元素,可以在这里添加 -->
						</div>
					</div>

					<!-- 订单列表 -->
					<div class="overflow-x-auto custom-scrollbar">
					{if condition="count($order)==0"}
						<div class="flex flex-col items-center justify-center py-16">
							<svg class="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
							</svg>
							<h3 class="mt-4 text-xl font-medium text-gray-600">暂无订单记录</h3>
							<p class="mt-2 text-gray-500">您还没有购买任何账号</p>
						</div>
					{else/}
						<table class="modern-table">
						<thead>
							<tr>
									<th>订单号</th>
									<th>支付方式</th>
									<th>价格</th>
									<th>商品类型</th>
									<th>商品名称</th>
									<th>创建时间</th>
									<th>操作</th>
							</tr>
						</thead>
						<tbody>
							{foreach $order as $index=>$val}
								<tr class="hover:bg-gray-50 transition-colors">
									<td class="font-mono text-sm">{$val.id}</td>
									<td>
										<span class="status-tag {$val.ord_type=='支付宝'?'success':'warning'}">
											{$val.ord_type}
										</span>
									</td>
									<td class="font-medium text-indigo-600">¥{$val.ord_money}</td>
									<td>
										<span class="status-tag {if condition="$val.ord_type2==1"}success{else/}warning{/if}">
											{if condition="$val.ord_type2==1"}独享{else/}共享{/if}
										</span>
									</td>
									<td class="max-w-xs truncate">{$val.ord_name}</td>
									<td class="text-gray-500 text-sm">{$val.time}</td>
									<td>
									{if condition="!empty($val.ord_aid)"}
										<div class="flex gap-2">
											<button class="btn-animate bg-blue-500 text-white px-3 py-1.5 rounded-lg text-sm hover:bg-blue-600" data-id="{$val.id}">
												查看
											</button>
											<button class="btn-animate bg-green-500 text-white px-3 py-1.5 rounded-lg text-sm hover:bg-green-600" data-id="{$val.id}">
												令牌
											</button>
											<button class="btn-animate bg-yellow-500 text-white px-3 py-1.5 rounded-lg text-sm hover:bg-yellow-600" data-id="{$val.id}">
												切换
											</button>
										</div>
									{elseif condition="$val.ord_ifpay==1 && $val.or_maturity==1 && empty($val.ord_aid)"}
										<button class="btn-animate bg-purple-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-600 extract-account-btn" data-id="{$val.id}">
											提取账号
										</button>
									{else/}
										<span class="text-gray-400">未分配账号</span>
									{/if}
								</td>
							</tr>
							{/foreach}
						</tbody>
					</table>
					{$order|raw}
					{/if}
					</div>
				</div>
			</div>
		</div>
	</main>

	<!-- Modals -->
	<div id="accountModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center hidden z-50">
		<div class="bg-white rounded-2xl shadow-xl p-6 w-full max-w-2xl mx-auto transform transition-all duration-300 scale-95">
			<!-- 模态框头部 -->
			<div class="flex items-center justify-between mb-6">
				<h2 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
					账号信息
				</h2>
				<button onclick="closeModal()" class="p-1 hover:bg-gray-100 rounded-full transition-colors">
					<svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
				</button>
			</div>

			<!-- 账号信息表格 -->
			<div class="overflow-hidden rounded-xl border border-gray-200">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gradient-to-r from-gray-50 to-gray-100">
						<tr>
							<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">ID</th>
							<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">账号</th>
							<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">密码</th>
							<th class="px-6 py-4 text-left text-sm font-semibold text-gray-600">到期时间</th>
					</tr>
				</thead>
					<tbody id="modalBody" class="bg-white divide-y divide-gray-200">
					<!-- 动态填充账号信息 -->
				</tbody>
			</table>
			</div>

			<!-- 底部按钮 -->
			<div class="mt-6 flex justify-end">
				<button onclick="closeModal()" 
						class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200">
					关闭
				</button>
			</div>
		</div>
	</div>

	<div id="tokenModal" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center hidden">
		<div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-lg mx-auto transform transition-transform duration-300 scale-95">
			<span class="close text-gray-500 hover:text-gray-800 cursor-pointer absolute top-2 right-2" onclick="closeTokenModal()">&times;</span>
			<h2 class="text-xl font-semibold mb-4 text-center">令牌信息</h2>
			<div class="token-container text-center">
				<div class="token-value bg-gray-100 p-4 rounded-lg mb-4 text-lg font-mono" id="tokenValue">令牌内容</div>
				<!-- <div class="token-timer mt-4 text-sm text-gray-600">令牌有效期剩余 <span id="timerSeconds">30</span> 秒</div> -->
			</div>
		</div>
	</div>

	<div id="switchModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center hidden z-50">
		<div class="bg-white rounded-2xl shadow-xl p-6 w-full max-w-4xl mx-auto transform transition-all duration-300 scale-95">
			<!-- 模态框头部 -->
			<div class="flex items-center justify-between mb-6">
				<h2 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
					切换账号
				</h2>
				<button onclick="closeSwitchModal()" class="p-1 hover:bg-gray-100 rounded-full transition-colors">
					<svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
				</button>
			</div>

			<!-- 账号卡片网格 -->
			<div id="switchModalGrid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto custom-scrollbar p-1">
				<!-- 动态填充账号卡片 -->
			</div>

			<!-- 底部按钮 -->
			<div class="mt-6 flex justify-end">
				<button onclick="closeSwitchModal()" 
						class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200">
					关闭
				</button>
			</div>
		</div>
	</div>

	<!-- 全局加载动画 -->
	<div id="globalLoader" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center hidden">
		<div class="loader ease-linear rounded-full border-8 border-t-8 border-gray-200 h-32 w-32"></div>
	</div>

	<!-- 添加确认对话框模态框 -->
	<div id="confirmModal" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center hidden z-50">
		<div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md mx-auto transform transition-transform duration-300 scale-95">
			<div class="flex justify-between items-center mb-4">
				<h3 class="text-xl font-semibold text-gray-800" id="confirmTitle">确认操作</h3>
				<button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="closeConfirmModal()">
					<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>
			<div class="mb-6">
				<p class="text-gray-600" id="confirmMessage">确定要执行此操作吗？</p>
			</div>
			<div class="flex justify-end space-x-3">
				<button type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 focus:outline-none transition-colors" onclick="closeConfirmModal()">
					取消
				</button>
				<button type="button" id="confirmButton" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none transition-colors">
					确认
				</button>
			</div>
		</div>
	</div>

<!-- 引入提示框脚本 -->
<script type="text/javascript" charset="utf-8">
	// 显示加载动画
	function showLoader() {
		$('#globalLoader').fadeIn(200);
	}

	// 隐藏加载动画
	function hideLoader() {
		$('#globalLoader').fadeOut(200);
	}

	// 显示提示框
	function showAlert(color, message, type) {
		if (type === 1) {
			showLoader();
		} else {
			var alertBox = $("<div>").addClass("alert-box").css({
				"background-color": color,
				"color": "white",
				"padding": "20px",
				"border-radius": "5px",
				"position": "fixed",
				"top": "20%",
				"left": "50%",
				"transform": "translate(-50%, -50%)",
				"z-index": "9999",
				"text-align": "center",
				"font-size": "18px"
			}).text(message);
			$("body").append(alertBox);
			alertBox.fadeIn(200);
		}
	}

	// 隐藏提示框
	function hideAlert() {
		hideLoader();
		$(".alert-box").fadeOut(200, function() {
			$(this).remove();
		});
	}
</script>

<script type="text/javascript" charset="utf-8">
	/**
	 * 格式化时间戳为年月日时分秒
	 * @param {number|string} timestamp - 时间戳
	 * @return {string} 格式化后的时间字符串
	 */
	function formatDateTime(timestamp) {
		// 检查时间戳
		if (!timestamp) return '未设置';
		
		try {
			// 确保时间戳是数字
			let time = parseInt(timestamp);
			
			// 检查时间戳长度，根据位数判断是秒还是毫秒
			if (time.toString().length === 10) {
				// 秒级时间戳转换为毫秒
				time = time * 1000;
			}
			
			const date = new Date(time);
			
			// 验证日期是否有效
			if (isNaN(date.getTime())) {
				return '时间格式错误';
			}

			// 格式化为本地时间字符串
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		const hours = String(date.getHours()).padStart(2, '0');
		const minutes = String(date.getMinutes()).padStart(2, '0');
		const seconds = String(date.getSeconds()).padStart(2, '0');

		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			
		} catch (error) {
			console.error('时间格式化错误:', error, '时间戳:', timestamp);
			return '时间格式错误';
		}
	}

	/**
	 * 打开模态框并加载账号信息
	 * @param {number} id - 订单ID
	 */
	function openModal(id) {
		showLoader();
		$.ajax({
			url: "/user/base/usOrder",
			type: "post",
			dataType: "json",
			data: { id: id },
			success: res => {
				hideLoader();
				$("#modalBody").empty();
				
				if(res.length == 0) {
					$("#modalBody").append(`
						<tr class="empty-state">
							<td colspan="4">
								<div class="flex flex-col items-center py-8">
									<svg class="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
											  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
									</svg>
									<p class="text-gray-500 text-lg">账号已到期</p>
								</div>
							</td>
                            </tr>
                        `);
				} else {
					res.forEach((account, index) => {
						// 转换时间戳为日期字符串
						const exitTime = formatDateTime(account.exit_time);
						
						$("#modalBody").append(`
							<tr class="hover:bg-gray-50 transition-colors" style="--row-index: ${index}">
								<td class="px-6 py-4">
									<span class="font-medium text-gray-900">${account.id}</span>
								</td>
								<td class="px-6 py-4">
									<div class="flex items-center">
										<div class="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center text-white font-bold mr-3">
											${account.ac_name.charAt(0).toUpperCase()}
										</div>
										<span class="font-medium text-gray-900">${account.ac_name}</span>
									</div>
								</td>
								<td class="px-6 py-4">
									<span class="font-mono text-gray-700">${account.ac_password}</span>
								</td>
								<td class="px-6 py-4">
									<div class="flex items-center text-gray-500">
										<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
												  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
										</svg>
										<span>${exitTime}</span>
									</div>
								</td>
                                </tr>
                            `);
					});
				}
				
				$("#accountModal").removeClass('hidden').find('.transform').addClass('scale-100');
			},
			error: () => {
				hideLoader();
				showAlert("red", "请求失败，请稍后重试", 0);
			}
		});
	}

	function closeModal() {
		const modal = $("#accountModal");
		modal.find('.transform').removeClass('scale-100').addClass('scale-95');
		setTimeout(() => {
			modal.addClass('hidden');
		}, 200);
	}

	/**
	 * 获取令牌并提示用户
	 * @param {number} id - 订单ID
	 */
	function getToken(id) {
		showAlert("#16b777", "正在加载", 1);
		$.ajax({
			url: "/user/base/getAccountToken",
			type: "post",
			dataType: "json",
			data: { id: id },
			success: function (res) {
				hideAlert();
				if (res.msg === 1 && res.data && res.data.token) {
					// 成功获取令牌，显示令牌信息
					showSuccessTokenModal(res.data.token);
				} else {
					// 显示错误信息，4秒后自动关闭
					showErrorTokenModal(res.content);	
				}
			},
			error: function () {
				hideAlert();
				showErrorTokenModal("请求失败，请检查网络或联系管理员");
			}
		});
	}

	// 显示成功获取令牌的模态框
	function showSuccessTokenModal(token) {
		$("#tokenValue").text(token);
		$("#tokenModal").fadeIn(200);
		startTokenTimer();
	}

	// 显示错误信息的模态框
	function showErrorTokenModal(message) {
		$("#tokenValue").text(message);
		$("#tokenModal").fadeIn(200);
		// 4秒后自动关闭
		setTimeout(() => {
			closeTokenModal();
		}, 4000);
	}

	// 关闭令牌模态框
	function closeTokenModal() {
		$("#tokenModal").fadeOut(200);
		$("#tokenValue").text(''); // 清空令牌值
		if (window.tokenTimer) {
			clearInterval(window.tokenTimer);
			window.tokenTimer = null;
		}
	}

	// 牌倒计时
	function startTokenTimer() {
		let seconds = 30;
		if (window.tokenTimer) {
			clearInterval(window.tokenTimer); // 清除之前的计时器
		}
		window.tokenTimer = setInterval(() => {
			seconds--;
			$("#tokenTimer").text(seconds);
			$("#timerSeconds").text(seconds);
			
			if (seconds <= 0) {
				clearInterval(window.tokenTimer);
				closeTokenModal();
			}
		}, 3000);
	}

	/**
	 * 打开切换账号的模态框并加载账号信息
	 * @param {number} id - 订单ID
	 */
	function openSwitchModal(id, orderId) {
		showLoader();
		$.ajax({
			url: "/user/base/uss", 
			type: "get",
			dataType: "json",
			data: { id: id },
			success: function(res) {
				hideLoader();
				$("#switchModalGrid").empty();
				
				if (res.accounts && res.accounts.length > 0) {
					res.accounts.forEach((account, index) => {
						const isAvailable = account.ac_state === '可用';
						const expiryTime = account.exit_time;
						
						const cardHTML = `
							<div class="account-card" style="--card-index: ${index}">
								<div class="p-4">
									<!-- 账号信息头部 -->
									<div class="flex items-center justify-between mb-3">
										<div class="flex items-center space-x-2">
											<div class="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center text-white font-bold">
												${account.ac_name.charAt(0).toUpperCase()}
											</div>
											<div class="font-medium text-gray-900">${account.ac_name}</div>
										</div>
										<span class="status-badge ${isAvailable ? 'available' : 'occupied'}">
											${isAvailable ? '可用' : '已占用'}
										</span>
									</div>
									
									<!-- 账号详情 -->
									<div class="space-y-2 mb-4">
										<div class="flex items-center text-sm">
											<div class="flex items-center ${isAvailable ? 'text-green-500' : 'text-gray-500'}">
												<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
														  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
												</svg>
												${isAvailable ? 
													'<span class="font-medium">当前可用</span>' : 
													`<span>到期时间: <span class="font-medium">${expiryTime}</span></span>`
												}
											</div>
										</div>
									</div>

									<!-- 切换按钮 -->
									<div class="flex items-center justify-center">
										${isAvailable ? `
											<!-- 可用账号状态 - 改进版 -->
											<button onclick="switchAccount('${account.id}', '${orderId}')" class="switch-account-btn flex items-center px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50">
												<svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
														  d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
												</svg>
												<span>切换到此账号</span>
											</button>
										` : `
											<!-- 被占用账号状态 - 改进版 -->
											<div class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-200 rounded-lg shadow-sm">
												<svg class="w-4 h-4 mr-1.5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
														  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
												</svg>
												<span>账号已被占用</span>
											</div>
										`}
									</div>
								</div>
							</div>
						`;
						$("#switchModalGrid").append(cardHTML);
					});
				} else {
					$("#switchModalGrid").append(`
						<div class="col-span-full flex flex-col items-center justify-center py-12">
							<svg class="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
									  d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
							</svg>
							<p class="text-gray-500 text-lg">暂无可切换的账号</p>
						</div>
					`);
				}
				
				$("#switchModal").removeClass('hidden').find('.transform').addClass('scale-100');
			},
			error: function(xhr, status, error) {
				console.error('API错误:', {xhr, status, error});
				hideLoader();
				showAlert("red", "请求失败，请稍后重试", 0);
			}
		});
	}

	// 关闭切换账号的模态框
	function closeSwitchModal() {
		const modal = $("#switchModal");
		modal.find('.transform').removeClass('scale-100').addClass('scale-95');
		setTimeout(() => {
			modal.addClass('hidden');
		}, 200);
	}

	// 切换账号
	function switchAccount(accountId, orderId) {
		showAlert("#16b777", "正在切换账号...", 1);
		
		console.log("切换账号参数:", {accountId, orderId}); // 调试日志
		
		$.ajax({
			url: "/user/base/switchAccount",
			type: "post",
			dataType: "json",
			data: {
				accountId: accountId,
				orderId: orderId
			},
			success: function(res) {
				hideAlert();
				console.log("切换账号响应:", res); // 调试日志
				
				if (res.msg === 1) {
					// 切换成功
					closeSwitchModal();
					showAlert("#10b981", "账号切换成功", 0);
					
					// 延迟刷新页面
					setTimeout(function() {
						location.reload();
					}, 1500);
				} else {
					// 切换失败，显示错误信息
					showAlert("#ef4444", res.content || "切换失败，请稍后再试", 0);
					
					// 自动关闭提示
					setTimeout(function() {
						hideAlert();
					}, 2000);
				}
			},
			error: function(xhr, status, error) {
				console.error("切换账号错误:", {xhr, status, error}); // 调试日志
				hideAlert();
				
				// 尝试解析错误响应
				let errorMsg = "网络错误，请稍后再试";
				try {
					if (xhr.responseJSON && xhr.responseJSON.content) {
						errorMsg = xhr.responseJSON.content;
					}
				} catch (e) {
					console.error("解析错误响应失败:", e);
				}
				
				showAlert("#ef4444", errorMsg, 0);
				
				// 自动关闭提示
				setTimeout(function() {
					hideAlert();
				}, 2000);
			}
		});
	}

	// 全局变量存储确认回调函数
	let confirmCallback = null;
	
	// 打开确认对话框
	function openConfirmModal(title, message, callback) {
		// 设置标题和消息
		document.getElementById('confirmTitle').textContent = title || '确认操作';
		document.getElementById('confirmMessage').textContent = message || '确定要执行此操作吗？';
		
		// 存储回调函数
		confirmCallback = callback;
		
		// 显示模态框
		document.getElementById('confirmModal').classList.remove('hidden');
	}
	
	// 关闭确认对话框
	function closeConfirmModal() {
		document.getElementById('confirmModal').classList.add('hidden');
		confirmCallback = null;
	}

	// 在文档加载完成后绑定事件
	$(document).ready(function() {
		// 绑定确认按钮点击事件
		document.getElementById('confirmButton').addEventListener('click', function() {
			if (typeof confirmCallback === 'function') {
				confirmCallback();
			}
			closeConfirmModal();
		});
		
		// 解绑之前可能存在的事件处理程序
		$(document).off('click', '.extract-account-btn');
		
		// 使用事件委托绑定点击事件
		$(document).on('click', '.extract-account-btn', function(e) {
			// 阻止事件冒泡和默认行为
			e.preventDefault();
			e.stopPropagation();
			
			// 获取订单ID
			var orderId = $(this).data('id');
			
			if (!orderId) {
				showAlert("#ff4d4f", "订单ID无效", 0);
				return;
			}
			
			// 使用自定义确认对话框
			openConfirmModal(
				"提取账号确认", 
				"确定要为此订单提取账号吗？", 
				function() {
					extractAccountAction(orderId);
				}
			);
		});
		
		// 添加查看账号按钮点击事件
		$(document).on('click', '.bg-blue-500', function() {
			var orderId = $(this).data('id');
			if (orderId) {
				openModal(orderId);
			}
		});
		
		// 添加提取令牌按钮点击事件
		$(document).on('click', '.bg-green-500', function() {
			var orderId = $(this).data('id');
			if (orderId) {
				getToken(orderId);
			}
		});
		
		// 添加切换账号按钮点击事件
		$(document).on('click', '.bg-yellow-500', function() {
			var orderId = $(this).data('id');
			if (orderId) {
				// 这里需要传递两个参数，我们假设第一个参数是订单ID，第二个也是订单ID
				// 如果实际逻辑不同，请根据需要调整
				openSwitchModal(orderId, orderId);
			}
		});
	});
	
	// 提取账号的实际操作
	function extractAccountAction(orderId) {
		showAlert("#16b777", "正在提取账号...", 1);
		
		// 打印参数，便于调试
		console.log("提取账号参数:", {id: orderId});
		
		$.ajax({
			url: "/user/index/extractOrderAccount",
			type: "post",
			dataType: "json",
			data: {
				id: orderId
			},
			success: function(res) {
				console.log("提取账号响应:", res);
				hideAlert();
				if (res.code === 0) {
					// 提取成功
					$("#modalBody").empty();
					$("#modalBody").append(`
						<tr>
							<td>${res.data.account_id}</td>
							<td>${res.data.account_name}</td>
							<td>${res.data.account_password}</td>
							<td>${res.data.exit_time}</td>
						</tr>
					`);
					
					// 显示模态框
					$("#accountModal").fadeIn(200);
					
					// 刷新页面
					setTimeout(function() {
						location.reload();
					}, 3000);
				} else {
					// 提取失败
					showAlert("#ff4d4f", res.msg, 0);
					setTimeout(function() {
						hideAlert();
					}, 2000);
				}
			},
			error: function(xhr, status, error) {
				console.log("提取账号错误:", {xhr, status, error});
				hideAlert();
				showAlert("#ff4d4f", "网络错误，请稍后再试", 0);
				setTimeout(function() {
					hideAlert();
				}, 2000);
			}
		});
	}
</script>
<!-- 引入页面悬浮按钮 -->
<!-- 引入页面底部 -->
</body>
<!-- 引用页脚 -->
{include file='../app/user/view/ticket/footer.html'}
</html>
