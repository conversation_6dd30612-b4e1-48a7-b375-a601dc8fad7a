<?php
namespace app\agent\controller;
use app\BaseController;
use app\admin\model\System;
use app\admin\model\Goods;
use app\admin\model\User;
use app\admin\model\Cdk;
use think\facade\Request;
use think\facade\Db;
use think\facade\Cookie;
use think\facade\View;
use think\facade\Session;

class Index extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\agent\middleware\CheckAgentStatus' => ['except' => ['login']]
    ];

    // 登录页面
    public function login()
    {
        if(request()->isPost()) {
            // 验证CSRF令牌
            $token = input('post.__token__');
            if(!$this->checkToken($token)) {
                return json(['code' => 0, 'msg' => '无效的请求']);
            }

            $username = input('post.username');
            $password = input('post.password');

            // 检查是否锁定
            $lockKey = 'login_lock_' . $username;
            if(Session::has($lockKey)) {
                $lockExpire = Session::get($lockKey);
                if(time() < $lockExpire) {
                    $remainingTime = ceil(($lockExpire - time()) / 60);
                    return json(['code' => 0, 'msg' => "账号已被锁定，请{$remainingTime}分钟后再试"]);
                } else {
                    Session::delete($lockKey);
                    Session::delete('login_fail_' . $username);
                }
            }

            // 首先查找用户名是否存在（不考虑状态）
            $agent = Db::name('agents')->where('username', $username)->find();

            if (!$agent) {
                // 用户不存在
                $failKey = 'login_fail_' . $username;
                $failCount = Session::get($failKey, 0) + 1;
                Session::set($failKey, $failCount);

                // 失败5次锁定账号30分钟
                if($failCount >= 5) {
                    $lockExpire = time() + 30 * 60; // 30分钟
                    Session::set('login_lock_' . $username, $lockExpire);
                    return json(['code' => 0, 'msg' => '账号或密码错误次数过多，账号已被锁定30分钟']);
                }

                return json(['code' => 0, 'msg' => "账号不存在，剩余尝试次数: " . (5 - $failCount)]);
            }

            // 检查账号状态
            if ($agent['status'] != 1) {
                return json(['code' => 0, 'msg' => '账号已被禁用，请联系管理员']);
            }

            // 验证密码
            if ($agent['password'] === md5($password)) {
                // 登录成功，清除失败计数
                Session::delete('login_fail_' . $username);

                session('agent_id', $agent['id']);
                session('agent_name', $agent['username']);

                // 生成新的CSRF令牌
                $newToken = $this->generateToken();

                return json(['code' => 1, 'msg' => '登录成功', 'url' => url('index'), 'token' => $newToken]);
            } else {
                // 密码错误，增加失败计数
                $failKey = 'login_fail_' . $username;
                $failCount = Session::get($failKey, 0) + 1;
                Session::set($failKey, $failCount);

                // 失败5次锁定账号30分钟
                if($failCount >= 5) {
                    $lockExpire = time() + 30 * 60; // 30分钟
                    Session::set('login_lock_' . $username, $lockExpire);
                    return json(['code' => 0, 'msg' => '账号或密码错误次数过多，账号已被锁定30分钟']);
                }

                return json(['code' => 0, 'msg' => "密码错误，剩余尝试次数: " . (5 - $failCount)]);
            }
        }

        // 生成CSRF令牌
        $token = $this->generateToken();
        View::assign('token', $token);

        return View::fetch();
    }

    // 生成CSRF令牌
    private function generateToken()
    {
        $token = md5(uniqid(mt_rand(), true));
        Session::set('__token__', $token);
        return $token;
    }

    // 验证CSRF令牌
    private function checkToken($token)
    {
        if(!$token || $token !== Session::get('__token__')) {
            return false;
        }
        return true;
    }

    /**
     * 统一的Session验证方法
     *
     * @return array|null 如果验证失败返回错误响应数组，成功返回null
     */
    private function validateAgentSession()
    {
        $agentId = session('agent_id');

        // 检查Session是否存在
        if(!$agentId) {
            return [
                'code' => 401,
                'msg' => '登录已过期，请重新登录',
                'session_expired' => true,
                'redirect' => '/agent/index/login'
            ];
        }

        try {
            // 查询代理信息
            $agent = Db::name('agents')->where('id', $agentId)->find();
        } catch (\Exception $e) {
            // 数据库查询失败，清除Session
            Session::delete('agent_id');
            Session::delete('agent_name');

            \think\facade\Log::error('代理状态查询失败: ' . $e->getMessage(), [
                'agent_id' => $agentId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => 401,
                'msg' => '会话已过期，请重新登录',
                'session_expired' => true,
                'redirect' => '/agent/index/login'
            ];
        }

        // 检查代理是否存在
        if (!$agent) {
            Session::delete('agent_id');
            Session::delete('agent_name');

            \think\facade\Log::warning('代理账号不存在', ['agent_id' => $agentId]);

            return [
                'code' => 404,
                'msg' => '代理账号不存在，请联系管理员',
                'account_not_found' => true,
                'redirect' => '/agent/index/login'
            ];
        }

        // 检查代理状态
        if ($agent['status'] != 1) {
            Session::delete('agent_id');
            Session::delete('agent_name');

            \think\facade\Log::info('代理账号被禁用', [
                'agent_id' => $agentId,
                'agent_username' => $agent['username'] ?? 'unknown',
                'status' => $agent['status']
            ]);

            return [
                'code' => 403,
                'msg' => '您的代理账号已被管理员禁用，请联系管理员',
                'account_disabled' => true,
                'redirect' => '/agent/index/login'
            ];
        }

        // 验证通过，返回null
        return null;
    }

    // 代理中心首页
    public function index()
    {
        if(!session('agent_id')) {
            return redirect('/agent/index/login');
        }

        $agent = Db::name('agents')->where('id', session('agent_id'))->find();
        View::assign('agent', $agent);

        // 统计数据
        $data = [
            'cdk_count' => Cdk::where('agent_id', session('agent_id'))->count(),
            'used_count' => Cdk::where(['agent_id'=>session('agent_id'), 'status'=>'used'])->count()
        ];
        View::assign('data', $data);

        return View::fetch();
    }

    // 检查代理状态接口
    public function checkStatus()
    {
        // 使用统一的Session验证
        $validationError = $this->validateAgentSession();
        if ($validationError) {
            return json($validationError);
        }

        // 获取代理信息
        $agent = Db::name('agents')->where('id', session('agent_id'))->find();

        return json([
            'code' => 200,
            'msg' => '状态正常',
            'agent_info' => [
                'id' => $agent['id'],
                'username' => $agent['username'],
                'balance' => $agent['balance']
            ]
        ]);
    }

    // 获取会员类型列表
    public function membershipTypes()
    {
        // 使用统一的Session验证
        $validationError = $this->validateAgentSession();
        if ($validationError) {
            return json($validationError);
        }

        try {
            $membershipTypes = Db::name('membership_pricing')
                ->field('id, membership_type, price, validity_period')
                ->select()
                ->toArray();

            return json([
                'code' => 1,
                'data' => $membershipTypes
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取会员类型列表失败：' . $e->getMessage()
            ]);
        }
    }

    // 生成卡密
    public function generate()
    {
        // 使用统一的Session验证
        $validationError = $this->validateAgentSession();
        if ($validationError) {
            return json($validationError);
        }

        if(request()->isPost()) {
            // 获取POST请求参数
            $goods_id = input('post.goods_id/d');      // 商品ID
            $cdk_type = input('post.cdk_type', 'AGT'); // 卡密类型
            $num = input('post.num/d', 1);             // 生成数量，默认1张
            $expiry_date2 = input('post.expiry_date2/d', 1); // 有效期天数，默认1天
            $membership_type = input('post.membership_type/d', 0); // 会员类型ID

            // 验证商品是否存在
            $goods = Db::name('goods')->where('id', $goods_id)->find();
            if(!$goods) {
                return json(['code' => 0, 'msg' => '商品不存在']);
            }

            // 获取代理信息
            $agent = Db::name('agents')->where('id', session('agent_id'))->find();

            // 计算成本
            $cost = 0;

            if($cdk_type === 'membership') {
                // 会员卡密成本计算
                if(!$membership_type) {
                    return json(['code' => 0, 'msg' => '请选择会员类型']);
                }

                $membershipInfo = Db::name('membership_pricing')
                    ->where('id', $membership_type)
                    ->find();

                if(!$membershipInfo) {
                    return json(['code' => 0, 'msg' => '会员类型不存在']);
                }

                $cost = $membershipInfo['price'] * ($agent['discount_rate']/100) * $num;
                $expiry_date2 = $membershipInfo['validity_period']; // 使用会员类型的有效期
            } else {
                // 普通卡密成本计算
                $combo = Db::name('combo')->where('co_goods', $goods_id)->find();
                if(!$combo) {
                    return json(['code' => 0, 'msg' => '商品套餐不存在']);
                }

                // 根据卡密类型选择价格
                $price = $cdk_type == 'day' ? $combo['day_price'] : $combo['hour_price'];
                $cost = $price * ($agent['discount_rate']/100) * $num;
            }

            // 验证代理余额是否充足
            if($agent['balance'] < $cost) {
                return json(['code' => 0, 'msg' => '余额不足']);
            }

            // 开启事务处理
            Db::startTrans();
            try {
                // 扣除代理余额
                Db::name('agents')
                    ->where('id', session('agent_id'))
                    ->update(['balance' => $agent['balance'] - $cost]);

                // 批量生成卡密数据
                $time = date('Y-m-d H:i:s');
                $cdks = [];
                for($i = 0; $i < $num; $i++) {
                    $cdkData = [
                        'agent_id' => session('agent_id'),
                        'cdk_code' => $this->generateCdkCode($cdk_type),
                        'cdk_type' => $cdk_type,
                        'cost' => $cost/$num,
                        'shop' => $goods_id,
                        'status' => 'unused',
                        'expiry_date' => null,
                        'expiry_date2' => $expiry_date2,
                        'created_at' => $time,
                    ];

                    // 如果是会员卡，添加会员类型ID
                    if($cdk_type === 'membership') {
                        $cdkData['membership_type_id'] = $membership_type;
                    }

                    $cdks[] = $cdkData;
                }

                // 批量插入卡密记录
                Db::table('cdk_cards')->insertAll($cdks);

                // 提交事务
                Db::commit();

                // 收集生成的卡密码
                $cdkCodes = array_column($cdks, 'cdk_code');

                if ($num == 1) {
                    return json(['code' => 1, 'msg' => '生成成功', 'cdk_code' => $cdkCodes[0]]);
                } else {
                    return json(['code' => 1, 'msg' => '生成成功', 'cdk_codes' => $cdkCodes]);
                }
            } catch (\Exception $e) {
                // 发生异常时回滚事务
                Db::rollback();
                return json(['code' => 0, 'msg' => '生成失败:'.$e->getMessage()]);
            }
        }

        // 非POST请求时，获取商品列表并渲染页面
        $goods_list = Db::name('goods')->where('goods_show', 1)->select();
        View::assign('goods_list', $goods_list);
        return View::fetch();
    }

    // 卡密列表
    public function cdklist()
    {
        // 使用统一的Session验证
        $validationError = $this->validateAgentSession();
        if ($validationError) {
            return json($validationError);
        }

        $where = ['agent_id' => session('agent_id')];

        // 处理状态筛选
        $status = input('status');
        if($status) {
            if($status === '已封禁') {
                // 筛选已封禁的卡密
                $where['is_banned'] = 1;
            } else {
                // 筛选未封禁的卡密，并按状态筛选
                $statusMap = [
                    '未使用' => 'unused',
                    '已使用' => 'used',
                    '已过期' => 'expired'
                ];
                $where['is_banned'] = 0;
                $where['status'] = $statusMap[$status] ?? $status;
            }
        }

        // 处理卡密类型筛选
        $type = input('type');
        if($type) {
            if($type === 'member') {
                // 会员卡, 用like查询cdk_type字段以member_开头的记录
                $where[] = ['cdk_type', 'like', 'member_%'];

                // 处理会员类型筛选
                $memberType = input('member_type');
                if($memberType) {
                    $where[] = ['cdk_type', '=', 'member_'.$memberType];
                }
            } else {
                // 游戏卡, 精确匹配day或hour
                $where['cdk_type'] = $type;
            }
        }

        // 添加搜索功能
        $keyword = input('keyword');
        $searchGoodsIds = [];
        $searchMemberTypes = [];

        if($keyword) {
            // 搜索商品名称，获取匹配的商品ID
            $matchedGoods = Db::name('goods')
                ->where('goods_name', 'like', "%{$keyword}%")
                ->column('id');

            if (!empty($matchedGoods)) {
                $searchGoodsIds = $matchedGoods;
            }

            // 搜索会员类型名称
            $matchedMemberTypes = Db::name('membership_pricing')
                ->where('membership_type', 'like', "%{$keyword}%")
                ->column('membership_type');

            if (!empty($matchedMemberTypes)) {
                $searchMemberTypes = $matchedMemberTypes;
            }
        }

        // 构建查询条件 - 使用直接数据库查询确保获取封禁状态
        $query = Db::table('cdk_cards')->where($where);

        if($keyword) {
            $query->where(function($q) use ($keyword, $searchGoodsIds, $searchMemberTypes) {
                // 搜索卡密码
                $q->whereLike('cdk_code', "%{$keyword}%");

                // 搜索商品ID（对应商品名称）
                if (!empty($searchGoodsIds)) {
                    $q->whereOr('shop', 'in', $searchGoodsIds);
                }

                // 搜索会员类型（对应会员名称）
                if (!empty($searchMemberTypes)) {
                    foreach ($searchMemberTypes as $memberType) {
                        $q->whereOr('cdk_type', '=', 'member_' . $memberType);
                    }
                }
            });
        }

        $list = $query->order('id desc')->paginate(15);

        // 获取所有卡密对应的商品ID
        $items = $list->items();
        $goodsIds = array_column($items, 'shop');

        // 批量查询商品信息
        $goodsNames = [];
        if (!empty($goodsIds)) {
            $goodsNames = Db::name('goods')
                ->whereIn('id', $goodsIds)
                ->column('goods_name', 'id');
        }

        // 收集会员类型ID
        $membershipTypeIds = [];
        foreach ($items as $item) {
            if (strpos($item['cdk_type'], 'member_') === 0 && !empty($item['shop'])) {
                $membershipTypeIds[] = $item['shop'];
            }
        }

        // 批量查询会员类型信息
        $membershipTypeNames = [];
        if (!empty($membershipTypeIds)) {
            $membershipTypeNames = Db::name('membership_pricing')
                ->whereIn('id', $membershipTypeIds)
                ->column('membership_type', 'id');
        }

        // 状态转换映射
        $statusMap = [
            'unused' => '未使用',
            'used' => '已使用',
            'expired' => '已过期'
        ];

        // 将商品名称和会员类型名称添加到每条卡密记录中
        foreach ($items as &$item) {
            // 转换状态为中文显示
            $item['status'] = $statusMap[$item['status']] ?? $item['status'];

            // 判断是否为会员卡 (cdk_type以member_开头)
            if (strpos($item['cdk_type'], 'member_') === 0) {
                // 从cdk_type中提取会员类型
                $memberType = substr($item['cdk_type'], 7); // 去掉'member_'前缀
                $item['membership_type_name'] = $memberType;
                $item['is_member_card'] = true;
            } else {
                $item['goods_name'] = $goodsNames[$item['shop']] ?? '';
                $item['is_member_card'] = false;
            }
        }

        return json([
            'code' => 1,
            'msg' => '获取卡密列表成功',
            'data' => [
                'list' => $items,
                'pagination' => [
                    'current' => $list->currentPage(),
                    'total' => $list->total()
                ]
            ]
        ]);
    }

    // 生成卡密码
    private function generateCdkCode($prefix_type = 'AGT')
    {
        $random_string = substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 5)), 0, 12);
//        return "Kongzue-{$prefix_type}-{$random_string}";
        return $random_string;
    }

    // 获取商品列表
    public function goodsList()
    {
        if(!session('agent_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        try {
            $goods = Db::name('goods')
                ->where('goods_show', 1)
                ->field('id, goods_name')
                ->select()
                ->toArray();

            return json([
                'code' => 1,
                'data' => $goods
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取商品列表失败：' . $e->getMessage()
            ]);
        }
    }

    // 获取离线商品列表
    public function offlineGoodsList()
    {
        if(!session('agent_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        try {
            $offlineGoods = Db::name('goods')
                ->alias('g')
                ->join('offline o', 'g.id = o.product_id', 'INNER')
                ->where('g.goods_show', 1)
                ->field('g.id, g.goods_name, o.product_amount')
                ->select()
                ->toArray();

            return json([
                'code' => 1,
                'data' => $offlineGoods
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取离线商品列表失败：' . $e->getMessage()
            ]);
        }
    }

    // 生成游戏卡密
    public function generateGameCard()
    {
        // 验证代理是否登录
        if(!session('agent_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        if(request()->isPost()) {
            $input = request()->getInput();
            $data = json_decode($input, true);

            // 获取POST请求参数
            $goods_id = $data['goods_id'] ?? 0;      // 商品ID
            $cdk_type = $data['cdk_type'] ?? '';     // 卡密类型 (day/hour)
            $num = isset($data['num']) ? intval($data['num']) : 1;  // 生成数量，默认1张
            $expiry_date2 = isset($data['expiry_date2']) ? intval($data['expiry_date2']) : 1; // 有效期，默认1

            // 验证参数
            if(!$goods_id || !$cdk_type) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 验证商品是否存在
            $goods = Db::name('goods')->where('id', $goods_id)->find();
            if(!$goods) {
                return json(['code' => 0, 'msg' => '商品不存在']);
            }

            // 获取代理信息
            $agent = Db::name('agents')->where('id', session('agent_id'))->find();

            // 计算成本
            $cost = 0;
            $price = 0;

            if($cdk_type == 'permanent') {
                // 永久卡密使用商品表的永久版价格
                $price = $goods['permanent_price2'];
                if(!$price || $price <= 0) {
                    return json(['code' => 0, 'msg' => '该商品未设置永久版价格']);
                }
                $expiry_date2 = 0; // 永久卡密设置为0表示永久
            } else {
                // 按天/按小时卡密使用套餐表价格
                $combo = Db::name('combo')->where('co_goods', $goods_id)->find();
                if(!$combo) {
                    return json(['code' => 0, 'msg' => '商品套餐不存在']);
                }

                // 根据卡密类型选择价格
                $price = $cdk_type == 'day' ? $combo['day_price'] : $combo['hour_price'];
            }

            $cost = $price * ($agent['discount_rate']/100) * $num;

            // 验证代理余额是否充足
            if($agent['balance'] < $cost) {
                return json(['code' => 0, 'msg' => '余额不足']);
            }

            // 开启事务处理
            Db::startTrans();
            try {
                // 扣除代理余额
                Db::name('agents')
                    ->where('id', session('agent_id'))
                    ->update(['balance' => $agent['balance'] - $cost]);

                // 批量生成卡密数据
                $time = date('Y-m-d H:i:s');
                $cdks = [];
                for($i = 0; $i < $num; $i++) {
                    $cdks[] = [
                        'agent_id' => session('agent_id'),
                        'cdk_code' => $this->generateCdkCode($cdk_type),
                        'cdk_type' => $cdk_type,
                        'cost' => $cost/$num,
                        'shop' => $goods_id,
                        'status' => 'unused',
                        'expiry_date' => null,
                        'expiry_date2' => $expiry_date2,
                        'created_at' => $time,
                    ];
                }

                // 批量插入卡密记录
                Db::table('cdk_cards')->insertAll($cdks);

                // 提交事务
                Db::commit();

                // 收集生成的卡密码
                $cdkCodes = array_column($cdks, 'cdk_code');

                if ($num == 1) {
                    return json(['code' => 1, 'msg' => '游戏卡密生成成功', 'cdk_code' => $cdkCodes[0]]);
                } else {
                    return json(['code' => 1, 'msg' => '游戏卡密生成成功', 'cdk_codes' => $cdkCodes]);
                }
            } catch (\Exception $e) {
                // 发生异常时回滚事务
                Db::rollback();
                return json(['code' => 0, 'msg' => '生成失败:'.$e->getMessage()]);
            }
        }

        return json(['code' => 0, 'msg' => '请求方法错误']);
    }

    // 生成会员卡密
    public function generateMemberCard()
    {
        // 验证代理是否登录
        if(!session('agent_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        if(request()->isPost()) {
            $input = request()->getInput();
            $data = json_decode($input, true);

            // 获取POST请求参数
            $membership_type = $data['membership_type'] ?? 0; // 会员类型ID
            $num = isset($data['num']) ? intval($data['num']) : 1; // 生成数量，默认1张

            // 验证参数
            if(!$membership_type) {
                return json(['code' => 0, 'msg' => '请选择会员类型']);
            }

            // 获取会员类型信息
            $membershipInfo = Db::name('membership_pricing')
                ->where('id', $membership_type)
                ->find();

            if(!$membershipInfo) {
                return json(['code' => 0, 'msg' => '会员类型不存在']);
            }

            // 获取代理信息
            $agent = Db::name('agents')->where('id', session('agent_id'))->find();

            // 计算成本
            $cost = $membershipInfo['price'] * ($agent['discount_rate']/100) * $num;
            $expiry_date2 = $membershipInfo['validity_period']; // 使用会员类型的有效期

            // 验证代理余额是否充足
            if($agent['balance'] < $cost) {
                return json(['code' => 0, 'msg' => '余额不足']);
            }

            // 开启事务处理
            Db::startTrans();
            try {
                // 扣除代理余额
                Db::name('agents')
                    ->where('id', session('agent_id'))
                    ->update(['balance' => $agent['balance'] - $cost]);

                // 获取会员类型名称作为cdk_type
                $cdk_type = 'member_' . $membershipInfo['membership_type']; // 使用会员类型名称，加前缀区分

                // 批量生成卡密数据
                $time = date('Y-m-d H:i:s');
                $cdks = [];
                for($i = 0; $i < $num; $i++) {
                    $cdks[] = [
                        'agent_id' => session('agent_id'),
                        'cdk_code' => $this->generateCdkCode('membership'),
                        'cdk_type' => $cdk_type, // 修改为具体的会员类型
                        'cost' => $cost/$num,
                        'shop' => $membership_type, // 使用会员类型ID作为shop值
                        'status' => 'unused',
                        'assigned_user' => null,
                        'expiry_date' => null,
                        'expiry_date2' => $expiry_date2,
                        'created_at' => $time,
                        'account_id' => null
                    ];
                }

                // 使用Cdk模型批量插入卡密记录
                Cdk::insertAll($cdks);

                // 提交事务
                Db::commit();

                // 收集生成的卡密码
                $cdkCodes = array_column($cdks, 'cdk_code');

                if ($num == 1) {
                    return json(['code' => 1, 'msg' => '会员卡密生成成功', 'cdk_code' => $cdkCodes[0]]);
                } else {
                    return json(['code' => 1, 'msg' => '会员卡密生成成功', 'cdk_codes' => $cdkCodes]);
                }
            } catch (\Exception $e) {
                // 发生异常时回滚事务
                Db::rollback();
                return json(['code' => 0, 'msg' => '生成失败:'.$e->getMessage()]);
            }
        }

        return json(['code' => 0, 'msg' => '请求方法错误']);
    }

    // 生成离线卡密
    public function generateOfflineCard()
    {
        // 验证代理是否登录
        if(!session('agent_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        // 检查代理状态
        $agent = Db::name('agents')->where('id', session('agent_id'))->find();
        if(!$agent) {
            return json(['code' => 0, 'msg' => '代理不存在']);
        }

        if($agent['status'] != 1) {
            return json(['code' => 403, 'msg' => '您的账户已被禁用，请联系管理员', 'disabled' => true]);
        }

        if(Request::isPost()) {
            $input = json_decode(file_get_contents('php://input'), true);
            $goods_id = $input['goods_id'] ?? 0;
            $num = $input['num'] ?? 1;

            // 验证参数
            if(!$goods_id || $num < 1 || $num > 100) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 获取离线商品信息和价格
            $offlineInfo = Db::name('offline')
                ->alias('o')
                ->join('goods g', 'o.product_id = g.id')
                ->where('o.product_id', $goods_id)
                ->field('o.product_amount, g.goods_name, g.id as goods_id')
                ->find();

            if(!$offlineInfo) {
                return json(['code' => 0, 'msg' => '该商品未设置离线价格']);
            }

            // 计算总成本（应用代理折扣）
            $unitCost = $offlineInfo['product_amount'] * ($agent['discount_rate'] / 100);
            $totalCost = $unitCost * $num;

            // 验证代理余额是否充足
            if($agent['balance'] < $totalCost) {
                return json(['code' => 0, 'msg' => '余额不足，需要 ¥' . number_format($totalCost, 2)]);
            }

            // 开启事务处理
            Db::startTrans();
            try {
                // 扣除代理余额
                Db::name('agents')
                    ->where('id', session('agent_id'))
                    ->update(['balance' => $agent['balance'] - $totalCost]);

                // 批量生成离线卡密数据
                $time = date('Y-m-d H:i:s');
                $cdks = [];
                for($i = 0; $i < $num; $i++) {
                    $cdks[] = [
                        'agent_id' => session('agent_id'),
                        'cdk_code' => $this->generateCdkCode('offline'),
                        'cdk_type' => 'offline',
                        'cost' => $unitCost,
                        'shop' => $goods_id,
                        'status' => 'unused',
                        'expiry_date' => null,
                        'expiry_date2' => 0, // 离线卡密永久有效
                        'created_at' => $time,
                    ];
                }

                // 批量插入卡密记录
                Cdk::insertAll($cdks);

                // 提交事务
                Db::commit();

                // 收集生成的卡密码
                $cdkCodes = array_column($cdks, 'cdk_code');

                if ($num == 1) {
                    return json(['code' => 1, 'msg' => '离线卡密生成成功', 'cdk_code' => $cdkCodes[0]]);
                } else {
                    return json(['code' => 1, 'msg' => '离线卡密生成成功', 'cdk_codes' => $cdkCodes]);
                }
            } catch (\Exception $e) {
                // 发生异常时回滚事务
                Db::rollback();
                return json(['code' => 0, 'msg' => '生成失败:'.$e->getMessage()]);
            }
        }

        return json(['code' => 0, 'msg' => '请求方法错误']);
    }

    // 退出登录
    public function logout()
    {
        // 清除session
        session('agent_id', null);
        session('agent_name', null);

        return json([
            'code' => 1,
            'msg' => '退出成功'
        ]);
    }

    /**
     * 代理封禁卡密功能
     */
    public function banCdk()
    {
        if (Request::isPost()) {
            $cdkCode = trim(Request::param('cdk_code', ''));
            $reason = trim(Request::param('reason', ''));
            $agentId = session('agent_id'); // 获取当前代理ID

            if (empty($cdkCode)) {
                return json(['code' => 0, 'msg' => '请输入卡密码']);
            }

            // 封禁原因不是必填的，可以为空

            try {
                // 查询卡密信息
                $cdkCard = Db::table('cdk_cards')
                    ->where('cdk_code', $cdkCode)
                    ->find();

                if (!$cdkCard) {
                    return json(['code' => 0, 'msg' => '卡密不存在']);
                }

                // 严格验证代理权限：只能封禁自己生成的卡密
                if (empty($cdkCard['agent_id']) || (int)$cdkCard['agent_id'] !== (int)$agentId) {
                    return json(['code' => 0, 'msg' => '您只能封禁自己生成的卡密']);
                }

                // 检查代理状态是否有效
                $agent = Db::name('agents')->where('id', $agentId)->find();
                if (!$agent || $agent['status'] != 1) {
                    return json(['code' => 0, 'msg' => '代理账号状态异常，无法执行操作']);
                }

                // 检查是否已被封禁（如果字段存在）
                if (isset($cdkCard['is_banned']) && $cdkCard['is_banned'] == 1) {
                    return json(['code' => 0, 'msg' => '该卡密已被封禁']);
                }

                // 开始事务
                Db::startTrans();

                // 检查数据库字段是否存在，如果不存在则先添加
                $this->ensureBanFields();

                // 封禁卡密
                $updateData = [
                    'is_banned' => 1,
                    'banned_at' => date('Y-m-d H:i:s'),
                    'banned_reason' => $reason,
                    'banned_by' => $agentId
                ];

                $updateResult = Db::table('cdk_cards')
                    ->where('cdk_code', $cdkCode)
                    ->update($updateData);

                if (!$updateResult) {
                    throw new \Exception('更新卡密状态失败');
                }

                $resultMsg = '卡密封禁成功';

                // 记录卡密状态信息用于调试
                \think\facade\Log::info('代理封禁卡密状态检查', [
                    'cdk_code' => $cdkCode,
                    'status' => $cdkCard['status'] ?? 'null',
                    'assigned_user' => $cdkCard['assigned_user'] ?? 'null',
                    'cdk_type' => $cdkCard['cdk_type'] ?? 'null'
                ]);

                // 如果卡密已使用，需要扣除对应权益
                // 支持多种状态格式：'used', '已使用', 'activated'
                $isUsed = in_array($cdkCard['status'], ['used', '已使用', 'activated']) &&
                         !empty($cdkCard['assigned_user']);

                if ($isUsed) {
                    $userId = $cdkCard['assigned_user'];
                    $cdkType = $cdkCard['cdk_type'];

                    \think\facade\Log::info('代理开始撤销卡密权益', [
                        'cdk_code' => $cdkCode,
                        'user_id' => $userId,
                        'cdk_type' => $cdkType,
                        'shop' => $cdkCard['shop']
                    ]);

                    if ($cdkType === 'permanent') {
                        // 永久版卡密：永久撤销权限 + 彻底回收账号 + 账号重置
                        $this->revokePermanentAccounts($userId, $cdkCard['shop']);
                        $resultMsg .= '，已永久撤销权益并重置账号';

                    } elseif ($cdkType === 'offline') {
                        // 离线卡密：删除用户的离线账号
                        $this->revokeOfflineAccounts($userId, $cdkCard['shop'], $cdkCard);
                        $resultMsg .= '，已删除用户的离线游戏账号';

                    } elseif (strpos($cdkType, 'member_') === 0) {
                        // 会员卡密：扣除会员时间
                        $this->revokeMembershipTime($userId, $cdkCard);
                        $resultMsg .= '，已扣除用户的会员时间';

                    } else {
                        // 在线卡密：扣除在线时间
                        $this->revokeOnlineTime($userId, $cdkCard);
                        $resultMsg .= '，已扣除用户的在线时间';
                    }

                    \think\facade\Log::info('代理卡密权益撤销完成', [
                        'cdk_code' => $cdkCode,
                        'user_id' => $userId,
                        'result_msg' => $resultMsg
                    ]);
                } else {
                    \think\facade\Log::info('代理封禁-卡密未使用或无分配用户，跳过权益撤销', [
                        'cdk_code' => $cdkCode,
                        'status' => $cdkCard['status'] ?? 'null',
                        'assigned_user' => $cdkCard['assigned_user'] ?? 'null'
                    ]);
                }

                // 记录操作日志（如果日志表存在）
                try {
                    // 检查日志表是否存在
                    $logTables = Db::query("SHOW TABLES LIKE 'tk_agent_logs'");
                    if (!empty($logTables)) {
                        Db::name('agent_logs')->insert([
                            'agent_id' => $agentId,
                            'action' => 'ban_cdk',
                            'content' => "封禁卡密: {$cdkCode}, 原因: {$reason}",
                            'ip' => Request::ip(),
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    } else {
                        // 如果日志表不存在，记录到系统日志
                        \think\facade\Log::info("代理封禁卡密", [
                            'agent_id' => $agentId,
                            'cdk_code' => $cdkCode,
                            'reason' => $reason,
                            'ip' => Request::ip()
                        ]);
                    }
                } catch (\Exception $logError) {
                    // 日志记录失败不影响主要功能
                    \think\facade\Log::error('记录封禁日志失败: ' . $logError->getMessage());
                }

                Db::commit();

                return json(['code' => 1, 'msg' => $resultMsg]);

            } catch (\Exception $e) {
                Db::rollback();
                \think\facade\Log::error('代理封禁卡密失败: ' . $e->getMessage());
                return json(['code' => 0, 'msg' => '封禁失败：' . $e->getMessage()]);
            }
        }

        return View::fetch('/cdk/ban');
    }

    /**
     * 代理批量封禁卡密功能
     */
    public function batchBanCdk()
    {
        if (Request::isPost()) {
            $cdkCodes = trim(Request::param('cdk_codes', ''));
            $reason = trim(Request::param('reason', ''));
            $agentId = session('agent_id');

            if (empty($cdkCodes)) {
                return json(['code' => 0, 'msg' => '请输入要封禁的卡密码']);
            }

            // 解析卡密列表（一行一个）
            $cdkList = array_filter(array_map('trim', explode("\n", $cdkCodes)));

            if (empty($cdkList)) {
                return json(['code' => 0, 'msg' => '请输入有效的卡密码']);
            }

            if (count($cdkList) > 50) {
                return json(['code' => 0, 'msg' => '代理单次最多只能封禁50张卡密']);
            }

            try {
                // 检查数据库字段是否存在
                $this->ensureBanFields();

                // 开始事务
                Db::startTrans();

                $results = [
                    'total' => count($cdkList),
                    'success' => 0,
                    'failed' => 0,
                    'details' => []
                ];

                foreach ($cdkList as $index => $cdkCode) {
                    $cdkCode = trim($cdkCode);
                    if (empty($cdkCode)) {
                        continue;
                    }

                    try {
                        $result = $this->processAgentSingleBan($cdkCode, $reason, $agentId);
                        if ($result['success']) {
                            $results['success']++;
                            $results['details'][] = [
                                'cdk_code' => $cdkCode,
                                'status' => 'success',
                                'message' => $result['message']
                            ];
                        } else {
                            $results['failed']++;
                            $results['details'][] = [
                                'cdk_code' => $cdkCode,
                                'status' => 'failed',
                                'message' => $result['message']
                            ];
                        }
                    } catch (\Exception $e) {
                        $results['failed']++;
                        $results['details'][] = [
                            'cdk_code' => $cdkCode,
                            'status' => 'failed',
                            'message' => '处理异常: ' . $e->getMessage()
                        ];
                    }
                }

                // 记录批量操作日志
                try {
                    $logTables = Db::query("SHOW TABLES LIKE 'tk_agent_logs'");
                    if (!empty($logTables)) {
                        Db::name('agent_logs')->insert([
                            'agent_id' => $agentId,
                            'action' => 'batch_ban_cdk',
                            'content' => "批量封禁卡密: 总数{$results['total']}, 成功{$results['success']}, 失败{$results['failed']}",
                            'ip' => Request::ip(),
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    }
                } catch (\Exception $logError) {
                    \think\facade\Log::error('记录代理批量封禁日志失败: ' . $logError->getMessage());
                }

                Db::commit();

                $message = "批量封禁完成！总数: {$results['total']}, 成功: {$results['success']}, 失败: {$results['failed']}";

                return json([
                    'code' => 1,
                    'msg' => $message,
                    'data' => $results
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                \think\facade\Log::error('代理批量封禁卡密失败: ' . $e->getMessage());
                return json(['code' => 0, 'msg' => '批量封禁失败: ' . $e->getMessage()]);
            }
        }

        return View::fetch('/cdk/batch_ban');
    }

    /**
     * 代理处理单个卡密封禁
     */
    private function processAgentSingleBan($cdkCode, $reason, $agentId)
    {
        // 查询卡密信息
        $cdkCard = Db::table('cdk_cards')
            ->where('cdk_code', $cdkCode)
            ->find();

        if (!$cdkCard) {
            return ['success' => false, 'message' => '卡密不存在'];
        }

        // 严格验证代理权限：只能封禁自己生成的卡密
        if (empty($cdkCard['agent_id']) || (int)$cdkCard['agent_id'] !== (int)$agentId) {
            return ['success' => false, 'message' => '您只能封禁自己生成的卡密'];
        }

        // 检查代理状态是否有效
        $agent = Db::name('agents')->where('id', $agentId)->find();
        if (!$agent || $agent['status'] != 1) {
            return ['success' => false, 'message' => '代理账号状态异常，无法执行操作'];
        }

        // 检查是否已被封禁
        if (isset($cdkCard['is_banned']) && $cdkCard['is_banned'] == 1) {
            return ['success' => false, 'message' => '该卡密已被封禁'];
        }

        // 封禁卡密
        $updateResult = Db::table('cdk_cards')
            ->where('cdk_code', $cdkCode)
            ->update([
                'is_banned' => 1,
                'banned_at' => date('Y-m-d H:i:s'),
                'banned_reason' => $reason,
                'banned_by' => $agentId
            ]);

        if (!$updateResult) {
            return ['success' => false, 'message' => '更新卡密状态失败'];
        }

        $resultMsg = '封禁成功';

        // 如果卡密已使用，需要扣除对应权益
        // 支持多种状态格式：'used', '已使用', 'activated'
        $isUsed = in_array($cdkCard['status'], ['used', '已使用', 'activated']) &&
                 !empty($cdkCard['assigned_user']);

        if ($isUsed) {
            $userId = $cdkCard['assigned_user'];
            $cdkType = $cdkCard['cdk_type'];

            \think\facade\Log::info('代理批量封禁-开始撤销卡密权益', [
                'cdk_code' => $cdkCode,
                'user_id' => $userId,
                'cdk_type' => $cdkType,
                'shop' => $cdkCard['shop']
            ]);

            if ($cdkType === 'permanent') {
                $this->revokePermanentAccounts($userId, $cdkCard['shop']);
                $resultMsg .= '，已撤销永久版权益';
            } elseif ($cdkType === 'offline') {
                $this->revokeOfflineAccounts($userId, $cdkCard['shop'], $cdkCard);
                $resultMsg .= '，已删除离线账号';
            } elseif (strpos($cdkType, 'member_') === 0) {
                $this->revokeMembershipTime($userId, $cdkCard);
                $resultMsg .= '，已扣除会员时间';
            } else {
                $this->revokeOnlineTime($userId, $cdkCard);
                $resultMsg .= '，已扣除在线时间';
            }
        } else {
            \think\facade\Log::info('代理批量封禁-卡密未使用或无分配用户，跳过权益撤销', [
                'cdk_code' => $cdkCode,
                'status' => $cdkCard['status'] ?? 'null',
                'assigned_user' => $cdkCard['assigned_user'] ?? 'null'
            ]);
        }

        return ['success' => true, 'message' => $resultMsg];
    }

    /**
     * 确保数据库字段存在
     */
    private function ensureBanFields()
    {
        try {
            // 检查 is_banned 字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM `cdk_cards` LIKE 'is_banned'");
            if (empty($columns)) {
                // 添加 is_banned 字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `is_banned` tinyint(1) DEFAULT 0 COMMENT '是否被封禁'");
            }

            // 检查 banned_at 字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM `cdk_cards` LIKE 'banned_at'");
            if (empty($columns)) {
                // 添加 banned_at 字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_at` datetime NULL COMMENT '封禁时间'");
            }

            // 检查 banned_reason 字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM `cdk_cards` LIKE 'banned_reason'");
            if (empty($columns)) {
                // 添加 banned_reason 字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_reason` varchar(255) NULL COMMENT '封禁原因'");
            }

            // 检查 banned_by 字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM `cdk_cards` LIKE 'banned_by'");
            if (empty($columns)) {
                // 添加 banned_by 字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_by` int(11) NULL COMMENT '封禁操作者ID'");
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('检查数据库字段失败: ' . $e->getMessage());
            // 字段检查失败不影响主要功能，继续执行
        }
    }

    /**
     * 删除卡密功能
     */
    public function deleteCdk()
    {
        if (Request::isPost()) {
            $cdkCode = trim(Request::param('cdk_code', ''));
            $agentId = session('agent_id'); // 获取当前代理ID

            if (empty($cdkCode)) {
                return json(['code' => 0, 'msg' => '请输入卡密码']);
            }

            try {
                // 查询卡密信息
                $cdkCard = Db::table('cdk_cards')
                    ->where('cdk_code', $cdkCode)
                    ->find();

                if (!$cdkCard) {
                    return json(['code' => 0, 'msg' => '卡密不存在']);
                }

                // 验证代理权限：只能删除自己生成的卡密
                if ($cdkCard['agent_id'] != $agentId) {
                    return json(['code' => 0, 'msg' => '您只能删除自己生成的卡密']);
                }

                // 开始事务
                Db::startTrans();

                // 删除卡密
                $deleteResult = Db::table('cdk_cards')
                    ->where('cdk_code', $cdkCode)
                    ->delete();

                if (!$deleteResult) {
                    throw new \Exception('删除卡密失败');
                }

                // 记录操作日志（如果日志表存在）
                try {
                    // 检查日志表是否存在
                    $logTables = Db::query("SHOW TABLES LIKE 'tk_agent_logs'");
                    if (!empty($logTables)) {
                        Db::name('agent_logs')->insert([
                            'agent_id' => $agentId,
                            'action' => 'delete_cdk',
                            'content' => "删除卡密: {$cdkCode}",
                            'ip' => Request::ip(),
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    } else {
                        // 如果日志表不存在，记录到系统日志
                        \think\facade\Log::info("代理删除卡密", [
                            'agent_id' => $agentId,
                            'cdk_code' => $cdkCode,
                            'ip' => Request::ip()
                        ]);
                    }
                } catch (\Exception $logError) {
                    // 日志记录失败不影响主要功能
                    \think\facade\Log::error('记录删除日志失败: ' . $logError->getMessage());
                }

                Db::commit();

                return json(['code' => 1, 'msg' => '卡密删除成功']);

            } catch (\Exception $e) {
                Db::rollback();
                \think\facade\Log::error('代理删除卡密失败: ' . $e->getMessage());
                return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
            }
        }

        return json(['code' => 0, 'msg' => '请求方法错误']);
    }

    // 以下方法与管理员后台相同，复用逻辑
    private function revokePermanentAccounts($userId, $goodsId)
    {
        try {
            $revokedCount = 0;

            // 查找用户的永久版账号
            $accounts = Db::name('account')
                ->where([
                    'ac_uid' => $userId,
                    'ac_goods' => $goodsId,
                    'ac_vip' => 2  // 永久版标识
                ])
                ->select();

            foreach ($accounts as $account) {
                // 1. 撤销相关的永久版订单 (永久撤销权限)
                $this->revokePermanentOrders($userId, $account['id']);

                // 2. 账号完全重置 (彻底回收账号 + 账号重置)
                $this->resetAccountCompletely($account['id'], '代理封禁永久版卡密');

                $revokedCount++;
                \think\facade\Log::info("代理永久版账号已重置: {$account['id']}, 用户: {$userId}");
            }

            \think\facade\Log::info("代理成功处理用户 {$userId} 的永久版账号", [
                'goods_id' => $goodsId,
                'processed_count' => $revokedCount,
                'action' => 'permanent_revoke_and_reset',
                'description' => '永久撤销权限 + 彻底回收账号 + 账号重置'
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error("代理撤销永久版账号异常: " . $e->getMessage(), [
                'user_id' => $userId,
                'goods_id' => $goodsId
            ]);
        }
    }

    /**
     * 撤销永久版账号相关的订单 (优化版：永久撤销权限)
     */
    private function revokePermanentOrders($userId, $accountId)
    {
        // 1. 处理普通永久版订单 (GoodType = 2)
        $orders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'ord_aid' => $accountId,
                'GoodType' => 2,  // 永久版账号
                'ord_ifpay' => 1,
                'or_maturity' => 1  // 未到期的订单
            ])
            ->select();

        foreach ($orders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("代理撤销永久版订单: {$order['id']}");
        }

        // 2. 处理永久版卡密兑换订单 (GoodType = 3)
        $cdkOrders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'GoodType' => 3,  // 永久版卡密
                'ord_ifpay' => 1,
                'or_maturity' => 1
            ])
            ->select();

        foreach ($cdkOrders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("代理撤销永久版卡密兑换订单: {$order['id']}");
        }

        // 3. 处理通用CDK兑换订单
        $generalCdkOrders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'ord_aid' => $accountId,
                'ord_type' => ['CDK兑换', '永久版卡密兑换'],
                'ord_ifpay' => 1,
                'or_maturity' => 1
            ])
            ->select();

        foreach ($generalCdkOrders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("代理撤销CDK兑换订单: {$order['id']}");
        }
    }

    /**
     * 撤销离线卡密权限（优化版：无需回收账号，只撤销订单权限）
     */
    private function revokeOfflineAccounts($userId, $goodsId, $cdkCard = null)
    {
        try {
            $revokedCount = 0;

            // 根据用户ID + ord_combo(商品ID) + or_maturity=1 查询有效的离线订单
            $orders = Db::name('order')
                ->where([
                    'ord_uid' => $userId,
                    'GoodType' => 0,  // 离线账号
                    'ord_ifpay' => 1,
                    'ord_combo' => $goodsId,  // 使用ord_combo字段匹配商品ID
                    'or_maturity' => 1  // 只处理有效的订单
                ])
                ->field('id, ord_bbh, ord_name, ord_combo, ord_aid')
                ->select();

            foreach ($orders as $order) {
                // 撤销订单权限（设置or_maturity=0）
                $this->revokeOrder($order['id']);
                $revokedCount++;
                \think\facade\Log::info("代理撤销离线卡密订单权限: 订单ID={$order['id']}, 订单号={$order['ord_bbh']}, 商品ID={$order['ord_combo']}");
            }

            // 如果没有找到订单，尝试通过ord_type查找（兼容性处理）
            if ($revokedCount === 0) {
                $fallbackOrders = Db::name('order')
                    ->where([
                        'ord_uid' => $userId,
                        'GoodType' => 0,  // 离线账号
                        'ord_ifpay' => 1,
                        'ord_type' => '离线卡密兑换',
                        'or_maturity' => 1
                    ])
                    ->field('id, ord_bbh, ord_name, ord_combo, ord_aid')
                    ->select();

                foreach ($fallbackOrders as $order) {
                    // 检查商品ID是否匹配
                    if ($order['ord_combo'] == $goodsId) {
                        $this->revokeOrder($order['id']);
                        $revokedCount++;
                        \think\facade\Log::info("代理通过兼容性查询撤销离线卡密订单: 订单ID={$order['id']}, 订单号={$order['ord_bbh']}");
                    }
                }
            }

            \think\facade\Log::info("代理成功撤销用户 {$userId} 的离线卡密权限", [
                'goods_id' => $goodsId,
                'revoked_orders_count' => $revokedCount,
                'action' => 'revoke_offline_orders_only',
                'description' => '仅撤销订单权限，不回收账号资源'
            ]);

            return $revokedCount;
        } catch (\Exception $e) {
            \think\facade\Log::error("代理撤销离线卡密权限异常: " . $e->getMessage(), [
                'user_id' => $userId,
                'goods_id' => $goodsId
            ]);
            return 0;
        }
    }

    /**
     * 释放账号
     */
    private function releaseAccount($accountId)
    {
        Db::name('account')
            ->where('id', $accountId)
            ->update([
                'ac_uid' => null,
                'ac_vip' => 0,
                'ac_sell' => 1,
                'ac_states' => 1,
                'exit_time' => null
            ]);
    }

    /**
     * 账号完全重置 (用于永久版卡密封禁：彻底回收账号 + 账号重置)
     * @param int $accountId 账号ID
     * @param string $reason 重置原因
     */
    private function resetAccountCompletely($accountId, $reason)
    {
        // 获取账号名称
        $acName = Db::name('account')->where('id', $accountId)->value('ac_name');

        if ($acName) {
            // ✅ 使用统一的账号回收方法
            \app\controller\Api::recycleAccount($acName);
        } else {
            // 如果找不到账号名称，使用原有逻辑
            Db::name('account')
                ->where('id', $accountId)
                ->update([
                    'ac_uid' => null,
                    'ac_vip' => 0,
                    'ac_sell' => 1,
                    'ac_states' => 1,
                    'exit_time' => null,
                    'token_limit' => 0,
                    'modify' => 0,
                    'expire_status' => 0,  // ✅ 重置过期状态
                    'is_locked' => 0
                ]);
        }

        \think\facade\Log::info("代理账号完全重置完成: {$accountId}, 原因: {$reason}");
    }

    /**
     * 撤销订单（设为已到期，用户页面不再显示）
     */
    private function revokeOrder($orderId)
    {
        Db::name('order')
            ->where('id', $orderId)
            ->update([
                'or_maturity' => 0,  // 设为已到期
                'ord_remarks' => '代理封禁卡密：权益已永久撤销'
            ]);
    }

    /**
     * 删除订单（彻底删除，用于离线卡密等不检查or_maturity的页面）
     */
    private function deleteOrder($orderId)
    {
        Db::name('order')
            ->where('id', $orderId)
            ->delete();

        \think\facade\Log::info("代理删除订单记录: {$orderId}");
    }

    /**
     * 记录卡密调试信息
     * 用于调试和排查卡密相关问题
     */
    private function logCdkDebugInfo($message, $data = [])
    {
        $debugInfo = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'data' => $data,
            'request_id' => uniqid('cdk_agent_', true),
            'agent_id' => session('agent_id') ?? 'unknown',
            'ip' => request()->ip() ?? 'unknown'
        ];

        // 记录到日志文件
        \think\facade\Log::info('代理CDK调试信息: ' . $message, $debugInfo);

        // 如果是重要操作，也记录到数据库（如果需要的话）
        if (in_array($message, ['卡密封禁', '卡密解封', '卡密生成', '卡密删除'])) {
            try {
                Db::name('agent_logs')->insert([
                    'agent_id' => session('agent_id') ?? 0,
                    'action' => 'cdk_debug',
                    'content' => json_encode($debugInfo, JSON_UNESCAPED_UNICODE),
                    'ip' => request()->ip(),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } catch (\Exception $e) {
                // 如果数据库记录失败，只记录到文件日志
                \think\facade\Log::warning('代理CDK调试信息数据库记录失败: ' . $e->getMessage());
            }
        }
    }

    private function revokeMembershipTime($userId, $cdkCard)
    {
        try {
            // 解析会员类型
            $membershipType = str_replace('member_', '', $cdkCard['cdk_type']);

            // 获取会员类型信息
            $membershipInfo = Db::name('membership_pricing')
                ->where('membership_type', $membershipType)
                ->find();

            if (!$membershipInfo) {
                \think\facade\Log::warning("未找到会员类型信息: {$membershipType}");
                return;
            }

            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                \think\facade\Log::warning("未找到用户: {$userId}");
                return;
            }

            // 检查用户是否有会员时间
            if (empty($user['exit_time'])) {
                \think\facade\Log::info("用户 {$userId} 没有会员时间，无需扣除");
                return;
            }

            // 计算要扣除的时间（使用卡密的有效期）
            $deductMonths = (int)$cdkCard['expiry_date2']; // 卡密的有效期（月数）

            // 计算新的到期时间
            $currentExpiry = strtotime($user['exit_time']);
            $deductSeconds = $deductMonths * 30 * 24 * 60 * 60; // 月数转换为秒
            $newExpiry = max(time(), $currentExpiry - $deductSeconds); // 不能早于当前时间

            // 更新用户会员时间
            $updateResult = Db::name('user')
                ->where('id', $userId)
                ->update([
                    'exit_time' => date('Y-m-d H:i:s', $newExpiry)
                ]);

            if ($updateResult) {
                \think\facade\Log::info("代理成功扣除用户 {$userId} 的会员时间", [
                    'original_expiry' => $user['exit_time'],
                    'new_expiry' => date('Y-m-d H:i:s', $newExpiry),
                    'deducted_months' => $deductMonths
                ]);
            } else {
                \think\facade\Log::error("代理扣除用户 {$userId} 会员时间失败");
            }
        } catch (\Exception $e) {
            \think\facade\Log::error("代理撤销会员时间异常: " . $e->getMessage(), [
                'user_id' => $userId,
                'cdk_card' => $cdkCard
            ]);
        }
    }

    private function revokeOnlineTime($userId, $cdkCard)
    {
        try {
            $revokedCount = 0;

            // 方法1：通过卡密的account_id字段查找（如果存在）
            if (isset($cdkCard['account_id']) && $cdkCard['account_id']) {
                $account = Db::name('account')->where('id', $cdkCard['account_id'])->find();
                if ($account && $account['ac_uid'] == $userId && $account['ac_goods'] == $cdkCard['shop']) {
                    $this->revokeOnlineAccount($account, $cdkCard);
                    // 查找并撤销相关订单
                    $this->revokeOnlineOrders($userId, $account['id']);
                    $revokedCount++;
                    \think\facade\Log::info("代理通过卡密account_id撤销在线账号: {$account['id']}");
                }
            }

            // 方法2：查找用户通过在线卡密获得的账号
            $accounts = Db::name('account')
                ->where([
                    'ac_uid' => $userId,
                    'ac_goods' => $cdkCard['shop'],
                    'ac_vip' => 1  // 在线VIP账号
                ])
                ->select();

            foreach ($accounts as $account) {
                $this->revokeOnlineAccount($account, $cdkCard);
                // 查找并撤销相关订单
                $this->revokeOnlineOrders($userId, $account['id']);
                $revokedCount++;
                \think\facade\Log::info("代理撤销在线账号: {$account['id']}");
            }

            \think\facade\Log::info("代理成功撤销用户 {$userId} 的在线时间", [
                'cdk_code' => $cdkCard['cdk_code'],
                'cdk_type' => $cdkCard['cdk_type'],
                'revoked_count' => $revokedCount
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error("代理撤销在线时间异常: " . $e->getMessage(), [
                'user_id' => $userId,
                'cdk_card' => $cdkCard
            ]);
        }
    }

    /**
     * 撤销单个在线账号的时间
     */
    private function revokeOnlineAccount($account, $cdkCard)
    {
        // 根据卡密类型扣除时间
        if ($cdkCard['cdk_type'] === 'day') {
            // 日卡：扣除天数
            $deductDays = (int)$cdkCard['expiry_date2'];
            $currentExpiry = $account['exit_time'];
            $newExpiry = max(time(), $currentExpiry - ($deductDays * 24 * 60 * 60));

            Db::name('account')
                ->where('id', $account['id'])
                ->update(['exit_time' => $newExpiry]);

        } elseif ($cdkCard['cdk_type'] === 'hour') {
            // 小时卡：扣除小时数
            $deductHours = (int)$cdkCard['expiry_date2'];
            $currentExpiry = $account['exit_time'];
            $newExpiry = max(time(), $currentExpiry - ($deductHours * 60 * 60));

            Db::name('account')
                ->where('id', $account['id'])
                ->update(['exit_time' => $newExpiry]);
        } else {
            // 其他类型的在线卡密，直接设置为已到期
            Db::name('account')
                ->where('id', $account['id'])
                ->update(['exit_time' => time()]);
        }
    }

    /**
     * 撤销在线账号相关的订单
     */
    private function revokeOnlineOrders($userId, $accountId)
    {
        // 查找相关的在线订单
        $orders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'ord_aid' => $accountId,
                'GoodType' => 1,  // 在线账号
                'ord_ifpay' => 1,
                'or_maturity' => 1  // 未到期的订单
            ])
            ->select();

        foreach ($orders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("代理撤销在线订单: {$order['id']}");
        }

        // 也查找CDK兑换类型的订单
        $cdkOrders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'ord_aid' => $accountId,
                'ord_type' => 'CDK兑换',
                'ord_ifpay' => 1,
                'or_maturity' => 1
            ])
            ->select();

        foreach ($cdkOrders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("代理撤销CDK兑换订单: {$order['id']}");
        }
    }

}