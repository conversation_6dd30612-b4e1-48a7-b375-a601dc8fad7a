

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>网站设置</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">网站设置</div>
          <div class="layui-card-body" pad15>
            
            <div class="layui-form" wid100 lay-filter="">
              <div class="layui-form-item">
                <label class="layui-form-label">网站名称</label>
                <div class="layui-input-block">
                  <input type="text" name="sy_title" value="{$system.sy_title}" class="layui-input">
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">网站域名</label>
                <div class="layui-input-block">
                  <input type="text" name="sy_url" lay-verify="url" value="{$system.sy_url}" class="layui-input">
                </div>
              </div>
              <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">首页标题</label>
                <div class="layui-input-block">
                  <textarea name="sy_name" class="layui-textarea">{$system.sy_name}</textarea>
                </div>
              </div>

                              <div class="layui-form-item">
                <label class="layui-form-label">底部微信号</label>
                <div class="layui-input-block">
                  <input type="text" name="sy_wx" value="{$system.sy_wx}" class="layui-input">
                </div>
              </div>
                            <div class="layui-form-item">
                <label class="layui-form-label">底部手机号</label>
                <div class="layui-input-block">
                  <input type="text" name="sy_sjh" value="{$system.sy_sjh}" class="layui-input">
                </div>
              </div>
                            <div class="layui-form-item">
                <label class="layui-form-label">底部邮箱号</label>
                <div class="layui-input-block">
                  <input type="text" name="sy_email" value="{$system.sy_email}" class="layui-input">
                </div>
              </div>
                
                <div class="layui-form-item">
                  <label class="layui-form-label">首页logo</label>
                  <div class="layui-input-inline">
                    <input type="text" name="sy_logo" lay-verify="required" placeholder="请上传图片" autocomplete="off" class="layui-input" value="{$system.sy_logo}">
                  </div>
                  <button style="float: left;" type="button" class="layui-btn" id="layuiadmin-upload-useradmin">上传图片</button> 
                </div>  
              <div class="layui-form-item">
                  <label class="layui-form-label">公众号二维码</label>
                  <div class="layui-input-inline">
                    <input type="text" name="sy_gzh" lay-verify="required" placeholder="请上传图片" autocomplete="off" class="layui-input" value="{$system.sy_gzh}">
                  </div>
                  <button style="float: left;" type="button" class="layui-btn" id="layuiadmin-upload-useradmin2">上传图片</button> 
                </div>
                <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">群链接</label>
                <div class="layui-input-block">
                  <textarea name="sy_qlj" class="layui-textarea">{$system.sy_qlj}</textarea>
                </div>
              </div>
              <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">网站关键词</label>
                <div class="layui-input-block">
                  <textarea name="sy_key" class="layui-textarea" placeholder="多个关键词用英文状态 , 号分割">{$system.sy_key}</textarea>
                </div>
              </div>
              <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">网站描述描述</label>
                <div class="layui-input-block">
                  <textarea name="sy_des" class="layui-textarea" placeholder="一个好的描述能更加吸引用户">{$system.sy_des}</textarea>
                </div>
              </div>
              <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">底部简介</label>
                <div class="layui-input-block">
                  <textarea name="sy_footdes" class="layui-textarea">{$system.sy_footdes}</textarea>
                </div>
              </div>
              <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">自定义JS</label>
                <div class="layui-input-block">
                  <textarea name="sy_js" class="layui-textarea" placeholder="可自定义三方统计代码">{$system.sy_js}</textarea>
                </div>
              </div>
              
              <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">底部版权</label>
                <div class="layui-input-block">
                  <textarea name="sy_footer" class="layui-textarea">{$system.sy_footer}</textarea>
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="set_website">确认保存</button>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
    
  }).use(['index','set', 'form','upload'], function(){
     //图片上传
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload ;
    upImg('#layuiadmin-upload-useradmin')
    upImg('#layuiadmin-upload-useradmin2')
    function upImg(id){
            //图片上传
        upload.render({
          elem: id
          ,url: '/uploads'
          ,accept: 'images'
          ,method: 'get'
          ,acceptMime: 'image/*'
          ,done: function(res){
            $(this.item).prev("div").children("input").val(res.data.src)
          }
        });
    }

  // 提交事件
  form.on('submit(set_website)', function(data){
    var field = data.field; // 获取表单字段值
    $.ajax({
        url: '/admin/base/website',
        method: 'post',
        data: data.field,
        success: function(res){
        layer.msg('保存成功');
        }
    });
    return false; // 阻止默认 form 跳转
  });
    
    
  });
  </script>
</body>
</html>