<!DOCTYPE html>
<html>
<head>
    <title>离线账号购买BUG修复测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .before, .after { padding: 15px; border-radius: 5px; }
        .before { background: #fef0f0; border: 1px solid #fde2e2; }
        .after { background: #f0f9ff; border: 1px solid #bfdbfe; }
        .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 3px; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .issue-yes { color: #dc3545; font-weight: bold; }
        .issue-no { color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>离线账号购买BUG修复测试</h1>
        
        <div class="test-section">
            <h3>1. 问题描述</h3>
            <div class="result error">
                <h4>🐛 发现的BUG：</h4>
                <p>用户在购买离线游戏时使用余额支付会出现错误：</p>
                <div style="background: #fff; border: 1px solid #ddd; padding: 10px; border-radius: 5px; margin: 10px 0; text-align: center; color: #dc3545; font-size: 18px;">
                    <strong>500</strong><br>
                    暂无可用离线账号，相应的在线账号正在被使用中或不存在
                </div>
                
                <h4>🔍 问题根源：</h4>
                <p>在 <code>app/admin/controller/Epay.php</code> 的 <code>handleTypeZeroPurchase</code> 方法中，有一个错误的逻辑：</p>
                <ul>
                    <li>要求离线账号必须有对应的在线账号</li>
                    <li>要求在线账号不能被占用</li>
                    <li>这个逻辑是完全错误的，离线账号应该独立存在</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. 离线账号状态检查</h3>
            <button onclick="checkOfflineAccountStatus()">检查离线账号状态</button>
            <div id="statusResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 修复方案对比</h3>
            <div class="comparison">
                <div class="before">
                    <h4>❌ 修复前的错误逻辑</h4>
                    <div class="code-block">// 查找所有符合条件的离线账号
$accounts = Account::where([
    "ac_goods" => $combo["co_goods"],
    "goods_Type" => 0,
])->select();

$validAccount = null;

// 遍历所有找到的离线账号
foreach ($accounts as $offlineAccount) {
    // 获取该离线账号的账号名称
    $acName = $offlineAccount['ac_name'];
    
    // 检查是否存在同名的在线账号
    $onlineAccount = Account::where([
        'ac_name' => $acName,
        'goods_Type' => 1
    ])->find();
    
    // 只有当存在同名在线账号且其状态不是0（未被占用）时，才可以分配
    if ($onlineAccount && $onlineAccount['ac_states'] != 0) {
        $validAccount = $offlineAccount;
        break; // 找到第一个符合条件的账号即可
    }
}

if (!$validAccount) {
    throw new \Exception("暂无可用离线账号，相应的在线账号正在被使用中或不存在");
}</div>
                    <h5>问题：</h5>
                    <ul>
                        <li>❌ 离线账号依赖在线账号存在</li>
                        <li>❌ 要求在线账号不能被占用</li>
                        <li>❌ 逻辑复杂且错误</li>
                    </ul>
                </div>
                
                <div class="after">
                    <h4>✅ 修复后的正确逻辑</h4>
                    <div class="code-block">// 查找可用的离线账号（离线账号独立存在，不依赖在线账号状态）
$validAccount = Account::where([
    "ac_goods" => $combo["co_goods"],
    "goods_Type" => 0,
    "ac_states" => 1,  // 只查找可用状态的离线账号
    "ac_sell" => 1     // 只查找可售卖的离线账号
])->find();

if (!$validAccount) {
    throw new \Exception("暂无可用离线账号，请联系管理员添加账号");
}</div>
                    <h5>优势：</h5>
                    <ul>
                        <li>✅ 离线账号独立存在</li>
                        <li>✅ 只检查自身状态</li>
                        <li>✅ 逻辑简单正确</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. 修复效果</h3>
            <div class="result success">
                <h4>✨ 修复后的改进：</h4>
                <ul>
                    <li><strong>逻辑正确：</strong>离线账号不再依赖在线账号状态</li>
                    <li><strong>性能提升：</strong>减少了不必要的数据库查询</li>
                    <li><strong>错误消除：</strong>不会再出现"相应的在线账号正在被使用中或不存在"错误</li>
                    <li><strong>用户体验：</strong>用户可以正常购买离线游戏</li>
                </ul>
                
                <h4>🎯 业务逻辑澄清：</h4>
                <ul>
                    <li><strong>离线账号：</strong>独立存在，不需要在线验证</li>
                    <li><strong>在线账号：</strong>需要实时验证和状态管理</li>
                    <li><strong>购买条件：</strong>离线账号只需要 ac_states=1 且 ac_sell=1</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>5. 测试指南</h3>
            <div class="result info">
                <h4>🧪 完整测试流程：</h4>
                <ol>
                    <li><strong>准备测试环境：</strong>
                        <ul>
                            <li>确保有可用的离线账号（ac_states=1, ac_sell=1）</li>
                            <li>确保用户有足够的余额</li>
                        </ul>
                    </li>
                    <li><strong>执行购买测试：</strong>
                        <ul>
                            <li>用户登录系统</li>
                            <li>选择离线游戏商品</li>
                            <li>选择使用余额支付</li>
                            <li>完成支付流程</li>
                        </ul>
                    </li>
                    <li><strong>验证结果：</strong>
                        <ul>
                            <li>不应该出现500错误</li>
                            <li>不应该提示"相应的在线账号正在被使用中或不存在"</li>
                            <li>应该成功创建订单</li>
                            <li>应该正确分配离线账号</li>
                        </ul>
                    </li>
                </ol>
                
                <h4>📋 测试要点：</h4>
                <ul>
                    <li><strong>余额支付：</strong>重点测试余额支付方式</li>
                    <li><strong>离线游戏：</strong>确保测试的是离线游戏商品</li>
                    <li><strong>账号状态：</strong>验证账号分配后的状态</li>
                    <li><strong>订单记录：</strong>检查订单是否正确创建</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function checkOfflineAccountStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在检查离线账号状态...';
            
            fetch('/admin/TestController/testOfflineAccountPurchase')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        const stats = data.data.offline_account_stats;
                        const goods = data.data.goods_with_offline_accounts;
                        const orders = data.data.recent_offline_orders;
                        const issues = data.data.issues;
                        const fixStatus = data.data.fix_status;
                        
                        let statusClass = issues.length > 0 ? 'warning' : 'success';
                        
                        resultDiv.className = `result ${statusClass}`;
                        resultDiv.innerHTML = `
                            <h4>📊 离线账号状态统计：</h4>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">${stats.total}</div>
                                    <div class="stat-label">总离线账号数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number ${stats.available > 0 ? 'issue-no' : 'issue-yes'}">${stats.available}</div>
                                    <div class="stat-label">可用账号数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.unavailable}</div>
                                    <div class="stat-label">不可用账号数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.not_for_sale}</div>
                                    <div class="stat-label">不可售账号数</div>
                                </div>
                            </div>
                            
                            ${issues.length > 0 ? `
                                <h5>⚠️ 发现的问题：</h5>
                                <ul>
                                    ${issues.map(issue => `<li class="issue-yes">${issue}</li>`).join('')}
                                </ul>
                            ` : '<h5 class="issue-no">✅ 没有发现问题，离线账号状态正常</h5>'}
                            
                            <h5>🔧 修复状态：</h5>
                            <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;">
                                <p><strong>问题：</strong>${fixStatus.problem}</p>
                                <p><strong>解决方案：</strong>${fixStatus.solution}</p>
                                <p><strong>修复文件：</strong>${fixStatus.fixed_file}</p>
                                <p><strong>修复方法：</strong>${fixStatus.fixed_method}</p>
                            </div>
                            
                            ${goods.length > 0 ? `
                                <h5>📦 有离线账号的商品：</h5>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>商品ID</th>
                                            <th>商品名称</th>
                                            <th>总账号数</th>
                                            <th>可用账号数</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${goods.map(good => `
                                            <tr>
                                                <td>${good.ac_goods}</td>
                                                <td>${good.goods_name || '未知商品'}</td>
                                                <td>${good.total_accounts}</td>
                                                <td class="${good.available_accounts > 0 ? 'issue-no' : 'issue-yes'}">${good.available_accounts}</td>
                                                <td class="${good.available_accounts > 0 ? 'issue-no' : 'issue-yes'}">${good.available_accounts > 0 ? '可购买' : '无可用账号'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            ` : '<p>没有找到离线账号商品</p>'}
                            
                            ${orders.length > 0 ? `
                                <h5>📋 最近的离线账号订单：</h5>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>订单号</th>
                                            <th>用户</th>
                                            <th>商品名称</th>
                                            <th>金额</th>
                                            <th>状态</th>
                                            <th>时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${orders.slice(0, 5).map(order => `
                                            <tr>
                                                <td>${order.ord_bbh}</td>
                                                <td>${order.us_username || '未知用户'}</td>
                                                <td>${order.ord_name}</td>
                                                <td>¥${order.ord_money}</td>
                                                <td class="${order.or_maturity == 1 ? 'issue-no' : 'issue-yes'}">${order.or_maturity == 1 ? '正常' : '异常'}</td>
                                                <td>${order.time}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            ` : '<p>没有找到最近的离线账号订单</p>'}
                            
                            <h5>🧪 测试建议：</h5>
                            <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;">
                                ${data.data.test_guide.steps.map(step => `<p>• ${step}</p>`).join('')}
                                <p><strong>预期结果：</strong>${data.data.test_guide.expected_result}</p>
                            </div>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `<p>❌ 检查失败：${data.msg}</p>`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<p>❌ 请求失败：${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
