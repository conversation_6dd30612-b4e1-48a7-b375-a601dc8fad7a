<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>邮件服务</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

<div class="layui-fluid">
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body">
          <div class="layui-tab layui-tab-card">
            <ul class="layui-tab-title">
              <li class="layui-this">邮件服务</li>
              <li>支付设置</li>
              <li>宝塔配置</li>
              <li>阿里云配置</li>
              <li>极验配置</li>
              <li>令牌设置</li>
              <li>账户管理</li>
            </ul>
            <div class="layui-tab-content">
              <!-- 邮件服务配置 -->
              <div class="layui-tab-item layui-show">
                <div class="layui-card-body">
                  <div class="layui-form" wid100 lay-filter="">
                    <div class="layui-form-item">
                      <label class="layui-form-label">SMTP服务器</label>
                      <div class="layui-input-inline">
                        <input type="text" name="em_host" value="{$config.em_host}" class="layui-input">
                      </div>
                      <div class="layui-form-mid layui-word-aux">如：smtp.163.com</div>
                    </div>
                    <div class="layui-form-item">
                      <label class="layui-form-label">SMTP端口号</label>
                      <div class="layui-input-inline" style="width: 80px;">
                        <input type="text" name="em_port" lay-verify="number" value="{$config.em_port}" class="layui-input">
                      </div>
                      <div class="layui-form-mid layui-word-aux">一般为 25 或 465</div>
                    </div>
                    <div class="layui-form-item">
                      <label class="layui-form-label">发件人邮箱</label>
                      <div class="layui-input-inline">
                        <input type="text" name="em_username" value="{$config.em_username}" lay-verify="email" autocomplete="off" class="layui-input">
                      </div>
                    </div>
                    <div class="layui-form-item">
                      <label class="layui-form-label">发件人昵称</label>
                      <div class="layui-input-inline">
                        <input type="text" name="em_name" value="{$config.em_name}" autocomplete="off" class="layui-input">
                      </div>
                    </div>
                    <div class="layui-form-item">
                      <label class="layui-form-label">邮箱登入密码</label>
                      <div class="layui-input-inline">
                        <input type="password" name="em_password" value="{$config.em_password}" autocomplete="off" class="layui-input">
                      </div>
                    </div>
                    <div class="layui-form-item">
                      <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="set_system_email">确认保存</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 支付设置 -->
              <div class="layui-tab-item">
                <div class="layui-form" wid100 lay-filter="">
                  <div class="layui-form-item">
                    <label class="layui-form-label">易支付地址</label>
                    <div class="layui-input-inline">
                      <input type="text" name="pay_url" value="{$config.pay_url}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">http(s)://*/,无需加submit.php</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">商 户 PID</label>
                    <div class="layui-input-inline">
                      <input type="text" name="pay_pid" lay-verify="" value="{$config.pay_pid}" class="layui-input">
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">商 户 密 钥</label>
                    <div class="layui-input-inline">
                      <input type="text" name="pay_key" value="{$config.pay_key}" lay-verify="" autocomplete="off" class="layui-input">
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">微信支付</label>
                    <div class="layui-input-inline">
                      <select name="pay_wxpay">
                        <option value="1" {$config.pay_wxpay == 1 ? 'selected' : ''}>开启</option>
                        <option value="0" {$config.pay_wxpay == 0 ? 'selected' : ''}>关闭</option>
                      </select>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">支付宝支付</label>
                    <div class="layui-input-inline">
                      <select name="pay_alipay">
                        <option value="1" {$config.pay_alipay == 1 ? 'selected' : ''}>开启</option>
                        <option value="0" {$config.pay_alipay == 0 ? 'selected' : ''}>关闭</option>
                      </select>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">QQ支付</label>
                    <div class="layui-input-inline">
                      <select name="pay_qqpay">
                        <option value="1" {$config.pay_qqpay == 1 ? 'selected' : ''}>开启</option>
                        <option value="0" {$config.pay_qqpay == 0 ? 'selected' : ''}>关闭</option>
                      </select>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">余额支付</label>
                    <div class="layui-input-inline">
                      <select name="pay_balance">
                        <option value="1" {$config.pay_balance == 1 ? 'selected' : ''}>开启</option>
                        <option value="0" {$config.pay_balance == 0 ? 'selected' : ''}>关闭</option>
                      </select>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <button class="layui-btn" lay-submit lay-filter="set_system_email">确认保存</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 宝塔配置 -->
              <div class="layui-tab-item">
                <div class="layui-form" wid100 lay-filter="">
                  <div class="layui-form-item">
                    <label class="layui-form-label">宝塔面板地址</label>
                    <div class="layui-input-inline">
                      <input type="text" name="email_api" value="{$config.email_api}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">如：141.11.238.56:38664</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">宝塔面板Token</label>
                    <div class="layui-input-inline">
                      <input type="text" name="email_tk" value="{$config.email_tk}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">如：sCK1ec4JvMJOp0fKp91FJ2vHZlI102l0Z23FWDWFfRbxdjqv</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">宝塔面板Cookie</label>
                    <div class="layui-input-inline">
                      <input type="text" name="email_ck" value="{$config.email_ck}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">如：rank=list; pro_end=-1; ltd_end=-1; Path=/www/wwwroot; b4c282c5a1f1bb8dac202e516641d070=9877bae1-5105-4e24-aed1-57f58d541bb9.qh-jQ10rnZGlPlJNdz4FIyNicU8</div>
                  </div>
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <button class="layui-btn" lay-submit lay-filter="set_system_email">确认保存</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 阿里云配置 -->
              <div class="layui-tab-item">
                <div class="layui-form" wid100 lay-filter="">
                  <!-- AccessKey配置 -->
                  <div class="layui-form-item">
                    <label class="layui-form-label">AccessKey ID</label>
                    <div class="layui-input-inline">
                      <input type="text" name="ali_id" value="{$config.ali_id}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">请填写从阿里云获取的AccessKey ID</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">AccessKey Secret</label>
                    <div class="layui-input-inline">
                      <input type="text" name="ali_ck" value="{$config.ali_ck}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">请填写从阿里云获取的AccessKey Secret</div>
                  </div>
                  <!-- 短信配置 -->
                  <div class="layui-form-item">
                    <label class="layui-form-label">短信签名</label>
                    <div class="layui-input-inline">
                      <input type="text" name="sign_Name" value="{$config.sign_Name}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">请填写从阿里云获取的短信签名</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">短信模板ID</label>
                    <div class="layui-input-inline">
                      <input type="text" name="template_Code" value="{$config.template_Code}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">阿里云短信模板ID</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">短信验证开关</label>
                    <div class="layui-input-inline">
                      <select name="sms_verify_switch">
                        <option value="1" {$config.sms_verify_switch == 1 ? 'selected' : ''}>开启</option>
                        <option value="0" {$config.sms_verify_switch == 0 ? 'selected' : ''}>关闭</option>
                      </select>
                    </div>
                    <div class="layui-form-mid layui-word-aux">控制注册和快捷登录时是否需要短信验证码</div>
                  </div>
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <button class="layui-btn" lay-submit lay-filter="set_system_email">确认保存</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 极验配置 -->
              <div class="layui-tab-item">
                <div class="layui-form" wid100 lay-filter="">
                  <div class="layui-form-item">
                    <label class="layui-form-label">极验服务器</label>
                    <div class="layui-input-inline">
                      <input type="text" name="gee_api" value="{$config.gee_api}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">请填写极验服务器地址</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">(会员/至尊会员)账号提取容忍值</label>
                    <div class="layui-input-inline">
                      <input type="text" name="vip_maxAccountLimit" value="{$config.vip_maxAccountLimit}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">请填写容忍值</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">延时按钮时长</label>
                    <div class="layui-input-inline">
                      <input type="text" name="delay_duration" value="{$config.delay_duration}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">定义至尊会员点击延时按钮从当前时间延时多久</div>
                  </div>
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <button class="layui-btn" lay-submit lay-filter="set_system_email">确认保存</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 令牌设置 -->
              <div class="layui-tab-item">
                <div class="layui-form" wid100 lay-filter="">
                  <div class="layui-form-item">
                    <label class="layui-form-label">令牌提取开关</label>
                    <div class="layui-input-inline">
                      <select name="token_switch">
                        <option value="1" {$config.token_switch == 1 ? 'selected' : ''}>开启</option>
                        <option value="0" {$config.token_switch == 0 ? 'selected' : ''}>关闭</option>
                      </select>
                    </div>
                  </div>
                  
                  <div class="layui-form-item">
                    <label class="layui-form-label">令牌接口地址</label>
                    <div class="layui-input-inline">
                      <input type="text" name="token_api" value="{$config.token_api}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">请填写令牌获取接口的完整地址</div>
                  </div>

                  <div class="layui-form-item">
                    <label class="layui-form-label">令牌提取限制</label>
                    <div class="layui-input-inline">
                      <input type="text" name="sy_token_limit" value="{$config.sy_token_limit}" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">仅用于至尊会员模块与离线账号模块的令牌提取次数限制阈值</div>
                  </div>

                  <div class="layui-form-item">
                    <label class="layui-form-label">禁止账号操作提示</label>
                    <div class="layui-input-block">
                      <textarea name="account_operation_disabled_message" placeholder="请输入提示内容" class="layui-textarea" style="min-height: 100px;">{$config.account_operation_disabled_message}</textarea>
                    </div>
                    <div class="layui-form-mid layui-word-aux">用户已提取游戏账号但后台禁止操作时的提示信息</div>
                  </div>

                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <button class="layui-btn" lay-submit lay-filter="set_system_email">确认保存</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 账户到期设置 -->
              <div class="layui-tab-item">
                <div class="layui-form" wid100 lay-filter="">
                  <div class="layui-form-item">
                    <label class="layui-form-label">允许到期不下线</label>
                    <div class="layui-input-inline">
                      <select name="allow_expire_no_offline">
                        <option value="1" {$config.allow_expire_no_offline == 1 ? 'selected' : ''}>开启</option>
                        <option value="0" {$config.allow_expire_no_offline == 0 ? 'selected' : ''}>关闭</option>
                      </select>
                    </div>
                    <div class="layui-form-mid layui-word-aux">0为关，1为开</div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">禁止账号操作提示</label>
                    <div class="layui-input-block">
                      <textarea name="account_operation_disabled_message" placeholder="请输入提示内容" class="layui-textarea" style="min-height: 100px;">{$config.account_operation_disabled_message}</textarea>
                    </div>
                    <div class="layui-form-mid layui-word-aux">用户已提取游戏账号但后台禁止操作时的提示信息</div>
                  </div>
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <button class="layui-btn" lay-submit lay-filter="set_system_email">确认保存</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'set']);
</script>
</body>
</html>
