<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>VIP账号管理</title>
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">VIP账号管理</div>
        <div class="layui-card-body">
            <div style="padding-bottom: 10px;">
                <button class="layui-btn" data-type="add">添加VIP账号</button>
                <button class="layui-btn layui-btn-danger" data-type="batchdel">批量删除</button>
            </div>
            <table id="vip-account-table" lay-filter="vip-account-table"></table>

            <!-- 操作列模板 -->
            <script type="text/html" id="table-content-list">
                <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            </script>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
    layui.config({
        base: '/layuiadmin/' // 静态资源所在路径
    }).extend({
        index: 'lib/index' // 主入口模块
    }).use(['index', 'table', 'form', 'jquery'], function(){
        var $ = layui.$,
            table = layui.table,
            form = layui.form,
            layer = layui.layer;

        // 渲染VIP账号表格
        table.render({
            elem: '#vip-account-table',
            url: '/admin/VipAccountController/getVipAccounts', // 后端接口
            method: 'GET',
            page: true,
            limit: 10,
            cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field: 'id', title: 'ID', sort: true, width:60},
                {field: 'username', title: '账号', width:200},
                {field: 'password', title: '密码', width:200, templet: function(d){
                        return '******'; // 隐藏密码
                    }},
                {field: 'status', title: '状态', width:100, templet: function(d){
                        return d.status == 1 ? '可用' : '不可用';
                    }},
                {field: 'user_id', title: '所属用户ID', width:120},
                {field: 'created_at', title: '添加时间', width:180},
                {field: 'updated_at', title: '更新时间', width:180},
                {fixed: 'right', title:'操作', toolbar: '#table-content-list', width:150}
            ]],
            height: 'full-300'
        });

        // 监听表格工具事件
        table.on('tool(vip-account-table)', function(obj){
            var data = obj.data;
            if(obj.event === 'edit'){
                // 打开编辑页面
                layer.open({
                    type: 2,
                    title: '编辑VIP账号',
                    content: '/admin/VipAccountController/editVipAccount?id=' + data.id,
                    area: ['500px', '400px'],
                    maxmin: true,
                    end: function(){
                        table.reload('vip-account-table');
                    }
                });
            } else if(obj.event === 'del'){
                // 删除单个VIP账号
                layer.confirm('确定删除该VIP账号吗？', function(index){
                    $.post('/admin/VipAccountController/delVipAccount', {id: data.id}, function(res){
                        if(res.code === 0){
                            layer.msg('删除成功', {icon: 1});
                            table.reload('vip-account-table');
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    layer.close(index);
                });
            }
        });

        // 监听添加和批量删除按钮
        var active = {
            add: function(){
                // 打开添加页面
                layer.open({
                    type: 2,
                    title: '添加VIP账号',
                    content: '/admin/VipAccountController/addVipAccount',
                    area: ['500px', '400px'],
                    maxmin: true,
                    btn: ['提交', '取消'],
                    yes: function(index, layero){
                        // 触发 iframe 内的提交按钮
                        var submit = layero.find('iframe').contents().find("#vip-account-form-submit");
                        submit.click();
                    },
                    end: function(){
                        table.reload('vip-account-table');
                    }
                });
            },
            batchdel: function(){
                var checkStatus = table.checkStatus('vip-account-table'),
                    checkData = checkStatus.data;

                if(checkData.length === 0){
                    return layer.msg('请选择要删除的VIP账号');
                }

                layer.confirm('确定删除选中的VIP账号吗？', function(index){
                    var ids = [];
                    checkData.forEach(function(item){
                        ids.push(item.id);
                    });

                    $.post('/admin/VipAccountController/delVipAccount', {id: ids}, function(res){
                        if(res.code === 0){
                            layer.msg('批量删除成功', {icon: 1});
                            table.reload('vip-account-table');
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    layer.close(index);
                });
            }
        };

        $('.layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>

</body>
</html>
