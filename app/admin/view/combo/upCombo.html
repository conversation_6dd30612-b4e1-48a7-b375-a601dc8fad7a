<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>编辑商品</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
      <label class="layui-form-label">商品ID</label>
      <div class="layui-input-inline">
        <input type="text" name="id" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input" value="{$goodsAll.id}" disabled="disabled" style="border:none">
      </div>
    </div>
      <div class="layui-form-item">
      <label class="layui-form-label">选择分类</label>
      <div class="layui-input-inline">
        <select name="goods_class" lay-verify="required" value="{$goodsAll.goods_class}">
            {foreach $goodsclassAll as $index=>$val}
              <option value="{$val.id}">{$val.cl_name}</option>
            {/foreach}
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">商品名称</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_name" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input" value="{$goodsAll.goods_name}">
      </div>
    </div>
     <div class="layui-form-item">
      <label class="layui-form-label">商品图</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img" lay-verify="required" placeholder="请上传图片" autocomplete="off" class="layui-input" value="{$goodsAll.goods_img}">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="layuiadmin-upload-useradmin">上传图片</button> 
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">商品价格</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_money" lay-verify="required" placeholder="商品价格" autocomplete="off" class="layui-input" value="{$goodsAll.goods_money}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">首页展示</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_show" lay-verify="required" placeholder="0不展示,1展示" autocomplete="off" class="layui-input"  value="{$goodsAll.goods_show}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">首页推荐</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_tj" lay-verify="required" placeholder="0不推荐,1推荐" autocomplete="off" class="layui-input" value="{$goodsAll.goods_tj}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">销量</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_xl" lay-verify="required" placeholder="默认为0" autocomplete="off" class="layui-input" value="{$goodsAll.goods_xl}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">标签词</label>
      <div class="layui-input-inline">
        <textarea name="goods_key" lay-verify="required" style="width: 300px; height: 100px;" autocomplete="off" class="layui-textarea" placeholder="多个标签使用,隔开">{$goodsAll.goods_key}</textarea>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">描述</label>
      <div class="layui-input-inline">
        <textarea name="goods_des" lay-verify="required" style="width: 300px; height: 100px;" autocomplete="off" class="layui-textarea" placeholder="商品描述">{$goodsAll.goods_des}</textarea>
      </div>
    </div>
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认添加">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-edit" id="layuiadmin-app-form-edit" value="确认编辑">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form','upload'], function(){
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload ;
      //图片上传
    upload.render({
      elem: '#layuiadmin-upload-useradmin'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });
    //监听提交
    // form.on('submit(layuiadmin-app-form-submit)', function(data){
    //   var field = data.field; //获取提交的字段
    //   var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引  
    // $.ajax({
    //     url: '/admin/base/addGoods',
    //     method: 'post',
    //     data: data.field,
    //     success: function(res){
    //     layer.msg('保存成功');
    //     }
    // });
    // return false; // 阻止默认 form 跳转


    //   //提交 Ajax 成功后，关闭当前弹层并重载表格
    //   //$.ajax({});
    //   parent.layui.table.reload('LAY-app-content-list'); //重载表格
    //   parent.layer.close(index); //再执行关闭 
    // });
  })
  </script>
</body>
</html>