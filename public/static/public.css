.header {
  background-color: #f7f7f7;
  height: 40px;
  min-width: 1200px;
}
.header .container {
  width: 1200px !important;
  margin: 0 auto;
}
.header .header_r .header_r_bk {
  border-right: 0;
}
.header .header_r li {
  line-height: 12px;
  margin-top: 14px;
  height: 12px;
  padding: 0 20px;
  cursor: pointer;
  font-size: 12px;
  color: #838383;
  border-right: 1px solid #848484;
  display: inline-block;
}
.header .personal {
  border-left: 1px solid #848484;
  position: relative;
}
.header .personal:hover {
  background-color: #fff;
  margin-top: 0;
  height: 40px;
  line-height: 40px;
  border-color: #ececec;
}
.header .personal:hover .personal_list {
  top: 40px;
  display: block;
}
.header .personal img {
  vertical-align: middle;
}
.header .personal_list {
  display: none;
  position: absolute;
  left: -1px;
  width: 108px;
  border: 1px solid #ececec;
  border-top: 0;
  background: #fff;
  z-index: 300;
}
.header .personal_item {
  display: block;
  height: 34px;
  line-height: 34px;
  font-size: 12px;
  text-align: center;
  color: #666;
}
.header .personal_item:hover {
  background: #fff4f4;
  color: #e6292c;
}
.header .personal_wxs {
  position: relative;
}
.header .personal_wxs:hover .personal_wx {
  display: block;
}
.header .personal_wx {
  width: 90px;
  height: 90px;
  position: absolute;
  background-color: #fff;
  z-index: 999;
  left: 0;
  bottom: -105px;
  -webkit-box-shadow: 0 2px 10px 0 #e2e2e1;
  box-shadow: 0 2px 10px 0 #e2e2e1;
  display: none;
}
.header .personal_wximg {
  width: 70px;
  height: 70px;
  margin: 10px;
}
.header .lh {
  line-height: 40px;
  color: #848484;
}
.header .ml {
  margin-left: 38px;
}
.header .ml .ml_1 {
  cursor: pointer;
}
.header .color_1 {
  color: #848484;
}
.header .color_red {
  color: #e6292c;
  cursor: pointer;
}
.win_bottom {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.5;
  z-index: 1000;
  display: none;
  cursor: pointer;
}
.win_top {
  background: #fff;
}
.w1200,
.win_top {
  width: 1200px;
  margin: 0 auto;
}
.slogan {
  max-width: 205px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
}
.search_com_box {
  width: 100%;
  background: #fff;
  height: 100px;
}
.search_com_box .logo_box {
  width: 210px;
  height: 100px;
  float: left;
  cursor: pointer;
}
.search_com_box .logo_box img {
  width: 210px;
  height: 100px;
}
.search_com_box .game_search {
  width: 285px;
  height: 40px;
  border: 2px solid #e6292c;
  float: right;
  margin-top: 29px;
}
.search_com_box .game_search .se-election {
  width: 86px;
  height: 40px;
  line-height: 40px;
  position: relative;
  margin-left: 14px;
  cursor: pointer;
  float: left;
  background: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.search_com_box .game_search .se-election:after {
  content: '';
  display: inline-block;
  border: 5px solid transparent;
  border-top-color: #a9a9a9;
  position: absolute;
  right: 12px;
  top: 18px;
}
.search_com_box .game_search .game_choix {
  position: relative;
}
.search_com_box .game_search .game_choix .choix {
  height: 40px;
}
.search_com_box .game_search .game_choix .choix form {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.search_com_box .game_search .game_choix .choix input {
  height: 14px;
  outline: 0;
  border: 0;
  margin-left: 1px;
  color: #000;
  padding-left: 20px;
  width: 190px;
  margin-top: 13px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.search_com_box .game_search .game_choix .choix a {
  display: inline-block;
  width: 88px;
  height: 40px;
  line-height: 40px;
  border: 1px solid #e6292c;
  text-align: center;
  margin-left: 1px;
  background: #e6292c;
  color: #fff;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.search-choose {
  border: 2px solid #e6292c;
  position: absolute;
  height: 300px;
  background: #fff;
  overflow-y: auto;
  z-index: 100;
  width: 580px;
  left: -2px;
  display: none;
}
.search-choose::-webkit-scrollbar {
  width: 6px;
  background: #fff;
  margin-right: 4px;
}
.search-choose::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}
.search-choose::-webkit-scrollbar-thumb {
  border-radius: 3px;
  height: 100px;
  background-color: #ccc;
}
.search-choose p {
  margin-top: 10px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  background: #f0f0f0;
}
.search-choose p a {
  float: left;
  text-align: center;
  padding: 0 5px;
  color: #666;
}
.search-choose p a.cur,
.search-choose p a:hover {
  color: #fff;
  background: #e6292c;
}
.search-choose p a:first-child {
  margin-left: 6px;
  padding: 0 9px;
}
.search-choose ul {
  padding: 10px;
}
.search-choose ul li {
  width: 105px;
  float: left;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-bottom: 1px dashed #ccc;
  margin: 2px 15px;
  color: #666;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.search-choose ul li a {
  position: absolute;
  right: 10px;
  bottom: 10px;
  color: red;
}
.search-choose ul li a:hover {
  text-decoration: underline;
}
.navigation {
  height: 50px;
  background-color: #fff;
  min-width: 1200px;
  border-bottom: 1px solid #eee;
}
.navigation .navigation_m {
  width: 1200px;
  margin: 0 auto;
}
.navigation_nav {
  text-align: center;
  line-height: 50px;
  color: #333;
  font-weight: normal;
  font-size: 14px;
  display: table-cell;
  cursor: pointer;
  position: relative;
  padding: 0 20px;
}
.navigation_nav a {
  font-size: 14px;
  color: #333;
  width: 100%;
  height: 100%;
  display: block;
}
.navigation_nav:hover {
  color: #e6292c;
}
.navigation_nav:hover a {
  color: #e6292c;
}
.navigation_nav .hot-icon {
  position: absolute;
  top: 0;
  right: 20px;
  -webkit-animation: hot_icon 1s linear infinite alternate;
  -moz-animation: hot_icon 1s linear infinite alternate;
  animation: hot_icon 1s linear infinite alternate;
}
.navigation_nav_s {
  display: table;
  width: 100%;
}
.navigation_nav_h {
  position: absolute;
  top: 14px;
  left: 24px;
}
.nav_s {
  position: relative;
}
#nav li.active a {
  color: #096dd9;
}
.navigation_nav_z {
  position: absolute;
  top: 10px;
  right: -14px;
  z-index: 3;
}
@-webkit-keyframes hot_icon {
  0%,
  to {
    top: 0;
  }
  50% {
    top: 10%;
  }
}
@-moz-keyframes hot_icon {
  0%,
  to {
    top: 0;
  }
  50% {
    top: 10%;
  }
}
@keyframes hot_icon {
  0%,
  to {
    top: 0;
  }
  50% {
    top: 10%;
  }
}
.blogroll_box {
  padding: 16px 0;
  background-color: #fff;
}
.blogroll_box dl {
  width: 1200px;
  font-size: 0;
  margin: 0 auto;
}
.blogroll_box dl dd {
  font-size: 16px;
  display: inline-block;
  margin-right: 20px;
  line-height: 30px;
}
.blogroll_box dl dd a {
  color: #666;
}
.blogroll_box dl dd a:hover {
  color: #ffa366;
}
.blogroll_box dl dt {
  font-size: 16px;
  display: inline-block;
  margin-right: 20px;
  color: #666;
}
.footer {
  height: 304px;
  background-color: #252525;
  min-width: 1200px;
}
.footer .footer_zx {
  height: 216px;
  border-bottom: 1px solid #363636;
  overflow: hidden;
}
.footer .footer_center_limt {
  margin: 0 20px;
}
.footer .footer_center_li {
  width: 142px;
  height: 80px;
  display: inline-block;
  text-align: center;
  color: #9b9b9b;
}
.footer .footer_ts {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 28px;
}
.footer .footer_center_mt {
  margin-top: 24px;
}
.footer .footer_center_li img {
  width: 40px;
  height: 40px;
}
.footer .footer_center_lis {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 54px;
  color: #9b9b9b;
}
.footer .ma_lrs {
  margin: 0 40px;
}
.footer .footer_center_lis li {
  display: inline-block;
  cursor: pointer;
}
.footer .footer_center_lis li a {
  color: #9b9b9b;
}
.footer .foot {
  background: #333;
  color: #fff;
  line-height: 22px;
  text-align: center;
  width: 100%;
  min-width: 1200px;
  clear: both;
}
.footer .foot a {
  color: #fff;
}
.footer .bz_s1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 18px;
}
.footer .bz_s1 li {
  float: left;
  width: 166px;
}
.footer .wx {
  position: relative;
}
.footer .wx_box {
  position: absolute;
  height: 100px;
  width: 90px;
  background: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/wx.png)
    no-repeat;
  top: -110px;
  left: -19px;
  display: none;
}
.footer .wx:hover .wx_box {
  display: block;
}
.footer .wx_img {
  height: 90px;
  width: 90px;
}
.footer .wx_img img {
  height: 70px;
  width: 70px;
  margin: 10px 0 0 10px;
}
.service {
  right: 2px;
  z-index: 999;
}
.service .service-item {
  width: 52px;
  height: 52px;
  background: #e6292c;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
}
.service .service-item a {
  display: inline-block;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 20px;
  overflow: hidden;
  position: relative;
}
.service .no_hov {
  margin-bottom: 4px;
}
.service .no_hov span {
  color: #fff;
}
.service .no_hov:last-child {
  margin-bottom: 0;
}
.notice_box {
  width: 145px;
  height: 260px;
  position: fixed;
  top: 500px;
  right: 50%;
  margin-right: 640px;
}
.notice_box a {
  display: block;
}
.notice_box a,
.notice_box a img {
  height: 100%;
  width: 100%;
}
.notice_box .close_btns {
  height: 15px;
  width: 15px;
  font-size: 20px;
  line-height: 15px;
  text-align: center;
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
}
.service {
  position: fixed;
  bottom: 120px;
  left: 55%;
  margin-left: 610px;
  width: 40px;
}
.service div {
  width: 52px;
  height: 52px;
  background: #e6292c;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
}
.service .lx_service {
  margin-bottom: 4px;
}
.service div a {
  display: inline-block;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 20px;
  overflow: hidden;
  position: relative;
}
.service .lx_service a img,
.service .toTop a img {
  margin: 8px 7px;
  top: 0;
}
.service .lx_service a img,
.service .lx_service a span,
.service .toTop a img,
.service .toTop a span {
  position: absolute;
  left: 0;
  transition: all 0.5s;
  -moz-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  -o-transition: all 0.5s;
}
.service .lx_service a span,
.service .toTop a span {
  display: inline-block;
  width: 40px;
  height: 40px;
  text-align: center;
  font-size: 12px;
  color: #fff;
  top: 40px;
}
.service .lx_service a:hover img {
  top: -40px;
}
.service .lx_service a:hover span,
.service .toTop a:hover span {
  top: 4px;
}
.service .toTop a:hover img {
  top: -40px;
}
#kf_txt {
  color: #fff;
}
.service .toTop a img {
  margin: 9px 11px;
}
.no_hov {
  margin-bottom: 4px;
}
.no_hov:last-child {
  margin-bottom: 0;
}
.load_bottom,
.load_top {
  display: none;
}
.load_bottom {
  top: 0;
  width: 100%;
  height: 100%;
  background: #000;
}
.load_bottom,
.load_top {
  position: fixed;
  left: 0;
  z-index: 800000;
}
.load_top {
  top: 10%;
  right: 0;
  width: 750px;
  margin: 0 auto;
  border-radius: 8px;
  color: #000;
  background: #fff;
}
.load_top .content {
  padding: 15px 20px 20px;
  height: auto;
}
.load_top .title-wrapper {
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  border-bottom: 1px solid #333;
  font-weight: 700;
}
.load_top .title-wrapper .load_close {
  float: right;
  margin-top: 12px;
  cursor: pointer;
}
.load_top .title-wrapper .notice-hide {
  float: right;
  margin-right: 10px;
  color: #c5c1c1;
  font-size: 12px;
  font-weight: 400;
  cursor: pointer;
}
.container {
  width: 1200px !important;
  margin: 0 auto;
}
.hot-tag {
  background: #ff4d4f;
  color: #fff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  position: absolute;
  top: 5px;
  right: -5px;
  line-height: normal;
}
.new-tag {
  background: #52c41a;
  color: #fff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  position: absolute;
  top: 5px;
  right: -5px;
  line-height: normal;
}
