<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>标签表单</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="tagForm" style="padding: 20px;">
    <div class="layui-form-item">
      <label class="layui-form-label">标签名称</label>
      <div class="layui-input-block">
        <input type="text" name="name" value="{$tag.name|default=''}" required lay-verify="required" placeholder="请输入标签名称" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <div class="layui-input-block">
        <button class="layui-btn" lay-submit lay-filter="tagForm">提交</button>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form'], function(){
    var form = layui.form
      ,$ = layui.jquery;
    
    form.on('submit(tagForm)', function(data){
      $.post('', data.field, function(res){
        if(res.code == 0){
          parent.layer.msg('保存成功');
          parent.layer.close(parent.layer.getFrameIndex(window.name));
          parent.layui.table.reload('tagList');
        }else{
          layer.msg(res.msg);
        }
      });
      return false;
    });
  });
  </script>
</body>
</html> 