<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>用户管理系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 防止缓存 -->
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <style>
        :root {
            /* 主色调 - 高端配色方案 */
            --primary: #3f51b5;
            --primary-light: #757de8;
            --primary-dark: #2c387e;
            --primary-gradient: linear-gradient(135deg, #3f51b5 0%, #536dfe 100%);
            
            /* 次要色 */
            --secondary: #ff4081;
            --secondary-light: #ff79b0;
            --secondary-dark: #c60055;
            
            /* 中性色 */
            --neutral-900: #1a202c;
            --neutral-800: #2d3748;
            --neutral-700: #4a5568;
            --neutral-600: #718096;
            --neutral-500: #a0aec0;
            --neutral-400: #cbd5e0;
            --neutral-300: #e2e8f0;
            --neutral-200: #edf2f7;
            --neutral-100: #f7fafc;
            --neutral-50: #f8f9fa;
            
            /* 功能色 */
            --success: #00c853;
            --success-light: #5efc82;
            --success-dark: #009624;
            
            --warning: #ffab00;
            --warning-light: #ffdd4b;
            --warning-dark: #c67c00;
            
            --error: #f50057;
            --error-light: #ff5983;
            --error-dark: #bb002f;
            
            --info: #2196f3;
            --info-light: #6ec6ff;
            --info-dark: #0069c0;
            
            /* 背景色 */
            --bg-gradient: linear-gradient(135deg, #f5f7fa 0%, #e4e7f0 100%);
            --card-bg: #ffffff;
            --card-bg-dark: #2c387e;
            
            /* 圆角 */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --radius-full: 9999px;
            
            /* 阴影 */
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08);
            --shadow-lg: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.05);
            --shadow-xl: 0 20px 40px rgba(0,0,0,0.2);
            --shadow-inner: inset 0 2px 4px 0 rgba(0,0,0,0.06);
            --shadow-outline: 0 0 0 3px rgba(63,81,181,0.2);
            
            /* 过渡 */
            --transition-fast: 0.15s ease;
            --transition: 0.3s ease;
            --transition-slow: 0.5s ease;
            
            /* 字体 */
            --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            --font-mono: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            --font-heading: 'Montserrat', sans-serif;
            
            /* 字体大小 */
            --text-xs: 0.75rem;    /* 12px */
            --text-sm: 0.875rem;   /* 14px */
            --text-base: 1rem;     /* 16px */
            --text-lg: 1.125rem;   /* 18px */
            --text-xl: 1.25rem;    /* 20px */
            --text-2xl: 1.5rem;    /* 24px */
            --text-3xl: 1.875rem;  /* 30px */
            
            /* 间距 */
            --spacing-1: 0.25rem;  /* 4px */
            --spacing-2: 0.5rem;   /* 8px */
            --spacing-3: 0.75rem;  /* 12px */
            --spacing-4: 1rem;     /* 16px */
            --spacing-5: 1.25rem;  /* 20px */
            --spacing-6: 1.5rem;   /* 24px */
            --spacing-8: 2rem;     /* 32px */
            --spacing-10: 2.5rem;  /* 40px */
        }

        /* 全局重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: var(--font-sans);
            background: var(--bg-gradient);
            color: var(--neutral-800);
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* 顶部导航栏 */
        .app-header {
            background: var(--primary-gradient);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 64px;
            z-index: 1000;
            box-shadow: var(--shadow-md);
            display: flex;
            align-items: center;
            padding: 0 var(--spacing-6);
        }
        
        .app-header h1 {
            font-size: var(--text-lg);
            font-weight: 600;
            margin: 0;
            flex-grow: 1;
        }
        
        .app-header .user-menu {
            display: flex;
            align-items: center;
        }
        
        .app-header .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background: var(--primary-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-left: var(--spacing-4);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .app-header .avatar:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-lg);
        }
        
        /* 主容器 */
        .app-container {
            padding-top: 20px;
            padding-bottom: var(--spacing-8);
            max-width: 1440px;
            margin: 0 auto;
            padding-left: var(--spacing-6);
            padding-right: var(--spacing-6);
        }
        
        /* 页面标题和描述 */
        .page-title {
            margin-bottom: var(--spacing-6);
        }
        
        .page-title h2 {
            font-size: var(--text-2xl);
            font-weight: 600;
            color: var(--neutral-900);
            margin-bottom: var(--spacing-2);
        }
        
        .page-title p {
            color: var(--neutral-600);
            font-size: var(--text-base);
        }
        
        /* 卡片设计 */
        .app-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            transition: var(--transition);
            margin-bottom: var(--spacing-6);
            position: relative;
        }
        
        .app-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }
        
        .app-card .card-header {
            background: var(--card-bg);
            padding: var(--spacing-6);
            border-bottom: 1px solid var(--neutral-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .app-card .card-title {
            font-size: var(--text-lg);
            font-weight: 600;
            color: var(--neutral-900);
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
        }
        
        .app-card .card-title i {
            color: var(--primary);
        }
        
        .app-card .card-body {
            padding: var(--spacing-6);
        }
        
        /* 搜索表单 */
        .search-form {
            background: var(--neutral-50);
            border-radius: var(--radius-md);
            padding: var(--spacing-5);
            margin-bottom: var(--spacing-6);
            box-shadow: var(--shadow-inner);
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        
        .form-col {
            flex: 1;
            padding: 0 10px;
            min-width: 250px;
            margin-bottom: var(--spacing-4);
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-3);
            margin-top: var(--spacing-4);
            flex-wrap: wrap;
        }
        
        /* 表单控件 */
        .form-group {
            margin-bottom: var(--spacing-4);
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--spacing-2);
            font-weight: 500;
            color: var(--neutral-700);
            font-size: var(--text-sm);
        }
        
        .form-control {
            width: 100%;
            height: 44px;
            padding: 0 var(--spacing-4);
            border: 2px solid var(--neutral-300);
            border-radius: var(--radius-md);
            background-color: white;
            color: var(--neutral-900);
            font-size: var(--text-base);
            transition: var(--transition-fast);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: var(--shadow-outline);
        }
        
        .form-control::placeholder {
            color: var(--neutral-500);
        }
        
        .input-with-icon {
            position: relative;
        }
        
        .input-with-icon i {
            position: absolute;
            top: 50%;
            left: var(--spacing-4);
            transform: translateY(-50%);
            color: var(--neutral-500);
        }
        
        .input-with-icon .form-control {
            padding-left: 40px;
        }
        
        .input-group {
            display: flex;
            position: relative;
        }
        
        .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            flex: 1;
        }
        
        .input-group-append {
            display: flex;
            align-items: center;
            padding: 0 var(--spacing-4);
            background-color: var(--neutral-100);
            border: 2px solid var(--neutral-300);
            border-left: none;
            color: var(--neutral-700);
            border-top-right-radius: var(--radius-md);
            border-bottom-right-radius: var(--radius-md);
            font-weight: 500;
        }
        
        /* 按钮 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0 var(--spacing-5);
            height: 44px;
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: var(--text-sm);
            transition: all var(--transition-fast);
            cursor: pointer;
            border: none;
            gap: var(--spacing-2);
        }
        
        .btn:focus {
            outline: none;
            box-shadow: var(--shadow-outline);
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }
        
        .btn-primary:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        .btn-primary:active {
            transform: translateY(0);
        }
        
        .btn-secondary {
            background-color: var(--secondary);
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: var(--secondary-dark);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        .btn-outline {
            background-color: transparent;
            border: 2px solid var(--neutral-300);
            color: var(--neutral-600);
        }
        
        .btn-outline:hover {
            border-color: var(--primary);
            color: var(--primary);
        }
        
        .btn-sm {
            height: 36px;
            padding: 0 var(--spacing-4);
            font-size: var(--text-xs);
        }
        
        .btn-xs {
            height: 30px;
            padding: 0 var(--spacing-3);
            font-size: var(--text-xs);
            border-radius: var(--radius-sm);
        }
        
        .btn-action {
            width: 36px;
            height: 36px;
            padding: 0;
            border-radius: var(--radius-full);
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-add {
            position: fixed;
            right: 30px;
            bottom: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            box-shadow: var(--shadow-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-xl);
            z-index: 999;
            transition: all var(--transition);
        }
        
        .btn-add:hover {
            transform: scale(1.1) rotate(90deg);
            background: var(--primary-dark);
        }
        
        /* 表格设计 */
        .table-responsive {
            overflow-x: auto;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
        }
        
        .app-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            white-space: nowrap;
            background-color: white;
        }
        
        .app-table th, .app-table td {
            padding: var(--spacing-4);
            text-align: left;
            border-bottom: 1px solid var(--neutral-200);
        }
        
        .app-table th {
            background-color: var(--neutral-100);
            font-weight: 600;
            color: var(--neutral-700);
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .app-table tr:last-child td {
            border-bottom: none;
        }
        
        .app-table tr {
            transition: background-color var(--transition-fast);
        }
        
        .app-table tr:hover {
            background-color: var(--primary-light);
            color: white;
        }
        
        .app-table tr:hover td {
            border-bottom-color: var(--primary-light);
        }
        
        .app-table tr.selected {
            background-color: var(--primary-light);
        }
        
        .app-table .cell-actions {
            display: flex;
            gap: var(--spacing-2);
            justify-content: flex-end;
        }
        
        .app-table .cell-status {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            border-radius: var(--radius-full);
            font-size: var(--text-xs);
            font-weight: 600;
        }
        
        .status-active {
            background-color: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-inactive {
            background-color: var(--neutral-300);
            color: var(--neutral-700);
        }
        
        .status-admin {
            background-color: var(--warning-light);
            color: var(--warning-dark);
        }
        
        /* 分页 */
        .pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: var(--spacing-6);
            gap: var(--spacing-2);
            flex-wrap: wrap;
        }
        
        .pagination .page-item {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
            padding: 0 var(--spacing-2);
            border-radius: var(--radius-md);
            background-color: white;
            color: var(--neutral-700);
            font-weight: 500;
            transition: all var(--transition-fast);
            cursor: pointer;
            border: 1px solid var(--neutral-300);
        }
        
        .pagination .page-item:hover {
            background-color: var(--primary-light);
            color: white;
            border-color: var(--primary);
        }
        
        .pagination .page-item.active {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        .pagination .page-item.disabled {
            opacity: 0.5;
            pointer-events: none;
        }
        
        /* 徽章 */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25em 0.6em;
            font-size: 75%;
            font-weight: 600;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: var(--radius-full);
        }
        
        .badge-primary {
            background-color: var(--primary);
            color: white;
        }
        
        .badge-success {
            background-color: var(--success);
            color: white;
        }
        
        .badge-danger {
            background-color: var(--error);
            color: white;
        }
        
        .badge-warning {
            background-color: var(--warning);
            color: var(--neutral-900);
        }
        
        .badge-info {
            background-color: var(--info);
            color: white;
        }
        
        .badge-secondary {
            background-color: var(--secondary);
            color: white;
        }
        
        .badge-light {
            background-color: var(--neutral-200);
            color: var(--neutral-800);
        }
        
        .badge-dark {
            background-color: var(--neutral-800);
            color: white;
        }

        /* 弹出层 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            animation: fadeIn 0.2s ease-out;
        }
        
        .modal-container {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            width: 90%;
            max-width: 500px;
            overflow: hidden;
            animation: slideIn 0.3s ease-out;
        }
        
        .modal-header {
            background: var(--primary-gradient);
            color: white;
            padding: var(--spacing-4) var(--spacing-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-title {
            font-size: var(--text-lg);
            font-weight: 600;
            margin: 0;
        }
        
        .modal-body {
            padding: var(--spacing-6);
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-3);
            padding: var(--spacing-4) var(--spacing-6);
            background-color: var(--neutral-50);
            border-top: 1px solid var(--neutral-200);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        /* 加载动画 */
        .loader {
            width: 48px;
            height: 48px;
            border: 5px solid var(--neutral-300);
            border-bottom-color: var(--primary);
            border-radius: 50%;
            display: inline-block;
            box-sizing: border-box;
            animation: rotation 1s linear infinite;
        }
        
        @keyframes rotation {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            flex-direction: column;
            gap: var(--spacing-4);
        }
        
        .loading-text {
            font-size: var(--text-lg);
            color: var(--primary);
            font-weight: 500;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .app-header {
                padding: 0 var(--spacing-4);
            }
            
            .app-container {
                padding-left: var(--spacing-4);
                padding-right: var(--spacing-4);
            }
            
            .app-card .card-header, 
            .app-card .card-body {
                padding: var(--spacing-4);
            }
            
            .search-form {
                padding: var(--spacing-4);
            }
            
            .form-col {
                flex-basis: 100%;
            }
            
            .btn-add {
                right: 20px;
                bottom: 20px;
                width: 50px;
                height: 50px;
            }
            
            .form-actions {
                justify-content: space-between;
            }
            
            .form-actions .btn {
                flex-grow: 1;
            }
            
            .modal-footer {
                position: sticky;
                bottom: 0;
                background: white;
                z-index: 10;
                box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
                padding: var(--spacing-3) var(--spacing-4);
            }
            
            .modal-body {
                padding-bottom: 60px !important;
            }
            
            .modal-container {
                max-height: 90vh !important;
                height: auto !important;
                display: flex;
                flex-direction: column;
            }
            
            .modal-body {
                flex: 1;
                overflow: auto;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideInRight {
            from { transform: translateX(20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        .animate-slide-in {
            animation: slideInRight 0.3s ease-out forwards;
        }
        
        /* 表格行动画 */
        .app-table tbody tr {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
        }
        
        /* 顶部反馈消息 */
        .alert {
            padding: var(--spacing-4) var(--spacing-6);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-6);
            position: relative;
            animation: slideInRight 0.3s ease-out forwards;
        }
        
        .alert-success {
            background-color: var(--success-light);
            color: var(--success-dark);
            border-left: 4px solid var(--success);
        }
        
        .alert-error {
            background-color: #ffe0e6;
            color: var(--error-dark);
            border-left: 4px solid var(--error);
        }
        
        .alert-warning {
            background-color: var(--warning-light);
            color: var(--warning-dark);
            border-left: 4px solid var(--warning);
        }
        
        .alert-info {
            background-color: #e3f2fd;
            color: var(--info-dark);
            border-left: 4px solid var(--info);
        }
        
        .alert-close {
            position: absolute;
            top: var(--spacing-4);
            right: var(--spacing-4);
            cursor: pointer;
            opacity: 0.7;
            transition: opacity var(--transition-fast);
        }
        
        .alert-close:hover {
            opacity: 1;
        }
        
        /* 工具提示 */
        .tooltip {
            position: relative;
            display: inline-block;
        }
        
        .tooltip .tooltip-text {
            visibility: hidden;
            background-color: var(--neutral-800);
            color: white;
            text-align: center;
            padding: 6px 12px;
            border-radius: var(--radius-sm);
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity var(--transition-fast);
            font-size: var(--text-xs);
            white-space: nowrap;
        }
        
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--neutral-100);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--neutral-400);
            border-radius: var(--radius-full);
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary);
        }
        
        /* 添加模态框关闭按钮 */
        .modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            color: white;
            cursor: pointer;
            font-size: 18px;
            opacity: 0.8;
            transition: opacity var(--transition-fast);
        }
        
        .modal-close:hover {
            opacity: 1;
        }

        /* 删除按钮样式 */
        .btn-danger {
            background: linear-gradient(135deg, #ff4757 0%, #ff6b81 100%);
            color: white;
            border: none;
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #ff1f30 0%, #ff4757 100%);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        /* 用户信息展示框 */
        .user-info-box {
            display: flex;
            align-items: center;
            background-color: var(--neutral-100);
            border-radius: var(--radius-lg);
            padding: 15px;
            margin: 20px 0;
            box-shadow: var(--shadow-sm);
            border-left: 4px solid var(--primary);
        }
        
        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        
        .user-avatar i {
            font-size: 24px;
            color: white;
        }
        
        .user-details {
            flex: 1;
        }
        
        .username {
            font-size: var(--text-lg);
            font-weight: 500;
            color: var(--neutral-800);
            margin: 0;
        }
        
        /* 操作列中的删除按钮 */
        .delete-action {
            color: var(--error);
            border: 1px solid var(--error);
            background-color: transparent;
            transition: var(--transition-fast);
        }
        
        .delete-action:hover {
            background-color: var(--error);
            color: white;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .user-info-box {
                flex-direction: column;
                text-align: center;
            }
            
            .user-avatar {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
        
        /* 确认动画 */
        @keyframes deleteWarningPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.03); }
            100% { transform: scale(1); }
        }
        
        .delete-confirm-animation {
            animation: deleteWarningPulse 1s ease-in-out infinite;
        }

        /* 同步账号后的高亮动画 */
        @keyframes highlightBackground {
            0% { background-color: rgba(63,81,181,0.3); }
            50% { background-color: rgba(63,81,181,0.1); }
            100% { background-color: transparent; }
        }

        .highlight-row {
            animation: highlightBackground 2s ease-out forwards;
        }

        /* 同步中的加载动画样式优化 */
        .loading-overlay .loading-text {
            transition: all 0.3s ease;
        }

        /* 同步账号按钮的悬停动画 */
        #sync-accounts-btn:hover i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 同步结果统计样式 */
        .sync-stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            text-align: center;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            background-color: var(--neutral-100);
            border-radius: var(--radius-md);
            min-width: 120px;
        }

        .stat-icon {
            font-size: 24px;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--neutral-900);
        }

        .stat-label {
            font-size: 14px;
            color: var(--neutral-600);
            margin-top: 5px;
        }
    </style>
</head>

<body>
    <!-- 主内容区域 -->
    <div class="app-container">
        <!-- 页面标题 -->
        <div class="page-title">
            <h2>用户管理</h2>
            <p>创建、查看和管理系统用户信息</p>
        </div>

        <!-- 搜索卡片 -->
        <div class="app-card animate-fade-in" style="animation-delay: 0.1s;">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-search"></i>
                    <span>用户筛选</span>
                </div>
            </div>
            <div class="card-body">
                <form class="search-form" id="search-form">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-user"></i>
                                    <input type="text" name="username" class="form-control" placeholder="请输入用户名">
                                </div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">全名</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-id-card"></i>
                                    <input type="text" name="full_name" class="form-control" placeholder="请输入全名">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="search-btn">
                            <i class="fas fa-search"></i>
                            <span>搜索</span>
                        </button>
                        <button type="button" class="btn btn-outline" id="reset-btn">
                            <i class="fas fa-sync-alt"></i>
                            <span>重置</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 用户列表卡片 -->
        <div class="app-card animate-fade-in" style="animation-delay: 0.2s;">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-users"></i>
                    <span>用户列表</span>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-primary" id="sync-accounts-btn">
                        <i class="fas fa-sync"></i>
                        <span>同步账号列表</span>
                    </button>
                    <button class="btn btn-primary" id="add-account-btn">
                        <i class="fas fa-plus"></i>
                        <span>添加账号</span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="app-table" id="users-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>全名</th>
                                <th>用户名</th>
                                <th>Steam账号</th>
                                <th>创建时间</th>
                                <th>修改时间</th>
                                <th>状态</th>
                                <th>角色</th>
                                <th class="text-right">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 表格数据将由JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                <div class="pagination" id="pagination">
                    <!-- 分页将由JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 添加账号模态框模板 -->
    <script type="text/template" id="add-account-template">
        <div class="modal-overlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3 class="modal-title"><i class="fas fa-plus-circle"></i> 添加邮箱账号</h3>
                </div>
                <div class="modal-body">
                    <form id="add-account-form">
                        <div class="form-group">
                            <label class="form-label">邮箱账号</label>
                            <div class="input-group">
                                <input type="text" name="username" class="form-control" required placeholder="请输入6位小写字母">
                                <div class="input-group-append">@zuhaom.com</div>
                            </div>
                            <div class="form-error" id="username-error"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" id="cancel-add">取消</button>
                    <button type="button" class="btn btn-primary" id="confirm-add">添加</button>
                </div>
            </div>
        </div>
    </script>

    <!-- 编辑用户模态框模板 -->
    <script type="text/template" id="edit-user-template">
        <div class="modal-overlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3 class="modal-title"><i class="fas fa-edit"></i> 编辑Steam账号</h3>
                </div>
                <div class="modal-body">
                    <form id="edit-user-form">
                        <input type="hidden" name="id" id="edit-user-id">
                        <div class="form-group">
                            <label class="form-label">Steam账号</label>
                            <input type="text" name="steam_user" class="form-control" required placeholder="请输入Steam账号">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" id="cancel-edit">取消</button>
                    <button type="button" class="btn btn-primary" id="confirm-edit">保存</button>
                </div>
            </div>
        </div>
    </script>

    <!-- 邮件列表模态框模板 -->
    <script type="text/template" id="email-list-template">
        <div class="modal-overlay">
            <div class="modal-container" style="max-width: 90%; height: auto; max-height: 80vh;">
                <div class="modal-header">
                    <h3 class="modal-title"><i class="fas fa-envelope"></i> 邮件列表 - <span id="email-username"></span></h3>
                    <span class="modal-close" id="close-email-list-x"><i class="fas fa-times"></i></span>
                </div>
                <div class="modal-body" style="padding: 0; overflow: auto;">
                    <div class="table-responsive" style="height: 100%;">
                        <table class="app-table" id="email-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>主题</th>
                                    <th>发件人</th>
                                    <th>收件人</th>
                                    <th class="text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 邮件列表将由JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="close-email-list">关闭</button>
                </div>
            </div>
        </div>
    </script>

    <!-- 邮件内容查看模板 -->
    <script type="text/template" id="email-view-template">
        <div class="modal-overlay">
            <div class="modal-container" style="max-width: 90%; height: auto; max-height: 80vh;">
                <div class="modal-header">
                    <h3 class="modal-title"><i class="fas fa-envelope-open"></i> <span id="email-subject"></span></h3>
                    <span class="modal-close" id="close-email-view-x"><i class="fas fa-times"></i></span>
                </div>
                <div class="modal-body" style="padding: 0; overflow: auto;">
                    <div style="padding: 20px; background: #f8f9fa; border-bottom: 1px solid #e9ecef;">
                        <div style="margin-bottom: 10px;"><strong>从:</strong> <span id="email-from"></span></div>
                        <div style="margin-bottom: 10px;"><strong>到:</strong> <span id="email-to"></span></div>
                        <div><strong>时间:</strong> <span id="email-time"></span></div>
                    </div>
                    <div style="padding: 20px; overflow: auto;" id="email-content">
                        <!-- 邮件内容将由JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="close-email-view">返回列表</button>
                </div>
            </div>
        </div>
    </script>

    <!-- 加载动画模板 -->
    <script type="text/template" id="loading-template">
        <div class="loading-overlay">
            <div class="loader"></div>
            <div class="loading-text">正在加载...</div>
        </div>
    </script>

    <!-- 添加删除确认对话框模板 -->
    <script type="text/template" id="delete-user-template">
        <div class="modal-overlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3 class="modal-title"><i class="fas fa-trash-alt"></i> 删除邮箱账户</h3>
                    <span class="modal-close" id="close-delete-x"><i class="fas fa-times"></i></span>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> 
                        <span>此操作将<strong>永久删除</strong>该邮箱账户及其所有数据，无法恢复！</span>
                    </div>
                    <p style="margin-top: 20px; text-align: center;">您确定要删除以下账户吗？</p>
                    <div class="user-info-box">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <p class="username"><span id="delete-username"></span></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" id="cancel-delete">取消</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete">
                        <i class="fas fa-trash-alt"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    </script>

    <!-- 同步成功模态框模板 -->
    <script type="text/template" id="sync-success-template">
        <div class="modal-overlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3 class="modal-title"><i class="fas fa-check-circle"></i> 同步完成</h3>
                    <span class="modal-close" id="close-sync-x"><i class="fas fa-times"></i></span>
                </div>
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-sync-alt"></i> 
                        <span>账号列表同步成功！</span>
                    </div>
                    <div class="sync-stats">
                        <div class="stat-item">
                            <div class="stat-icon"><i class="fas fa-users"></i></div>
                            <div class="stat-content">
                                <div class="stat-value" id="sync-total-count">0</div>
                                <div class="stat-label">账号总数</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon"><i class="fas fa-user-plus"></i></div>
                            <div class="stat-content">
                                <div class="stat-value" id="sync-new-count">0</div>
                                <div class="stat-label">新增账号</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="close-sync">确定</button>
                </div>
            </div>
        </div>
    </script>

    <!-- JavaScript -->
    <script src="/layuiadmin/layui/layui.js"></script>
    <script>
        layui.config({
            base: '../../../layuiadmin/'
        }).extend({
            index: 'lib/index'
        }).use(['index', 'jquery', 'layer'], function(){
            var $ = layui.$,
                layer = layui.layer;
            
            var allData = []; // 存储所有用户数据
            var currentPage = 1;
            var pageSize = 10;
            var totalPages = 0;
            
            // 初始化函数
            function init() {
                loadUserList();
                bindEvents();
                setAnimationDelays();
            }
            
            // 设置表格行的动画延迟
            function setAnimationDelays() {
                setTimeout(function() {
                    $('#users-table tbody tr').each(function(index) {
                        $(this).css('animation-delay', (index * 0.05) + 's');
                    });
                }, 200);
            }
            
            // 加载用户列表
            function loadUserList() {
                showLoading();
                
                $.ajax({
                    url: '/admin/index/getExistingUsers',
                    method: 'GET',
                    success: function(res) {
                        hideLoading();
                        
                        if(res.code === 200 || res.code === 0) {
                            allData = res.data || [];
                            totalPages = Math.ceil((res.count || 0) / pageSize);
                            renderUserList(allData, currentPage);
                            renderPagination(totalPages, currentPage);
                        } else {
                            showAlert('error', res.msg || '获取用户列表失败');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showAlert('error', '请求失败: ' + xhr.status);
                    }
                });
            }
            
            // 渲染用户列表
            function renderUserList(data, page) {
                var startIndex = (page - 1) * pageSize;
                var endIndex = Math.min(startIndex + pageSize, data.length);
                var pageData = data.slice(startIndex, endIndex);
                var html = '';
                
                if(pageData.length === 0) {
                    html = '<tr><td colspan="9" style="text-align: center;">暂无数据</td></tr>';
                } else {
                    for(var i = 0; i < pageData.length; i++) {
                        var item = pageData[i];
                        html += '<tr data-id="' + item.id + '">' +
                            '<td>' + item.id + '</td>' +
                            '<td>' + item.full_name + '</td>' +
                            '<td>' + item.username + '</td>' +
                            '<td>' + (item.steam_user || '-') + '</td>' +
                            '<td>' + formatDateTime(item.created) + '</td>' +
                            '<td>' + formatDateTime(item.modified) + '</td>' +
                            '<td>' + renderStatus(item.active) + '</td>' +
                            '<td>' + renderRole(item.is_admin) + '</td>' +
                            '<td class="cell-actions">' +
                                '<div class="btn-group">' +
                                    '<button class="btn btn-primary btn-xs edit-user tooltip" data-id="' + item.id + '">' +
                                        '<i class="fas fa-edit"></i>' +
                                        '<span class="tooltip-text">编辑</span>' +
                                    '</button>' +
                                    '<button class="btn btn-outline btn-xs view-emails tooltip" data-username="' + item.username + '">' +
                                        '<i class="fas fa-envelope"></i>' +
                                        '<span class="tooltip-text">查看邮件</span>' +
                                    '</button>' +
                                    '<button class="btn btn-xs delete-action tooltip" data-username="' + item.username + '">' +
                                        '<i class="fas fa-trash-alt"></i>' +
                                        '<span class="tooltip-text">删除账户</span>' +
                                    '</button>' +
                                '</div>' +
                            '</td>' +
                        '</tr>';
                    }
                }
                
                $('#users-table tbody').html(html);
                setAnimationDelays();
            }
            
            // 渲染状态徽章
            function renderStatus(status) {
                if(status == 1) {
                    return '<span class="cell-status status-active">活跃</span>';
                } else {
                    return '<span class="cell-status status-inactive">未激活</span>';
                }
            }
            
            // 渲染角色徽章
            function renderRole(isAdmin) {
                if(isAdmin == 1) {
                    return '<span class="cell-status status-admin">管理员</span>';
                } else {
                    return '<span class="cell-status status-inactive">普通用户</span>';
                }
            }
            
            // 渲染分页
            function renderPagination(total, current) {
                var html = '';
                
                if(total > 1) {
                    html += '<div class="page-item ' + (current === 1 ? 'disabled' : '') + '" data-page="prev">' +
                        '<i class="fas fa-chevron-left"></i>' +
                    '</div>';
                    
                    var start = Math.max(1, current - 2);
                    var end = Math.min(total, start + 4);
                    
                    if(start > 1) {
                        html += '<div class="page-item" data-page="1">1</div>';
                        if(start > 2) {
                            html += '<div class="page-item disabled">...</div>';
                        }
                    }
                    
                    for(var i = start; i <= end; i++) {
                        html += '<div class="page-item ' + (i === current ? 'active' : '') + '" data-page="' + i + '">' + i + '</div>';
                    }
                    
                    if(end < total) {
                        if(end < total - 1) {
                            html += '<div class="page-item disabled">...</div>';
                        }
                        html += '<div class="page-item" data-page="' + total + '">' + total + '</div>';
                    }
                    
                    html += '<div class="page-item ' + (current === total ? 'disabled' : '') + '" data-page="next">' +
                        '<i class="fas fa-chevron-right"></i>' +
                    '</div>';
                }
                
                $('#pagination').html(html);
            }
            
            // 绑定事件
            function bindEvents() {
                // 搜索表单提交
                $('#search-form').on('submit', handleSearch);
                
                // 重置按钮
                $('#reset-btn').on('click', handleReset);
                
                // 添加账号按钮
                $('#add-account-btn').on('click', showAddAccountModal);
                
                // 分页点击
                $('#pagination').on('click', '.page-item:not(.disabled)', handlePageChange);
                
                // 编辑用户
                $('#users-table').on('click', '.edit-user', handleEditUser);
                
                // 查看邮件
                $('#users-table').on('click', '.view-emails', handleViewEmails);
                
                // 删除用户按钮点击事件
                $('#users-table').on('click', '.delete-action', handleDeleteUser);
                
                // 同步账号列表按钮
                $('#sync-accounts-btn').on('click', handleSyncAccounts);
                
                // 模态框相关事件将在打开模态框时绑定
            }
            
            // 处理搜索
            function handleSearch(e) {
                e.preventDefault();
                
                var username = $('input[name="username"]').val().trim();
                var fullName = $('input[name="full_name"]').val().trim();
                
                if(!username && !fullName) {
                    loadUserList();
                    return;
                }
                
                showLoading();
                
                // 本地搜索
                var filteredData = allData.filter(function(item) {
                    var matchUsername = !username || item.username.toLowerCase().includes(username.toLowerCase());
                    var matchFullName = !fullName || item.full_name.toLowerCase().includes(fullName.toLowerCase());
                    return matchUsername && matchFullName;
                });
                
                hideLoading();
                
                totalPages = Math.ceil(filteredData.length / pageSize);
                currentPage = 1;
                
                renderUserList(filteredData, currentPage);
                renderPagination(totalPages, currentPage);
                
                showAlert('info', '找到 ' + filteredData.length + ' 条记录');
            }
            
            // 处理重置
            function handleReset() {
                $('#search-form')[0].reset();
                loadUserList();
            }
            
            // 处理分页变更
            function handlePageChange() {
                var page = $(this).data('page');
                
                if(page === 'prev') {
                    currentPage--;
                } else if(page === 'next') {
                    currentPage++;
                } else {
                    currentPage = page;
                }
                
                renderUserList(allData, currentPage);
                renderPagination(totalPages, currentPage);
                
                // 滚动到表格顶部
                $('html, body').animate({
                    scrollTop: $('#users-table').offset().top - 20
                }, 300);
            }
            
            // 显示添加账号模态框
            function showAddAccountModal() {
                var html = $('#add-account-template').html();
                $('body').append(html);
                
                // 生成随机用户名
                var randomUsername = generateRandomUsername();
                $('input[name="username"]').val(randomUsername);
                
                // 绑定模态框事件
                $('#cancel-add').on('click', closeModal);
                $('#confirm-add').on('click', handleAddAccount);
                
                // 用户名验证 - 修改绑定方式
                $('input[name="username"]').on('input', function() {
                    var username = $(this).val().trim();
                    var regex = /^[a-z]{6}$/;
                    var error = '';
                    
                    if(!regex.test(username)) {
                        error = '用户名必须为6位小写字母';
                    }
                    
                    $('#username-error').text(error);
                });
            }
            
            // 处理添加账号
            function handleAddAccount() {
                var username = $('input[name="username"]').val().trim();
                
                // 直接验证用户名，不依赖this上下文
                var regex = /^[a-z]{6}$/;
                if(!regex.test(username)) {
                    $('#username-error').text('用户名必须为6位小写字母');
                    return;
                }
                
                showLoading();
                
                $.ajax({
                    url: '/admin/index/addMailbox',  // 修改为正确的API接口
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ username: username }),  // 使用正确的数据格式
                    success: function(res) {
                        hideLoading();
                        if(res.code == 200) {
                            closeModal();
                            loadUserList();
                            showAlert('success', '账号添加成功');
                        } else {
                            showAlert('error', res.msg || '添加账号失败');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showAlert('error', '请求失败: ' + xhr.status);
                    }
                });
            }
            
            // 处理编辑用户
            function handleEditUser() {
                var userId = $(this).data('id');
                var userData = findUserById(userId);
                
                if(!userData) {
                    showAlert('error', '未找到用户数据');
                    return;
                }
                
                var html = $('#edit-user-template').html();
                $('body').append(html);
                
                $('#edit-user-id').val(userData.id);
                $('input[name="steam_user"]').val(userData.steam_user || '');
                
                $('#cancel-edit').on('click', closeModal);
                $('#confirm-edit').on('click', handleSaveUser);
            }
            
            // 处理保存用户
            function handleSaveUser() {
                var id = $('#edit-user-id').val();
                var steamUser = $('input[name="steam_user"]').val().trim();
                
                if(!steamUser) {
                    showAlert('error', 'Steam账号不能为空');
                    return;
                }
                
                showLoading();
                
                $.ajax({
                    url: '/admin/index/updateUser',
                    method: 'POST',
                    data: { id: id, steam_user: steamUser },
                    success: function(res) {
                        if(res.code === 0 || res.code === 200) {
                            closeModal();
                            loadUserList();
                            showAlert('success', '更新成功');
                        } else {
                            hideLoading();
                            showAlert('error', res.msg || '更新失败');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showAlert('error', '请求失败: ' + xhr.status);
                    }
                });
            }
            
            // 处理查看邮件
            function handleViewEmails() {
                var username = $(this).data('username');
                
                showLoading();
                
                $.ajax({
                    url: '/admin/index/getEmaiList',
                    method: 'GET',
                    data: { username: username },
                    success: function(res) {
                        hideLoading();
                        
                        if(res.status === true && Array.isArray(res.data)) {
                            showEmailListModal(username, res.data);
                        } else {
                            showAlert('error', res.msg || '获取邮件列表失败');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showAlert('error', '请求失败: ' + xhr.status);
                    }
                });
            }
            
            // 显示邮件列表模态框
            function showEmailListModal(username, emailData) {
                var html = $('#email-list-template').html();
                $('body').append(html);
                
                $('#email-username').text(username);
                
                var tableHtml = '';
                if(emailData.length === 0) {
                    tableHtml = '<tr><td colspan="5" style="text-align: center;">暂无邮件</td></tr>';
                } else {
                    for(var i = 0; i < emailData.length; i++) {
                        var email = emailData[i];
                        tableHtml += '<tr data-index="' + i + '">' +
                            '<td>' + formatDateTime(email.time * 1000) + '</td>' +
                            '<td>' + email.subject + '</td>' +
                            '<td>' + email.from + '</td>' +
                            '<td>' + email.to + '</td>' +
                            '<td class="cell-actions">' +
                                '<button class="btn btn-primary btn-xs view-email-content" data-index="' + i + '">' +
                                    '<i class="fas fa-eye"></i> 查看内容' +
                                '</button>' +
                            '</td>' +
                        '</tr>';
                    }
                }
                
                $('#email-table tbody').html(tableHtml);
                
                // 存储邮件数据
                window.emailListData = emailData;
                
                // 绑定事件
                $('#close-email-list').on('click', closeModal);
                $('#email-table').on('click', '.view-email-content', function() {
                    var index = $(this).data('index');
                    showEmailContentModal(window.emailListData[index]);
                });
                
                setAnimationDelays();
            }
            
            // 显示邮件内容模态框
            function showEmailContentModal(emailData) {
                closeModal(); // 关闭邮件列表模态框
                
                var html = $('#email-view-template').html();
                $('body').append(html);
                
                $('#email-subject').text(emailData.subject);
                $('#email-from').text(emailData.from);
                $('#email-to').text(emailData.to);
                $('#email-time').text(formatDateTime(emailData.time * 1000));
                $('#email-content').html(emailData.html);
                
                $('#close-email-view').on('click', function() {
                    closeModal();
                    showEmailListModal($('#email-username').text(), window.emailListData);
                });
            }
            
            // 辅助函数：生成随机用户名
            function generateRandomUsername() {
                var chars = 'abcdefghijklmnopqrstuvwxyz';
                var username = '';
                for(var i = 0; i < 6; i++) {
                    username += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return username;
            }
            
            // 辅助函数：根据ID查找用户
            function findUserById(id) {
                for(var i = 0; i < allData.length; i++) {
                    if(allData[i].id == id) {
                        return allData[i];
                    }
                }
                return null;
            }
            
            // 辅助函数：格式化日期时间
            function formatDateTime(timestamp) {
                if(!timestamp) return '-';
                var date = new Date(timestamp);
                return date.toLocaleString();
            }
            
            // 辅助函数：显示加载动画
            function showLoading() {
                var html = $('#loading-template').html();
                $('body').append(html);
            }
            
            // 辅助函数：隐藏加载动画
            function hideLoading() {
                $('.loading-overlay').remove();
            }
            
            // 辅助函数：关闭模态框
            function closeModal() {
                $('.modal-overlay').remove();
            }
            
            // 辅助函数：显示提示消息
            function showAlert(type, message) {
                var icon = {
                    'success': 'fas fa-check-circle',
                    'error': 'fas fa-exclamation-circle',
                    'warning': 'fas fa-exclamation-triangle',
                    'info': 'fas fa-info-circle'
                }[type];
                
                var alertHtml = '<div class="alert alert-' + type + '">' +
                    '<i class="' + icon + '"></i> ' + message +
                    '<span class="alert-close"><i class="fas fa-times"></i></span>' +
                '</div>';
                
                // 移除已有的提示
                $('.alert').remove();
                
                $('.app-container').prepend(alertHtml);
                
                // 绑定关闭事件
                $('.alert-close').on('click', function() {
                    $(this).parent().fadeOut(300, function() {
                        $(this).remove();
                    });
                });
                
                // 自动关闭
                setTimeout(function() {
                    $('.alert').fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
            
            // 处理删除用户
            function handleDeleteUser() {
                var username = $(this).data('username');
                showDeleteUserModal(username);
            }
            
            // 显示删除用户确认对话框
            function showDeleteUserModal(username) {
                var html = $('#delete-user-template').html();
                $('body').append(html);
                
                // 设置用户名
                $('#delete-username').text(username);
                
                // 添加警告动画
                $('.alert-warning').addClass('delete-confirm-animation');
                
                // 绑定事件
                $('#close-delete-x, #cancel-delete').on('click', closeModal);
                $('#confirm-delete').on('click', function() {
                    deleteUser(username);
                });
            }
            
            // 执行删除用户操作
            function deleteUser(username) {
                showLoading();
                
                $.ajax({
                    url: '/admin/index/deleteMailbox',
                    method: 'POST',
                    data: { username: username },
                    success: function(res) {
                        hideLoading();
                        
                        // 解析响应结果（如果是字符串）
                        if (typeof res === 'string') {
                            try {
                                res = JSON.parse(res);
                            } catch (e) {
                                // 保持原样
                            }
                        }
                        
                        // 判断是否成功
                        if(res.status === true || res.code === 200 || res.code === 0) {
                            closeModal();
                            loadUserList(); // 重新加载用户列表
                            showAlert('success', '账户 ' + username + '@zuhaom.com 已成功删除');
                        } else {
                            closeModal();
                            showAlert('error', res.msg || '删除账户失败，请稍后重试');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        closeModal();
                        showAlert('error', '请求失败: ' + xhr.status);
                    }
                });
            }
            
            // 处理同步账号列表
            function handleSyncAccounts() {
                showLoading();
                
                // 显示同步中动画
                var loadingText = $('.loading-text');
                if (loadingText.length) {
                    loadingText.text('正在从邮箱服务器同步账号，请稍候...');
                }
                
                $.ajax({
                    url: '/api/getEmailUserList',
                    method: 'GET',
                    success: function(res) {
                        hideLoading();
                        
                        // 检查响应状态
                        if (res.code === 200 || !res.error) {
                            // 计算新增用户数量
                            var newUserCount = 0;
                            if (res.data && Array.isArray(res.data)) {
                                newUserCount = res.data.length;
                            }
                            
                            // 重新加载用户列表
                            loadUserList();
                            
                            // 显示成功消息
                            showAlert('success', '账号同步成功！');
                            
                            // 添加动画效果到新同步的用户行
                            setTimeout(function() {
                                $('#users-table tbody tr:nth-child(-n+' + newUserCount + ')').addClass('highlight-row');
                            }, 500);
                        } else {
                            // 显示错误消息
                            showAlert('error', res.error || res.msg || '同步账号失败，请稍后重试');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        
                        // 根据错误状态显示不同消息
                        if (xhr.status === 500) {
                            showAlert('error', '服务器错误，请联系管理员');
                        } else if (xhr.status === 404) {
                            showAlert('error', '同步接口不存在，请检查API配置');
                        } else {
                            showAlert('error', '同步请求失败: ' + xhr.status);
                        }
                    }
                });
            }
            
            // 初始化
            init();
        });
    </script>
</body>
</html>