<?php
namespace app\controller;

use app\BaseController;
use think\facade\View;
use app\admin\model\Config;
use think\facade\Request;
class Email extends BaseController
{
    // index(收件人没有填false,邮件内容)
    static public function index($qq,$content){
        require app()->getRootPath().'plugin/sendmail.php';
        $cn=Config::find(1);
        if($qq==false){
            $qq=$cn["em_username"];
        }
         $arr=[
                "Host"=>$cn["em_host"],//第一个参数为服务器地址
                "Port"=>$cn['em_port'],//服务器端口
                "FromName"=>$cn["em_name"],
                "Username"=>$cn["em_username"],//smtp登录的账号
                "Password"=>$cn["em_password"],//授权码
                "From"=>$cn["em_username"],//发件人邮箱
                "Subject"=>"邮件通知",//邮件主题
                "addAddress"=>$qq,//发送的qq邮箱
                "Body"=>$content//发送的内容
                ];
        sendmail($arr);
    }
    // public function test(){
    //     $re=Request::get("body");
    //     if(isset($re)){
    //         $cn=Db::table("fk_config")->where("id",1)->find();
    //         $this->index($cn["eName"],$re);   
    //     }
    // }
}