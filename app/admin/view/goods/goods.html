<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layuiAdmin 内容系统 - 评论管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
  <meta http-equiv="Expires" content="0">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
        <div class="layui-form-item">
          <!-- <div class="layui-inline">
            <label class="layui-form-label">商品ID</label>
            <div class="layui-input-inline">
              <input type="text" name="id" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
          </div> -->
          <div class="layui-inline">
            <label class="layui-form-label">商品名称</label>
            <div class="layui-input-inline">
              <input type="text" name="goods_name" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">分类名</label>
            <div class="layui-input-inline">
              <select name="goods_class">
                <option value="">请选择标签</option>
                {foreach $goodsclassAll as $val}
                    <option value="{$val.id}">{$val.cl_name}</option>
                {/foreach}
              </select>
            </div>
          </div>
          <script type="text/html" id="buttonTpl">
          {{#  if(d.goods_show==1){ }}
            <button class="layui-btn layui-btn-xs">正常显示</button>
          {{#  } else { }}
            <button class="layui-btn layui-btn-primary layui-btn-xs">已隐藏</button>
          {{#  } }}
        </script>
          <script type="text/html" id="tj">
          {{#  if(d.goods_tj==1){ }}
            <button class="layui-btn layui-btn-xs">推荐</button>
          {{#  } else { }}
            <button class="layui-btn layui-btn-primary layui-btn-xs">不推荐</button>
          {{#  } }}
        </script>
          <div class="layui-inline">
            <button class="layui-btn layuiadmin-btn-comm" data-type="reload" lay-submit lay-filter="LAY-app-contcomm-search">
              <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
            </button>
            <button type="reset" class="layui-btn layui-btn-primary" id="resetSearch">重置</button>
          </div>
        </div>
      </div>
      <div class="layui-card-body">

        <!-- 搜索结果信息 -->
        <div class="search-result-info" id="searchResultInfo" style="display: none; background: #f8f9fa; padding: 10px; margin-bottom: 15px; border-left: 4px solid #009688; border-radius: 4px;">
          <div class="search-result-text">
            <span id="searchResultText"></span>
            <button class="clear-search" onclick="clearSearchResult()" style="margin-left: 10px; background: none; border: none; color: #009688; cursor: pointer; text-decoration: underline;">清除搜索</button>
          </div>
        </div>

        <div style="padding-bottom: 10px;">
          <button class="layui-btn layuiadmin-btn-comm" data-type="batchdel">删除</button>
          <button class="layui-btn layuiadmin-btn-comm" data-type="add">添加</button>
        </div>
        <table id="LAY-app-content-list2" lay-filter="LAY-app-content-list2"></table>  
        <script type="text/html" id="table-content-com">
          <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="tags"><i class="layui-icon layui-icon-tabs"></i>标签设置</a>
          <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
        </script>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'contlist', 'table'], function(){
    var $ = layui.$
    ,form = layui.form
    ,table = layui.table;
    
    //表格初始化
    var tableIns = table.render({
      elem: '#LAY-app-content-list2'
      ,url: '/admin/base/goodsList'
      ,cols: [[
        {type: 'checkbox', fixed: 'left'}
        ,{field: 'id', title: 'ID', sort: true}
        ,{field: 'goods_img', title: '商品图片', templet: '#imgTpl'}
        ,{field: 'goods_name', title: '商品名称'}
        ,{field: 'permanent_price2', title: '永久版价格'}
        ,{field: 'goods_key', title: '标签', templet: '#tagsTpl'}
        ,{title: '操作', align: 'center', fixed: 'right', toolbar: '#table-content-com'}
      ]]
      ,page: true
      ,limit: 20
      ,text: {
        none: '暂无相关数据'
      }
      ,done: function(res, curr, count) {
        console.log('表格数据加载完成:', res);
        console.log('当前页:', curr, '总数:', count, '数据条数:', res.data.length);

        // 显示搜索结果信息
        if (res.search_info && res.search_info.has_search) {
          console.log('显示搜索结果信息');
          showSearchResultInfo(res.search_info.total_found, res.search_info);
        } else {
          console.log('隐藏搜索结果信息，显示所有数据');
          hideSearchResultInfo();
        }
      }
    });

    // 显示搜索结果信息
    function showSearchResultInfo(count, searchInfo) {
      var searchText = '搜索结果：找到 ' + count + ' 条匹配的商品记录';

      // 构建搜索条件描述
      var conditions = [];
      if (searchInfo.search_params) {
        if (searchInfo.search_params.goods_name) {
          conditions.push('商品名称："' + searchInfo.search_params.goods_name + '"');
        }
        if (searchInfo.search_params.goods_class) {
          conditions.push('分类ID：' + searchInfo.search_params.goods_class);
        }
      }

      if (conditions.length > 0) {
        searchText += '，搜索条件：' + conditions.join('，');
      }

      $('#searchResultText').text(searchText);
      $('#searchResultInfo').show();
    }

    // 隐藏搜索结果信息
    function hideSearchResultInfo() {
      $('#searchResultInfo').hide();
    }

    // 显示所有商品数据（统一的重置函数）
    function showAllGoods() {
      console.log('显示所有商品数据...');

      // 清空所有搜索字段
      $('input[name="goods_name"]').val('');
      $('select[name="goods_class"]').val('');

      // 重新渲染表单
      form.render();

      // 隐藏搜索结果信息
      hideSearchResultInfo();

      // 最简单有效的解决方案：直接刷新页面
      // 这样可以完全清除所有搜索状态和缓存
      console.log('使用页面刷新方案彻底清除搜索状态');

      // 清除URL中的搜索参数
      if (window.history && window.history.replaceState) {
        window.history.replaceState({}, document.title, window.location.pathname);
      }

      // 显示提示信息
      layer.msg('正在显示所有商品...', {icon: 1, time: 1000});

      // 延迟刷新，让用户看到提示
      setTimeout(function() {
        window.location.reload();
      }, 500);
    }

    // 清除搜索结果（调用统一函数）
    window.clearSearchResult = function() {
      showAllGoods();
    };

    //监听搜索
    form.on('submit(LAY-app-contcomm-search)', function(data){
      var field = data.field;
      console.log('搜索表单提交:', field);

      // 清理空值，只传递有效的搜索参数
      var searchParams = {};
      var hasValidSearch = false;

      if (field.goods_name && field.goods_name.trim() !== '') {
        searchParams.goods_name = field.goods_name.trim();
        hasValidSearch = true;
      }
      if (field.goods_class !== '') {
        searchParams.goods_class = field.goods_class;
        hasValidSearch = true;
      }

      console.log('搜索参数:', searchParams, '有效搜索:', hasValidSearch);

      // 如果没有有效的搜索条件，显示所有数据
      if (!hasValidSearch) {
        console.log('没有搜索条件，显示所有数据');
        showAllGoods();
        return false;
      }

      // 添加搜索标识
      searchParams.search = true;

      //执行重载
      tableIns.reload({
        where: searchParams,
        page: {
          curr: 1 // 重新从第一页开始
        }
      });

      return false;
    });

    // 重置搜索按钮
    $('#resetSearch').on('click', function(e) {
      e.preventDefault();
      console.log('重置搜索按钮被点击');
      showAllGoods();
      return false;
    });

    //监听工具条
    table.on('tool(LAY-app-content-list2)', function(obj){
      var data = obj.data;
      if(obj.event === 'del'){
        layer.confirm('确定删除此商品？', function(index){
          // 发送删除请求
          $.ajax({
            url: '/admin/base/delGoods',
            method: 'post',
            data: {id: [data.id]},
            success: function(res){
              obj.del(); // 从表格中删除该行
              layer.msg('删除成功');
              layer.close(index);
            },
            error: function(err){
              layer.msg('删除失败，请重试');
              layer.close(index);
            }
          });
        });
      } else if(obj.event === 'edit'){
        layer.open({
          type: 2
          ,title: '编辑商品'
          ,content: '/admin/index/upGoods?id='+ data.id
          ,maxmin: true
          ,area: ['1000px', '550px']
          ,btn: ['确定', '取消']
          ,yes: function(index, layero){
            var iframeWindow = window['layui-layer-iframe' + index];
            var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
            
            //监听提交
            iframeWindow.layui.form.on('submit(layuiadmin-app-form-submit)', function(data){
              var field = data.field;
              
              //提交数据
              $.ajax({
                url: '/admin/base/upGoods'
                ,type: 'POST'
                ,data: field
                ,success: function(res){
                  layer.msg('修改成功');
                  tableIns.reload(); //重载表格
                  layer.close(index); //关闭弹层
                }
              });
            });
            
            submit.trigger('click');
          }
        }); 
      } else if(obj.event === 'tags'){
        layer.open({
          type: 2
          ,title: '商品标签管理'
          ,content: '/admin/index/goodsTags?id='+ data.id
          ,maxmin: true
          ,area: ['550px', '450px']
          ,btn: ['确定', '取消']
          ,yes: function(index, layero){
            var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
            submit.click();
          }
        }); 
      }
    });
    
    //点击事件
    var active = {
      batchdel: function(){
        var checkStatus = table.checkStatus('LAY-app-content-list2')
        ,checkData = checkStatus.data; //得到选中的数据

        if(checkData.length === 0){
          return layer.msg('请选择数据');
        }
      
        layer.confirm('确定删除吗？', function(index) {
            var arr=[]
            checkData.forEach(function(val,index){
                arr.push(val.id)
            })
		    layui.$.ajax({
                url: '/admin/base/delGoods',
                method: 'post',
                data: {id:arr},
                success: function(res){
                layer.msg('删除成功');
                }
            });
          tableIns.reload();
          layer.msg('已删除');
        });
      },
      add: function(){
            layer.open({
              type: 2
              ,title: '添加商品'
              ,content: '/admin/index/addGoods'
              ,maxmin: true
              ,area: ['1000px', '550px']
              // ,btn: ['确定', '取消']
              ,yes: function(index, layero){
                //点击确认触发 iframe 内容中的按钮提交
                var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
                submit.click();
              }
            }); 
          }
    }  

    $('.layui-btn.layuiadmin-btn-comm').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });
  });
  </script>

  <!-- 添加标签模板 -->
  <script type="text/html" id="tagsTpl">
    {{# var tags = d.goods_key ? d.goods_key.split(',') : []; }}
    {{# layui.each(tags, function(index, item){ }}
      <span class="layui-badge layui-bg-blue" style="margin-right: 5px;">{{ item }}</span>
    {{# }); }}
  </script>

  <!-- 添加图片模板 -->
  <script type="text/html" id="imgTpl">
    <img src="{{d.goods_img}}" style="height: 40px; width: 40px; object-fit: cover;">
  </script>
</body>
</html>

