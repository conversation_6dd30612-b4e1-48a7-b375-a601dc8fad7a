<?php
namespace app\admin\controller;
use app\BaseController;
use app\admin\model\System;
use app\admin\model\Goodsclass;
use app\admin\model\Goods;
use app\admin\model\Config;
use app\admin\model\Account;
use app\admin\model\Combo;
use app\admin\model\Order;
use app\admin\model\Notice;
use app\admin\model\Coupons;
use app\admin\model\Workorder;
use app\admin\model\Withdraw;
use app\admin\model\Banner;
use app\admin\model\User;
use think\facade\Request;
use think\facade\Db;
use think\facade\Cookie;
use think\facade\Log;

class Base
{
    public function login(){
        $ad = Request::param();

        $username = $ad["data"]['username'];
        $admin = Db::name("admin")->where("username", $username)->find();
        if (!$admin) {
            return json("账号不存在");
        }
        // ✅ 已移除验证码验证，直接进行密码验证
        // if(!captcha_check($ad["data"]["captcha"])){
        //     return json("验证码错误");
        // }
        if ($admin["password"] != md5($ad["data"]['password'])) {
            return json("密码有误");
        }
        // 设置安全的Cookie，默认2小时过期
        $defaultExpire = 7200; // 2小时
        $rememberExpire = 86400 * 7; // 7天

        if(@$ad["data"]['remember']=="on"){
            // 记住登录状态，设置7天过期
            Cookie::set('adname', $admin["username"], $rememberExpire);
            Cookie::set('islogin', 1, $rememberExpire);
            Cookie::set('logintime', date('Y-m-d H:i'), $rememberExpire);
        } else {
            // 普通登录，设置2小时过期
            Cookie::set('adname', $admin["username"], $defaultExpire);
            Cookie::set('islogin', 1, $defaultExpire);
            Cookie::set('logintime', date('Y-m-d H:i'), $defaultExpire);
        }
        Db::name("admin")->where("id", $admin["id"])->update([
            "time" => date("Y-m-d H:i:s")
        ]);
        return json("验证成功");
    }


    public function website(){
        $data=Request::param();
        $data["id"]=1;
        return System::update($data);
    }
    //显示全部商品分类
    public function goodsclassList(){
        // 搜索分类
        $reqData=Request::param();
        $page=$reqData['page'];
        $limit=$reqData['limit'];
       if(isset($reqData['search'])){
            switch ($reqData) {
                case $reqData['cl_name']!=null:
                     $dataGoodsClassAll=Goodsclass::where("cl_name", 'like', '%' . $reqData["cl_name"] . '%')->limit($limit*($page-1),$limit)->select();
                    break;
                default:
                    $dataGoodsClassAll=Goodsclass::limit($limit*($page-1),$limit)->select();
                    break;
            }
        }else{
           $dataGoodsClassAll=Goodsclass::limit($limit*($page-1),$limit)->select();
        }
        $list=[
            "code"=>0,
            "count"=>count(Goodsclass::select()),
            "data"=>$dataGoodsClassAll
            ];
            return json($list);
    }
    // 添加商品分类
    public function addGoodsClass(){
        $reqData=Request::param();
        $data=[
            "status"=>isset($reqData['status']),
            "cl_name"=>$reqData['title'],
            "time"=>date("Y-m-d H:i:s"),
            ];
        $goodsClass=Goodsclass::create($data);
        return json($goodsClass);
    }
    // 删除商品分类/批量
        public function delGoodsClass(){
        $delGoodsClass=Goodsclass::destroy(Request::param("id"));
        return json($delGoodsClass);
    }
    // 修改商品分类
        public function upGoodsClass(){
            $queData=Request::param();
            if($queData["status"]=="on"){
                $queData["status"]=1;
            }else {
                $queData["status"]=0;
            }
        $st=Goodsclass::update($queData);
        return $st;
    }
    // 显示全部商品/搜索商品
    public function goodsList(){
        // 搜索商品
        $reqData = Request::param();
        $page = $reqData['page'] ?? 1;
        $limit = $reqData['limit'] ?? 20;

        // 构建查询条件
        $where = [];
        $hasSearch = false;
        $searchInfo = [];

        if (isset($reqData['search'])) {
            // 商品ID搜索
            if (!empty($reqData['id'])) {
                $where[] = ['id', '=', $reqData['id']];
                $hasSearch = true;
                $searchInfo['id'] = $reqData['id'];
            }

            // 商品分类搜索
            if (!empty($reqData['goods_class'])) {
                $where[] = ['goods_class', '=', $reqData['goods_class']];
                $hasSearch = true;
                $searchInfo['goods_class'] = $reqData['goods_class'];
            }

            // 商品名称模糊搜索
            if (!empty($reqData['goods_name'])) {
                $where[] = ['goods_name', 'like', '%' . $reqData['goods_name'] . '%'];
                $hasSearch = true;
                $searchInfo['goods_name'] = $reqData['goods_name'];
            }
        }

        // 获取总数（用于分页和搜索结果统计）
        $totalCount = empty($where) ? Goods::count() : Goods::where($where)->count();

        // 构建查询
        $query = Goods::order('id', 'desc'); // 添加默认排序
        if (!empty($where)) {
            $query = $query->where($where);
        }
        $dataGoodsAll = $query->limit($limit * ($page - 1), $limit)->select();

        // 获取商品分类
        $goodsclassAll = Goodsclass::select();
        $goodsclassMap = [];
        foreach ($goodsclassAll as $goodsclass) {
            $goodsclassMap[$goodsclass['id']] = $goodsclass['cl_name'];
        }

        // 处理商品分类名称显示
        foreach ($dataGoodsAll as $index => $val) {
            if (isset($goodsclassMap[$val["goods_class"]])) {
                $dataGoodsAll[$index]["goods_class_name"] = $goodsclassMap[$val["goods_class"]];
            } else {
                $dataGoodsAll[$index]["goods_class_name"] = '未分类';
            }
        }

        // 构建返回数据
        $list = [
            "code" => 0,
            "count" => $totalCount,
            "data" => $dataGoodsAll
        ];

        // 如果有搜索条件，添加搜索信息
        if ($hasSearch) {
            $list['search_info'] = [
                'has_search' => true,
                'search_params' => $searchInfo,
                'total_found' => $totalCount
            ];
        }

        return json($list);
    }
    // 添加商品
    public function addGoods(){
        $reqData = Request::param();
        $reqData["time"] = date("Y-m-d H:i:s");

        // 查重：判断同名商品是否已存在
        $exists = \app\admin\model\Goods::where('goods_name', $reqData['goods_name'])->find();
        if ($exists) {
            return json(['code' => 1, 'msg' => '添加失败：已存在同名商品']);
        }
        try {
            $goods = Goods::create($reqData);
            return json(['code' => 0, 'msg' => '添加成功', 'data' => $goods]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('添加商品失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '添加失败：' . $e->getMessage()]);
        }
    }
    // 添加套餐
    public function addCombo(){
        $reqData=Request::param();
        $reqData["time"]=date("Y-m-d H:i:s");
        $goods=Combo::create($reqData);
        return json($goods);
    }
    // 删除套餐
    public function delCombo(){
        $id = Request::param("id");

        // 检查ID是否有效
        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数错误：缺少套餐ID']);
        }

        try {
            Db::startTrans();

            // 获取套餐信息，找到对应的商品ID
            $combo = Combo::find($id);
            if (!$combo) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '套餐不存在或已被删除']);
            }

            // 删除套餐
            $delCombo = Combo::destroy($id);

            // 同时删除对应的离线套餐记录
            if ($delCombo) {
                Db::name('offline')->where('product_id', $combo['co_goods'])->delete();
            }

            Db::commit();
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    // 更新套餐
    public function upCombo(){
        $queData=Request::param();
        $queData["time"]=date("Y-m-d H:i:s");
        $st=Combo::update($queData);
        return $st;
    }

    /**
     * 获取套餐列表
     *
     * @return \think\response\Json
     */
    public function comboList()
    {
        $page = input('page', 1);
        $limit = input('limit', 15);
        $goods_name = input('goods_name', '');

        try {
            $query = Db::name('combo')
                ->alias('c')
                ->join('goods g', 'c.co_goods = g.id')
                ->leftJoin('offline o', 'c.co_goods = o.product_id')
                ->field('c.*, g.goods_name, o.product_amount');

            // 搜索条件 - 支持模糊搜索，空值时返回全部数据
            if (!empty($goods_name)) {
                $query->where('g.goods_name', 'like', "%{$goods_name}%");
            }

            // 获取总数
            $count = $query->count();

            // 获取分页数据
            $data = $query
                ->page($page, $limit)
                ->order('c.id desc')
                ->select();

            // 构建返回数据，包含搜索信息
            $result = [
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ];

            // 如果有搜索条件，添加搜索信息
            if (!empty($goods_name)) {
                $result['search_info'] = [
                    'has_search' => true,
                    'keyword' => $goods_name,
                    'result_count' => $count
                ];
            }

            return json($result);
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '获取数据失败：' . $e->getMessage(),
                'count' => 0,
                'data' => []
            ]);
        }
    }
    // 修改商品
    public function upGoods()
    {
        $data = Request::param();
        Db::startTrans();
        try {
            // 更新商品基本信息
            $goods = Goods::find($data['id']);
            $goods->save([
                'goods_name' => $data['goods_name'],
                'goods_class' => $data['goods_class'],
                'limited' => $data['limited'],
                'goods_img' => $data['goods_img'],
                'goods_img2' => $data['goods_img2'],
                'goods_img3' => $data['goods_img3'],
                'goods_img4' => $data['goods_img4'],
                'goods_video' => $data['goods_video'],
                'goods_xl' => $data['goods_xl'],
                'goods_des' => $data['goods_des'],
                'permanent_price2' => $data['permanent_price2'],
                'online_status' => isset($data['online_status']) ? (int)$data['online_status'] : 1,
                'offline_status' => isset($data['offline_status']) ? (int)$data['offline_status'] : 1
            ]);

            // 更新游戏标签
            if (isset($data['tags']) && !empty($data['tags'])) {
                foreach ($data['tags'] as $tagName) {
                    // 检查标签是否已存在
                    $existingTag = Db::name('game_tags')->where('name', $tagName)->find();
                    if (!$existingTag) {
                        // 如果标签不存在，创建新标签
                        Db::name('game_tags')->insert([
                            'name' => $tagName,
                            'create_time' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
            }

            Db::commit();
            return json(['code' => 0, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '更新失败:'.$e->getMessage()]);
        }
    }
        // 删除商品/批量
    public function delGoods(){
        $delGoods=Goods::destroy(Request::param("id"));
        return json($delGoods);
    }
    //全部账号
    public function accountList(){
        $reqData=Request::param();
        $page=$reqData['page'] ?? 1;
        $limit=$reqData['limit'] ?? 15;

        // 构建查询条件
        $where = [];
        $hasSearch = false;
        $searchInfo = [];

        // 账号名称搜索
        if(!empty($reqData['ac_name'])){
            $where[] = ['ac_name', 'like', '%' . $reqData['ac_name'] . '%'];
            $hasSearch = true;
            $searchInfo['ac_name'] = $reqData['ac_name'];
        }

        // 账号状态搜索
        if(isset($reqData['ac_states']) && $reqData['ac_states'] !== ''){
            $where[] = ['ac_states', '=', $reqData['ac_states']];
            $hasSearch = true;
            $searchInfo['ac_states'] = $reqData['ac_states'];
        }

        // 商品搜索 - 支持ID和名称搜索
        if(!empty($reqData['ac_goods']) && $reqData['ac_goods'] !== ''){
            $where[] = ['ac_goods', '=', $reqData['ac_goods']];
            $hasSearch = true;
            $searchInfo['ac_goods'] = $reqData['ac_goods'];
        }

        // 商品名称模糊搜索
        if(!empty($reqData['ac_goods_name']) && $reqData['ac_goods_name'] !== ''){
            // 先根据商品名称查找商品ID
            $goods = Goods::where('goods_name', 'like', '%' . $reqData['ac_goods_name'] . '%')->select();
            if(!empty($goods)){
                $goodsIds = array_column($goods->toArray(), 'id');
                $where[] = ['ac_goods', 'in', $goodsIds];
                $hasSearch = true;
                $searchInfo['ac_goods_name'] = $reqData['ac_goods_name'];
            } else {
                // 如果没有找到匹配的商品，设置一个不可能的条件
                $where[] = ['ac_goods', '=', -1];
                $hasSearch = true;
                $searchInfo['ac_goods_name'] = $reqData['ac_goods_name'];
            }
        }

        // 账号类型搜索
        if(isset($reqData['goods_Type']) && $reqData['goods_Type'] !== ''){
            $where[] = ['goods_Type', '=', $reqData['goods_Type']];
            $hasSearch = true;
            $searchInfo['goods_Type'] = $reqData['goods_Type'];
        }

        // 获取总数（用于分页）
        $totalCount = empty($where) ? Account::count() : Account::where($where)->count();

        // 获取分页数据
        $query = Account::order('id', 'desc'); // 添加默认排序
        if (!empty($where)) {
            $query = $query->where($where);
        }
        $dataList = $query->limit($limit*($page-1),$limit)->select();

        $goodsAll=Goods::select();
        $userModel = new User();

        foreach ($dataList as $index=>$val){
            // 处理商品名称
            foreach ($goodsAll as $index2=>$val2){
                if($dataList[$index]["ac_goods"]==$goodsAll[$index2]["id"]){
                    $dataList[$index]["ac_goods"]=$goodsAll[$index2]["goods_name"];
                }
            }

            // 处理租用用户信息
            if(!empty($val['ac_uid'])) {
                $user = $userModel->where('id', $val['ac_uid'])->find();
                $dataList[$index]['ac_uid'] = $user ? $user['us_username'] : '暂未被租用';
            } else {
                $dataList[$index]['ac_uid'] = '暂未被租用';
            }
        }

        // 构建返回数据
        $list=[
            "code"=>0,
            "count"=>$totalCount,
            "data"=>$dataList
        ];

        // 如果有搜索条件，添加搜索信息
        if ($hasSearch) {
            $list['search_info'] = [
                'has_search' => true,
                'search_params' => $searchInfo,
                'total_found' => $totalCount
            ];
        }

        return json($list);
    }
    // 批量添加账号
    public function addAccount(){
        $accountData = Request::param();
        $notAddedAccounts = []; // 用于存储未添加的账号及原因

        foreach ($accountData['account'] as $val) {
            $acName = $val[0];

            // 检查Steam账号令牌信息是否存在
            $existingAccount = Db::name('steamaccountdata')->where('account_name', $acName)->find();

            if (!$existingAccount) {
                $notAddedAccounts[] = [
                    'ac_name' => $acName,
                    'reason' => '你需要先添加这个账号的令牌信息'
                ];
                continue; // 跳过当前账号
            }

            // 检查是否已存在相同的账号、商品分类和商品类型
            $existingRecord = Account::where([
                'ac_name' => $acName,
                'ac_goods' => $accountData["goodsclass"],
                'goods_Type' => $accountData["goodsType"]
            ])->find();

            if ($existingRecord) {
                $notAddedAccounts[] = [
                    'ac_name' => $acName,
                    'reason' => '该账号已存在相同商品分类和商品类型的记录'
                ];
                continue; // 跳过当前账号
            }

            $data = [
                "ac_name" => $acName,
                "ac_password" => isset($val[1]) ? $val[1] : "",
                "ac_goods" => $accountData["goodsclass"],
                "ac_sell" => 1,
                "ac_states" => 1,
                "goods_Type" => $accountData["goodsType"],
                "time" => date("Y-m-d H:i:s")
            ];
            Account::create($data);
        }

        return json([
            'added_count' => count($accountData['account']) - count($notAddedAccounts),
            'not_added' => $notAddedAccounts
        ]);
    }
      // 删除账号/批量
    public function delAccount(){
        $delAccount=Account::destroy(Request::param("id"));
        return json($delAccount);
    }
    // 更新邮箱
    public function upEmail(){
        $reqData=Request::param();
        $reqData['id']=1;
        return Config::update($reqData);
    }
    // 全部订单
    public function orderList(){
        $reqData=Request::param();
        $page=$reqData['page'];
        $limit=$reqData['limit'];
        $dataList=Order::limit($limit*($page-1),$limit)->select();
        if(isset($reqData["search"])){
            switch ($reqData) {
                case $reqData['ord_ybh']!=null:
                    $dataList=Order::where("ord_ybh",$reqData['ord_ybh'])->limit($limit*($page-1),$limit)->select();
                    break;
                case $reqData['id']!=null:
                    $dataList=Order::where("id",$reqData['id'])->limit($limit*($page-1),$limit)->select();
                    break;
                case $reqData['ord_ifpay']!=null:
                    $dataList=Order::where("ord_ifpay",$reqData['ord_ifpay'])->limit($limit*($page-1),$limit)->select();
                    break;
                case $reqData['ord_uid']!=null:
                    $user = User::where('us_username', 'like', '%' . $reqData['ord_uid'] . '%')->find();
                    if ($user) {
                        $dataList = Order::where("ord_uid", $user->id)
                                         ->limit($limit * ($page - 1), $limit)
                                         ->select();
                    } else {
                        $dataList = []; // 如果没有找到用户，返回空数组
                    }
                    break;
                default:
                    // code...
                    break;
            }
        }
        $ComboList=Combo::select();
        $GoodsList=Goods::select(); // 新增：获取商品列表
        // 当前账号
        foreach ($dataList as $index=>$val){
            // 修复：区分普通订单和永久版订单的显示逻辑
            if ($val['is_permanent'] && in_array($val['is_permanent'], [1, 2, 3])) {
                // 永久版订单：ord_combo存储的是商品ID
                foreach ($GoodsList as $goods) {
                    if($dataList[$index]["ord_combo"] == $goods["id"]) {
                        $dataList[$index]["ord_combo"] = $goods["goods_name"]."--商品id:{$val['ord_combo']}".($val['ord_aid']?"--账号id:".$val['ord_aid']:"")." [永久版]";
                        break;
                    }
                }
            } else {
                // 普通订单：ord_combo存储的是套餐ID
                foreach ($ComboList as $index2=>$val2){
                    if($dataList[$index]["ord_combo"]==$ComboList[$index2]["id"]){
                        $dataList[$index]["ord_combo"]=$ComboList[$index2]["co_name"]."--套餐id:{$val['ord_combo']}".($val['ord_aid']?"--账号id:".$val['ord_aid']:"");
                        break;
                    }
                }
            }
            // 新增代码：根据 ord_uid 查询 ac_name
            if ($val['ord_uid'] !== null) {
                $user = User::where('id', $val['ord_uid'])->find();
                $dataList[$index]['ord_uid'] = $user ? $user['us_username'] : '未知用户';
            } else {
                $dataList[$index]['ord_uid'] = '未知用户';
            }

            // 新增代码：根据 ord_type 转换支付方式
            switch ($val['ord_type']) {
                case 'yepay':
                    $dataList[$index]['ord_type'] = '余额支付';
                    break;
                case 'alipay':
                    $dataList[$index]['ord_type'] = '支付宝';
                    break;
                case 'wxpay':
                    $dataList[$index]['ord_type'] = '微信支付';
                    break;
                case 'CDK兑换':
                    $dataList[$index]['ord_type'] = 'CDK兑换';
                    break;
                case '永久版卡密兑换':
                    $dataList[$index]['ord_type'] = '永久版卡密兑换';
                    break;
                case '离线卡密兑换':
                    $dataList[$index]['ord_type'] = '离线卡密兑换';
                    break;
                case '会员充值':
                    $dataList[$index]['ord_type'] = '会员充值';
                    break;
                default:
                    // 保持原始值，不转换为"未知支付方式"
                    $dataList[$index]['ord_type'] = $val['ord_type'];
                    break;
            }
        }
        $list=[
            "code"=>0,
            "count"=>count(Order::select()),
            "data"=>$dataList
        ];
        return json($list);
    }
    // 删除订单/批量
    public function delOrder(){
        $delOrder=Order::destroy(Request::param("id"));
        return json($delOrder);
    }
        // 全部公告/搜索公告
    public function noticeList(){
        $reqData=Request::param();
        if(isset($reqData['search'])){
            switch ($reqData) {
                case $reqData['id']!=null:
                    $dataNotice=Notice::where("id",$reqData["id"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                case $reqData['not_title']!=null:
                    $dataNotice=Notice::where("not_title",$reqData["not_title"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                case $reqData['not_system']!=null:
                    $dataNotice=Notice::where("not_system",$reqData["not_system"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                default:
                    $dataNotice=Notice::limit(10*($reqData['page']-1),10)->select();
                    break;
            }
        }else{
           $dataNotice=Notice::limit(10*($reqData['page']-1),10)->select();
        }
        $list=[
            "code"=>0,
            "count"=>count(Notice::select()),
            "data"=>$dataNotice
            ];
            return json($list);
    }
    // 添加公告
    public function addNotice(){
        $reqData=Request::param();
        $reqData["time"]=date("Y-m-d H:i:s");
        $goods=Notice::create($reqData);
        return json($goods);
    }   // 修改公告
    public function upNotice(){
        $reqData=Request::param();
        unset($reqData["file"]);
        $reqData["time"]=date("Y-m-d H:i:s");
        $notice = Notice::where("id",$reqData['id'])->update($reqData);
        return json($notice);
    }
        // 删除公告/批量
    public function delNotice(){
        $delGoods=Notice::destroy(Request::param("id"));
        return json($delGoods);
    }

    // 全部优惠卷/搜索优惠卷
    public function couponsList(){
        $reqData=Request::param();
        if(isset($reqData['search'])){
            switch ($reqData) {
                case $reqData['id']!=null:
                    $coupons=Coupons::where("id",$reqData["id"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                case $reqData['coupons_use']!=null:
                    $coupons=Coupons::where("coupons_use",$reqData["coupons_use"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                default:
                    $coupons=Coupons::limit(10*($reqData['page']-1),10)->select();
                    break;
            }
        }else{
           $coupons=Coupons::limit(10*($reqData['page']-1),10)->select();
        }
        $list=[
            "code"=>0,
            "count"=>count(Coupons::select()),
            "data"=>$coupons
            ];
            return json($list);
    }
    // 生成随机优惠卷
    public function randCoupons(){
        $sqlCoupons=Coupons::select();
        $rand="0123456789QWERTYUIOPASDFGHJKLZXCVBNM";
        $nu="";
        for($i=0;$i<12;$i++){
            $nu.=substr($rand,mt_rand(0,strlen($rand)-1),1);
        }
        foreach ($sqlCoupons as $val){
            if($nu==$val["coupon_code"]){
                $this->randCoupons();
            }
        }
        return ($nu);
    }
    // 添加优惠卷
    public function addCoupons(){
        $reqData=Request::param();
        $reqData["time"]=date("Y-m-d H:i:s");
        $reqData["coupons_use"]=0;
        $reqData["co_receive"]=0;
        for($i=0;$i<$reqData["coupons_arr"];$i++){
            $reqData["coupon_code"]=$this->randCoupons();
            $coupons=Coupons::create($reqData);
        }
        return json($coupons);
    }   // 修改优惠卷
    public function upCoupons(){
        $reqData=Request::param();
        unset($reqData["file"]);
        $reqData["time"]=date("Y-m-d H:i:s");
        $upcoupons = Coupons::where("id",$reqData['id'])->update($reqData);
        return json($upcoupons);
    }
        // 删除优惠卷/批量
    public function delCoupons(){
        $upcoupons=Coupons::destroy(Request::param("id"));
        return json($upcoupons);
    }
    // 全部工单
public function workorder(){
        $reqData=Request::param();
        // 后台搜索工单
        if(isset($reqData['search'])){
            switch ($reqData) {
                case $reqData['id']!=null:
                    $workorder=Workorder::where("id",$reqData["id"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                case $reqData['work_goods']!=null:
                    $workorder=Workorder::where("work_goods",$reqData["work_goods"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                case $reqData['work_type']!=null:
                    $workorder=Workorder::where("work_type",$reqData["work_type"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                case $reqData['work_state']!=null:
                    $workorder=Workorder::where("work_state",$reqData["work_state"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                default:
                    $workorder=Workorder::limit(10*($reqData['page']-1),10)->select();
                    break;
            }
        }else{
          $workorder=Workorder::limit(10*($reqData['page']-1),10)->select();
        }
        foreach ($workorder as $index=>$key){
            if($key["work_state"]==0){
                 $workorder[$index]["progress"]="10%";//工单处理进度
                 $workorder[$index]["work_state"]="处理中";//工单处理进度
            }
            if($key["work_state"]==1){
                 $workorder[$index]["progress"]="100%";//工单处理进度
                 $workorder[$index]["work_state"]="已处理";//工单处理进度
            }
            if($key["work_type"]==0){
                $workorder[$index]["work_type"]="售前";
            }
            if($key["work_type"]==1){
                $workorder[$index]["work_type"]="售后"."订单ID:";
            }
        }
        $list=[
            "code"=>0,
            "count"=>count(Workorder::select()),
            "data"=>$workorder
            ];
            return json($list);
    }
// 编辑工单
    public function upWorkorder(){
        $reqData=Request::param();
        // unset($reqData["file"]);
        $reqData["time"]=date("Y-m-d H:i:s");
        $notice = Workorder::where("id",$reqData['id'])->update($reqData);
        return json($notice);
    }
// 删除工单
    public function delWorkorder(){
        $del=Workorder::destroy(Request::param("id"));
        return json($del);
    }
    // 全部提现申请
public function withdraw(){
        $reqData=Request::param();
        // 后台搜索工单
        if(isset($reqData['search'])){
            switch ($reqData) {
                case $reqData['id']!=null:
                    $Withdraw=Withdraw::where("id",$reqData["id"])->limit(10*($reqData['page']-1),10)->select();
                    break;
                case $reqData['wi_state']!=null:
                    $Withdraw=Withdraw::where("wi_state",$reqData["wi_state"])->limit(10*($reqData['page']-1),10)->select();
                    break;
            }
        }else{
          $Withdraw=Withdraw::limit(10*($reqData['page']-1),10)->select();
        }
        foreach ($Withdraw as $index=>$key){
            if($key["wi_state"]==0){
                 $Withdraw[$index]["progress"]="10%";//提现申请处理进度
                 $Withdraw[$index]["wi_state"]="处理中";//提现申请处理进度
            }
            if($key["wi_state"]==1){
                 $Withdraw[$index]["progress"]="100%";//提现申请处理进度
                 $Withdraw[$index]["wi_state"]="已处理";//提现申请处理进度
            }
            if($key["wi_type"]=="wxpay"){
                $Withdraw[$index]["wi_type"]="微信";
            }
            if($key["wi_type"]=="alipay"){
                $Withdraw[$index]["wi_type"]="支付宝";
            }
        }
        $list=[
            "code"=>0,
            "count"=>count(Withdraw::select()),
            "data"=>$Withdraw
            ];
            return json($list);
    }
// 编辑提现申请
    public function upWithdraw(){
        $reqData=Request::param();
        $reqData["time"]=date("Y-m-d H:i:s");
        $Withdraw = Withdraw::where("id",$reqData['id'])->update($reqData);
        return json($Withdraw);
    }
// 删除提现申请
    public function delWithdraw(){
        $del=Withdraw::destroy(Request::param("id"));
        return json($del);
    }
//控制台数据统计
    public function console(){
        $reqData=Request::param();
        $sumY=0;//年总收入
        $sumD=0;//今日总收入
        $orderY=[];
        //本年的所有订单
        foreach(Order::whereYear("time")->select() as $val){
            if($val["ord_type"]!="yepay" && $val["ord_ifpay"]==1){
                $orderY[]=$val;
                $sumY+=$val["ord_money"];
            }
        }
        //本月的所有订单
        foreach(Order::whereMonth("time")->select() as $val){
            if($val["ord_type"]!="yepay" && $val["ord_ifpay"]==1){
                $orderD[]=$val;
                $sumD+=$val["ord_money"];
            }
        }
        $data=[
            "countOrder"=>count($orderY),//总订单数量
            "countUser"=>count(User::select()),//总用户数量
            "sumY"=>$sumY,//今年总收入
            "sumD"=>$sumD,//今日总收入
            "System"=>System::value("sy_url"),
            ];
         if(isset($reqData["getAll"])){
             $sum=0;
            $arr=[];
             for($i=1;$i<13;$i++){
                 $Ye=Order::whereMonth('time', date("Y").'-'.$i)->select();
                 foreach ($Ye as $val){
                     if($val["ord_ifpay"]==1 && $val["ord_type"]!="yepay"){
                        $sum=$sum+$val["ord_money"];
                     }
                     $Ye=$sum;
                 }
                array_push($arr,$Ye);
             }
             return json($arr);
         }
            return $data;
    }
    // 全部轮播图
    public function BannerList(){
        $reqData=Request::param();
            $page=$reqData['page'];
            $limit=$reqData['limit'];
            $dataBanner=Banner::limit($limit*($page-1),$limit)->select();
        $list=[
            "code"=>0,
            "count"=>count(Banner::select()),
            "data"=>$dataBanner
            ];
            return json($list);
    }
    // 添加轮播图
    public function addBanner(){
        $reqData=Request::param();
        $reqData["time"]=date("Y-m-d H:i:s");
        $upBanner=Banner::create($reqData);
        return json($upBanner);
    }   // 修改轮播图
    public function upBanner(){
        $reqData=Request::param();
        unset($reqData["file"]);
        $reqData["time"]=date("Y-m-d H:i:s");
        $upBanner = Banner::where("id",$reqData['id'])->update($reqData);
        return json($upBanner);
    }
    // 删除轮播图
    public function delBanner(){
        $del=Banner::destroy(Request::param("id"));
        return json($del);
    }
    // 用户日志
    public function userlogo(){
        $reqData = Request::param();
        $page = $reqData['page'] ?? 1; // 默认页码为1
        $limit = $reqData['limit'] ?? 10; // 默认每页显示10条
        $query = Db::name("userlogo");

        // 添加搜索条件
        if (!empty($reqData['search'])) {
            if (!empty($reqData['id'])) {
                $query->where("id", $reqData['id']);
            }
            if (!empty($reqData['uid'])) {
                $query->where("uid", $reqData['uid']);
            }
        }

        // 获取总数和分页数据
        $totalCount = $query->count();
        $dataList = $query->limit($limit * ($page - 1), $limit)->select()->toArray(); // 转换为数组

        // 使用 User 模型替换 uid 为 us_username
        $userIds = array_column($dataList, 'uid');
        $users = User::whereIn('id', $userIds)->column('us_username', 'id');

        foreach ($dataList as &$data) {
            $data['uid'] = $users[$data['uid']] ?? '用户未找到';
        }

        // 返回结果
        return json([
            "code" => 0,
            "count" => $totalCount,
            "data" => $dataList
        ]);
    }
        // 删除用户日志
    public function delUserlogo(){
        $reqData=Request::param("id");
        if(is_array($reqData)){
            foreach ($reqData as $val){
                Db::name("userlogo")->where("id",$val)->delete();
            }
          }else{
                Db::name("userlogo")->where("id",$reqData)->delete();
            }
        return json("删除成功");
    }
    // 修改管理员密码
    public function password(){
        $reqData=Request::param();
        if($reqData["password"]==$reqData["repassword"]){
            return Db::name("admin")->where("id",1)->update(["username"=>$reqData["username"],"password"=>md5($reqData["password"]),"time"=>date("Y-m-d H:i:s")]);
        }
    }
    public function update(){
        $date="2.0";
        $url=file_get_contents("https://up.xbwlkj.top/?zhw&bbh=".$date);
        return $url;
    }
    public function exit()
    {
        Cookie::delete('adname');
        Cookie::delete('login');
        header("Location:/admin");
    }
    // 更新账号
    public function updateAccount() {
        $accountData = Request::param();

        try {
            $account = Account::find($accountData['id']);
            if (!$account) {
                return json(['code' => 1, 'msg' => '账号不存在']);
            }

            $updateData = [
                'ac_name' => $accountData['ac_name'],
                'ac_password' => $accountData['ac_password'],
                'ac_goods' => $accountData['ac_goods'],
                'ac_states' => $accountData['ac_states'],
                'exit_time' => $accountData['exit_time'],
                'goods_Type' => $accountData['goods_Type'],
                //'goods_Type' => $accountData['goods_Type'],
                //'time' => date('Y-m-d H:i:s')
            ];

            if ($account->save($updateData)) {
                return json(['code' => 0, 'msg' => '更新成功']);
            } else {
                return json(['code' => 1, 'msg' => '更新失败']);
            }
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }
    /**
     * 获取离线商品列表
     *
     * @return \think\response\Json
     */
    public function getOfflineProducts()
    {
        try {
            $params = Request::param();
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 15;

            $query = Db::name('offline')  // 使用正确的表名 'offline'
                ->alias('o')
                ->join('goods g', 'o.product_id = g.id', 'LEFT')
                ->field('o.*, g.goods_name');

            // 搜索条件
            if (!empty($params['goods_name'])) {
                $query->where('g.goods_name', 'like', '%' . $params['goods_name'] . '%');
            }

            // 获取总数
            $count = $query->count();

            // 获取数据
            $list = $query->page($page, $limit)->select()->toArray();

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $list
            ]);
        } catch (\Exception $e) {
            // 返回错误信息
            return json([
                'code' => 1,
                'msg' => '获取数据失败: ' . $e->getMessage(),
                'count' => 0,
                'data' => []
            ]);
        }
    }

    /**
     * 添加离线商品
     *
     * @return \think\response\Json
     */
    public function addOfflineProduct()
    {
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $params = Request::param();

        if (empty($params['product_id']) || empty($params['product_amount']) ||
            empty($params['day_price']) || empty($params['hour_price'])) {
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        try {
            Db::startTrans();

            // 检查商品是否存在
            $goods = Goods::find($params['product_id']);
            if (!$goods) {
                return json(['code' => 1, 'msg' => '商品不存在']);
            }

            // 检查该商品是否已经有离线价格记录
            $existingOffline = Db::name('offline')->where('product_id', $params['product_id'])->find();
            if ($existingOffline) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '该商品已设置离线价格，请使用更新功能']);
            }

            // 添加普通价格
            $comboData = [
                'co_goods' => $params['product_id'],
                'day_price' => $params['day_price'],
                'hour_price' => $params['hour_price'],
                'time' => date('Y-m-d H:i:s')
            ];
            $comboResult = Db::name('combo')->insert($comboData);

            // 添加离线价格
            $offlineResult = Db::name('offline')->insert([
                'product_id' => $params['product_id'],
                'product_amount' => $params['product_amount'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if ($comboResult && $offlineResult) {
                Db::commit();
                return json(['code' => 0, 'msg' => '添加成功']);
            }

            Db::rollback();
            return json(['code' => 1, 'msg' => '添加失败']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 更新离线商品价格
     *
     * @return \think\response\Json
     */
    public function updateOfflineProductPrice()
    {
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $params = Request::param();

        if (empty($params['id']) || empty($params['product_amount']) ||
            empty($params['day_price']) || empty($params['hour_price'])) {
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        try {
            Db::startTrans();

            // 更新普通价格
            $comboResult = Db::name('combo')
                ->where('id', $params['id'])
                ->update([
                    'day_price' => $params['day_price'],
                    'hour_price' => $params['hour_price'],
                    'time' => date('Y-m-d H:i:s')
                ]);

            // 更新离线价格
            $offlineResult = Db::name('offline')
                ->where('product_id', $params['product_id'])
                ->update([
                    'product_amount' => $params['product_amount'],
                    'updated_at' => date('Y-m-d H:i:s')  // updated_at 会自动更新
                ]);

            if ($comboResult !== false && $offlineResult !== false) {
                Db::commit();
                return json(['code' => 0, 'msg' => '更新成功']);
            }

            Db::rollback();
            return json(['code' => 1, 'msg' => '更新失败']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 删除离线商品
     *
     * @return \think\response\Json
     */
    public function deleteOfflineProduct()
    {
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $id = Request::param('id');

        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        try {
            // 删除离线商品
            $result = Db::name('offline')->where('id', $id)->delete();  // 使用正确的表名 'offline'

            if ($result) {
                return json(['code' => 0, 'msg' => '删除成功']);
            }
            return json(['code' => 1, 'msg' => '删除失败']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    // 获取所有标签
    public function getAllTags()
    {
        try {
            $tags = Db::name('game_tags')
                ->field('id, name')
                ->select();
            return json(['code' => 0, 'data' => $tags]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取标签失败：' . $e->getMessage()]);
        }
    }

    // 删除标签
    public function deleteTag()
    {
        $tagId = input('id');

        try {
            $result = Db::name('game_tags')->where('id', $tagId)->delete();
            if ($result) {
                return json(['code' => 0, 'msg' => '删除成功']);
            }
            return json(['code' => 1, 'msg' => '删除失败']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    public function updateGoodsTags()
    {
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $params = Request::param();
        $goodsId = intval($params['goods_id']);

        if (empty($goodsId)) {
            return json(['code' => 1, 'msg' => '商品ID不能为空']);
        }

        try {
            // 获取选中标签的名称并更新goods_key
            if (!empty($params['tags'])) {
                $tagIds = array_keys($params['tags']);
                $tags = Db::name('game_tags')->whereIn('id', $tagIds)->column('name');

                Db::name('goods')->where('id', $goodsId)->update([
                    'goods_key' => implode(',', $tags)
                ]);
            } else {
                // 如果没有选择标签，清空goods_key
                Db::name('goods')->where('id', $goodsId)->update([
                    'goods_key' => ''
                ]);
            }

            return json(['code' => 0, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 添加标签
     *
     * 接收前端传来的标签名称，添加到数据库中
     *
     * @return \think\response\Json
     */
    public function addTag()
    {
        // 获取标签名称
        $tagName = input('post.tag_name', '', 'trim');

        // 验证标签名称
        if (empty($tagName)) {
            return json(['code' => 1, 'msg' => '标签名称不能为空']);
        }

        try {
            // 检查标签是否已存在
            $exists = Db::name('game_tags')
                ->where('name', $tagName)
                ->find();

            if ($exists) {
                return json(['code' => 0, 'msg' => '标签已存在', 'data' => $exists]);
            }

            // 添加新标签 - 使用正确的日期时间格式
            $id = Db::name('game_tags')->insertGetId([
                'name' => $tagName,
                'create_time' => date('Y-m-d H:i:s') // 使用MySQL兼容的日期时间格式
            ]);

            if ($id) {
                return json(['code' => 0, 'msg' => '添加成功', 'data' => ['id' => $id, 'tag_name' => $tagName]]);
            } else {
                return json(['code' => 1, 'msg' => '添加失败']);
            }
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('添加标签失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取标签列表
     *
     * 返回所有标签的列表
     *
     * @return \think\response\Json
     */
    public function getTagsList()
    {
        try {
            // 获取所有标签
            $tags = Db::name('game_tags')
                ->field('id, name as tag_name')
                ->select()
                ->toArray();

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $tags]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取标签列表失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 批量删除离线商品
     *
     * @return \think\response\Json
     */
    public function batchDeleteOfflineProduct()
    {
        if (!Request::isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $ids = Request::param('id');

        if (empty($ids) || !is_array($ids)) {
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        try {
            // 批量删除离线商品
            $result = Db::name('offline')->whereIn('id', $ids)->delete();  // 使用正确的表名 'offline'

            if ($result) {
                return json(['code' => 0, 'msg' => '删除成功']);
            }
            return json(['code' => 1, 'msg' => '删除失败']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }
}