<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>{$system.sy_title}</title>
		<meta name="description" content="{$system.sy_des}" />
		<meta name="keywords" content="{$system.sy_key}" />
		<link href="/static/css/tailwind.min.css" rel="stylesheet">
		<script src="/static/js/jquery.min.js"></script>
		<style>
			.line-clamp-2 {
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
			}
			.pagination {
				display: flex;
				justify-content: center;
				margin-top: 1rem;
			}
			.pagination li {
				margin: 0 0.25rem;
			}
			.pagination li a, .pagination li span {
				display: inline-block;
				padding: 0.5rem 0.75rem;
				border: 1px solid #e2e8f0;
				border-radius: 0.25rem;
			}
			.pagination li.active span {
				background-color: #3b82f6;
				color: white;
				border-color: #3b82f6;
			}
		</style>
	</head>

	<body class="bg-gray-100 text-gray-800">

		<!-- 引用头部 -->
		{include file='../app/user/view/ticket/header.html'}	

		<!-- Main Content -->
		<main class="container mx-auto px-4 py-8">
			<div class="flex flex-col md:flex-row bg-white shadow-md rounded-lg overflow-hidden mb-8">
				<!-- 左侧边栏 -->
				<div class="md:w-1/4 p-4 bg-gray-50">
					<div class="flex flex-col items-center">
						<img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" alt="User Logo" class="w-24 h-24 rounded-full border-2 border-gray-300 mb-4">
						<span class="text-lg font-semibold">{$user.us_username}</span>
					</div>
					<!-- 引入通用侧边导航 -->
					{include file='../app/user/view/ticket/index.html'}
				</div>
				
				<!-- 右侧内容区域 -->
				<div class="md:w-3/4 p-4">
					<!-- 主内容区域标题 -->
					<div class="flex justify-between items-center mb-4 border-b pb-2">
						<h2 class="text-xl font-bold text-gray-800">
							{eq name="currentSection" value="help"}帮助中心{else/}系统公告{/eq}
						</h2>
						<div>
							<a href="/user/index/notice" class="mr-4 {eq name='currentSection' value='help'}text-blue-500 font-semibold{else/}text-gray-600{/eq} hover:text-blue-500">帮助中心</a>
							<a href="/user/index/systemGG" class="{eq name='currentSection' value='system'}text-blue-500 font-semibold{else/}text-gray-600{/eq} hover:text-blue-500">系统公告</a>
						</div>
					</div>
					
					<!-- 公告列表 -->
					<div class="bg-white rounded-lg overflow-hidden">
						{notempty name="notices"}
							<ul id="list_ul" class="divide-y divide-gray-200">
								{volist name="notices" id="val"}
								<a href="/user/index/noticeList?id={$val.id}" class="block hover:bg-gray-50 transition">
									<li class="p-4 flex items-start">
										<div class="flex-shrink-0 mr-4">
											<div class="w-20 h-20 flex items-center justify-center bg-gray-100 rounded-md border border-gray-200">
												<svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{:rand(1,4) == 1 ? 'M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z' : (rand(1,3) == 1 ? 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' : (rand(1,2) == 1 ? 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' : 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'))}"></path>
												</svg>
											</div>
										</div>
										<div class="flex-1 min-w-0">
											<h3 class="text-lg font-semibold text-gray-800 mb-1">{$val.not_title}</h3>
											<p class="text-gray-600 text-sm line-clamp-2 mb-2">{$val.not_des}</p>
											<p class="text-xs text-gray-500">{$val.time}</p>
										</div>
									</li>
								</a>
								{/volist}
							</ul>
							
							<!-- 分页 -->
							<div class="px-4 py-3 border-t border-gray-200">
								{$notices|raw}
							</div>
						{else/}
							<div class="p-8 text-center text-gray-500">
								<svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								<p>暂无内容</p>
							</div>
						{/notempty}
					</div>
				</div>
			</div>
		</main>

		<script>
			// 高亮当前部分
			$(document).ready(function() {
				// 已由模板条件判断处理高亮
			});
		</script>
	</body>
</html>