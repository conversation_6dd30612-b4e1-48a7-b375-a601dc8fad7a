
<title>商品列表</title>

<div class="layui-card layadmin-header">
  <div class="layui-breadcrumb" lay-filter="breadcrumb">
    <a lay-href="">主页</a>
    <a><cite>商城系统</cite></a>
    <a><cite>商品列表</cite></a>
  </div>
</div>

<style>
/* 这段样式只是用于演示 */
.layadmin-trailer{position: absolute; left: 0; top: 0; height: 100%; width: 100%; padding: 15px; display: flex; justify-content: center; flex-direction: column; text-align: center; box-sizing: border-box; font-size: 20px; font-weight: 300; color: #ccc;}
</style>

<div class="layadmin-trailer">
  即将开放
</div>

<script>
layui.use(['admin'], function(){
  var $ = layui.$
  ,admin = layui.admin
  ,element = layui.element
  ,router = layui.router();

  element.render();
  
  
});
</script>