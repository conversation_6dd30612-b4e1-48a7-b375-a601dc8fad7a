<!DOCTYPE html>
<html>
<head>
    <title>会员状态测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input { padding: 5px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>会员状态测试工具</h1>
        
        <div class="test-section">
            <h3>1. 测试用户会员状态</h3>
            <input type="number" id="userId" placeholder="输入用户ID" value="">
            <button onclick="testMembershipStatus()">检查会员状态</button>
            <div id="membershipResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 模拟会员购买测试</h3>
            <p>说明：这个测试会模拟会员购买的判断逻辑，但不会实际创建订单</p>
            <input type="number" id="testUserId" placeholder="输入用户ID" value="">
            <button onclick="simulateMembershipPurchase()">模拟购买检查</button>
            <div id="purchaseResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 修复说明</h3>
            <div class="result">
                <h4>修复前的问题：</h4>
                <ul>
                    <li>只检查订单表中是否有会员订单</li>
                    <li>没有检查用户的实际会员到期时间</li>
                    <li>导致会员过期后仍然无法购买新会员</li>
                </ul>
                
                <h4>修复后的逻辑：</h4>
                <ul>
                    <li>✅ 优先检查用户表的 exit_time 字段</li>
                    <li>✅ 判断会员是否真正过期</li>
                    <li>✅ 双重保险：同时检查订单表</li>
                    <li>✅ 过期会员可以正常购买新会员</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testMembershipStatus() {
            const userId = document.getElementById('userId').value;
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            const resultDiv = document.getElementById('membershipResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在查询...';
            
            fetch(`/admin/TestController/testMembershipStatus?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        const membershipData = data.data.membership_status;
                        const canPurchase = data.data.can_purchase_membership;
                        
                        let statusClass = membershipData.is_member ? 'success' : 'warning';
                        
                        resultDiv.className = `result ${statusClass}`;
                        resultDiv.innerHTML = `
                            <h4>用户会员状态：</h4>
                            <p><strong>用户ID：</strong>${data.data.user_info.id}</p>
                            <p><strong>用户名：</strong>${data.data.user_info.username}</p>
                            <p><strong>会员状态：</strong>${membershipData.status}</p>
                            <p><strong>当前时间：</strong>${membershipData.current_time}</p>
                            <p><strong>到期时间：</strong>${membershipData.exit_time || '无'}</p>
                            <p><strong>剩余天数：</strong>${membershipData.remaining_days}天</p>
                            <p><strong>可以购买会员：</strong>${canPurchase ? '是' : '否'}</p>
                            <p><strong>会员订单数量：</strong>${data.data.member_orders_count}</p>
                            <p><strong>未到期订单数量：</strong>${data.data.active_member_orders_count}</p>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `<p>查询失败：${data.msg}</p>`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<p>请求失败：${error.message}</p>`;
                });
        }
        
        function simulateMembershipPurchase() {
            const userId = document.getElementById('testUserId').value;
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            const resultDiv = document.getElementById('purchaseResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在模拟购买检查...';
            
            // 这里可以添加模拟购买的逻辑
            // 暂时显示说明信息
            resultDiv.className = 'result warning';
            resultDiv.innerHTML = `
                <h4>购买检查模拟：</h4>
                <p>请先使用上面的"检查会员状态"功能查看用户状态</p>
                <p>如果显示"可以购买会员：是"，则表示修复成功</p>
                <p>如果显示"可以购买会员：否"，则需要检查用户的会员到期时间</p>
            `;
        }
    </script>
</body>
</html>
