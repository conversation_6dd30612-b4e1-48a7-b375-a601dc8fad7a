<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>编辑商品</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
    <input type="hidden" name="id" value="{$combo.id}">
    
    <div class="layui-form-item">
      <label class="layui-form-label">商品ID</label>
      <div class="layui-input-inline">
        <input type="text" name="id" lay-verify="required" autocomplete="off" class="layui-input" value="{$combo.id}" disabled="disabled" style="border:none">
      </div>
    </div>
    
    <div class="layui-form-item">
      <label class="layui-form-label">对应商品</label>
      <div class="layui-input-inline">
        <select id="select1" name="product_id" lay-verify="required" disabled>
          {foreach $goodsAll as $index=>$val}
          <option value="{$val.id}">{$val.goods_name}</option>
          {/foreach}
        </select>
      </div>
    </div>
    
    <div class="layui-form-item">
      <label class="layui-form-label">按天价格</label>
      <div class="layui-input-inline">
        <input type="number" name="day_price" lay-verify="required|number" autocomplete="off" class="layui-input" value="{$combo.day_price|default=''}">
      </div>
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">按小时价格</label>
      <div class="layui-input-inline">
        <input type="number" name="hour_price" lay-verify="required|number" autocomplete="off" class="layui-input" value="{$combo.hour_price|default=''}">
      </div>
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">离线价格</label>
      <div class="layui-input-inline">
        <input type="number" name="product_amount" lay-verify="required|number" autocomplete="off" class="layui-input" value="{$combo.product_amount|default=''}">
      </div>
    </div>
    
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认添加">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-edit" id="layuiadmin-app-form-edit" value="确认编辑">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  document.querySelector("#select1").value="{$combo.co_goods}";
  
  layui.config({
    base: '../../../layuiadmin/'
  }).extend({
    index: 'lib/index'
  }).use(['index', 'form', 'upload'], function(){
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload;
    
    // 重新渲染表单
    form.render();
    
    //监听提交
    form.on('submit(layuiadmin-app-form-edit)', function(data){
      var field = data.field;
      var index = parent.layer.getFrameIndex(window.name);
      
      $.ajax({
        url: '/admin/base/updateOfflineProductPrice',
        method: 'post',
        data: field,
        success: function(res){
          if(res.code === 0 || res.code === undefined) {
            layer.msg('保存成功');
            parent.layui.table.reload('LAY-app-content-list2');
            parent.layer.close(index);
          } else {
            layer.msg('保存失败：' + (res.msg || '未知错误'));
          }
        }
      });
      
      return false;
    });
  });
  </script>
</body>
</html>