<style>
        * {
            padding: 0;
            margin: 0;
        }

        .alert_box2 {
    display: none;
    position: fixed;
    width: 350px;
    z-index: 9999;
    top: 200px;
    left: 40%;
    background-color: #ffffff; /* 简约白 */
    border-radius: 10px;
    border: 1px solid #ddd; /* 添加边框以提高对比度 */
}
        #itme_ul{
            overflow: auto;
            max-height: 370px;
        }
        .fixed a{
            color: #fff;
        }
        #itme_ul::-webkit-scrollbar {
             display: none; 
        }
        .alert_top {
            margin-top: -100px;

        }

        .alert_top>img {
            width: 100%;
        }
        .itme_box>ul {
            padding:10px 0;
            border-bottom: 1px solid rgba(255,255,255,.09);
        }
        .itme_box li {
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-radius: 10px;
            padding: 17px 0px;
            margin: 15px;
            background-color: rgb(66, 66, 66);
            font-size: 12px;
            font-weight: bold;
            color: aliceblue;
        }

        .itme_box li>div {
            margin-left: 10px;
            /* border: 1px dotted rgba(236, 236, 146, 0.09); */
        }

        .li_left>:nth-child(1) {
            text-align: center;
            font-size: 15px;
            color: rgb(245, 108, 108);
        }

        .li_left>:nth-child(2) {
            font-weight: 100;
            font-size: 12px;
        }

        .li_right>div {
            padding: 3px;
        }

        .li_left {
            position: relative;
        }

        .li_left::after {
            content: "";
            height: 18px;
            top: 10px;
            right: -20px;
            position: absolute;
            border-left: 2px dotted rgba(255, 255, 255, 1);
        }

        .li_right>div:nth-child(2) {
            font-weight: 100;
            font-size: 12px;
        }

        .submit {
            color: rgba(227, 230, 21, 9);
            padding: 5px 15px;
            border-radius: 10px;
            border: 1px dotted rgba(227, 230, 21, 9);
            cursor: pointer;
        }
        .closure{
            max-width: 250px;
            font-size: 12px;
            font-weight: bold;
            color: aliceblue;
            padding: 10px;
            margin: 10px auto;
            background-color: rgb(22,104,220);
            border-radius: 10px;
            text-align: center;
        }
    </style>
<div class="alert_box2">
        <div class="alert_top">
            <img src="/static/images/yhj-header.png">
        </div>
        <div class="itme_box">
            <ul id="itme_ul">
            </ul>
            <div class="closure">关闭</div>
        </div>
    </div>
<script src="/static/js/jquery/1.10.2/jquery.min.js"></script>
<script src="/static/js/alert.js"></script>
<script type="text/javascript" charset="utf-8">
$(".closure").click(function(){
    $(".alert_box2").fadeOut(300)
    $("#box").remove()
})
    $.post("/user/base/couponsList",function(e){
        if(e.length==0){
            $("#itme_ul").html("<h2 style='color:red;text-align:center;padding:50px'>暂无优惠卷</h2>")
            return false
        }
        $("#itme_ul").html("")//去除优惠卷列表
        e.forEach(function(val,index){
            $("#itme_ul").append(`
               <li>
                    <div class="li_left">
                        <div>${val.coupon_value}</div>
                        <div>${val.coupons_typeuser}立减</div>
                    </div>
                    <div class="li_right">
                        <div class="li_title">${val.coupons_typeuser}立减</div>
                        <div>领取${val.coupons_date}天内使用</div>
                    </div>
                    <div class="submit" onclick="subCoupons(${val.id})">领取</div>
                </li>
           `)
        })
    })
   function subCoupons(id){
        $.post("/user/base/getCoupons",{id:id},function(e){
            createAlert("#16b777",e,1)
            upUrl()
        })
    }
</script>