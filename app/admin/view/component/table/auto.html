

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>列宽自动分配 - 数据表格</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-card layadmin-header">
    <div class="layui-breadcrumb" lay-filter="breadcrumb">
      <a lay-href="">主页</a>
      <a><cite>组件</cite></a>
      <a><cite>数据表格</cite></a>
      <a><cite>列宽自动分配</cite></a>
    </div>
  </div>
  
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">列宽自动分配</div>
          <div class="layui-card-body">
            <table class="layui-hide" id="test-table-autowidth"></table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'table'], function(){
    var table = layui.table;
  
    table.render({
      elem: '#test-table-autowidth'
      ,url: layui.setter.base + 'json/table/user.js'
      ,cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
      ,cols: [[
        {field:'id', title: 'ID', sort: true}
        ,{field:'username', title: '用户名'} //width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
        ,{field:'sex', title: '性别', sort: true}
        ,{field:'city', title: '城市'}
        ,{field:'sign', title: '签名'}
        ,{field:'classify', title: '职业', align: 'center'} //单元格内容水平居中
        ,{field:'experience', title: '积分', sort: true, align: 'right'} //单元格内容水平居中
        ,{field:'score', title: '评分', sort: true, align: 'right'}
        ,{field:'wealth', title: '财富', sort: true, align: 'right'}
      ]]
    });
  
  });
  </script>
</body>
</html>