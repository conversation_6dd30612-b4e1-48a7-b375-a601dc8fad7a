<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量生成卡密</title>
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>

<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2>批量生成卡密</h2>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" id="batchGenerateForm">
                <!-- 卡密类型选择 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">卡密类型</label>
                    <div class="layui-input-block">
                        <input type="radio" name="type" value="membership" title="会员卡密" checked lay-filter="cdkTypeRadio">
                        <input type="radio" name="type" value="permanent" title="永久版卡密" lay-filter="cdkTypeRadio">
                    </div>
                </div>

                <!-- 会员类型选择框 -->
                <div class="layui-form-item" id="membershipTypeBlock">
                    <label class="layui-form-label">会员类型</label>
                    <div class="layui-input-block">
                        <select name="target_id" id="membershipType" lay-filter="membershipSelect" lay-search>
                            <option value="">请选择会员类型</option>
                            {foreach $membershipTypes as $type}
                            <option value="{$type.id}" data-validity="{$type.validity_period}">{$type.membership_type} (¥{$type.price})</option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <!-- 商品类型选择框 -->
                <div class="layui-form-item" id="goodsTypeBlock" style="display:none;">
                    <label class="layui-form-label">商品类型</label>
                    <div class="layui-input-block">
                        <select name="target_id" id="goodsType" lay-filter="targetSelect" lay-search>
                            <option value="">请选择商品类型</option>
                            {foreach $goodsList as $goods}
                            <option value="{$goods.id}">{$goods.goods_name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <!-- 有效期显示（只读） -->
                <div class="layui-form-item" id="validityPeriodBlock">
                    <label class="layui-form-label">有效期(月)</label>
                    <div class="layui-input-block">
                        <input type="text" id="validityPeriodDisplay" class="layui-input" readonly>
                        <input type="hidden" name="validity_period" id="validityPeriod">
                    </div>
                </div>

                <!-- 生成数量 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">生成数量</label>
                    <div class="layui-input-block">
                        <input type="number" name="num" id="generateNum" placeholder="请输入生成数量(1-100)" autocomplete="off" class="layui-input" value="1" min="1" max="100" required>
                    </div>
                </div>

                <!-- 代理ID输入框 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">代理ID</label>
                    <div class="layui-input-block">
                        <input type="number" name="agent_id" id="agentId" placeholder="可选，留空表示管理员生成" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <!-- 提交和重置按钮 -->
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="submitBatchGenerate">批量生成</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>

                <!-- 生成结果 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">生成结果</label>
                    <div class="layui-input-block">
                        <textarea id="cdkResult" class="layui-textarea" style="height: 200px;" readonly></textarea>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
    layui.config({
        base: '/layuiadmin/' // 静态资源所在路径
    }).extend({
        index: 'lib/index' // 主入口模块
    }).use(['index', 'form'], function () {
        var $ = layui.$, form = layui.form;

        // 监听卡密类型选择
        form.on('radio(cdkTypeRadio)', function(data){
            if(data.value === 'membership') {
                $('#membershipTypeBlock').show();
                $('#goodsTypeBlock').hide();
                $('#validityPeriodBlock').show();
                $('#goodsType').val(''); // 清空商品选择
            } else {
                $('#membershipTypeBlock').hide();
                $('#goodsTypeBlock').show();
                $('#validityPeriodBlock').hide();
                $('#membershipType').val(''); // 清空会员类型选择
            }
            form.render('select');
        });
        
        // 监听会员类型选择，自动填充有效期
        form.on('select(membershipSelect)', function(data){
            var validityPeriod = $(data.elem).find("option:selected").attr("data-validity");
            $('#validityPeriodDisplay').val(validityPeriod);
            $('#validityPeriod').val(validityPeriod);
        });

        // 监听表单提交
        form.on('submit(submitBatchGenerate)', function (data) {
            var formData = data.field;
            
            // 验证表单
            if(formData.type === 'membership' && !formData.target_id) {
                layer.msg('请选择会员类型', {icon: 2});
                return false;
            }
            
            if(formData.type === 'permanent' && !formData.target_id) {
                layer.msg('请选择商品类型', {icon: 2});
                return false;
            }
            
            var num = parseInt(formData.num);
            if(isNaN(num) || num < 1 || num > 100) {
                layer.msg('生成数量必须在1-100之间', {icon: 2});
                return false;
            }
            
            // 提交请求
            $.ajax({
                url: '/admin/CdkController/batchGenerate',
                type: 'POST',
                data: formData,
                success: function (response) {
                    if (response.msg === 1) {
                        layer.msg('批量生成成功', { icon: 1 });
                        
                        // 显示生成的卡密码
                        var cdkList = response.cdk_codes.join('\n');
                        $('#cdkResult').val(cdkList);
                        
                        // 刷新父页面的表格
                        if(parent.layui.table) {
                            parent.layui.table.reload('LAY-app-cdk-list');
                        }
                    } else {
                        layer.msg('生成失败：' + response.content, { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('服务器错误，请稍后再试', { icon: 2 });
                }
            });
            return false;
        });
    });
</script>
</body>

</html> 