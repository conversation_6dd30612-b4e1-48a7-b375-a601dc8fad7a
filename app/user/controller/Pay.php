<?php
/**
 * Pay.php
 *
 * 支付控制器：负责处理用户支付相关逻辑。
 *
 * @package     app\user\controller
 * <AUTHOR>
 * @date        2024-12-08
 * @copyright   Copyright (c) 2024 Kongzue
 * @license     MIT License
 * @version     1.0
 * @description 本控制器实现了订单的创建以及不同支付场景的处理。
 */

namespace app\user\controller;

use app\BaseController;            // 引入基础控制器
use app\admin\model\System;        // 引入系统设置模型（未使用，或可移除）
use think\facade\View;             // 引入视图类（未使用，或可移除）
use app\admin\model\User as Us;    // 引入用户模型（未使用，或可移除）
use think\facade\Request;          // 引入请求类
use app\admin\model\Order;         // 引入订单模型
use app\admin\model\Combo;         // 引入套餐模型
use think\facade\Cookie;           // 引入Cookie类

/**
 * 支付控制器
 *
 * @description 实现支付相关功能，包括后台充值订单的创建和套餐订单的生成。
 */
class Pay extends BaseController
{
    /**
     * 支付入口方法
     *
     * @description 根据传入的数据，处理不同类型的支付订单。
     *
     * @return array 返回操作结果，包括消息和订单数据。
     */
    public function index()
    {
        try {
            // 获取请求参数
            $data = Request::param();
            
            // 验证必要参数
            if (empty($data['ord_name'])) {
                return json(['code' => 400, 'msg' => '订单名称不能为空']);
            }
            
            if (!Cookie::get('id')) {
                return json(['code' => 401, 'msg' => '用户未登录']);
            }
            
            // 设置公共订单数据
            $data['ord_ifpay'] = 0;
            $data['ord_uid'] = Cookie::get('id');
            $data['time'] = date('Y-m-d H:i:s');
            
            // 添加订单查重逻辑
            if (!empty($data['ord_bbh'])) {
                $existingOrder = Order::where('ord_bbh', $data['ord_bbh'])->find();
                if ($existingOrder) {
                    return json([
                        'code' => 409,
                        'msg' => '订单已存在，请返回首页重新发起订单',
                        'data' => $existingOrder
                    ]);
                }
            }

            // 后台充值逻辑
            if ($data['ord_name'] == '余额充值') {
                if (empty($data['ord_money']) || !is_numeric($data['ord_money'])) {
                    return json(['code' => 400, 'msg' => '充值金额无效']);
                }
                
                $order = Order::create($data);
                return json(['code' => 200, 'msg' => '订单创建成功', 'data' => $order]);
            }
            
            // 套餐订单逻辑
            if (empty($data['ord_combo'])) {
                return json(['code' => 400, 'msg' => '套餐ID不能为空']);
            }
            
            // 验证套餐是否存在
            $combo = Combo::find($data['ord_combo']);
            if (!$combo) {
                return json(['code' => 404, 'msg' => '套餐不存在']);
            }
            
            // 添加重复购买检查
            if (!empty($data['ord_name'])) {
                $existingOrder = Order::where([
                    'ord_name' => $data['ord_name'],
                    'ord_uid' => Cookie::get('id'),
                ])->find();
                
                // 检查订单是否到期
                if ($existingOrder && $existingOrder->ord_maturity != 0) {
                    return json([
                        'code' => 409,
                        'msg' => '您已购买过该商品，不能重复购买',
                        'data' => $existingOrder
                    ]);
                }
            }

            $order = Order::create($data);
            return json(['code' => 200, 'msg' => '订单创建成功', 'data' => $order]);
            
        } catch (\think\exception\DbException $e) {
            \think\facade\Log::error('数据库错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '数据库操作失败',
                'error' => $e->getMessage()
            ]);
        } catch (\think\Exception $e) {
            \think\facade\Log::error('ThinkPHP错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => 'ThinkPHP框架错误',
                'error' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('系统错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '系统错误',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }


    public function offlinePay()
    {
        try {
            // 获取请求参数
            $data = Request::param();
            
            // 验证必要参数
            if (empty($data['ord_name'])) {
                return json(['code' => 400, 'msg' => '订单名称不能为空']);
            }
            
            if (!Cookie::get('id')) {
                return json(['code' => 401, 'msg' => '用户未登录']);
            }
            
            // 设置公共订单数据
            $data['ord_ifpay'] = 0;
            $data['ord_uid'] = Cookie::get('id');
            $data['time'] = date('Y-m-d H:i:s');
            
            // 添加订单查重逻辑
            if (!empty($data['ord_bbh'])) {
                $existingOrder = Order::where('ord_bbh', $data['ord_bbh'])->find();
                if ($existingOrder) {
                    return json([
                        'code' => 409,
                        'msg' => '订单已存在，请返回首页重新发起订单',
                        'data' => $existingOrder
                    ]);
                }
            }

            // // 套餐订单逻辑
            // if (empty($data['ord_combo'])) {
            //     return json(['code' => 400, 'msg' => '套餐ID不能为空']);
            // }
            
            // // 验证套餐是否存在
            // $combo = Combo::find($data['ord_combo']);
            // if (!$combo) {
            //     return json(['code' => 404, 'msg' => '套餐不存在']);
            // }

            // 添加重复购买检查
            if (!empty($data['ord_name'])) {
                $existingOrder = Order::where([
                    'ord_name' => $data['ord_name'],
                    'ord_uid' => Cookie::get('id'),
                    'ord_ifpay' => 1,  // 只检查已支付的订单
                ])->find();
                
                if ($existingOrder) {
                    return json([
                        'code' => 409,
                        'msg' => '您已购买过该商品，不能重复购买',
                        'data' => $existingOrder
                    ]);
                }
            }

            $order = Order::create($data);
            return json(['code' => 200, 'msg' => '订单创建成功', 'data' => $order]);
            
        } catch (\think\exception\DbException $e) {
            \think\facade\Log::error('数据库错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '数据库操作失败',
                'error' => $e->getMessage()
            ]);
        } catch (\think\Exception $e) {
            \think\facade\Log::error('ThinkPHP错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => 'ThinkPHP框架错误',
                'error' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('系统错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '系统错误',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function createPermanentOrder()
    {
        try {
            // 获取请求参数
            $data = Request::param();
            
            // 验证必要参数
            if (empty($data['ord_name'])) {
                return json(['code' => 400, 'msg' => '订单名称不能为空']);
            }
            
            if (!Cookie::get('id')) {
                return json(['code' => 401, 'msg' => '用户未登录']);
            }
            
            // 设置公共订单数据
            $data['ord_ifpay'] = 0;
            $data['ord_uid'] = Cookie::get('id');
            $data['time'] = date('Y-m-d H:i:s');
            //$data['ord_maturity'] = 0; // 设置订单为永久有效
            
            // 添加订单查重逻辑
            if (!empty($data['ord_bbh'])) {
                $existingOrder = Order::where('ord_bbh', $data['ord_bbh'])->find();
                if ($existingOrder) {
                    return json([
                        'code' => 409,
                        'msg' => '订单已存在，请返回首页重新发起订单',
                        'data' => $existingOrder
                    ]);
                }
            }

            // 添加重复购买检查
            if (!empty($data['ord_name'])) {
                $existingOrder = Order::where([
                    'ord_name' => $data['ord_name'],
                    'ord_uid' => Cookie::get('id'),
                    'ord_ifpay' => 1,  // 只检查已支付的订单
                ])->find();
                
                if ($existingOrder) {
                    return json([
                        'code' => 409,
                        'msg' => '您已购买过该商品，不能重复购买',
                        'data' => $existingOrder
                    ]);
                }
            }

            $order = Order::create($data);
            return json(['code' => 200, 'msg' => '永久订单创建成功', 'data' => $order]);
            
        } catch (\think\exception\DbException $e) {
            \think\facade\Log::error('数据库错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '数据库操作失败',
                'error' => $e->getMessage()
            ]);
        } catch (\think\Exception $e) {
            \think\facade\Log::error('ThinkPHP错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => 'ThinkPHP框架错误',
                'error' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('系统错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '系统错误',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 会员订单支付处理
     *
     * @description 处理会员订单的创建和支付逻辑
     * @return array 返回操作结果，包括消息和订单数据
     */
    public function membershipPay()
    {
        try {
            // 获取请求参数
            $data = Request::param();
            
            // 验证必要参数
            if (empty($data['ord_name'])) {
                return json(['code' => 400, 'msg' => '订单名称不能为空']);
            }
            
            if (!Cookie::get('id')) {
                return json(['code' => 401, 'msg' => '用户未登录']);
            }
            
            // 设置公共订单数据
            $data['ord_ifpay'] = 0;
            $data['ord_uid'] = Cookie::get('id');
            $data['time'] = date('Y-m-d H:i:s');
            $data['ord_type'] = $data['ord_type']; // 标记为会员订单
            $data['or_maturity'] = 1; // 设置为未到期状态
            
            // 添加订单查重逻辑
            if (!empty($data['ord_bbh'])) {
                $existingOrder = Order::where('ord_bbh', $data['ord_bbh'])->find();
                if ($existingOrder) {
                    return json([
                        'code' => 409,
                        'msg' => '订单已存在，请返回首页重新发起订单',
                        'data' => $existingOrder
                    ]);
                }
            }

            // 检查用户当前的会员状态（通过用户表的exit_time字段）
            $userId = Cookie::get('id');
            $user = Us::where('id', $userId)->find();

            if ($user && !empty($user['exit_time'])) {
                $currentTime = time();
                $exitTime = strtotime($user['exit_time']);

                // 如果会员时间还未过期，不允许重复购买
                if ($exitTime > $currentTime) {
                    return json([
                        'code' => 409,
                        'msg' => '您当前已是会员，无需重复购买',
                        'data' => [
                            'current_time' => date('Y-m-d H:i:s', $currentTime),
                            'exit_time' => $user['exit_time'],
                            'remaining_days' => ceil(($exitTime - $currentTime) / (24 * 60 * 60))
                        ]
                    ]);
                }
            }

            // 额外检查：查询是否有未到期的会员订单（双重保险）
            $activeMemberOrders = Order::where([
                'ord_uid' => $userId,
                'ord_ifpay' => 1,
                'or_maturity' => 1
            ])->select();

            $hasActiveMemberOrder = false;
            foreach ($activeMemberOrders as $order) {
                if (strpos($order['ord_name'], '会员') !== false) {
                    $hasActiveMemberOrder = true;
                    break;
                }
            }

            // 如果有未到期的会员订单，也不允许重复购买
            if ($hasActiveMemberOrder) {
                return json([
                    'code' => 409,
                    'msg' => '您有未到期的会员订单，无需重复购买',
                    'data' => $activeMemberOrders
                ]);
            }

            $order = Order::create($data);
            return json(['code' => 200, 'msg' => '会员订单创建成功', 'data' => $order]);
            
        } catch (\think\exception\DbException $e) {
            \think\facade\Log::error('数据库错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '数据库操作失败',
                'error' => $e->getMessage()
            ]);
        } catch (\think\Exception $e) {
            \think\facade\Log::error('ThinkPHP错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => 'ThinkPHP框架错误',
                'error' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('系统错误：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '系统错误',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
