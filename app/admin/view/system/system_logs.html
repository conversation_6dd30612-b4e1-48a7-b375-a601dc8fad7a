<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统日志管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
    <style>
        .search-toolbar {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border: 1px solid #e6e6e6;
        }
        .search-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }
        .search-row:last-child {
            margin-bottom: 0;
        }
        .search-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .search-item label {
            font-weight: 500;
            color: #333;
            white-space: nowrap;
            min-width: 70px;
        }
        .search-item .layui-input {
            width: 180px;
        }
        .search-item .layui-select {
            width: 150px;
        }
        .search-buttons {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .search-result-info {
            background: #e8f4fd;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 15px;
            color: #1890ff;
            font-size: 13px;
        }
        .search-result-info .close-btn {
            float: right;
            cursor: pointer;
            font-weight: bold;
            margin-left: 10px;
        }
        .table-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .table-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        .copy-text {
            cursor: pointer;
            color: #1890ff;
            text-decoration: underline;
        }
        .copy-text:hover {
            color: #40a9ff;
        }
        .status-success {
            color: #52c41a;
            font-weight: 500;
        }
        .status-error {
            color: #ff4d4f;
            font-weight: 500;
        }
    </style>
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-header">
        <h3 class="table-title">
          <i class="layui-icon layui-icon-file"></i> 系统日志管理
        </h3>
      </div>
      <div class="layui-card-body">
        <!-- 搜索工具栏 -->
        <div class="search-toolbar">
          <div class="search-row">
            <div class="search-item">
              <label>账号名称:</label>
              <input type="text" id="searchAccount" class="layui-input" placeholder="请输入账号名称" autocomplete="off">
            </div>
            <div class="search-item">
              <label>操作类型:</label>
              <select id="searchAction" class="layui-select">
                <option value="">全部操作</option>
                <option value="更新密码">更新密码</option>
                <option value="回收后改密">回收后改密</option>
                <option value="解锁">解锁</option>
                <option value="改密">改密</option>
                <option value="锁定">锁定</option>
                <option value="回收">回收</option>
              </select>
            </div>
            <div class="search-item">
              <label>操作状态:</label>
              <select id="searchStatus" class="layui-select">
                <option value="">全部状态</option>
                <option value="success">成功</option>
                <option value="failed">失败</option>
                <option value="completed">完成</option>
              </select>
            </div>
          </div>
          <div class="search-row">
            <div class="search-item">
              <label>开始时间:</label>
              <input type="text" id="searchStartTime" class="layui-input" placeholder="选择开始时间" readonly>
            </div>
            <div class="search-item">
              <label>结束时间:</label>
              <input type="text" id="searchEndTime" class="layui-input" placeholder="选择结束时间" readonly>
            </div>
            <div class="search-buttons">
              <button type="button" class="layui-btn layui-btn-primary" id="searchBtn">
                <i class="layui-icon layui-icon-search"></i> 搜索
              </button>

              <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">
                <i class="layui-icon layui-icon-refresh"></i> 重置
              </button>
              <button type="button" class="layui-btn layui-btn-primary" id="exportBtn">
                <i class="layui-icon layui-icon-export"></i> 导出
              </button>
              <button type="button" class="layui-btn layui-btn-danger" id="clearLogsBtn">
                <i class="layui-icon layui-icon-delete"></i> 清理日志
              </button>
            </div>
          </div>
        </div>

        <!-- 搜索结果信息 -->
        <div id="searchResultInfo" class="search-result-info" style="display: none;">
          <span id="searchResultText"></span>
          <span class="close-btn" onclick="clearSearchResult()">×</span>
        </div>

        <!-- 批量操作工具栏 -->
        <div class="table-toolbar">
          <div>
            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="batchDeleteBtn" style="display: none;">
              <i class="layui-icon layui-icon-delete"></i> 批量删除
            </button>
          </div>
        </div>

        <!-- 数据表格 -->
        <table id="systemLogsTable" lay-filter="systemLogsTable"></table>

        <!-- 操作列工具栏模板 -->
        <script type="text/html" id="logOperationBar">
          <div class="layui-btn-group">
            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="copyLogInfo({{d.id}})">
              <i class="layui-icon layui-icon-file"></i> 复制信息
            </button>
            <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteLog({{d.id}})">
              <i class="layui-icon layui-icon-delete"></i> 删除
            </button>
          </div>
        </script>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
    layui.use(['table', 'laydate', 'form', 'layer'], function(){
        var table = layui.table;
        var laydate = layui.laydate;
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;

        // 当前搜索参数
        var currentSearchParams = {};

        // 初始化日期选择器
        laydate.render({
            elem: '#searchStartTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss'
        });

        laydate.render({
            elem: '#searchEndTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss'
        });

        // 初始化表格
        var tableIns = table.render({
            elem: '#systemLogsTable',
            url: '/admin/index/getSystemLogs',
            page: true,
            limit: 15,
            limits: [10, 15, 20, 30, 50],
            loading: true,
            autoSort: false,
            cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field: 'id', title: 'ID', width: 80, sort: true, align: 'center', fixed: 'left'},
                {field: 'account_name', title: '账号名称', minWidth: 120, templet: function(d){
                    return d.account_name ? '<span style="color: #1890ff; font-weight: 500;">' + d.account_name + '</span>' : '未知账号';
                }},
                {field: 'action', title: '操作类型', minWidth: 100, templet: function(d){
                    var actionMap = {
                        'login': '<span class="layui-badge layui-bg-blue">登录</span>',
                        'logout': '<span class="layui-badge layui-bg-gray">登出</span>',
                        'change_password': '<span class="layui-badge layui-bg-orange">修改密码</span>',
                        'create_account': '<span class="layui-badge layui-bg-green">创建账号</span>',
                        'delete_account': '<span class="layui-badge layui-bg-red">删除账号</span>',
                        'update_account': '<span class="layui-badge layui-bg-cyan">更新账号</span>'
                    };
                    return actionMap[d.action] || '<span class="layui-badge">' + (d.action || '未知') + '</span>';
                }},
                {field: 'monitor_type', title: '方法类型', minWidth: 100},
                {field: 'timestamp', title: '操作时间', minWidth: 150, sort: true, templet: function(d){
                    return d.timestamp ? '<span style="color: #666;">' + d.timestamp + '</span>' : '未知时间';
                }},
                {field: 'status', title: '操作状态', width: 100, align: 'center', templet: function(d){
                    if (d.status === 'success') {
                        return '<span class="status-success"><i class="layui-icon layui-icon-ok-circle"></i> 成功</span>';
                    } else if (d.status === 'failed') {
                        return '<span class="status-error"><i class="layui-icon layui-icon-close-fill"></i> 失败</span>';
                    } else {
                        return '<span style="color: #52c41a;"><i class="layui-icon layui-icon-ok"></i> 完成</span>';
                    }
                }},
                {field: 'error_message', title: '错误信息', minWidth: 150, templet: function(d){
                    if (d.error_message && d.error_message.trim() !== '') {
                        var shortMsg = d.error_message.length > 20 ? d.error_message.substring(0, 20) + '...' : d.error_message;
                        return '<span style="color: #ff4d4f; cursor: pointer;" onclick="showFullMessage(\'' + encodeURIComponent(d.error_message) + '\')" title="点击查看完整信息">' + shortMsg + '</span>';
                    }
                    return '<span style="color: #52c41a;">无错误信息</span>';
                }},
                {field: 'old_password', title: '改密前密码', minWidth: 100, templet: function(d){
                    return d.old_password ? '<span class="copy-text" onclick="copyToClipboard(\'' + d.old_password + '\')" title="点击复制">' + d.old_password + '</span>' : '<span style="color: #ccc;">暂无数据</span>';
                }},
                {field: 'new_password', title: '改密后密码', minWidth: 100, templet: function(d){
                    return d.new_password ? '<span class="copy-text" onclick="copyToClipboard(\'' + d.new_password + '\')" title="点击复制">' + d.new_password + '</span>' : '<span style="color: #ccc;">暂无数据</span>';
                }},
                {title: '操作', width: 200, align: 'center', fixed: 'right', toolbar: '#logOperationBar'}
            ]],
            done: function(res, curr, count) {
                // 显示搜索结果信息
                if (res.search_info && res.search_info.has_search) {
                    showSearchResultInfo(count, res.search_info);
                } else {
                    hideSearchResultInfo();
                }

                // 监听复选框选择事件
                updateBatchDeleteButton();
            }
        });

        // 监听表格复选框选择
        table.on('checkbox(systemLogsTable)', function(obj) {
            updateBatchDeleteButton();
        });

        // 更新批量删除按钮显示状态
        function updateBatchDeleteButton() {
            var checkStatus = table.checkStatus('systemLogsTable');
            if (checkStatus.data.length > 0) {
                $('#batchDeleteBtn').show();
            } else {
                $('#batchDeleteBtn').hide();
            }
        }

        // 批量删除按钮事件
        $('#batchDeleteBtn').on('click', function() {
            var checkStatus = table.checkStatus('systemLogsTable');
            var selectedData = checkStatus.data;

            if (selectedData.length === 0) {
                layer.msg('请选择要删除的日志记录', {icon: 2});
                return;
            }

            var ids = selectedData.map(function(item) {
                return item.id;
            });

            layer.confirm('确定要删除选中的 ' + selectedData.length + ' 条日志记录吗？<br><span style="color: red;">此操作不可恢复！</span>', {
                icon: 3,
                title: '批量删除确认'
            }, function(index) {
                batchDeleteLogs(ids);
                layer.close(index);
            });
        });

        // 搜索按钮事件
        $('#searchBtn').on('click', function() {
            performSearch();
        });



        // 重置按钮事件（重新载入页面）
        $('#resetBtn').on('click', function() {
            location.reload();
        });

        // 导出按钮事件
        $('#exportBtn').on('click', function() {
            exportLogs();
        });

        // 清理日志按钮事件
        $('#clearLogsBtn').on('click', function() {
            showClearLogsWarning();
        });

        // 账号名称输入框回车搜索
        $('#searchAccount').on('keydown', function(e) {
            if (e.keyCode === 13) {
                performSearch();
            }
        });

        // 实时搜索（防抖处理）
        var searchTimeout;
        $('#searchAccount').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                if ($('#searchAccount').val().length >= 2 || $('#searchAccount').val().length === 0) {
                    performSearch();
                }
            }, 500);
        });

        // 执行搜索
        function performSearch() {
            currentSearchParams = {
                search_account: $('#searchAccount').val(),
                search_action: $('#searchAction').val(),
                search_status: $('#searchStatus').val(),
                search_start_time: $('#searchStartTime').val(),
                search_end_time: $('#searchEndTime').val()
            };

            tableIns.reload({
                where: currentSearchParams,
                page: {
                    curr: 1 // 重新从第 1 页开始
                }
            });
        }

        // 清除搜索
        function clearSearch() {
            $('#searchAccount').val('');
            $('#searchAction').val('');
            $('#searchStatus').val('');
            $('#searchStartTime').val('');
            $('#searchEndTime').val('');
            form.render('select');

            currentSearchParams = {};
            tableIns.reload({
                where: {},
                page: {
                    curr: 1
                }
            });

            hideSearchResultInfo();
        }

        // 重置搜索（保留原函数以防其他地方调用）
        function resetSearch() {
            clearSearch();
        }

        // 显示搜索结果信息
        function showSearchResultInfo(count, searchInfo) {
            var infoText = '搜索结果：共找到 ' + count + ' 条记录';
            var conditions = [];

            if (searchInfo.account) conditions.push('账号"' + searchInfo.account + '"');
            if (searchInfo.action) conditions.push('操作"' + searchInfo.action + '"');
            if (searchInfo.status) conditions.push('状态"' + searchInfo.status + '"');
            if (searchInfo.start_time) conditions.push('开始时间"' + searchInfo.start_time + '"');
            if (searchInfo.end_time) conditions.push('结束时间"' + searchInfo.end_time + '"');

            if (conditions.length > 0) {
                infoText += '，筛选条件：' + conditions.join('、');
            }

            $('#searchResultText').text(infoText);
            $('#searchResultInfo').show();
        }

        // 隐藏搜索结果信息
        function hideSearchResultInfo() {
            $('#searchResultInfo').hide();
        }

        // 导出日志
        function exportLogs() {
            var params = new URLSearchParams(currentSearchParams);
            params.append('export', '1');

            layer.confirm('确定要导出当前搜索结果的日志数据吗？', {
                icon: 3,
                title: '确认导出'
            }, function(index) {
                window.open('/admin/index/exportSystemLogs?' + params.toString());
                layer.close(index);
            });
        }

        // 显示清理日志警告弹窗
        function showClearLogsWarning() {
            var countdown = 5;
            var warningContent = `
                <div style="text-align: center; padding: 30px 20px;">
                    <div style="font-size: 48px; color: #ff4d4f; margin-bottom: 20px;">
                        <i class="layui-icon layui-icon-close-fill"></i>
                    </div>
                    <div style="font-size: 20px; font-weight: bold; color: #ff4d4f; margin-bottom: 15px;">
                        ⚠️ 危险操作警告 ⚠️
                    </div>
                    <div style="font-size: 16px; color: #333; line-height: 1.8; margin-bottom: 20px;">
                        您即将<span style="color: #ff4d4f; font-weight: bold;">永久删除所有系统日志</span>！<br>
                        此操作将清空整个日志数据库，包括：<br>
                        <div style="text-align: left; margin: 15px 0; padding: 15px; background: #fff2f0; border-left: 4px solid #ff4d4f; border-radius: 4px;">
                            • 所有账号操作记录<br>
                            • 所有密码变更历史<br>
                            • 所有系统监控日志<br>
                            • 所有错误信息记录
                        </div>
                        <span style="color: #ff4d4f; font-weight: bold; font-size: 18px;">此操作无法撤销！</span>
                    </div>
                    <div style="font-size: 14px; color: #666; margin-bottom: 20px;">
                        请确保您已经备份了重要的日志数据
                    </div>
                    <div id="countdownDisplay" style="font-size: 24px; font-weight: bold; color: #ff4d4f; margin-bottom: 20px;">
                        倒计时：<span id="countdownNumber">${countdown}</span> 秒
                    </div>
                    <div style="font-size: 14px; color: #999;">
                        倒计时结束后才能执行清理操作
                    </div>
                </div>
            `;

            var warningIndex = layer.open({
                type: 1,
                title: false,
                closeBtn: 1,
                area: ['500px', 'auto'],
                skin: 'layui-layer-rim',
                content: warningContent,
                btn: ['我已了解风险，立即清理', '取消操作'],
                btnAlign: 'c',
                btn1: function(index) {
                    if (countdown > 0) {
                        layer.msg('请等待倒计时结束！', {icon: 2});
                        return false;
                    }
                    layer.close(index);
                    confirmClearLogs();
                },
                btn2: function(index) {
                    layer.close(index);
                },
                success: function(layero, index) {
                    // 初始状态下禁用确认按钮
                    layero.find('.layui-layer-btn0').addClass('layui-btn-disabled').css({
                        'background-color': '#ccc',
                        'border-color': '#ccc',
                        'cursor': 'not-allowed'
                    });

                    // 开始倒计时
                    var timer = setInterval(function() {
                        countdown--;
                        $('#countdownNumber').text(countdown);

                        if (countdown <= 0) {
                            clearInterval(timer);
                            $('#countdownDisplay').html('<span style="color: #52c41a;">✓ 现在可以执行清理操作</span>');

                            // 启用确认按钮
                            layero.find('.layui-layer-btn0').removeClass('layui-btn-disabled').css({
                                'background-color': '#ff4d4f',
                                'border-color': '#ff4d4f',
                                'cursor': 'pointer'
                            }).text('确认清理所有日志');
                        }
                    }, 1000);

                    // 当弹窗关闭时清除定时器
                    layero.on('remove', function() {
                        clearInterval(timer);
                    });
                }
            });
        }

        // 确认清理日志
        function confirmClearLogs() {
            layer.confirm('最后确认：您真的要删除所有系统日志吗？<br><span style="color: #ff4d4f; font-weight: bold;">此操作不可恢复！</span>', {
                icon: 3,
                title: '最终确认',
                btn: ['确认删除', '我再想想']
            }, function(index) {
                layer.close(index);
                executeClearLogs();
            });
        }

        // 执行清理日志
        function executeClearLogs() {
            var loadingIndex = layer.load(2, {
                content: '正在清理日志，请稍候...',
                success: function(layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '40px',
                        'width': '70px'
                    });
                }
            });

            $.ajax({
                url: '/admin/index/clearAllSystemLogs',
                type: 'POST',
                timeout: 30000, // 30秒超时
                success: function(res) {
                    layer.close(loadingIndex);
                    if (res.code === 0) {
                        layer.alert('🎉 系统日志清理完成！<br><br>已成功删除 <span style="color: #52c41a; font-weight: bold;">' + (res.deleted_count || 0) + '</span> 条日志记录', {
                            icon: 1,
                            title: '清理成功',
                            btn: ['知道了'],
                            yes: function(index) {
                                layer.close(index);
                                tableIns.reload(); // 重新加载表格
                            }
                        });
                    } else {
                        layer.alert('❌ 清理失败：' + (res.msg || '未知错误'), {
                            icon: 2,
                            title: '清理失败'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    layer.close(loadingIndex);
                    var errorMsg = '网络错误';
                    if (status === 'timeout') {
                        errorMsg = '请求超时，清理操作可能仍在进行中';
                    }
                    layer.alert('❌ 清理失败：' + errorMsg, {
                        icon: 2,
                        title: '清理失败'
                    });
                }
            });
        }

        // 全局函数：复制到剪贴板
        window.copyToClipboard = function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    layer.msg('已复制到剪贴板: ' + text, {icon: 1});
                }).catch(function() {
                    fallbackCopyToClipboard(text);
                });
            } else {
                fallbackCopyToClipboard(text);
            }
        };

        // 兼容性复制方法
        function fallbackCopyToClipboard(text) {
            var tempInput = document.createElement("input");
            tempInput.style = "position: absolute; left: -1000px; top: -1000px";
            tempInput.value = text;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand("copy");
            document.body.removeChild(tempInput);
            layer.msg('已复制到剪贴板: ' + text, {icon: 1});
        }

        // 全局函数：显示完整错误信息
        window.showFullMessage = function(encodedMessage) {
            var message = decodeURIComponent(encodedMessage);
            layer.open({
                type: 1,
                title: '完整错误信息',
                area: ['600px', '400px'],
                content: '<div style="padding: 20px; line-height: 1.6; word-break: break-all;">' + message + '</div>',
                btn: ['复制', '关闭'],
                yes: function(index) {
                    copyToClipboard(message);
                }
            });
        };

        // 全局函数：清除搜索结果
        window.clearSearchResult = function() {
            hideSearchResultInfo();
        };

        // 全局函数：删除单条日志
        window.deleteLog = function(logId) {
            layer.confirm('确定要删除这条日志记录吗？<br><span style="color: red;">此操作不可恢复！</span>', {
                icon: 3,
                title: '删除确认'
            }, function(index) {
                $.ajax({
                    url: '/admin/index/deleteSystemLog',
                    type: 'POST',
                    data: {
                        id: logId
                    },
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg('删除成功', {icon: 1});
                            tableIns.reload(); // 重新加载表格
                        } else {
                            layer.msg(res.msg || '删除失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('网络错误，删除失败', {icon: 2});
                    }
                });
                layer.close(index);
            });
        };

        // 批量删除日志
        function batchDeleteLogs(ids) {
            $.ajax({
                url: '/admin/index/batchDeleteSystemLogs',
                type: 'POST',
                data: {
                    ids: ids.join(',')
                },
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg('批量删除成功', {icon: 1});
                        tableIns.reload(); // 重新加载表格
                        $('#batchDeleteBtn').hide(); // 隐藏批量删除按钮
                    } else {
                        layer.msg(res.msg || '批量删除失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，批量删除失败', {icon: 2});
                }
            });
        }

        // 全局函数：复制日志信息
        window.copyLogInfo = function(logId) {
            // 从表格数据中找到对应的日志记录
            var tableData = tableIns.config.data || [];
            var logData = null;

            // 如果表格数据为空，通过AJAX获取
            if (tableData.length === 0) {
                $.ajax({
                    url: '/admin/index/getSystemLogDetail',
                    type: 'GET',
                    data: {
                        id: logId
                    },
                    success: function(res) {
                        if (res.code === 0 && res.data) {
                            copyLogInfoToClipboard(res.data);
                        } else {
                            layer.msg('获取日志详情失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('网络错误，获取日志详情失败', {icon: 2});
                    }
                });
                return;
            }

            // 从表格数据中查找
            for (var i = 0; i < tableData.length; i++) {
                if (tableData[i].id == logId) {
                    logData = tableData[i];
                    break;
                }
            }

            if (logData) {
                copyLogInfoToClipboard(logData);
            } else {
                layer.msg('未找到日志记录', {icon: 2});
            }
        };

        // 复制日志信息到剪贴板
        function copyLogInfoToClipboard(logData) {
            var logInfo = '=== 系统日志详情 ===\n';
            logInfo += 'ID: ' + (logData.id || '') + '\n';
            logInfo += '账号名称: ' + (logData.account_name || '') + '\n';
            logInfo += '操作类型: ' + (logData.action || '') + '\n';
            logInfo += '方法类型: ' + (logData.monitor_type || '') + '\n';
            logInfo += '操作时间: ' + (logData.timestamp || '') + '\n';
            logInfo += '操作状态: ' + (logData.status || '') + '\n';
            logInfo += '错误信息: ' + (logData.error_message || '无') + '\n';
            logInfo += '改密前密码: ' + (logData.old_password || '无') + '\n';
            logInfo += '改密后密码: ' + (logData.new_password || '无') + '\n';
            logInfo += '===================';

            if (navigator.clipboard) {
                navigator.clipboard.writeText(logInfo).then(function() {
                    layer.msg('日志信息已复制到剪贴板', {icon: 1});
                }).catch(function() {
                    fallbackCopyToClipboard(logInfo);
                });
            } else {
                fallbackCopyToClipboard(logInfo);
            }
        }
    });
  </script>
</body>
</html>