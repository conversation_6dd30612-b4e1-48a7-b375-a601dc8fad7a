<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$system.sy_title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <script src="/static/js/jquery.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 全局基础样式 */
        :root {
            --color-primary: #4f46e5;
            --color-primary-dark: #4338ca;
            --color-primary-light: #818cf8;
            --color-secondary: #9333ea;
            --color-accent: #f43f5e;
            --color-success: #16a34a;
            --color-warning: #f59e0b;
            --color-error: #ef4444;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
            scroll-behavior: smooth;
            background-color: #f5f7fa;
            color: #1f2937;
        }

        /* 模态框动画 */
        .scale-in-center {
            transform: scale(0.95);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .scale-in-center.active {
            transform: scale(1);
            opacity: 1;
        }

        /* 令牌模态框样式 */
        #tokenModal {
            opacity: 0;
            transition: opacity 0.3s ease;
            --countdown-size: 110px;
        }

        .countdown-circle {
            position: relative;
            width: var(--countdown-size);
            height: var(--countdown-size);
            margin: 0 auto;
        }

        .countdown-circle svg {
            position: absolute;
            top: 0;
            left: 0;
            width: var(--countdown-size);
            height: var(--countdown-size);
            transform: rotate(-90deg);
        }

        .countdown-circle circle {
            fill: none;
            stroke-width: 8;
            stroke: var(--color-primary);
            stroke-linecap: round;
            stroke-dasharray: 283;
            stroke-dashoffset: 0;
            transition: stroke-dashoffset 0.5s linear, stroke 0.5s;
        }

        .timer-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .token-value {
            word-break: break-all;
            position: relative;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        /* 表格样式 */
        .data-table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .data-table th {
            background-color: #f9fafb;
            font-weight: 600;
            text-align: left;
            padding: 1rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: #6b7280;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table td {
            padding: 1rem;
            vertical-align: middle;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.875rem;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover td {
            background-color: #f9fafb;
        }

        /* 卡片样式 */
        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .btn-primary {
            background-color: var(--color-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--color-primary-dark);
        }

        .btn-success {
            background-color: var(--color-success);
            color: white;
        }

        .btn-success:hover {
            background-color: #15803d;
        }

        .btn-error {
            background-color: var(--color-error);
            color: white;
        }

        .btn-error:hover {
            background-color: #dc2626;
        }

        .btn-warning {
            background-color: var(--color-warning);
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
        }

        /* 动画效果 */
        @keyframes pulse-subtle {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.8;
            }
        }

        .animate-pulse-subtle {
            animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* 可用账号模态框样式 */
        #availableAccountsModal {
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        #availableAccountsContent {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }

        #availableAccountsContent::-webkit-scrollbar {
            width: 6px;
        }

        #availableAccountsContent::-webkit-scrollbar-track {
            background: transparent;
        }

        #availableAccountsContent::-webkit-scrollbar-thumb {
            background-color: rgba(156, 163, 175, 0.5);
            border-radius: 20px;
            border: 2px solid transparent;
        }

        /* 文本截断 */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 暗模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #111827;
                color: #f3f4f6;
            }

            .card {
                background-color: #1f2937;
            }

            .data-table th {
                background-color: #374151;
                color: #d1d5db;
                border-bottom: 1px solid #4b5563;
            }

            .data-table td {
                border-bottom: 1px solid #4b5563;
            }

            .data-table tr:hover td {
                background-color: #374151;
            }

            .dark\:bg-gray-750 {
                background-color: #1f2937;
            }

            .dark\:border-gray-700 {
                border-color: #374151;
            }
        }

        /* 令牌模态框整体样式优化 */
        #tokenModal .scale-in-center {
            max-width: 480px;
            background: linear-gradient(to bottom, #ffffff, #f8fafc);
            border-radius: 24px;
            overflow: hidden;
            box-shadow:
                0 20px 40px -12px rgba(0, 0, 0, 0.15),
                0 0 1px rgba(0, 0, 0, 0.1);
        }

        /* 模态框头部样式优化 */
        #tokenModal .modal-header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            padding: 1.5rem 2rem;
            position: relative;
            overflow: hidden;
        }

        #tokenModal .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zm60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.5;
        }

        /* 倒计时区域样式优化 */
        .token-container {
            padding: 1.5rem 2rem 2rem; /* 调整内边距 */
            background: linear-gradient(180deg, #ffffff, #f8fafc);
        }

        .countdown-circle {
            --countdown-size: 160px;
            margin: 0.5rem auto 2rem; /* 调整上边距 */
            padding-top: 0.5rem; /* 添加内边距 */
        }

        .countdown-circle::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: radial-gradient(circle at center, rgba(79, 70, 229, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
        }

        .countdown-circle circle {
            stroke-width: 8;
            filter: drop-shadow(0 0 12px rgba(79, 70, 229, 0.3));
        }

        /* 调整倒计时数字位置 */
        .timer-text {
            transform: translate(-50%, -45%); /* 之前是 -50%, -50% */
        }

        .timer-text span:first-child {
            font-size: 3.5rem;
            font-weight: 800;
            letter-spacing: -1px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            display: block;
            margin-top: 8px; /* 添加上边距，进一步下移数字 */
        }

        .timer-text span:last-child {
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* 令牌显示区域样式优化 */
        .token-value-container {
            margin: 2rem 0;
            padding: 0 1rem;
        }

        .token-value {
            background: linear-gradient(145deg, #f9fafb, #ffffff);
            border: 2px solid rgba(79, 70, 229, 0.1);
            border-radius: 16px;
            padding: 1.75rem;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 1.25rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            color: #4338ca;
            box-shadow:
                0 4px 20px -8px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.5) inset;
            backdrop-filter: blur(8px);
        }

        .token-value::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.8), transparent);
            border-radius: 16px 16px 0 0;
            pointer-events: none;
        }

        /* 复制按钮样式优化 */
        #copyTokenBtn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.75rem;
            border-radius: 12px;
            background: rgba(79, 70, 229, 0.1);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow:
                0 4px 12px -2px rgba(79, 70, 229, 0.2),
                0 0 1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #copyTokenBtn:hover {
            background: rgba(79, 70, 229, 0.15);
            transform: translateY(-2px);
            box-shadow:
                0 8px 16px -4px rgba(79, 70, 229, 0.3),
                0 0 1px rgba(0, 0, 0, 0.1);
        }

        /* 提示信息样式优化 */
        .token-info {
            text-align: center;
            padding: 0 2rem 0.5rem; /* 调整内边距 */
        }

        .token-info p {
            color: #6b7280;
            font-size: 0.875rem;
            line-height: 1.5;
            margin-bottom: 0.5rem;
        }

        .token-info p:first-child {
            color: #4b5563;
            font-weight: 500;
        }

        /* 模态框底部样式优化 */
        .modal-footer {
            padding: 1.25rem 2rem;
            background: #f8fafc;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: flex-end; /* 将按钮靠右对齐 */
        }

        .modal-footer .btn {
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-radius: 12px;
            min-width: 120px; /* 设置最小宽度确保按钮不会太窄 */
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-footer .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
        }

        .payment-method-card input:checked + div {
            border-color: var(--color-primary);
            background-color: rgba(79, 70, 229, 0.05);
        }

        .membership-card {
            transition: all 0.3s ease;
        }

        .membership-card:hover {
            transform: translateY(-5px);
        }

        .account-type-tab {
            transition: all 0.3s ease;
        }
        .account-type-tab.active {
            background-color: var(--color-primary);
            color: white;
        }
        .account-type-tab:not(.active):hover {
            background-color: rgba(79, 70, 229, 0.1);
        }

        /* 确保在小屏幕上正确堆叠 */
        @media (max-width: 768px) {
            .grid-cols-2 {
                grid-template-columns: 1fr;
            }
        }

        /* 优化中等屏幕的布局 */
        @media (min-width: 769px) and (max-width: 1280px) {
            .grid-cols-2 {
                grid-template-columns: repeat(2, 1fr);
            }

            /* 调整卡片内边距 */
            .card {
                padding: 1rem;
            }
        }

        /* 大屏幕优化 */
        @media (min-width: 1281px) {
            .container {
                max-width: 1440px;
            }

            .grid-cols-2 {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }
        }

        /* 在现有的样式基础上添加以下内容 */

        /* 账号卡片布局优化 */
        .account-section {
            display: none;
        }

        .account-section.active {
            display: block;
        }

        /* 账号卡片网格布局 */
        .grid-cols-2 {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        /* 账号卡片样式优化 */
        .card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        /* 账号信息区域样式 */
        .account-info {
            padding: 1rem;
        }

        .account-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .account-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .account-meta {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        /* 账号密码区域样式 */
        .credentials-section {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 0.375rem;
            margin: 1rem 0;
        }

        .credential-item {
            margin-bottom: 0.75rem;
        }

        .credential-item:last-child {
            margin-bottom: 0;
        }

        .credential-label {
            font-size: 0.75rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }

        .credential-value {
            font-family: monospace;
            font-size: 0.875rem;
            color: #1f2937;
            background: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
            border: 1px solid #e5e7eb;
            word-break: break-all;
        }

        /* 操作按钮区域样式 */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
        }

        .btn-primary {
            background: var(--color-primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--color-primary-dark);
        }

        .btn-success {
            background: var(--color-success);
            color: white;
        }

        .btn-success:hover {
            background: #15803d;
        }

        .btn-error {
            background: var(--color-error);
            color: white;
        }

        .btn-error:hover {
            background: #dc2626;
        }

        /* 响应式布局优化 */
        @media (max-width: 768px) {
            .grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            .card {
                background: #1f2937;
                border-color: #374151;
            }

            .account-title {
                color: #f3f4f6;
            }

            .account-meta {
                color: #9ca3af;
            }

            .credentials-section {
                background: #374151;
            }

            .credential-value {
                background: #1f2937;
                color: #f3f4f6;
                border-color: #4b5563;
            }

            .action-buttons {
                background: #374151;
                border-color: #4b5563;
            }
        }

        /* 账号类型标签样式 */
        .account-type-tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .online-tag {
            background: #dcfce7;
            color: #166534;
        }

        .offline-tag {
            background: #f3e8fd;
            color: #6b21b8;
        }

        /* 复制按钮样式 */
        .copy-btn {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .credential-item:hover .copy-btn {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200">

    <!-- 引用头部 -->
    {include file='../app/user/view/ticket/header.html'}

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-12 gap-6">
            <!-- 左侧用户信息和导航 (3列宽度) -->
            <div class="col-span-12 md:col-span-3">
                <div class="card p-6 bg-white dark:bg-gray-800 mb-6">
                    <div class="flex flex-col items-center">
                        <div class="relative mb-4">
                            <img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" alt="{$user.us_username}"
                                class="w-24 h-24 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg">
                            <div class="absolute bottom-0 right-0 bg-green-500 rounded-full w-4 h-4 border-2 border-white dark:border-gray-700"></div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{$user.us_username}</h3>
                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>会员到期：{$user.exit_time|date="Y-m-d H:i"}</span>
                        </div>

                        <!-- 会员信息卡片 -->
                        <div class="w-full mt-4 p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl text-white shadow-lg">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                    </svg>
                                    <span class="font-semibold">会员状态</span>
                                </div>
                                <span class="px-2 py-1 bg-white bg-opacity-20 rounded-full text-xs">{$user.membership_level}</span>
                            </div>
                            <div class="flex justify-between items-center text-sm text-white text-opacity-90">
                                <span>账户余额</span>
                                <span class="font-bold">￥{$user.us_money|default='0.00'}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 导航菜单 -->
                    <nav class="mt-6">
                        {include file='../app/user/view/ticket/index.html'}
                    </nav>
                </div>
            </div>

            <!-- 右侧主内容区 (9列宽度) -->
            <div class="col-span-12 md:col-span-9">
                <!-- 状态卡片行 -->
                <div class="grid grid-cols-1 gap-4 mb-6">
                    <!-- 剩余时间卡片已删除 -->

                    <div class="card p-4 bg-white dark:bg-gray-800">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-500 bg-opacity-10">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">会员等级</h3>
                                <p class="text-lg font-semibold text-gray-900 dark:text-gray-100">{$user.membership_level}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主要内容 -->
                <div class="card bg-white dark:bg-gray-800 overflow-hidden">
                    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            VIP账号管理
                        </h2>
                        {if $message neq '您当前的会员等级无权访问此功能' && $message neq '您的会员已过期,请续费后使用'}
                        <button class="btn btn-primary" onclick="getAvailableAccounts()">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            提取账号
                        </button>
                        {/if}
                    </div>

                    <!-- 内容区域 -->
                    <div class="p-6">
                        {if $message == 'success'}
                            {if empty($onlineAccounts) && empty($offlineAccounts)}
                                <!-- 空状态显示 -->
                                <div class="flex flex-col items-center justify-center py-12 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-300 dark:text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <h3 class="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">当前没有账号</h3>
                                    <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-md">您还没有提取任何VIP账号，点击"提取账号"按钮开始使用。</p>
                                    <button class="btn btn-primary px-6 py-2" onclick="getAvailableAccounts()">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        提取账号
                                    </button>
                                </div>
                            {else}
                                <!-- 账号类型切换标签 -->
                                <div class="flex space-x-4 mb-6">
                                    <button class="account-type-tab active px-6 py-2 rounded-lg text-sm font-medium" data-type="online">
                                        在线账号 ({$onlineAccounts|count})
                                    </button>
                                    <button class="account-type-tab px-6 py-2 rounded-lg text-sm font-medium" data-type="offline">
                                        离线账号 ({$offlineAccounts|count})
                                    </button>
                                </div>

                                <!-- 账号列表容器 -->
                                <div id="accountsContainer">
                                    <!-- 在线账号部分 -->
                                    <div class="account-section active" data-type="online">
                                        {if !empty($onlineAccounts)}
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                {foreach $onlineAccounts as $account}
                                                <div class="card bg-white dark:bg-gray-750 border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-700 transition-all duration-200">
                                                    <!-- 账号信息头部 -->
                                                    <div class="p-4 border-b border-gray-100 dark:border-gray-700">
                                                        <div class="flex items-center">
                                                                <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z" />
                                                                    </svg>
                                                                </div>
                                                            <div class="flex-1 min-w-0">
                                                                    <h4 class="font-medium text-gray-900 dark:text-gray-100">在线账号 #{$account.goods_name}</h4>
                                                                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">到期时间: {$account.exit_time|date="Y-m-d H:i"}</p>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    <!-- 账号密码信息 -->
                                                    <div class="p-4 space-y-3">
                                                        <!-- 账号 -->
                                                        <div class="group relative">
                                                            <label class="text-xs text-gray-500 dark:text-gray-400 mb-1 block">账号</label>
                                                            <div class="flex items-center bg-gray-50 dark:bg-gray-800 rounded-md p-2 group-hover:bg-gray-100 dark:group-hover:bg-gray-700 transition-colors">
                                                                <div class="flex-1 font-mono text-sm break-all">{$account.ac_name}</div>
                                                                <button onclick="copyText('{$account.ac_name}')" class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 opacity-0 group-hover:opacity-100 transition-opacity">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        </div>

                                                        <!-- 密码 -->
                                                        <div class="group relative">
                                                            <label class="text-xs text-gray-500 dark:text-gray-400 mb-1 block">密码</label>
                                                            <div class="flex items-center bg-gray-50 dark:bg-gray-800 rounded-md p-2 group-hover:bg-gray-100 dark:group-hover:bg-gray-700 transition-colors">
                                                                <div class="flex-1 font-mono text-sm break-all">{$account.ac_password}</div>
                                                                <button onclick="copyText('{$account.ac_password}')" class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 opacity-0 group-hover:opacity-100 transition-opacity">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- 操作按钮 -->
                                                    <div class="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700 flex flex-wrap gap-2 justify-end">
                                                        <button onclick="getToken({$account.id}, '{$account.goods_Type}')" class="btn btn-primary py-1.5 px-3 text-sm flex items-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                                            </svg>
                                                            提取令牌
                                                        </button>
                                                        <button onclick="confirmDelay({$account.id}, '{$account.goods_Type}')" class="btn btn-success py-1.5 px-3 text-sm flex items-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                            延长时间
                                                        </button>
                                                        <button onclick="cancelLease({$account.id}, '{$account.ord_bbh}', '1')" class="btn btn-error py-1.5 px-3 text-sm flex items-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                            </svg>
                                                            取消租用
                                                        </button>
                                                    </div>
                                                </div>
                                                {/foreach}
                                            </div>
                                        {/if}
                                    </div>

                                    <!-- 离线账号部分 -->
                                    <div class="account-section" data-type="offline">
                                        {if !empty($offlineAccounts)}
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                {foreach $offlineAccounts as $account}
                                                <!-- 离线账号卡片结构相同，只需修改图标和样式 -->
                                                <div class="card bg-white dark:bg-gray-750 border border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-700 transition-all duration-200">
                                                    <!-- 账号信息头部 -->
                                                    <div class="p-4 border-b border-gray-100 dark:border-gray-700">
                                                        <div class="flex items-center">
                                                            <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3">
                                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                                </svg>
                                                            </div>
                                                            <div class="flex-1 min-w-0">
                                                                <h4 class="font-medium text-gray-900 dark:text-gray-100">离线账号 #{$account.goods_name}</h4>
                                                                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">订单号: {$account.ord_bbh}</p>
                                                                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">到期时间: {$account.exit_time}</p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- 账号密码信息 -->
                                                    <div class="p-4 space-y-3">
                                                        <div class="group relative">
                                                            <label class="text-xs text-gray-500 dark:text-gray-400 mb-1 block">账号</label>
                                                            <div class="flex items-center bg-gray-50 dark:bg-gray-800 rounded-md p-2 group-hover:bg-gray-100 dark:group-hover:bg-gray-700 transition-colors">
                                                                <div class="flex-1 font-mono text-sm break-all">{$account.ac_name}</div>
                                                                <button onclick="copyText('{$account.ac_name}')" class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 opacity-0 group-hover:opacity-100 transition-opacity">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div class="group relative">
                                                            <label class="text-xs text-gray-500 dark:text-gray-400 mb-1 block">密码</label>
                                                            <div class="flex items-center bg-gray-50 dark:bg-gray-800 rounded-md p-2 group-hover:bg-gray-100 dark:group-hover:bg-gray-700 transition-colors">
                                                                <div class="flex-1 font-mono text-sm break-all">{$account.ac_password}</div>
                                                                <button onclick="copyText('{$account.ac_password}')" class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 opacity-0 group-hover:opacity-100 transition-opacity">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- 操作按钮 -->
                                                    <div class="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700 flex flex-wrap gap-2 justify-end">
                                                        <button onclick="getToken({$account.id}, '{$account.goods_Type}')" class="btn btn-primary py-1.5 px-3 text-sm flex items-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                                            </svg>
                                                            提取令牌
                                                        </button>
                                                        <button onclick="cancelLease({$account.id}, '{$account.ord_bbh}', '0')" class="btn btn-error py-1.5 px-3 text-sm flex items-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                            </svg>
                                                            取消租用
                                                        </button>
                                                    </div>
                                                </div>
                                                {/foreach}
                                            </div>
                                        {/if}
                                    </div>
                                </div>
                            {/if}
                        {elseif $message == '您当前的会员等级无权访问此功能' || $message == '您的会员已过期,请续费后使用'}
                            <!-- 会员升级提示 -->
                            <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl overflow-hidden shadow-xl">
                                <div class="px-8 py-12 text-center text-white">
                                    <div class="mb-6">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto opacity-75" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                        </svg>
                                    </div>
                                    <h2 class="text-2xl font-bold mb-3">
                                        {$message == '您当前的会员等级无权访问此功能' ? '提升您的会员权限' : '您的会员已过期'}
                                    </h2>
                                    <p class="mb-8 text-white text-opacity-90 max-w-md mx-auto">
                                        {$message == '您当前的会员等级无权访问此功能' ? '开通VIP会员后即可使用此功能，享受更多专属特权。' : '续费会员以继续享受所有特权服务。'}
                                    </p>
                                    <button class="btn px-8 py-3 bg-white text-indigo-600 hover:bg-gray-100 font-medium rounded-full shadow-lg transform hover:scale-105 transition-all" onclick="showMembershipModal()">
                                        {$message == '您当前的会员等级无权访问此功能' ? '立即开通会员' : '立即续费会员'}
                                    </button>

                                    <!-- 会员特权展示 -->
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-12">
                                        <div class="text-left p-4 bg-white bg-opacity-10 rounded-lg backdrop-filter backdrop-blur-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <h3 class="font-medium text-sm mb-1">无限时长</h3>
                                            <p class="text-xs text-white text-opacity-80">可无限量提取账号</p>
                                        </div>
                                        <div class="text-left p-4 bg-white bg-opacity-10 rounded-lg backdrop-filter backdrop-blur-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                            </svg>
                                            <h3 class="font-medium text-sm mb-1">专属特权</h3>
                                            <p class="text-xs text-white text-opacity-80">会员专属特别功能</p>
                                        </div>
                                        <div class="text-left p-4 bg-white bg-opacity-10 rounded-lg backdrop-filter backdrop-blur-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                            </svg>
                                            <h3 class="font-medium text-sm mb-1">安全保障</h3>
                                            <p class="text-xs text-white text-opacity-80">账号使用全程安全</p>
                                        </div>
                                        <div class="text-left p-4 bg-white bg-opacity-10 rounded-lg backdrop-filter backdrop-blur-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                                            </svg>
                                            <h3 class="font-medium text-sm mb-1">专属客服</h3>
                                            <p class="text-xs text-white text-opacity-80">7x24小时专属服务</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 令牌模态框 -->
    <div id="tokenModal" class="fixed inset-0 bg-gray-900 bg-opacity-75 backdrop-blur-sm flex items-center justify-center hidden z-50 transition-opacity duration-300">
        <div class="scale-in-center">
            <!-- 模态框头部 -->
            <div class="modal-header">
                <h2 class="text-xl font-bold text-white flex items-center relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                    </svg>
                    <span class="text-2xl">令牌信息</span>
                </h2>
            </div>

            <!-- 模态框内容 -->
            <div class="token-container">
                <!-- 倒计时区域 -->
                <div class="countdown-wrapper">
                    <div class="countdown-circle">
                        <svg>
                            <circle cx="80" cy="80" r="70"></circle>
                        </svg>
                        <div class="timer-text">
                            <span id="timerSeconds">30</span>
                            <span>秒</span>
                        </div>
                    </div>
                </div>

                <!-- 令牌显示区域 -->
                <div class="token-value-container">
                    <div class="token-value" id="tokenValue">
                        令牌内容将在此显示
                    </div>
                    <button id="copyTokenBtn" class="group">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </button>
                </div>

                <!-- 提示信息 -->
                <div class="token-info">
                    <p>令牌有效期为30秒，请尽快使用</p>
                    <p>令牌用于验证账号身份，请勿泄露给他人</p>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="modal-footer">
                <button class="btn btn-primary flex items-center justify-center" onclick="closeTokenModal()">
                    <span>关闭</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 可用账号模态框 -->
    <div id="availableAccountsModal" class="fixed inset-0 bg-gray-900 bg-opacity-75 backdrop-blur-sm flex items-center justify-center hidden z-50 transition-all duration-300">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-3xl scale-in-center overflow-hidden">
            <!-- 模态框头部 -->
            <div class="relative bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    选择可用账号
                </h3>
                <button class="absolute top-4 right-4 text-white opacity-80 hover:opacity-100 transition-opacity" onclick="closeAvailableAccountsModal()">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- 搜索框区域 -->
            <div class="px-6 pt-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <input type="text" id="searchInput" placeholder="搜索游戏名称..." class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center" id="searchClearBtn" style="display: none;">
                        <button class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 添加账号类型切换标签 -->
                <div class="flex space-x-4 mt-4">
                    <button id="onlineAccounts" class="account-type-tab active px-4 py-2 rounded-lg text-sm font-medium">
                        在线账号
                    </button>
                    <button id="offlineAccounts" class="account-type-tab px-4 py-2 rounded-lg text-sm font-medium">
                        离线账号
                    </button>
                </div>

                <!-- 现有的计数和排序部分 -->
                <div class="flex justify-between items-center mt-2 text-sm">
                    <div class="text-gray-500 dark:text-gray-400" id="searchResultsCount">
                        共 <span class="font-medium text-indigo-600 dark:text-indigo-400">0</span> 个可用账号
                    </div>
                    <div class="flex items-center">
                        <span class="text-gray-500 dark:text-gray-400 mr-2">排序方式:</span>
                        <select id="sortOrder" class="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200">
                            <option value="name">名称</option>
                            <option value="count">账号数量</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 账号列表区域 -->
            <div class="overflow-y-auto p-4" style="max-height: 50vh;" id="availableAccountsContent">
                <!-- 动态内容将在此注入 -->
                <div class="flex justify-center items-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 flex justify-between items-center">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    <span class="text-indigo-600 dark:text-indigo-400 font-medium">提示:</span> 点击"提取"按钮后将自动获取账号
                </div>
                <button onclick="closeAvailableAccountsModal()" class="btn btn-primary">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 消息模态框 -->
    <div id="messageModal" class="fixed inset-0 bg-gray-900 bg-opacity-75 backdrop-blur-sm flex items-center justify-center hidden z-50 transition-opacity duration-300">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md scale-in-center overflow-hidden">
            <!-- 模态框头部 -->
            <div id="messageModalHeader" class="relative px-6 py-4">
                <h2 class="text-xl font-bold text-white flex items-center">
                    <span id="messageModalIcon" class="mr-2">
                        <!-- 图标将通过 JS 动态插入 -->
                    </span>
                    <span id="messageModalTitle">提示</span>
                </h2>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6">
                <p id="messageModalContent" class="text-gray-700 dark:text-gray-300 text-center"></p>
            </div>

            <!-- 模态框底部 -->
            <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 flex justify-end">
                <button id="messageModalBtn" class="btn btn-primary px-4 py-2" onclick="closeMessageModal()">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- 确认模态框 -->
    <div id="confirmModal" class="fixed inset-0 bg-gray-900 bg-opacity-75 backdrop-blur-sm flex items-center justify-center hidden z-50 transition-opacity duration-300">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md scale-in-center overflow-hidden">
            <!-- 模态框头部 -->
            <div id="confirmModalHeader" class="relative px-6 py-4">
                <h2 class="text-xl font-bold text-white flex items-center">
                    <span id="confirmModalIcon" class="mr-2">
                        <!-- 图标将通过 JS 动态插入 -->
                    </span>
                    <span id="confirmModalTitle">确认操作</span>
                </h2>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6">
                <p id="confirmModalContent" class="text-gray-700 dark:text-gray-300 text-center"></p>
            </div>

            <!-- 模态框底部 -->
            <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 flex justify-end space-x-3">
                <button id="confirmModalCancelBtn" class="btn bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2">
                    取消
                </button>
                <button id="confirmModalConfirmBtn" class="btn btn-primary px-4 py-2">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- 会员开通模态框 -->
    <div id="membershipModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl overflow-hidden transform transition-all">
            <!-- 模态框头部 -->
            <div class="relative bg-gradient-to-r from-pink-500 to-purple-600 px-6 py-4">
                <h3 class="text-xl font-bold text-white">会员特权开通</h3>
                <button id="closeMembershipModal" class="absolute top-4 right-4 text-white hover:text-gray-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>


            <!-- 模态框内容 -->
            <div class="p-6">
                <div class="text-center mb-6">
                    <p class="text-gray-600">选择适合您的会员方案，享受更多特权</p>
                </div>

                <!-- 会员类型选择 -->
                <div class="flex mb-6">
                    <button id="durationMemberBtn" class="flex-1 py-2 px-4 bg-pink-500 text-white font-medium rounded-l focus:outline-none">
                        时长会员
                    </button>
                    <button id="vipMemberBtn" class="flex-1 py-2 px-4 bg-gray-200 text-gray-700 font-medium rounded-r focus:outline-none">
                        至尊会员
                    </button>
                </div>

                <!-- 时长会员选项 -->
                <div id="durationMemberOptions" class="grid grid-cols-3 gap-4 mb-6">
                    {foreach $membershipPricing as $membership}
                        {if condition="$membership.membership_type != '至尊会员'"}
                            <div class="border {if condition="$membership.membership_type == '年费会员'"}border-pink-500 bg-pink-50{else}border-gray-200{/if} rounded-lg p-4 text-center cursor-pointer hover:border-pink-500 duration-member-option" data-duration="{$membership.validity_period}" data-price="{$membership.price}">
                                <h4 class="font-medium text-gray-800">{$membership.membership_type}</h4>
                                <p class="text-pink-500 text-xl font-bold mt-2">¥{$membership.price}</p>
                                <p class="text-gray-500 text-sm mt-1">有效期{$membership.validity_period}天</p>
                            </div>
                        {/if}
                    {/foreach}
                </div>


                <!-- 至尊会员选项 -->
                <div id="vipMemberOptions" class="hidden mb-6">
                    {foreach $membershipPricing as $membership}
                        {if condition="$membership.membership_type == '至尊会员'"}
                            <div class="border-2 border-purple-600 rounded-lg p-6 text-center bg-gradient-to-b from-purple-50 to-white relative overflow-hidden">
                                <h4 class="font-bold text-purple-800 text-xl mb-2">{$membership.membership_type}</h4>
                                <p class="text-purple-600 text-3xl font-bold mt-4">¥{$membership.price}</p>
                                <p class="text-gray-600 mt-4">一次付费，终身享受</p>

                                <div class="mt-6 bg-white p-4 rounded-lg shadow-sm">
                                    <h5 class="font-medium text-gray-800 mb-2">专属特权</h5>
                                    <ul class="text-left text-gray-600 space-y-2">
                                        <li class="flex items-start">
                                            <svg class="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>所有游戏账号无限制使用</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>专属客服7×24小时服务</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>新游戏优先体验权</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>平台所有功能永久免费使用</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        {/if}
                    {/foreach}
                </div>

                <!-- 会员特权说明 -->
                <div class="bg-gray-50 p-4 rounded-lg mb-6">
                    <h4 class="font-medium text-gray-800 mb-2">会员特权</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-start">
                            <div class="bg-pink-100 p-2 rounded-full mr-3">
                                <svg class="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">租号时长翻倍</h5>
                                <p class="text-gray-500 text-sm">同等价格，使用时间延长一倍</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-pink-100 p-2 rounded-full mr-3">
                                <svg class="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">专属优惠折扣</h5>
                                <p class="text-gray-500 text-sm">所有游戏账号享受8折优惠</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-pink-100 p-2 rounded-full mr-3">
                                <svg class="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">账号安全保障</h5>
                                <p class="text-gray-500 text-sm">专属安全保障，无需担心账号问题</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-pink-100 p-2 rounded-full mr-3">
                                <svg class="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">优先使用权</h5>
                                <p class="text-gray-500 text-sm">热门游戏账号优先使用权</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 购买按钮 -->
                <div class="text-center">
                    <button id="purchaseMembership" class="bg-pink-500 hover:bg-pink-600 text-white font-medium py-3 px-8 rounded-lg shadow-md transition duration-300 transform hover:scale-105">
                        立即开通
                    </button>
                    <p class="text-gray-500 text-xs mt-2">点击立即开通，表示您同意<a href="#" class="text-pink-500">《会员服务协议》</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 会员开通模态框移动端适配样式 -->
    <style>
        /* 会员开通模态框WAP适配优化 */
        @media (max-width: 768px) {
            /* 模态框大小调整 */
            #membershipModal .bg-white.rounded-lg.shadow-xl {
                width: 95% !important;
                max-width: 95% !important;
                max-height: 85vh !important;
                overflow-y: auto !important;
                margin: 0 auto !important;
            }

            /* 模态框头部调整 */
            #membershipModal .relative.bg-gradient-to-r {
                padding: 0.75rem 1rem !important;
            }

            #membershipModal .text-xl.font-bold.text-white {
                font-size: 1rem !important;
            }

            #membershipModal .absolute.top-4.right-4 {
                top: 0.75rem !important;
                right: 0.75rem !important;
            }

            /* 模态框内容区域 */
            #membershipModal .p-6 {
                padding: 1rem !important;
            }

            /* 时长会员选项调整为单列 */
            #durationMemberOptions {
                grid-template-columns: 1fr !important;
            }

            /* 会员特权说明调整为单列 */
            #membershipModal .grid.grid-cols-2.gap-4 {
                grid-template-columns: 1fr !important;
            }

            /* 按钮文字大小调整 */
            #membershipModal .flex.mb-6 button {
                font-size: 0.875rem !important;
                padding: 0.5rem 1rem !important;
            }

            /* 价格文字调整 */
            #membershipModal .text-3xl.font-bold {
                font-size: 1.5rem !important;
            }

            /* 至尊会员价格调整 */
            #vipMemberOptions .text-purple-600.text-3xl.font-bold {
                font-size: 1.75rem !important;
            }
        }
    </style>

    <script src="/static/js/alert.js"></script>
    <script type="text/javascript" charset="utf-8">
        var userId = {$user.id};
        var username = "{$user.us_username}";
        let allAccountsData = [];
        let currentSort = 'name'; // 默认排序方式

        // 创建提示框函数
        function createAlert(color, message, time) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed top-4 right-4 z-50 p-4 rounded shadow-lg ${color === 'red' ? 'bg-red-500' : 'bg-green-500'} text-white`;
            alertDiv.textContent = message;

            document.body.appendChild(alertDiv);

            if (time !== 0) {
                setTimeout(() => {
                    alertDiv.remove();
                }, time || 3000);
            } else {
                const closeBtn = document.createElement('button');
                closeBtn.className = 'ml-4 text-white';
                closeBtn.innerHTML = '&times;';
                closeBtn.addEventListener('click', () => alertDiv.remove());
                alertDiv.appendChild(closeBtn);
            }
        }

        // 检查登录状态函数
        function checkLogin() {
            function getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                return null;
            }

            const userId = getCookie("id");
            const userLogin = getCookie("login");

            return (userId && userLogin);
        }

        // 页面加载完成时执行
        document.addEventListener('DOMContentLoaded', function() {
            // 为模态框添加事件监听
            initModalListeners();

            // 设置会员相关变量和功能
            const durationMemberBtn = document.getElementById('durationMemberBtn');
            const vipMemberBtn = document.getElementById('vipMemberBtn');
            const durationMemberOptions = document.getElementById('durationMemberOptions');
            const vipMemberOptions = document.getElementById('vipMemberOptions');
            const purchaseMembership = document.getElementById('purchaseMembership');
            const closeMembershipModal = document.getElementById('closeMembershipModal');

            // 当前选择的会员类型和选项
            let currentMemberType = 'duration'; // 'duration' 或 'vip'
            let selectedDurationOption = null;
            let selectedPrice = 0;

            // 初始化时长会员选项
            const durationOptions = document.querySelectorAll('.duration-member-option');
            if (durationOptions.length > 0) {
                // 默认选中年度会员或第一个选项
                let defaultOption = durationOptions[2] || durationOptions[0];
                defaultOption.classList.add('border-pink-500', 'bg-pink-50');
                defaultOption.classList.remove('border-gray-200');
                selectedDurationOption = defaultOption;
                selectedPrice = parseFloat(defaultOption.getAttribute('data-price') || 0);
            }

            // 关闭模态框事件
            if(closeMembershipModal) {
                closeMembershipModal.addEventListener('click', function() {
                    const membershipModal = document.getElementById('membershipModal');
                    membershipModal.classList.add('hidden');
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                });
            }

            // 切换会员类型
            if(durationMemberBtn) {
                durationMemberBtn.addEventListener('click', function() {
                    currentMemberType = 'duration';
                    durationMemberBtn.classList.remove('bg-gray-200', 'text-gray-700');
                    durationMemberBtn.classList.add('bg-pink-500', 'text-white');
                    if(vipMemberBtn) {
                        vipMemberBtn.classList.remove('bg-pink-500', 'text-white');
                        vipMemberBtn.classList.add('bg-gray-200', 'text-gray-700');
                    }
                    if(durationMemberOptions) durationMemberOptions.classList.remove('hidden');
                    if(vipMemberOptions) vipMemberOptions.classList.add('hidden');
                    selectedPrice = getSelectedDurationPrice();
                });
            }

            if(vipMemberBtn) {
                vipMemberBtn.addEventListener('click', function() {
                    currentMemberType = 'vip';
                    vipMemberBtn.classList.remove('bg-gray-200', 'text-gray-700');
                    vipMemberBtn.classList.add('bg-pink-500', 'text-white');
                    if(durationMemberBtn) {
                        durationMemberBtn.classList.remove('bg-pink-500', 'text-white');
                        durationMemberBtn.classList.add('bg-gray-200', 'text-gray-700');
                    }
                    if(vipMemberOptions) vipMemberOptions.classList.remove('hidden');
                    if(durationMemberOptions) durationMemberOptions.classList.add('hidden');

                    // 从数据属性获取至尊会员价格
                    const vipOption = document.querySelector('#vipMemberOptions .border-purple-600');
                    if (vipOption) {
                        const priceElement = vipOption.querySelector('.text-purple-600');
                        if (priceElement) {
                            const priceText = priceElement.textContent;
                            selectedPrice = parseFloat(priceText.replace('¥', '')) || 0;
                        }
                    }
                });
            }

            // 选择时长会员选项
            durationOptions.forEach(option => {
                option.addEventListener('click', function() {
                    durationOptions.forEach(opt => {
                        opt.classList.remove('border-pink-500', 'bg-pink-50');
                        opt.classList.add('border-gray-200');
                    });
                    this.classList.remove('border-gray-200');
                    this.classList.add('border-pink-500', 'bg-pink-50');
                    selectedDurationOption = this;
                    selectedPrice = parseFloat(this.getAttribute('data-price') || 0);
                });
            });

            // 获取选中的时长会员价格
            function getSelectedDurationPrice() {
                if (!selectedDurationOption) {
                    // 默认选中第一个选项
                    const firstOption = document.querySelector('.duration-member-option');
                    if (firstOption) {
                        return parseFloat(firstOption.getAttribute('data-price') || 0);
                    }
                    return 0;
                }

                return parseFloat(selectedDurationOption.getAttribute('data-price') || 0);
            }

            // 购买会员按钮事件
            if(purchaseMembership) {
                purchaseMembership.addEventListener('click', function() {
                    if(!checkLogin()) {
                        const membershipModal = document.getElementById('membershipModal');
                        membershipModal.classList.add('hidden');
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        // 这里可以添加跳转到登录页面的逻辑
                        window.location.href = '/user/login';
                        return;
                    }

                    // 会员购买逻辑
                    const memberType = currentMemberType;
                    const price = selectedPrice;

                    let memberName = '';
                    let duration = 0;

                    if (memberType === 'duration') {
                        if (selectedDurationOption) {
                            const titleElement = selectedDurationOption.querySelector('h4');
                            memberName = titleElement ? titleElement.textContent : '会员';

                            if (memberName.includes('月')) {
                                duration = 30;
                            } else if (memberName.includes('季')) {
                                duration = 90;
                            } else if (memberName.includes('年')) {
                                duration = 365;
                            }
                        }
                    } else {
                        memberName = '至尊会员';
                        duration = -1; // 表示永久
                    }

                    console.log(`购买会员: ${memberName}, 价格: ${price}, 时长: ${duration}天`);

                    // 发送AJAX请求
                    $.ajax({
                        url: "/user/index/purchaseMembership",
                        type: "post",
                        dataType: "json",
                        data: {
                            memberName: encodeURIComponent(memberName),
                            ord_type: "member"
                        },
                        success: function(res) {
                            if (res.code === 0 && res.src) {
                                // 显示成功提示
                                createAlert("green", "订单创建成功，3秒后跳转...", 3000);
                                // 3秒后跳转
                                setTimeout(function() {
                                    window.location.href = res.src;
                                }, 3000);
                            } else {
                                // 显示错误提示
                                createAlert("red", res.msg || "请联系管理员", 0);
                            }
                        },
                        error: function() {
                            createAlert("red", "网络错误，请重试", 0);
                        }
                    });
                });
            }


        });

        // 添加全局的showMembershipModal函数
        window.showMembershipModal = function() {
            const membershipModal = document.getElementById('membershipModal');
            if (membershipModal) {
                membershipModal.classList.remove('hidden');
                document.body.classList.add('modal-open');
                document.body.style.overflow = 'hidden';
            }
        };

        // 检查是否存在showAlert函数
        function showErrorMessage(message) {
            if (typeof showAlert === 'function') {
                showAlert("#ff4444", message, 2);
                } else {
                alert("错误: " + message);
                }
        }

        function showSuccessMessage(message) {
            if (typeof showAlert === 'function') {
                showAlert("#16b777", message, 1);
            } else {
                alert("成功: " + message);
            }
        }

        // 初始化模态框事件监听
        function initModalListeners() {
            // 令牌模态框点击外部关闭
            document.getElementById('tokenModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeTokenModal();
                }
            });

            // 账号模态框点击外部关闭
            document.getElementById('availableAccountsModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeAvailableAccountsModal();
                }
            });
        }

        // 关闭令牌模态框
        function closeTokenModal() {
            const modal = document.getElementById('tokenModal');
            modal.querySelector('.scale-in-center').classList.remove('active');

            // 清除计时器
            if (window.tokenTimer) {
                clearInterval(window.tokenTimer);
                window.tokenTimer = null;
            }

            setTimeout(() => {
                modal.classList.add('hidden');

                // 重置UI状态
                document.getElementById('tokenValue').textContent = '令牌内容将在此显示';
                document.getElementById('timerSeconds').textContent = '30';

                const circleElement = document.querySelector('.countdown-circle circle');
                if (circleElement) {
                    circleElement.style.strokeDasharray = '';
                    circleElement.style.strokeDashoffset = '';
                    circleElement.style.stroke = 'var(--color-primary)';
                }
            }, 300);
        }

        // 关闭可用账号模态框
        function closeAvailableAccountsModal() {
            const modal = document.getElementById('availableAccountsModal');
            modal.querySelector('.scale-in-center').classList.remove('active');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }

        function extractAccount() {
            $.ajax({
                url: "/user/index/extractVipAccount",
                type: "POST",
                dataType: "json",
                data: {},
                success: function(res) {
                    if(res.code === 0){
                        showSuccessMessage("已分配账号，请刷新页面查看");
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showErrorMessage(res.msg);
                    }
                },
                error: function() {
                    showErrorMessage("请求失败，请稍后重试");
                }
            });
        }

        // 修改getAvailableAccounts函数，确保搜索框正确绑定事件
        function getAvailableAccounts() {
            // 显示模态框
            const modal = document.getElementById('availableAccountsModal');
            modal.classList.remove('hidden');

            // 重置内容并显示加载动画
            document.getElementById('availableAccountsContent').innerHTML = `
                <div class="flex justify-center items-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
                </div>
            `;

            // 确保模态框动画生效
            setTimeout(() => {
                const content = modal.querySelector('.scale-in-center');
                content.classList.add('active');

                // 发送AJAX请求
                $.ajax({
                    url: "/user/Buy/getAvailableAccounts1",
                    type: "GET",
                    dataType: "json",
                    success: function(res) {
                        console.log("接收到数据:", res);

                        if(res.code === 200 && res.data && res.data.length > 0) {
                            // 保存所有数据
                            allAccountsData = res.data;

                            // 过滤出在线账号数据
                            const onlineAccounts = res.data.filter(item =>
                item.account_type === 'online' || item.account_type === undefined
            );

                            // 更新计数为在线账号数量
                            document.querySelector('#searchResultsCount span').textContent = onlineAccounts.length;

                            // 只渲染在线账号数据
                            renderFilteredAccounts(onlineAccounts);

                            // 初始化模态框控件
                            initModalControls();

                            // 确保在线账号标签处于激活状态
                            document.getElementById('onlineAccounts')?.classList.add('active');
                            document.getElementById('offlineAccounts')?.classList.remove('active');

                            console.log("初始化显示在线账号，数量:", onlineAccounts.length);
                        } else {
                            // 处理空数据或错误
                            document.querySelector('#searchResultsCount span').textContent = 0;
                            document.getElementById('availableAccountsContent').innerHTML = `
                                <div class="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                    <p class="text-lg font-medium">${res.msg || "暂无可用账号"}</p>
                                    <p class="mt-1">请稍后再试</p>
                            </div>
                        `;
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("请求失败:", status, error);
                        document.getElementById('availableAccountsContent').innerHTML = `
                            <div class="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4 text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p class="text-lg font-medium">网络错误</p>
                                <p class="mt-1">获取账号信息失败，请检查网络连接或刷新页面</p>
                        </div>
                    `;
                    }
                });
            }, 50);
        }

        // 新函数：初始化模态框控件（搜索框、标签等）
        function initModalControls() {
            // 绑定搜索框事件
            bindSearchBox();

            // 绑定账号类型标签切换事件
            bindAccountTypeTabs();

            // 绑定排序选择器事件
            bindSortSelector();

            console.log("模态框控件事件已初始化");
        }

        // 在线账号标签点击处理函数
        function handleOnlineTabClick() {
            // 更新标签样式
            document.getElementById('onlineAccounts').classList.add('active');
            document.getElementById('offlineAccounts').classList.remove('active');

            // 过滤显示在线账号
            const searchTerm = document.getElementById('searchInput')?.value.toLowerCase().trim() || '';
            let filteredData = allAccountsData.filter(item =>
                item.account_type === 'online' || item.account_type === undefined
            );

            if (searchTerm) {
                filteredData = filteredData.filter(item =>
                    (item.goods_name || "").toLowerCase().includes(searchTerm)
                );
            }

            // 更新计数
            document.querySelector('#searchResultsCount span').textContent = filteredData.length;

            // 渲染过滤后的数据
            renderFilteredAccounts(filteredData);

            console.log("已过滤显示在线账号，数量:", filteredData.length);
        }

        // 离线账号标签点击处理函数
        function handleOfflineTabClick() {
            // 更新标签样式
            document.getElementById('offlineAccounts').classList.add('active');
            document.getElementById('onlineAccounts').classList.remove('active');

            // 过滤显示离线账号
            const searchTerm = document.getElementById('searchInput')?.value.toLowerCase().trim() || '';
            let filteredData = allAccountsData.filter(item => item.account_type === 'offline');

            if (searchTerm) {
                filteredData = filteredData.filter(item =>
                    (item.goods_name || "").toLowerCase().includes(searchTerm)
                );
            }

            // 更新计数
            document.querySelector('#searchResultsCount span').textContent = filteredData.length;

            // 渲染过滤后的数据
            renderFilteredAccounts(filteredData);

            console.log("已过滤显示离线账号，数量:", filteredData.length);
        }

        // 绑定搜索框事件
        function bindSearchBox() {
            const searchInput = document.getElementById('searchInput');
            const searchClearBtn = document.getElementById('searchClearBtn');

            if (!searchInput) {
                console.error("找不到搜索框元素");
                return;
            }

            // 先移除可能存在的旧事件监听器（防止重复绑定）
            searchInput.removeEventListener('input', handleSearchInput);

            // 添加新的事件监听器
            searchInput.addEventListener('input', handleSearchInput);

            // 为清除按钮绑定事件
            if (searchClearBtn) {
                searchClearBtn.removeEventListener('click', handleSearchClear);
                searchClearBtn.addEventListener('click', handleSearchClear);
            }

            console.log("搜索框事件已绑定");
        }

        // 搜索输入处理函数
        function handleSearchInput(e) {
            const searchTerm = e.target.value.toLowerCase().trim();
            const searchClearBtn = document.getElementById('searchClearBtn');

            // 显示/隐藏清除按钮
            if (searchClearBtn) {
                searchClearBtn.style.display = searchTerm.length > 0 ? 'flex' : 'none';
            }

            // 获取当前选中的账号类型
            const activeTab = document.querySelector('#availableAccountsModal .account-type-tab.active');
            const accountType = activeTab ? activeTab.id : 'onlineAccounts';

            // 根据当前选中的标签类型过滤数据
            let filteredData;
            if (accountType === 'onlineAccounts') {
                filteredData = allAccountsData.filter(item =>
                    item.account_type === 'online' || item.account_type === undefined
                );
            } else if (accountType === 'offlineAccounts') {
                filteredData = allAccountsData.filter(item =>
                    item.account_type === 'offline'
                );
            } else {
                filteredData = allAccountsData;
            }

            // 再根据搜索词进一步过滤
            if (searchTerm) {
                filteredData = filteredData.filter(item =>
                    (item.goods_name || "").toLowerCase().includes(searchTerm)
                );
            }

            // 更新结果计数
            document.querySelector('#searchResultsCount span').textContent = filteredData.length;

            // 渲染过滤后的数据
            renderFilteredAccounts(filteredData);

            console.log("搜索过滤完成，显示", filteredData.length, "条结果");
        }

        // 搜索清除按钮处理函数
        function handleSearchClear() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
                searchInput.dispatchEvent(new Event('input'));
            }
        }

        // 绑定账号类型标签切换事件
        function bindAccountTypeTabs() {
            const onlineAccountsTab = document.getElementById('onlineAccounts');
            const offlineAccountsTab = document.getElementById('offlineAccounts');

            if (!onlineAccountsTab || !offlineAccountsTab) {
                console.error("找不到账号类型标签元素");
                return;
            }

            // 清除现有事件（防止重复绑定）
            onlineAccountsTab.removeEventListener('click', handleOnlineTabClick);
            offlineAccountsTab.removeEventListener('click', handleOfflineTabClick);

            // 绑定新事件
            onlineAccountsTab.addEventListener('click', handleOnlineTabClick);
            offlineAccountsTab.addEventListener('click', handleOfflineTabClick);

            // 默认选中在线账号标签
            onlineAccountsTab.classList.add('active');
            offlineAccountsTab.classList.remove('active');

            console.log("账号类型标签切换事件已绑定");
        }

        // 排序选择器事件绑定
        function bindSortSelector() {
            const sortSelect = document.getElementById('sortOrder');
            if (!sortSelect) {
                console.error("找不到排序选择器元素");
                return;
            }

            sortSelect.removeEventListener('change', handleSortChange);
            sortSelect.addEventListener('change', handleSortChange);

            console.log("排序选择器事件已绑定");
        }

        // 排序变更处理函数
        function handleSortChange() {
            const sortValue = this.value;
            const searchTerm = document.getElementById('searchInput')?.value.toLowerCase().trim() || '';

            // 获取当前选中的账号类型
            const activeTab = document.querySelector('#availableAccountsModal .account-type-tab.active');
            const accountType = activeTab ? activeTab.id : 'onlineAccounts';

            // 根据当前选中的标签类型过滤数据
            let filteredData;
            if (accountType === 'onlineAccounts') {
                filteredData = allAccountsData.filter(item =>
                    item.account_type === 'online' || item.account_type === undefined
                );
            } else if (accountType === 'offlineAccounts') {
                filteredData = allAccountsData.filter(item =>
                    item.account_type === 'offline'
                );
            } else {
                filteredData = allAccountsData;
            }

            // 再根据搜索词进一步过滤
            if (searchTerm) {
                filteredData = filteredData.filter(item =>
                    (item.goods_name || "").toLowerCase().includes(searchTerm)
                );
            }

            // 根据排序方式排序
            if (sortValue === 'name') {
                filteredData.sort((a, b) => (a.goods_name || "").localeCompare(b.goods_name || ""));
            } else if (sortValue === 'count') {
                filteredData.sort((a, b) => (b.count || 0) - (a.count || 0));
            }

            // 渲染排序后的数据
            renderFilteredAccounts(filteredData);

            console.log("排序完成，使用", sortValue, "方式排序");
        }

        function renderAvailableAccounts(data) {
            allAccountsData = data;
            document.querySelector('#searchResultsCount span').textContent = data.length;

            // 初始化搜索和排序功能
            initSearchAndSort();

            // 渲染数据
            renderFilteredAccounts(data);
        }

        function renderFilteredAccounts(data) {
            const content = document.getElementById('availableAccountsContent');
            content.innerHTML = '';

            if (!data || data.length === 0) {
                content.innerHTML = `
                    <div class="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-lg font-medium">未找到匹配的游戏</p>
                        <p class="mt-1">请尝试使用其他关键词</p>
                    </div>
                `;
                return;
            }

            content.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    ${data.map(item => {
                        const id = item?.id || 0;
                        const goodsName = item?.goods_name || "未知游戏";
                        const count = item?.count || 0;
                        const goodsTime = item?.goods_time || "不限";
                        const goodsImg = item?.goods_img || "/static/images/default-game.png";
                        const accountType = item?.account_type || "online";

                        // 添加账号类型标签的样式
                        const typeTagClass = accountType === 'online'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
                        const typeText = accountType === 'online' ? '在线账号' : '离线账号';

                        return `
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                                <div class="p-4">
                                    <div class="flex items-center mb-3">
                                        <div class="flex-shrink-0 w-16 h-16 mr-3 overflow-hidden rounded">
                                            <img src="${goodsImg}" alt="${goodsName}" class="w-full h-full object-cover" onerror="this.src='/static/images/placeholder.png'">
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="font-medium text-gray-900 dark:text-gray-100 line-clamp-1 text-lg">${goodsName}</h4>
                                            <div class="flex flex-wrap items-center gap-2 mt-1">
                                                <span class="px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
                                                    ${count} 个可用
                                                </span>
                                                <span class="px-2 py-0.5 ${typeTagClass} text-xs rounded-full">
                                                    ${typeText}
                                                </span>
                                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                                    使用期限: <span class="font-medium">${goodsTime}</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3 flex justify-end">
                                        <button onclick="extractSpecificAccount(${id}, '${accountType}')" class="btn btn-primary py-1.5 px-4">
                                            提取
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        }

        // 修改提取账号的函数
        function extractSpecificAccount(goodsId, accountType) {
            const button = event.currentTarget;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                处理中...
            `;

            $.ajax({
                url: "/user/index/extractRegularMemberVipAccount",
                type: "POST",
                dataType: "json",
                data: {
                    id: userId,
                    username: username,
                    account_id: goodsId,
                    account_type: accountType // 添加账号类型参数
                },
                success: function(res) {
                    if(res.code === 0 || res.code === 200) {
                        showMessageModal('success', res.msg || "账号提取成功！", () => {
                            closeAvailableAccountsModal();
                            location.reload();
                        });
                    } else {
                        button.disabled = false;
                        button.innerHTML = originalText;
                        showMessageModal('error', res.msg || "提取失败，请稍后重试");
                    }
                },
                error: function(xhr, status, error) {
                    button.disabled = false;
                    button.innerHTML = originalText;
                    handleAjaxError(xhr, "提取账号失败");
                }
            });
        }

        // 添加账号类型切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.account-type-tab');
            const sections = document.querySelectorAll('.account-section');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 更新标签样式
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // 获取选中的类型
                    const type = this.getAttribute('data-type');

                    // 更新内容显示
                    sections.forEach(section => {
                        if (section.getAttribute('data-type') === type) {
                            section.classList.add('active');
                        } else {
                            section.classList.remove('active');
                        }
                    });
                });
            });

            // 确保默认选择"在线账号"标签
            const onlineTab = document.querySelector('.account-type-tab[data-type="online"]');
            if (onlineTab) {
                onlineTab.classList.add('active');

                // 确保显示在线账号部分
                const onlineSection = document.querySelector('.account-section[data-type="online"]');
                if (onlineSection) {
                    onlineSection.classList.add('active');
                }

                // 隐藏离线账号部分
                const offlineSection = document.querySelector('.account-section[data-type="offline"]');
                if (offlineSection) {
                    offlineSection.classList.remove('active');
                }
            }
        });

        // 显示确认模态框
        function showConfirmModal(options) {
            const {
                type = 'warning',
                title = '确认操作',
                message,
                confirmText = '确定',
                cancelText = '取消',
                onConfirm,
                onCancel
            } = options;

            const modal = document.getElementById('confirmModal');
            const header = document.getElementById('confirmModalHeader');
            const icon = document.getElementById('confirmModalIcon');
            const titleEl = document.getElementById('confirmModalTitle');
            const content = document.getElementById('confirmModalContent');
            const confirmBtn = document.getElementById('confirmModalConfirmBtn');
            const cancelBtn = document.getElementById('confirmModalCancelBtn');

            // 设置样式和内容
            switch(type) {
                case 'warning':
                    header.className = 'relative bg-gradient-to-r from-yellow-500 to-orange-600 px-6 py-4';
                    icon.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    `;
                    break;
                case 'danger':
                    header.className = 'relative bg-gradient-to-r from-red-500 to-red-600 px-6 py-4';
                    icon.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    `;
                    break;
            }

            titleEl.textContent = title;
            content.textContent = message;
            confirmBtn.textContent = confirmText;
            cancelBtn.textContent = cancelText;

            // 绑定事件
            confirmBtn.onclick = () => {
                closeConfirmModal();
                onConfirm?.();
            };

            cancelBtn.onclick = () => {
                closeConfirmModal();
                onCancel?.();
            };

            // 显示模态框
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.scale-in-center').classList.add('active');
            }, 10);
        }

        // 关闭确认模态框
        function closeConfirmModal() {
            const modal = document.getElementById('confirmModal');
            modal.querySelector('.scale-in-center').classList.remove('active');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }

        // 修改取消租用函数
        function cancelLease(accountId, orderNumber, goodsType) {
            showConfirmModal({
                type: 'danger',
                title: '取消租用',
                message: '确定要取消租用此账号吗？此操作不可撤销。',
                confirmText: '确定取消',
                cancelText: '再想想',
                onConfirm: () => {
                    // 检查必要参数
                    if (!userId || !username) {
                        showMessageModal('error', '用户信息不完整，请刷新页面后重试');
                        return;
                    }

                    if (!accountId) {
                        showMessageModal('error', '账号ID无效，请刷新页面后重试');
                        return;
                    }

                    // 显示加载提示
                    if (typeof showAlert === 'function') {
                        showAlert("#16b777", "正在处理", 1);
                    }

                    // 根据账号类型选择不同的接口
                    const url = goodsType === '0'
                        ? "/user/base/cancelOfflineLease"
                        : "/user/index/取消租用1";

                    $.ajax({
                        url: url,
                        type: "POST",
                        dataType: "json",
                        data: {
                            id: userId,
                            username: username,
                            vip_id: accountId,
                            ord_bbh: orderNumber // 添加订单号参数
                        },
                        success: function(res) {
                            if (typeof hideAlert === 'function') {
                                hideAlert();
                            }

                            if(res.msg === 1 || res.code === 200) {
                                showMessageModal('success', res.content || "账号已成功取消租用", () => {
                                    location.reload();
                                });
                            } else {
                                showMessageModal('error', res.content || "操作失败，请稍后重试");
                            }
                        },
                        error: function(xhr, status, error) {
                            if (typeof hideAlert === 'function') {
                                hideAlert();
                            }

                            let errorMsg = "请求失败，请检查网络连接";
                            if (xhr.status === 404) {
                                errorMsg = "取消租用功能未找到，请联系管理员 (错误: 404)";
                            } else if (xhr.status === 500) {
                                errorMsg = "服务器内部错误，请稍后再试 (错误: 500)";
                            }

                            showMessageModal('error', errorMsg);
                        }
                    });
                }
            });
        }

        // 修改延长租用时间函数
        function confirmDelay(accountId, accountType) {
            showConfirmModal({
                type: 'warning',
                title: '延长租用',
                message: '确定要延长此账号租用时间3小时吗？',
                confirmText: '确定延长',
                cancelText: '取消',
                onConfirm: () => {
                    // 检查必要参数
                    if (!userId || !username) {
                        showMessageModal('error', '用户信息不完整，请刷新页面后重试');
                        return;
                    }

                    if (!accountId) {
                        showMessageModal('error', '账号ID无效，请刷新页面后重试');
                        return;
                    }

                    // 显示加载提示
                    if (typeof showAlert === 'function') {
                        showAlert("#16b777", "正在处理", 1);
                    }

                    $.ajax({
                        url: "/user/index/delayVipAccount",
                        type: "POST",
                        dataType: "json",
                        data: {
                            id: userId,
                            username: username,
                            vip_id: accountId,
                            account_type: accountType
                        },
                        success: function(res) {
                            if (typeof hideAlert === 'function') {
                                hideAlert();
                            }

                            if(res.code === 0 || res.code === 200) {
                                showMessageModal('success', res.msg || "账号租用时间已成功延长", () => {
                                    location.reload();
                                });
                            } else {
                                showMessageModal('error', res.msg || "操作失败，请稍后重试");
                            }
                        },
                        error: function(xhr, status, error) {
                            if (typeof hideAlert === 'function') {
                                hideAlert();
                            }

                            let errorMsg = "请求失败，请检查网络连接";
                            if (xhr.status === 404) {
                                errorMsg = "延长功能未找到，请联系管理员 (错误: 404)";
                            } else if (xhr.status === 500) {
                                errorMsg = "服务器内部错误，请稍后再试 (错误: 500)";
                            }

                            showMessageModal('error', errorMsg);
                        }
                    });
                }
            });
        }

        // 获取令牌
        function getToken(accountId, goodsType) {
            // 显示加载中的提示
            if (typeof showAlert === 'function') {
                showAlert("#16b777", "正在加载", 1);
            }

            // 根据账号类型选择不同的接口
            const url = goodsType === '0' ? "/user/base/getOfflineToken" : "/user/base/getVIipAccountToken";

            $.ajax({
                url: url,
                type: "POST",
                dataType: "json",
                data: {
                    id: accountId
                },
                success: function(res) {
                    // 隐藏加载提示
                    if (typeof hideAlert === 'function') {
                        hideAlert();
                    }

                    if (res.msg === 1 && res.data && res.data.token) {
                        const modal = document.getElementById('tokenModal');
                        modal.classList.remove('hidden');
                        setTimeout(() => {
                            modal.style.opacity = 1;
                            modal.querySelector('.scale-in-center').classList.add('active');

                            // 添加令牌显示动画
                            const tokenValue = document.getElementById('tokenValue');
                            tokenValue.textContent = res.data.token;
                            tokenValue.classList.add('show');

                            // 启动倒计时
                            startTokenCountdown();
                        }, 10);
                    } else {
                        showMessageModal('error', res.content || "已达到令牌提取次数限制");
                    }
                },
                error: function(xhr, status, error) {
                    // 隐藏加载提示
                    if (typeof hideAlert === 'function') {
                        hideAlert();
                    }

                    let errorMsg = "请求失败，请检查网络连接";
                    if (xhr.status === 404) {
                        errorMsg = "获取令牌功能未找到，请联系管理员";
                    } else if (xhr.status === 500) {
                        errorMsg = "服务器内部错误，请稍后再试";
                    }

                    showMessageModal('error', errorMsg);
                }
            });
        }

        // 启动令牌倒计时
        function startTokenCountdown() {
            const timerElement = document.getElementById('timerSeconds');
            const circleElement = document.querySelector('.countdown-circle circle');
            const circumference = 2 * Math.PI * 45;

            let timeLeft = 30;
            timerElement.textContent = timeLeft;

            if (window.tokenTimer) {
                clearInterval(window.tokenTimer);
            }

            // 初始化圆环动画
            circleElement.style.strokeDasharray = `${circumference}`;
            circleElement.style.strokeDashoffset = '0';

            window.tokenTimer = setInterval(() => {
                timeLeft--;

                // 平滑更新数字
                const currentNumber = parseInt(timerElement.textContent);
                if (currentNumber !== timeLeft) {
                    timerElement.style.transform = 'translateY(-20px) scale(0.8)';
                    timerElement.style.opacity = '0';

                    setTimeout(() => {
                        timerElement.textContent = timeLeft;
                        timerElement.style.transform = 'translateY(20px) scale(0.8)';

                        requestAnimationFrame(() => {
                            timerElement.style.transform = 'translateY(0) scale(1)';
                            timerElement.style.opacity = '1';
                        });
                    }, 150);
                }

                // 更新圆环进度
                const dashOffset = circumference * (1 - timeLeft / 30);
                circleElement.style.strokeDashoffset = dashOffset;

                // 颜色过渡效果
                if (timeLeft <= 10) {
                    circleElement.style.stroke = 'var(--color-warning)';
                    circleElement.style.filter = 'drop-shadow(0 0 8px rgba(245, 158, 11, 0.4))';
                }
                if (timeLeft <= 5) {
                    circleElement.style.stroke = 'var(--color-error)';
                    circleElement.style.filter = 'drop-shadow(0 0 8px rgba(239, 68, 68, 0.4))';
                }

                if (timeLeft <= 0) {
                    clearInterval(window.tokenTimer);
                    window.tokenTimer = null;

                    // 添加消失动画
                    modal.querySelector('.token-value').style.transform = 'translateY(20px) scale(0.95)';
                    modal.querySelector('.token-value').style.opacity = '0';

                    setTimeout(() => {
                        closeTokenModal();
                    }, 300);
                }
            }, 1000);
        }

        // 复制令牌到剪贴板
        function copyToken() {
            const tokenText = document.getElementById('tokenValue').textContent;
            const copyBtn = document.getElementById('copyTokenBtn');

            navigator.clipboard.writeText(tokenText).then(() => {
                copyBtn.classList.add('copy-success');
                copyBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                `;
                copyBtn.style.background = 'rgba(22, 163, 74, 0.2)';
                copyBtn.style.color = 'var(--color-success)';

                setTimeout(() => {
                    copyBtn.classList.remove('copy-success');
                    copyBtn.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    `;
                    copyBtn.style.background = 'rgba(79, 70, 229, 0.1)';
                    copyBtn.style.color = 'var(--color-primary)';
                }, 2000);

                showMessageModal('success', "令牌已复制到剪贴板");
            }).catch(err => {
                showMessageModal('error', "复制失败，请手动复制");
                console.error('复制失败:', err);
            });
        }

        /**
         * 处理AJAX错误
         * @param {Object} xhr - XMLHttpRequest对象
         * @param {string} status - 错误状态
         * @param {string} error - 错误信息
         * @param {Function} [callback] - 可选的回调函数
         */
        function handleAjaxError(xhr, status, error, callback) {
            // 隐藏任何可能存在的加载提示
            hideLoadingIndicator();

            // 默认错误消息
            let errorMsg = '服务器错误，请联系客服';

            // 尝试从响应体解析错误信息（适用于所有状态码）
            try {
                const response = JSON.parse(xhr.responseText);
                if (response && response.msg) {
                    errorMsg = response.msg;
                    // 成功解析到错误消息，直接显示并返回
                    showMessageModal('error', errorMsg);

                    // 如果提供了回调函数，则执行
                    if (typeof callback === 'function') {
                        callback(errorMsg);
                    }

                    console.error(`AJAX错误 [${xhr.status}]: ${errorMsg}`);
                    return;
                }
            } catch (e) {
                console.error('解析错误响应失败:', e);
                // 解析失败时，根据状态码提供默认错误消息
            }

            // 如果没有从响应体获取到错误信息，则根据HTTP状态码提供默认错误消息
            switch (xhr.status) {
                case 400:
                    errorMsg = '请求参数有误，请检查后重试';
                    break;
                case 401:
                    errorMsg = '身份验证失败，请重新登录';
                    // 可以加入自动跳转到登录页面的逻辑
                    setTimeout(() => {
                        window.location.href = '/user/login';
                    }, 2000);
                    break;
                case 403:
                    errorMsg = '您没有权限执行此操作';
                    break;
                case 404:
                    errorMsg = '请求的资源不存在，请联系客服';
                    break;
                case 405:
                    errorMsg = '不支持的请求方法';
                    break;
                case 408:
                    errorMsg = '请求超时，请检查网络连接';
                    break;
                case 429:
                    errorMsg = '请求过于频繁，请稍后再试';
                    break;
                case 500:
                    errorMsg = '服务器内部错误，请联系客服';
                    break;
                case 502:
                    errorMsg = '网关错误，请稍后重试';
                    break;
                case 503:
                    errorMsg = '服务暂时不可用，请稍后重试';
                    break;
                case 504:
                    errorMsg = '网关超时，请稍后重试';
                    break;
                default:
                    if (xhr.status >= 500) {
                        errorMsg = '服务器错误，请联系客服';
                    } else {
                        errorMsg = `请求失败(${xhr.status})，请稍后重试`;
                    }
            }

            // 显示错误消息
            showMessageModal('error', errorMsg);

            // 如果提供了回调函数，则执行
            if (typeof callback === 'function') {
                callback(errorMsg);
            }

            console.error(`AJAX错误 [${xhr.status}]: ${errorMsg}`);
        }

        /**
         * 隐藏加载指示器
         */
        function hideLoadingIndicator() {
            // 查找并移除所有加载指示器
            const loadingIndicators = document.querySelectorAll('.loading-indicator, .processing-overlay');
            loadingIndicators.forEach(indicator => {
                if (indicator && indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            });
        }

        /**
         * 显示消息模态框
         * @param {string} type - 消息类型: 'success'|'error'|'warning'|'info'
         * @param {string} message - 显示的消息内容
         * @param {Function|number} [callbackOrDuration] - 回调函数或自动关闭时间（毫秒）
         */
        function showMessageModal(type, message, callbackOrDuration) {
            // 判断第三个参数是回调函数还是持续时间
            let callback = null;
            let duration = 2000; // 默认2秒

            if (typeof callbackOrDuration === 'function') {
                callback = callbackOrDuration;
                duration = 0; // 有回调函数时不自动关闭
            } else if (typeof callbackOrDuration === 'number') {
                duration = callbackOrDuration;
            }
            // 创建模态框覆盖层
            const modalOverlay = document.createElement('div');
            modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            modalOverlay.style.backdropFilter = 'blur(4px)';

            // 设置图标和颜色
            let icon, bgColor, textColor;
            switch (type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle text-5xl"></i>';
                    bgColor = 'bg-green-500';
                    textColor = 'text-green-500';
                    break;
                case 'error':
                    icon = '<i class="fas fa-times-circle text-5xl"></i>';
                    bgColor = 'bg-red-500';
                    textColor = 'text-red-500';
                    break;
                case 'warning':
                    icon = '<i class="fas fa-exclamation-triangle text-5xl"></i>';
                    bgColor = 'bg-yellow-500';
                    textColor = 'text-yellow-500';
                    break;
                case 'info':
                default:
                    icon = '<i class="fas fa-info-circle text-5xl"></i>';
                    bgColor = 'bg-blue-500';
                    textColor = 'text-blue-500';
                    break;
            }

            // 创建模态框内容
            modalOverlay.innerHTML = `
                <div class="bg-white rounded-xl p-6 max-w-md text-center shadow-xl animate-fadeIn">
                    <div class="mb-4 ${textColor}">
                        ${icon}
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">${type === 'success' ? '操作成功' : '操作失败'}</h3>
                    <p class="text-gray-600 mb-4">${message}</p>
                    <button class="px-4 py-2 ${bgColor} text-white rounded-lg hover:opacity-90 transition-colors close-modal-btn">
                        关闭
                    </button>
                </div>
            `;

            // 添加到body
            document.body.appendChild(modalOverlay);

            // 关闭模态框的函数
            function closeModal() {
                if (document.body.contains(modalOverlay)) {
                    document.body.removeChild(modalOverlay);
                    // 执行回调函数
                    if (callback) {
                        callback();
                    }
                }
            }

            // 添加关闭按钮事件
            const closeBtn = modalOverlay.querySelector('.close-modal-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', closeModal);
            }

            // 点击背景关闭
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    closeModal();
                }
            });

            // 自动关闭
            if (duration > 0) {
                setTimeout(() => {
                    closeModal();
                }, duration);
            }
        }

        // 修改提取账号的AJAX调用
        function extractAccount(accountId, accountType) {
            // 显示加载提示
            showLoadingIndicator('正在提取账号...');

            $.ajax({
                url: "/user/index/extractRegularMemberVipAccount",
                type: "POST",
                dataType: "json",
                data: {
                    id: userId,
                    username: username,
                    account_id: accountId,
                    account_type: accountType
                },
                success: function(res) {
                    hideLoadingIndicator();

                    if (res.code === 0 || res.code === 200) {
                        showMessageModal('success', res.msg || '账号提取成功', () => {
                            closeAvailableAccountsModal();
                            location.reload();
                        });
                    } else {
                        showMessageModal('error', res.msg || '账号提取失败');
                    }
                },
                error: function(xhr, status, error) {
                    // 使用定义好的错误处理函数
                    handleAjaxError(xhr, status, error);
                }
            });
        }

        /**
         * 显示加载指示器
         * @param {string} message - 加载提示信息
         */
        function showLoadingIndicator(message = '加载中...') {
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center loading-indicator';
            loadingOverlay.style.backdropFilter = 'blur(4px)';

            loadingOverlay.innerHTML = `
                <div class="bg-white rounded-xl p-6 max-w-md text-center shadow-xl animate-fadeIn">
                    <div class="mb-4 text-blue-500">
                        <i class="fas fa-spinner fa-spin text-4xl"></i>
                    </div>
                    <p class="text-gray-600">${message}</p>
                </div>
            `;

            document.body.appendChild(loadingOverlay);
        }
    </script>

    <!-- 引用页脚 -->
    {include file='../app/user/view/ticket/footer.html'}

    <!-- 添加复制功能的JavaScript -->
    <script>
    function copyText(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                showMessageModal('success', '复制成功');
            }).catch((err) => {
                showMessageModal('error', '复制失败，请手动复制');
                console.error('复制失败:', err);
            });
        } else {
            // 兼容不支持clipboard API的浏览器
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.opacity = 0;
            document.body.appendChild(textarea);
            textarea.select();

            try {
                document.execCommand('copy');
                showMessageModal('success', '复制成功');
            } catch (err) {
                showMessageModal('error', '复制失败，请手动复制');
                console.error('复制失败:', err);
            } finally {
                document.body.removeChild(textarea);
            }
        }
    }
    </script>
</body>
</html>