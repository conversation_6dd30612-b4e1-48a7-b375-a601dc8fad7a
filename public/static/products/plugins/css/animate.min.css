@charset "UTF-8";/*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license

Copyright (c) 2013 Daniel Eden

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/.animated{-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:both;animation-fill-mode:both}.animated.infinite{-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite}.animated.hinge{-webkit-animation-duration:2s;animation-duration:2s}@-webkit-keyframes bounce{0%,100%,20%,50%,80%{-webkit-transform:translateY(0);transform:translateY(0)}40%{-webkit-transform:translateY(-30px);transform:translateY(-30px)}60%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}}@keyframes bounce{0%,100%,20%,50%,80%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}40%{-webkit-transform:translateY(-30px);-ms-transform:translateY(-30px);transform:translateY(-30px)}60%{-webkit-transform:translateY(-15px);-ms-transform:translateY(-15px);transform:translateY(-15px)}}.bounce{-webkit-animation-name:bounce;animation-name:bounce}@-webkit-keyframes flash{0%,100%,50%{opacity:1}25%,75%{opacity:0}}@keyframes flash{0%,100%,50%{opacity:1}25%,75%{opacity:0}}.flash{-webkit-animation-name:flash;animation-name:flash}@-webkit-keyframes pulse{0%{-webkit-transform:scale(1);transform:scale(1)}50%{-webkit-transform:scale(1.1);transform:scale(1.1)}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes pulse{0%{-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}50%{-webkit-transform:scale(1.1);-ms-transform:scale(1.1);transform:scale(1.1)}100%{-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}}.pulse{-webkit-animation-name:pulse;animation-name:pulse}@-webkit-keyframes rubberBand{0%{-webkit-transform:scale(1);transform:scale(1)}30%{-webkit-transform:scaleX(1.25) scaleY(0.75);transform:scaleX(1.25) scaleY(0.75)}40%{-webkit-transform:scaleX(0.75) scaleY(1.25);transform:scaleX(0.75) scaleY(1.25)}60%{-webkit-transform:scaleX(1.15) scaleY(0.85);transform:scaleX(1.15) scaleY(0.85)}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes rubberBand{0%{-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}30%{-webkit-transform:scaleX(1.25) scaleY(0.75);-ms-transform:scaleX(1.25) scaleY(0.75);transform:scaleX(1.25) scaleY(0.75)}40%{-webkit-transform:scaleX(0.75) scaleY(1.25);-ms-transform:scaleX(0.75) scaleY(1.25);transform:scaleX(0.75) scaleY(1.25)}60%{-webkit-transform:scaleX(1.15) scaleY(0.85);-ms-transform:scaleX(1.15) scaleY(0.85);transform:scaleX(1.15) scaleY(0.85)}100%{-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}}.rubberBand{-webkit-animation-name:rubberBand;animation-name:rubberBand}@-webkit-keyframes shake{0%,100%{-webkit-transform:translateX(0);transform:translateX(0)}10%,30%,50%,70%,90%{-webkit-transform:translateX(-10px);transform:translateX(-10px)}20%,40%,60%,80%{-webkit-transform:translateX(10px);transform:translateX(10px)}}@keyframes shake{0%,100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}10%,30%,50%,70%,90%{-webkit-transform:translateX(-10px);-ms-transform:translateX(-10px);transform:translateX(-10px)}20%,40%,60%,80%{-webkit-transform:translateX(10px);-ms-transform:translateX(10px);transform:translateX(10px)}}.shake{-webkit-animation-name:shake;animation-name:shake}@-webkit-keyframes swing{20%{-webkit-transform:rotate(15deg);transform:rotate(15deg)}40%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}60%{-webkit-transform:rotate(5deg);transform:rotate(5deg)}80%{-webkit-transform:rotate(-5deg);transform:rotate(-5deg)}100%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}}@keyframes swing{20%{-webkit-transform:rotate(15deg);-ms-transform:rotate(15deg);transform:rotate(15deg)}40%{-webkit-transform:rotate(-10deg);-ms-transform:rotate(-10deg);transform:rotate(-10deg)}60%{-webkit-transform:rotate(5deg);-ms-transform:rotate(5deg);transform:rotate(5deg)}80%{-webkit-transform:rotate(-5deg);-ms-transform:rotate(-5deg);transform:rotate(-5deg)}100%{-webkit-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg)}}.swing{-webkit-transform-origin:top center;-ms-transform-origin:top center;transform-origin:top center;-webkit-animation-name:swing;animation-name:swing}@-webkit-keyframes tada{0%{-webkit-transform:scale(1);transform:scale(1)}10%,20%{-webkit-transform:scale(0.9) rotate(-3deg);transform:scale(0.9) rotate(-3deg)}30%,50%,70%,90%{-webkit-transform:scale(1.1) rotate(3deg);transform:scale(1.1) rotate(3deg)}40%,60%,80%{-webkit-transform:scale(1.1) rotate(-3deg);transform:scale(1.1) rotate(-3deg)}100%{-webkit-transform:scale(1) rotate(0);transform:scale(1) rotate(0)}}@keyframes tada{0%{-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}10%,20%{-webkit-transform:scale(0.9) rotate(-3deg);-ms-transform:scale(0.9) rotate(-3deg);transform:scale(0.9) rotate(-3deg)}30%,50%,70%,90%{-webkit-transform:scale(1.1) rotate(3deg);-ms-transform:scale(1.1) rotate(3deg);transform:scale(1.1) rotate(3deg)}40%,60%,80%{-webkit-transform:scale(1.1) rotate(-3deg);-ms-transform:scale(1.1) rotate(-3deg);transform:scale(1.1) rotate(-3deg)}100%{-webkit-transform:scale(1) rotate(0);-ms-transform:scale(1) rotate(0);transform:scale(1) rotate(0)}}.tada{-webkit-animation-name:tada;animation-name:tada}@-webkit-keyframes wobble{0%{-webkit-transform:translateX(0%);transform:translateX(0%)}15%{-webkit-transform:translateX(-25%) rotate(-5deg);transform:translateX(-25%) rotate(-5deg)}30%{-webkit-transform:translateX(20%) rotate(3deg);transform:translateX(20%) rotate(3deg)}45%{-webkit-transform:translateX(-15%) rotate(-3deg);transform:translateX(-15%) rotate(-3deg)}60%{-webkit-transform:translateX(10%) rotate(2deg);transform:translateX(10%) rotate(2deg)}75%{-webkit-transform:translateX(-5%) rotate(-1deg);transform:translateX(-5%) rotate(-1deg)}100%{-webkit-transform:translateX(0%);transform:translateX(0%)}}@keyframes wobble{0%{-webkit-transform:translateX(0%);-ms-transform:translateX(0%);transform:translateX(0%)}15%{-webkit-transform:translateX(-25%) rotate(-5deg);-ms-transform:translateX(-25%) rotate(-5deg);transform:translateX(-25%) rotate(-5deg)}30%{-webkit-transform:translateX(20%) rotate(3deg);-ms-transform:translateX(20%) rotate(3deg);transform:translateX(20%) rotate(3deg)}45%{-webkit-transform:translateX(-15%) rotate(-3deg);-ms-transform:translateX(-15%) rotate(-3deg);transform:translateX(-15%) rotate(-3deg)}60%{-webkit-transform:translateX(10%) rotate(2deg);-ms-transform:translateX(10%) rotate(2deg);transform:translateX(10%) rotate(2deg)}75%{-webkit-transform:translateX(-5%) rotate(-1deg);-ms-transform:translateX(-5%) rotate(-1deg);transform:translateX(-5%) rotate(-1deg)}100%{-webkit-transform:translateX(0%);-ms-transform:translateX(0%);transform:translateX(0%)}}.wobble{-webkit-animation-name:wobble;animation-name:wobble}@-webkit-keyframes bounceIn{0%{opacity:0;-webkit-transform:scale(.3);transform:scale(.3)}50%{opacity:1;-webkit-transform:scale(1.05);transform:scale(1.05)}70%{-webkit-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}@keyframes bounceIn{0%{opacity:0;-webkit-transform:scale(.3);-ms-transform:scale(.3);transform:scale(.3)}50%{opacity:1;-webkit-transform:scale(1.05);-ms-transform:scale(1.05);transform:scale(1.05)}70%{-webkit-transform:scale(.9);-ms-transform:scale(.9);transform:scale(.9)}100%{opacity:1;-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}}.bounceIn{-webkit-animation-name:bounceIn;animation-name:bounceIn}@-webkit-keyframes bounceInDown{0%{opacity:0;-webkit-transform:translateY(-2000px);transform:translateY(-2000px)}60%{opacity:1;-webkit-transform:translateY(30px);transform:translateY(30px)}80%{-webkit-transform:translateY(-10px);transform:translateY(-10px)}100%{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes bounceInDown{0%{opacity:0;-webkit-transform:translateY(-2000px);-ms-transform:translateY(-2000px);transform:translateY(-2000px)}60%{opacity:1;-webkit-transform:translateY(30px);-ms-transform:translateY(30px);transform:translateY(30px)}80%{-webkit-transform:translateY(-10px);-ms-transform:translateY(-10px);transform:translateY(-10px)}100%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.bounceInDown{-webkit-animation-name:bounceInDown;animation-name:bounceInDown}@-webkit-keyframes bounceInLeft{0%{opacity:0;-webkit-transform:translateX(-2000px);transform:translateX(-2000px)}60%{opacity:1;-webkit-transform:translateX(30px);transform:translateX(30px)}80%{-webkit-transform:translateX(-10px);transform:translateX(-10px)}100%{-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes bounceInLeft{0%{opacity:0;-webkit-transform:translateX(-2000px);-ms-transform:translateX(-2000px);transform:translateX(-2000px)}60%{opacity:1;-webkit-transform:translateX(30px);-ms-transform:translateX(30px);transform:translateX(30px)}80%{-webkit-transform:translateX(-10px);-ms-transform:translateX(-10px);transform:translateX(-10px)}100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.bounceInLeft{-webkit-animation-name:bounceInLeft;animation-name:bounceInLeft}@-webkit-keyframes bounceInRight{0%{opacity:0;-webkit-transform:translateX(2000px);transform:translateX(2000px)}60%{opacity:1;-webkit-transform:translateX(-30px);transform:translateX(-30px)}80%{-webkit-transform:translateX(10px);transform:translateX(10px)}100%{-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes bounceInRight{0%{opacity:0;-webkit-transform:translateX(2000px);-ms-transform:translateX(2000px);transform:translateX(2000px)}60%{opacity:1;-webkit-transform:translateX(-30px);-ms-transform:translateX(-30px);transform:translateX(-30px)}80%{-webkit-transform:translateX(10px);-ms-transform:translateX(10px);transform:translateX(10px)}100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.bounceInRight{-webkit-animation-name:bounceInRight;animation-name:bounceInRight}@-webkit-keyframes bounceInUp{0%{opacity:0;-webkit-transform:translateY(2000px);transform:translateY(2000px)}60%{opacity:1;-webkit-transform:translateY(-30px);transform:translateY(-30px)}80%{-webkit-transform:translateY(10px);transform:translateY(10px)}100%{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes bounceInUp{0%{opacity:0;-webkit-transform:translateY(2000px);-ms-transform:translateY(2000px);transform:translateY(2000px)}60%{opacity:1;-webkit-transform:translateY(-30px);-ms-transform:translateY(-30px);transform:translateY(-30px)}80%{-webkit-transform:translateY(10px);-ms-transform:translateY(10px);transform:translateY(10px)}100%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.bounceInUp{-webkit-animation-name:bounceInUp;animation-name:bounceInUp}@-webkit-keyframes bounceOut{0%{-webkit-transform:scale(1);transform:scale(1)}25%{-webkit-transform:scale(.95);transform:scale(.95)}50%{opacity:1;-webkit-transform:scale(1.1);transform:scale(1.1)}100%{opacity:0;-webkit-transform:scale(.3);transform:scale(.3)}}@keyframes bounceOut{0%{-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}25%{-webkit-transform:scale(.95);-ms-transform:scale(.95);transform:scale(.95)}50%{opacity:1;-webkit-transform:scale(1.1);-ms-transform:scale(1.1);transform:scale(1.1)}100%{opacity:0;-webkit-transform:scale(.3);-ms-transform:scale(.3);transform:scale(.3)}}.bounceOut{-webkit-animation-name:bounceOut;animation-name:bounceOut}@-webkit-keyframes bounceOutDown{0%{-webkit-transform:translateY(0);transform:translateY(0)}20%{opacity:1;-webkit-transform:translateY(-20px);transform:translateY(-20px)}100%{opacity:0;-webkit-transform:translateY(2000px);transform:translateY(2000px)}}@keyframes bounceOutDown{0%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}20%{opacity:1;-webkit-transform:translateY(-20px);-ms-transform:translateY(-20px);transform:translateY(-20px)}100%{opacity:0;-webkit-transform:translateY(2000px);-ms-transform:translateY(2000px);transform:translateY(2000px)}}.bounceOutDown{-webkit-animation-name:bounceOutDown;animation-name:bounceOutDown}@-webkit-keyframes bounceOutLeft{0%{-webkit-transform:translateX(0);transform:translateX(0)}20%{opacity:1;-webkit-transform:translateX(20px);transform:translateX(20px)}100%{opacity:0;-webkit-transform:translateX(-2000px);transform:translateX(-2000px)}}@keyframes bounceOutLeft{0%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}20%{opacity:1;-webkit-transform:translateX(20px);-ms-transform:translateX(20px);transform:translateX(20px)}100%{opacity:0;-webkit-transform:translateX(-2000px);-ms-transform:translateX(-2000px);transform:translateX(-2000px)}}.bounceOutLeft{-webkit-animation-name:bounceOutLeft;animation-name:bounceOutLeft}@-webkit-keyframes bounceOutRight{0%{-webkit-transform:translateX(0);transform:translateX(0)}20%{opacity:1;-webkit-transform:translateX(-20px);transform:translateX(-20px)}100%{opacity:0;-webkit-transform:translateX(2000px);transform:translateX(2000px)}}@keyframes bounceOutRight{0%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}20%{opacity:1;-webkit-transform:translateX(-20px);-ms-transform:translateX(-20px);transform:translateX(-20px)}100%{opacity:0;-webkit-transform:translateX(2000px);-ms-transform:translateX(2000px);transform:translateX(2000px)}}.bounceOutRight{-webkit-animation-name:bounceOutRight;animation-name:bounceOutRight}@-webkit-keyframes bounceOutUp{0%{-webkit-transform:translateY(0);transform:translateY(0)}20%{opacity:1;-webkit-transform:translateY(20px);transform:translateY(20px)}100%{opacity:0;-webkit-transform:translateY(-2000px);transform:translateY(-2000px)}}@keyframes bounceOutUp{0%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}20%{opacity:1;-webkit-transform:translateY(20px);-ms-transform:translateY(20px);transform:translateY(20px)}100%{opacity:0;-webkit-transform:translateY(-2000px);-ms-transform:translateY(-2000px);transform:translateY(-2000px)}}.bounceOutUp{-webkit-animation-name:bounceOutUp;animation-name:bounceOutUp}@-webkit-keyframes fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}.fadeIn{-webkit-animation-name:fadeIn;animation-name:fadeIn}@-webkit-keyframes fadeInDown{0%{opacity:0;-webkit-transform:translateY(-20px);transform:translateY(-20px)}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fadeInDown{0%{opacity:0;-webkit-transform:translateY(-20px);-ms-transform:translateY(-20px);transform:translateY(-20px)}100%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.fadeInDown{-webkit-animation-name:fadeInDown;animation-name:fadeInDown}@-webkit-keyframes fadeInDownBig{0%{opacity:0;-webkit-transform:translateY(-2000px);transform:translateY(-2000px)}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fadeInDownBig{0%{opacity:0;-webkit-transform:translateY(-2000px);-ms-transform:translateY(-2000px);transform:translateY(-2000px)}100%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.fadeInDownBig{-webkit-animation-name:fadeInDownBig;animation-name:fadeInDownBig}@-webkit-keyframes fadeInLeft{0%{opacity:0;-webkit-transform:translateX(-20px);transform:translateX(-20px)}100%{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes fadeInLeft{0%{opacity:0;-webkit-transform:translateX(-20px);-ms-transform:translateX(-20px);transform:translateX(-20px)}100%{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.fadeInLeft{-webkit-animation-name:fadeInLeft;animation-name:fadeInLeft}@-webkit-keyframes fadeInLeftBig{0%{opacity:0;-webkit-transform:translateX(-2000px);transform:translateX(-2000px)}100%{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes fadeInLeftBig{0%{opacity:0;-webkit-transform:translateX(-2000px);-ms-transform:translateX(-2000px);transform:translateX(-2000px)}100%{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.fadeInLeftBig{-webkit-animation-name:fadeInLeftBig;animation-name:fadeInLeftBig}@-webkit-keyframes fadeInRight{0%{opacity:0;-webkit-transform:translateX(20px);transform:translateX(20px)}100%{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes fadeInRight{0%{opacity:0;-webkit-transform:translateX(20px);-ms-transform:translateX(20px);transform:translateX(20px)}100%{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.fadeInRight{-webkit-animation-name:fadeInRight;animation-name:fadeInRight}@-webkit-keyframes fadeInRightBig{0%{opacity:0;-webkit-transform:translateX(2000px);transform:translateX(2000px)}100%{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes fadeInRightBig{0%{opacity:0;-webkit-transform:translateX(2000px);-ms-transform:translateX(2000px);transform:translateX(2000px)}100%{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.fadeInRightBig{-webkit-animation-name:fadeInRightBig;animation-name:fadeInRightBig}@-webkit-keyframes fadeInUp{0%{opacity:0;-webkit-transform:translateY(20px);transform:translateY(20px)}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fadeInUp{0%{opacity:0;-webkit-transform:translateY(20px);-ms-transform:translateY(20px);transform:translateY(20px)}100%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}@-webkit-keyframes fadeInUpBig{0%{opacity:0;-webkit-transform:translateY(2000px);transform:translateY(2000px)}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fadeInUpBig{0%{opacity:0;-webkit-transform:translateY(2000px);-ms-transform:translateY(2000px);transform:translateY(2000px)}100%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.fadeInUpBig{-webkit-animation-name:fadeInUpBig;animation-name:fadeInUpBig}@-webkit-keyframes fadeOut{0%{opacity:1}100%{opacity:0}}@keyframes fadeOut{0%{opacity:1}100%{opacity:0}}.fadeOut{-webkit-animation-name:fadeOut;animation-name:fadeOut}@-webkit-keyframes fadeOutDown{0%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(20px);transform:translateY(20px)}}@keyframes fadeOutDown{0%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(20px);-ms-transform:translateY(20px);transform:translateY(20px)}}.fadeOutDown{-webkit-animation-name:fadeOutDown;animation-name:fadeOutDown}@-webkit-keyframes fadeOutDownBig{0%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(2000px);transform:translateY(2000px)}}@keyframes fadeOutDownBig{0%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(2000px);-ms-transform:translateY(2000px);transform:translateY(2000px)}}.fadeOutDownBig{-webkit-animation-name:fadeOutDownBig;animation-name:fadeOutDownBig}@-webkit-keyframes fadeOutLeft{0%{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(-20px);transform:translateX(-20px)}}@keyframes fadeOutLeft{0%{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(-20px);-ms-transform:translateX(-20px);transform:translateX(-20px)}}.fadeOutLeft{-webkit-animation-name:fadeOutLeft;animation-name:fadeOutLeft}@-webkit-keyframes fadeOutLeftBig{0%{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(-2000px);transform:translateX(-2000px)}}@keyframes fadeOutLeftBig{0%{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(-2000px);-ms-transform:translateX(-2000px);transform:translateX(-2000px)}}.fadeOutLeftBig{-webkit-animation-name:fadeOutLeftBig;animation-name:fadeOutLeftBig}@-webkit-keyframes fadeOutRight{0%{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(20px);transform:translateX(20px)}}@keyframes fadeOutRight{0%{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(20px);-ms-transform:translateX(20px);transform:translateX(20px)}}.fadeOutRight{-webkit-animation-name:fadeOutRight;animation-name:fadeOutRight}@-webkit-keyframes fadeOutRightBig{0%{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(2000px);transform:translateX(2000px)}}@keyframes fadeOutRightBig{0%{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(2000px);-ms-transform:translateX(2000px);transform:translateX(2000px)}}.fadeOutRightBig{-webkit-animation-name:fadeOutRightBig;animation-name:fadeOutRightBig}@-webkit-keyframes fadeOutUp{0%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(-20px);transform:translateY(-20px)}}@keyframes fadeOutUp{0%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(-20px);-ms-transform:translateY(-20px);transform:translateY(-20px)}}.fadeOutUp{-webkit-animation-name:fadeOutUp;animation-name:fadeOutUp}@-webkit-keyframes fadeOutUpBig{0%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(-2000px);transform:translateY(-2000px)}}@keyframes fadeOutUpBig{0%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(-2000px);-ms-transform:translateY(-2000px);transform:translateY(-2000px)}}.fadeOutUpBig{-webkit-animation-name:fadeOutUpBig;animation-name:fadeOutUpBig}@-webkit-keyframes flip{0%{-webkit-transform:perspective(400px) translateZ(0) rotateY(0) scale(1);transform:perspective(400px) translateZ(0) rotateY(0) scale(1);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}40%{-webkit-transform:perspective(400px) translateZ(150px) rotateY(170deg) scale(1);transform:perspective(400px) translateZ(150px) rotateY(170deg) scale(1);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}50%{-webkit-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}80%{-webkit-transform:perspective(400px) translateZ(0) rotateY(360deg) scale(.95);transform:perspective(400px) translateZ(0) rotateY(360deg) scale(.95);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}100%{-webkit-transform:perspective(400px) translateZ(0) rotateY(360deg) scale(1);transform:perspective(400px) translateZ(0) rotateY(360deg) scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}}@keyframes flip{0%{-webkit-transform:perspective(400px) translateZ(0) rotateY(0) scale(1);-ms-transform:perspective(400px) translateZ(0) rotateY(0) scale(1);transform:perspective(400px) translateZ(0) rotateY(0) scale(1);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}40%{-webkit-transform:perspective(400px) translateZ(150px) rotateY(170deg) scale(1);-ms-transform:perspective(400px) translateZ(150px) rotateY(170deg) scale(1);transform:perspective(400px) translateZ(150px) rotateY(170deg) scale(1);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}50%{-webkit-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);-ms-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}80%{-webkit-transform:perspective(400px) translateZ(0) rotateY(360deg) scale(.95);-ms-transform:perspective(400px) translateZ(0) rotateY(360deg) scale(.95);transform:perspective(400px) translateZ(0) rotateY(360deg) scale(.95);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}100%{-webkit-transform:perspective(400px) translateZ(0) rotateY(360deg) scale(1);-ms-transform:perspective(400px) translateZ(0) rotateY(360deg) scale(1);transform:perspective(400px) translateZ(0) rotateY(360deg) scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}}.animated.flip{-webkit-backface-visibility:visible;-ms-backface-visibility:visible;backface-visibility:visible;-webkit-animation-name:flip;animation-name:flip}@-webkit-keyframes flipInX{0%{-webkit-transform:perspective(400px) rotateX(90deg);transform:perspective(400px) rotateX(90deg);opacity:0}40%{-webkit-transform:perspective(400px) rotateX(-10deg);transform:perspective(400px) rotateX(-10deg)}70%{-webkit-transform:perspective(400px) rotateX(10deg);transform:perspective(400px) rotateX(10deg)}100%{-webkit-transform:perspective(400px) rotateX(0deg);transform:perspective(400px) rotateX(0deg);opacity:1}}@keyframes flipInX{0%{-webkit-transform:perspective(400px) rotateX(90deg);-ms-transform:perspective(400px) rotateX(90deg);transform:perspective(400px) rotateX(90deg);opacity:0}40%{-webkit-transform:perspective(400px) rotateX(-10deg);-ms-transform:perspective(400px) rotateX(-10deg);transform:perspective(400px) rotateX(-10deg)}70%{-webkit-transform:perspective(400px) rotateX(10deg);-ms-transform:perspective(400px) rotateX(10deg);transform:perspective(400px) rotateX(10deg)}100%{-webkit-transform:perspective(400px) rotateX(0deg);-ms-transform:perspective(400px) rotateX(0deg);transform:perspective(400px) rotateX(0deg);opacity:1}}.flipInX{-webkit-backface-visibility:visible!important;-ms-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipInX;animation-name:flipInX}@-webkit-keyframes flipInY{0%{-webkit-transform:perspective(400px) rotateY(90deg);transform:perspective(400px) rotateY(90deg);opacity:0}40%{-webkit-transform:perspective(400px) rotateY(-10deg);transform:perspective(400px) rotateY(-10deg)}70%{-webkit-transform:perspective(400px) rotateY(10deg);transform:perspective(400px) rotateY(10deg)}100%{-webkit-transform:perspective(400px) rotateY(0deg);transform:perspective(400px) rotateY(0deg);opacity:1}}@keyframes flipInY{0%{-webkit-transform:perspective(400px) rotateY(90deg);-ms-transform:perspective(400px) rotateY(90deg);transform:perspective(400px) rotateY(90deg);opacity:0}40%{-webkit-transform:perspective(400px) rotateY(-10deg);-ms-transform:perspective(400px) rotateY(-10deg);transform:perspective(400px) rotateY(-10deg)}70%{-webkit-transform:perspective(400px) rotateY(10deg);-ms-transform:perspective(400px) rotateY(10deg);transform:perspective(400px) rotateY(10deg)}100%{-webkit-transform:perspective(400px) rotateY(0deg);-ms-transform:perspective(400px) rotateY(0deg);transform:perspective(400px) rotateY(0deg);opacity:1}}.flipInY{-webkit-backface-visibility:visible!important;-ms-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipInY;animation-name:flipInY}@-webkit-keyframes flipOutX{0%{-webkit-transform:perspective(400px) rotateX(0deg);transform:perspective(400px) rotateX(0deg);opacity:1}100%{-webkit-transform:perspective(400px) rotateX(90deg);transform:perspective(400px) rotateX(90deg);opacity:0}}@keyframes flipOutX{0%{-webkit-transform:perspective(400px) rotateX(0deg);-ms-transform:perspective(400px) rotateX(0deg);transform:perspective(400px) rotateX(0deg);opacity:1}100%{-webkit-transform:perspective(400px) rotateX(90deg);-ms-transform:perspective(400px) rotateX(90deg);transform:perspective(400px) rotateX(90deg);opacity:0}}.flipOutX{-webkit-animation-name:flipOutX;animation-name:flipOutX;-webkit-backface-visibility:visible!important;-ms-backface-visibility:visible!important;backface-visibility:visible!important}@-webkit-keyframes flipOutY{0%{-webkit-transform:perspective(400px) rotateY(0deg);transform:perspective(400px) rotateY(0deg);opacity:1}100%{-webkit-transform:perspective(400px) rotateY(90deg);transform:perspective(400px) rotateY(90deg);opacity:0}}@keyframes flipOutY{0%{-webkit-transform:perspective(400px) rotateY(0deg);-ms-transform:perspective(400px) rotateY(0deg);transform:perspective(400px) rotateY(0deg);opacity:1}100%{-webkit-transform:perspective(400px) rotateY(90deg);-ms-transform:perspective(400px) rotateY(90deg);transform:perspective(400px) rotateY(90deg);opacity:0}}.flipOutY{-webkit-backface-visibility:visible!important;-ms-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipOutY;animation-name:flipOutY}@-webkit-keyframes lightSpeedIn{0%{-webkit-transform:translateX(100%) skewX(-30deg);transform:translateX(100%) skewX(-30deg);opacity:0}60%{-webkit-transform:translateX(-20%) skewX(30deg);transform:translateX(-20%) skewX(30deg);opacity:1}80%{-webkit-transform:translateX(0%) skewX(-15deg);transform:translateX(0%) skewX(-15deg);opacity:1}100%{-webkit-transform:translateX(0%) skewX(0deg);transform:translateX(0%) skewX(0deg);opacity:1}}@keyframes lightSpeedIn{0%{-webkit-transform:translateX(100%) skewX(-30deg);-ms-transform:translateX(100%) skewX(-30deg);transform:translateX(100%) skewX(-30deg);opacity:0}60%{-webkit-transform:translateX(-20%) skewX(30deg);-ms-transform:translateX(-20%) skewX(30deg);transform:translateX(-20%) skewX(30deg);opacity:1}80%{-webkit-transform:translateX(0%) skewX(-15deg);-ms-transform:translateX(0%) skewX(-15deg);transform:translateX(0%) skewX(-15deg);opacity:1}100%{-webkit-transform:translateX(0%) skewX(0deg);-ms-transform:translateX(0%) skewX(0deg);transform:translateX(0%) skewX(0deg);opacity:1}}.lightSpeedIn{-webkit-animation-name:lightSpeedIn;animation-name:lightSpeedIn;-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}@-webkit-keyframes lightSpeedOut{0%{-webkit-transform:translateX(0%) skewX(0deg);transform:translateX(0%) skewX(0deg);opacity:1}100%{-webkit-transform:translateX(100%) skewX(-30deg);transform:translateX(100%) skewX(-30deg);opacity:0}}@keyframes lightSpeedOut{0%{-webkit-transform:translateX(0%) skewX(0deg);-ms-transform:translateX(0%) skewX(0deg);transform:translateX(0%) skewX(0deg);opacity:1}100%{-webkit-transform:translateX(100%) skewX(-30deg);-ms-transform:translateX(100%) skewX(-30deg);transform:translateX(100%) skewX(-30deg);opacity:0}}.lightSpeedOut{-webkit-animation-name:lightSpeedOut;animation-name:lightSpeedOut;-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}@-webkit-keyframes rotateIn{0%{-webkit-transform-origin:center center;transform-origin:center center;-webkit-transform:rotate(-200deg);transform:rotate(-200deg);opacity:0}100%{-webkit-transform-origin:center center;transform-origin:center center;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}}@keyframes rotateIn{0%{-webkit-transform-origin:center center;-ms-transform-origin:center center;transform-origin:center center;-webkit-transform:rotate(-200deg);-ms-transform:rotate(-200deg);transform:rotate(-200deg);opacity:0}100%{-webkit-transform-origin:center center;-ms-transform-origin:center center;transform-origin:center center;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}}.rotateIn{-webkit-animation-name:rotateIn;animation-name:rotateIn}@-webkit-keyframes rotateInDownLeft{0%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}100%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}}@keyframes rotateInDownLeft{0%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(-90deg);-ms-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}100%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}}.rotateInDownLeft{-webkit-animation-name:rotateInDownLeft;animation-name:rotateInDownLeft}@-webkit-keyframes rotateInDownRight{0%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:0}100%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}}@keyframes rotateInDownRight{0%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);opacity:0}100%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}}.rotateInDownRight{-webkit-animation-name:rotateInDownRight;animation-name:rotateInDownRight}@-webkit-keyframes rotateInUpLeft{0%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:0}100%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}}@keyframes rotateInUpLeft{0%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);opacity:0}100%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}}.rotateInUpLeft{-webkit-animation-name:rotateInUpLeft;animation-name:rotateInUpLeft}@-webkit-keyframes rotateInUpRight{0%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}100%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}}@keyframes rotateInUpRight{0%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(-90deg);-ms-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}100%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}}.rotateInUpRight{-webkit-animation-name:rotateInUpRight;animation-name:rotateInUpRight}@-webkit-keyframes rotateOut{0%{-webkit-transform-origin:center center;transform-origin:center center;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:center center;transform-origin:center center;-webkit-transform:rotate(200deg);transform:rotate(200deg);opacity:0}}@keyframes rotateOut{0%{-webkit-transform-origin:center center;-ms-transform-origin:center center;transform-origin:center center;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:center center;-ms-transform-origin:center center;transform-origin:center center;-webkit-transform:rotate(200deg);-ms-transform:rotate(200deg);transform:rotate(200deg);opacity:0}}.rotateOut{-webkit-animation-name:rotateOut;animation-name:rotateOut}@-webkit-keyframes rotateOutDownLeft{0%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:0}}@keyframes rotateOutDownLeft{0%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);opacity:0}}.rotateOutDownLeft{-webkit-animation-name:rotateOutDownLeft;animation-name:rotateOutDownLeft}@-webkit-keyframes rotateOutDownRight{0%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}}@keyframes rotateOutDownRight{0%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(-90deg);-ms-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}}.rotateOutDownRight{-webkit-animation-name:rotateOutDownRight;animation-name:rotateOutDownRight}@-webkit-keyframes rotateOutUpLeft{0%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}}@keyframes rotateOutUpLeft{0%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:left bottom;-ms-transform-origin:left bottom;transform-origin:left bottom;-webkit-transform:rotate(-90deg);-ms-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}}.rotateOutUpLeft{-webkit-animation-name:rotateOutUpLeft;animation-name:rotateOutUpLeft}@-webkit-keyframes rotateOutUpRight{0%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:0}}@keyframes rotateOutUpRight{0%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);opacity:1}100%{-webkit-transform-origin:right bottom;-ms-transform-origin:right bottom;transform-origin:right bottom;-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);opacity:0}}.rotateOutUpRight{-webkit-animation-name:rotateOutUpRight;animation-name:rotateOutUpRight}@-webkit-keyframes slideInDown{0%{opacity:0;-webkit-transform:translateY(-2000px);transform:translateY(-2000px)}100%{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideInDown{0%{opacity:0;-webkit-transform:translateY(-2000px);-ms-transform:translateY(-2000px);transform:translateY(-2000px)}100%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.slideInDown{-webkit-animation-name:slideInDown;animation-name:slideInDown}@-webkit-keyframes slideInLeft{0%{opacity:0;-webkit-transform:translateX(-2000px);transform:translateX(-2000px)}100%{-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes slideInLeft{0%{opacity:0;-webkit-transform:translateX(-2000px);-ms-transform:translateX(-2000px);transform:translateX(-2000px)}100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.slideInLeft{-webkit-animation-name:slideInLeft;animation-name:slideInLeft}@-webkit-keyframes slideInRight{0%{opacity:0;-webkit-transform:translateX(2000px);transform:translateX(2000px)}100%{-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes slideInRight{0%{opacity:0;-webkit-transform:translateX(2000px);-ms-transform:translateX(2000px);transform:translateX(2000px)}100%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}}.slideInRight{-webkit-animation-name:slideInRight;animation-name:slideInRight}@-webkit-keyframes slideOutLeft{0%{-webkit-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(-2000px);transform:translateX(-2000px)}}@keyframes slideOutLeft{0%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(-2000px);-ms-transform:translateX(-2000px);transform:translateX(-2000px)}}.slideOutLeft{-webkit-animation-name:slideOutLeft;animation-name:slideOutLeft}@-webkit-keyframes slideOutRight{0%{-webkit-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(2000px);transform:translateX(2000px)}}@keyframes slideOutRight{0%{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{opacity:0;-webkit-transform:translateX(2000px);-ms-transform:translateX(2000px);transform:translateX(2000px)}}.slideOutRight{-webkit-animation-name:slideOutRight;animation-name:slideOutRight}@-webkit-keyframes slideOutUp{0%{-webkit-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(-2000px);transform:translateY(-2000px)}}@keyframes slideOutUp{0%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(-2000px);-ms-transform:translateY(-2000px);transform:translateY(-2000px)}}.slideOutUp{-webkit-animation-name:slideOutUp;animation-name:slideOutUp}@-webkit-keyframes slideInUp{0%{opacity:0;-webkit-transform:translateY(2000px);transform:translateY(2000px)}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slideInUp{0%{opacity:0;-webkit-transform:translateY(2000px);-ms-transform:translateY(2000px);transform:translateY(2000px)}100%{opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}}.slideInUp{-webkit-animation-name:slideInUp;animation-name:slideInUp}@-webkit-keyframes slideOutDown{0%{-webkit-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(2000px);transform:translateY(2000px)}}@keyframes slideOutDown{0%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}100%{opacity:0;-webkit-transform:translateY(2000px);-ms-transform:translateY(2000px);transform:translateY(2000px)}}.slideOutDown{-webkit-animation-name:slideOutDown;animation-name:slideOutDown}@-webkit-keyframes hinge{0%{-webkit-transform:rotate(0);transform:rotate(0);-webkit-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}20%,60%{-webkit-transform:rotate(80deg);transform:rotate(80deg);-webkit-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}40%{-webkit-transform:rotate(60deg);transform:rotate(60deg);-webkit-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}80%{-webkit-transform:rotate(60deg) translateY(0);transform:rotate(60deg) translateY(0);-webkit-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;opacity:1}100%{-webkit-transform:translateY(700px);transform:translateY(700px);opacity:0}}@keyframes hinge{0%{-webkit-transform:rotate(0);-ms-transform:rotate(0);transform:rotate(0);-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}20%,60%{-webkit-transform:rotate(80deg);-ms-transform:rotate(80deg);transform:rotate(80deg);-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}40%{-webkit-transform:rotate(60deg);-ms-transform:rotate(60deg);transform:rotate(60deg);-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}80%{-webkit-transform:rotate(60deg) translateY(0);-ms-transform:rotate(60deg) translateY(0);transform:rotate(60deg) translateY(0);-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;opacity:1}100%{-webkit-transform:translateY(700px);-ms-transform:translateY(700px);transform:translateY(700px);opacity:0}}.hinge{-webkit-animation-name:hinge;animation-name:hinge}@-webkit-keyframes rollIn{0%{opacity:0;-webkit-transform:translateX(-100%) rotate(-120deg);transform:translateX(-100%) rotate(-120deg)}100%{opacity:1;-webkit-transform:translateX(0px) rotate(0deg);transform:translateX(0px) rotate(0deg)}}@keyframes rollIn{0%{opacity:0;-webkit-transform:translateX(-100%) rotate(-120deg);-ms-transform:translateX(-100%) rotate(-120deg);transform:translateX(-100%) rotate(-120deg)}100%{opacity:1;-webkit-transform:translateX(0px) rotate(0deg);-ms-transform:translateX(0px) rotate(0deg);transform:translateX(0px) rotate(0deg)}}.rollIn{-webkit-animation-name:rollIn;animation-name:rollIn}@-webkit-keyframes rollOut{0%{opacity:1;-webkit-transform:translateX(0px) rotate(0deg);transform:translateX(0px) rotate(0deg)}100%{opacity:0;-webkit-transform:translateX(100%) rotate(120deg);transform:translateX(100%) rotate(120deg)}}@keyframes rollOut{0%{opacity:1;-webkit-transform:translateX(0px) rotate(0deg);-ms-transform:translateX(0px) rotate(0deg);transform:translateX(0px) rotate(0deg)}100%{opacity:0;-webkit-transform:translateX(100%) rotate(120deg);-ms-transform:translateX(100%) rotate(120deg);transform:translateX(100%) rotate(120deg)}}.rollOut{-webkit-animation-name:rollOut;animation-name:rollOut}@-webkit-keyframes zoomIn{0%{opacity:0;-webkit-transform:scale(.3);transform:scale(.3)}50%{opacity:1}}@keyframes zoomIn{0%{opacity:0;-webkit-transform:scale(.3);-ms-transform:scale(.3);transform:scale(.3)}50%{opacity:1}}.zoomIn{-webkit-animation-name:zoomIn;animation-name:zoomIn}@-webkit-keyframes zoomInDown{0%{opacity:0;-webkit-transform:scale(.1) translateY(-2000px);transform:scale(.1) translateY(-2000px);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}60%{opacity:1;-webkit-transform:scale(.475) translateY(60px);transform:scale(.475) translateY(60px);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}}@keyframes zoomInDown{0%{opacity:0;-webkit-transform:scale(.1) translateY(-2000px);-ms-transform:scale(.1) translateY(-2000px);transform:scale(.1) translateY(-2000px);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}60%{opacity:1;-webkit-transform:scale(.475) translateY(60px);-ms-transform:scale(.475) translateY(60px);transform:scale(.475) translateY(60px);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}}.zoomInDown{-webkit-animation-name:zoomInDown;animation-name:zoomInDown}@-webkit-keyframes zoomInLeft{0%{opacity:0;-webkit-transform:scale(.1) translateX(-2000px);transform:scale(.1) translateX(-2000px);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}60%{opacity:1;-webkit-transform:scale(.475) translateX(48px);transform:scale(.475) translateX(48px);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}}@keyframes zoomInLeft{0%{opacity:0;-webkit-transform:scale(.1) translateX(-2000px);-ms-transform:scale(.1) translateX(-2000px);transform:scale(.1) translateX(-2000px);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}60%{opacity:1;-webkit-transform:scale(.475) translateX(48px);-ms-transform:scale(.475) translateX(48px);transform:scale(.475) translateX(48px);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}}.zoomInLeft{-webkit-animation-name:zoomInLeft;animation-name:zoomInLeft}@-webkit-keyframes zoomInRight{0%{opacity:0;-webkit-transform:scale(.1) translateX(2000px);transform:scale(.1) translateX(2000px);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}60%{opacity:1;-webkit-transform:scale(.475) translateX(-48px);transform:scale(.475) translateX(-48px);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}}@keyframes zoomInRight{0%{opacity:0;-webkit-transform:scale(.1) translateX(2000px);-ms-transform:scale(.1) translateX(2000px);transform:scale(.1) translateX(2000px);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}60%{opacity:1;-webkit-transform:scale(.475) translateX(-48px);-ms-transform:scale(.475) translateX(-48px);transform:scale(.475) translateX(-48px);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}}.zoomInRight{-webkit-animation-name:zoomInRight;animation-name:zoomInRight}@-webkit-keyframes zoomInUp{0%{opacity:0;-webkit-transform:scale(.1) translateY(2000px);transform:scale(.1) translateY(2000px);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}60%{opacity:1;-webkit-transform:scale(.475) translateY(-60px);transform:scale(.475) translateY(-60px);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}}@keyframes zoomInUp{0%{opacity:0;-webkit-transform:scale(.1) translateY(2000px);-ms-transform:scale(.1) translateY(2000px);transform:scale(.1) translateY(2000px);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}60%{opacity:1;-webkit-transform:scale(.475) translateY(-60px);-ms-transform:scale(.475) translateY(-60px);transform:scale(.475) translateY(-60px);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}}.zoomInUp{-webkit-animation-name:zoomInUp;animation-name:zoomInUp}@-webkit-keyframes zoomOut{0%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:0;-webkit-transform:scale(.3);transform:scale(.3)}100%{opacity:0}}@keyframes zoomOut{0%{opacity:1;-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}50%{opacity:0;-webkit-transform:scale(.3);-ms-transform:scale(.3);transform:scale(.3)}100%{opacity:0}}.zoomOut{-webkit-animation-name:zoomOut;animation-name:zoomOut}@-webkit-keyframes zoomOutDown{40%{opacity:1;-webkit-transform:scale(.475) translateY(-60px);transform:scale(.475) translateY(-60px);-webkit-animation-timing-function:linear;animation-timing-function:linear}100%{opacity:0;-webkit-transform:scale(.1) translateY(2000px);transform:scale(.1) translateY(2000px);-webkit-transform-origin:center bottom;transform-origin:center bottom}}@keyframes zoomOutDown{40%{opacity:1;-webkit-transform:scale(.475) translateY(-60px);-ms-transform:scale(.475) translateY(-60px);transform:scale(.475) translateY(-60px);-webkit-animation-timing-function:linear;animation-timing-function:linear}100%{opacity:0;-webkit-transform:scale(.1) translateY(2000px);-ms-transform:scale(.1) translateY(2000px);transform:scale(.1) translateY(2000px);-webkit-transform-origin:center bottom;-ms-transform-origin:center bottom;transform-origin:center bottom}}.zoomOutDown{-webkit-animation-name:zoomOutDown;animation-name:zoomOutDown}@-webkit-keyframes zoomOutLeft{40%{opacity:1;-webkit-transform:scale(.475) translateX(42px);transform:scale(.475) translateX(42px);-webkit-animation-timing-function:linear;animation-timing-function:linear}100%{opacity:0;-webkit-transform:scale(.1) translateX(-2000px);transform:scale(.1) translateX(-2000px);-webkit-transform-origin:left center;transform-origin:left center}}@keyframes zoomOutLeft{40%{opacity:1;-webkit-transform:scale(.475) translateX(42px);-ms-transform:scale(.475) translateX(42px);transform:scale(.475) translateX(42px);-webkit-animation-timing-function:linear;animation-timing-function:linear}100%{opacity:0;-webkit-transform:scale(.1) translateX(-2000px);-ms-transform:scale(.1) translateX(-2000px);transform:scale(.1) translateX(-2000px);-webkit-transform-origin:left center;-ms-transform-origin:left center;transform-origin:left center}}.zoomOutLeft{-webkit-animation-name:zoomOutLeft;animation-name:zoomOutLeft}@-webkit-keyframes zoomOutRight{40%{opacity:1;-webkit-transform:scale(.475) translateX(-42px);transform:scale(.475) translateX(-42px);-webkit-animation-timing-function:linear;animation-timing-function:linear}100%{opacity:0;-webkit-transform:scale(.1) translateX(2000px);transform:scale(.1) translateX(2000px);-webkit-transform-origin:right center;transform-origin:right center}}@keyframes zoomOutRight{40%{opacity:1;-webkit-transform:scale(.475) translateX(-42px);-ms-transform:scale(.475) translateX(-42px);transform:scale(.475) translateX(-42px);-webkit-animation-timing-function:linear;animation-timing-function:linear}100%{opacity:0;-webkit-transform:scale(.1) translateX(2000px);-ms-transform:scale(.1) translateX(2000px);transform:scale(.1) translateX(2000px);-webkit-transform-origin:right center;-ms-transform-origin:right center;transform-origin:right center}}.zoomOutRight{-webkit-animation-name:zoomOutRight;animation-name:zoomOutRight}@-webkit-keyframes zoomOutUp{40%{opacity:1;-webkit-transform:scale(.475) translateY(60px);transform:scale(.475) translateY(60px);-webkit-animation-timing-function:linear;animation-timing-function:linear}100%{opacity:0;-webkit-transform:scale(.1) translateY(-2000px);transform:scale(.1) translateY(-2000px);-webkit-transform-origin:center top;transform-origin:center top}}@keyframes zoomOutUp{40%{opacity:1;-webkit-transform:scale(.475) translateY(60px);-ms-transform:scale(.475) translateY(60px);transform:scale(.475) translateY(60px);-webkit-animation-timing-function:linear;animation-timing-function:linear}100%{opacity:0;-webkit-transform:scale(.1) translateY(-2000px);-ms-transform:scale(.1) translateY(-2000px);transform:scale(.1) translateY(-2000px);-webkit-transform-origin:center top;-ms-transform-origin:center top;transform-origin:center top}}.zoomOutUp{-webkit-animation-name:zoomOutUp;animation-name:zoomOutUp}
@charset "UTF-8";
*,
*::before,
*::after {
  box-sizing: border-box; }

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }

@-ms-viewport {
  width: device-width; }
article, aside, dialog, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block; }

body {
  min-height: 100vh;
  margin: 0;
  font-family: "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #333333;
  background-color: #f8f8f8;
  text-align: left; }

[tabindex="-1"]:focus {
  outline: 0 !important; }

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible; }

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: .5rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem; }

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0; }

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit; }

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem; }

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0; }

dt {
  font-weight: 700; }

dd {
  margin-bottom: .5rem;
  margin-left: 0; }

blockquote {
  margin: 0 0 1rem; }

dfn {
  font-style: italic; }

b,
strong {
  font-weight: bolder; }

small {
  font-size: 80%; }

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline; }

sub {
  bottom: -.25em; }

sup {
  top: -.5em; }

a {
  color: #333333;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects; }

a:not([href]):not([tabindex]) {
  text-decoration: none; }
  a:not([href]):not([tabindex]):hover, a:not([href]):not([tabindex]):focus {
    color: inherit;
    text-decoration: none; }
  a:not([href]):not([tabindex]):focus {
    outline: 0; }

pre,
code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: 1em; }

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar; }

figure {
  margin: 0 0 1rem; }

img {
  vertical-align: middle;
  border-style: none; }

svg:not(:root) {
  overflow: hidden; }

table {
  border-collapse: collapse; }

caption {
  padding-top: .75rem;
  padding-bottom: .3rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom; }

th {
  text-align: inherit; }

::-ms-clear, ::-ms-reveal {
  display: none; }

label {
  display: inline-block;
  margin-bottom: .5rem; }

button {
  border-radius: 0; }

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  /* -webkit-appearance: none; */
  outline: none; }
  input::-webkit-input-safebox-button,
  button::-webkit-input-safebox-button,
  select::-webkit-input-safebox-button,
  optgroup::-webkit-input-safebox-button,
  textarea::-webkit-input-safebox-button {
    display: none; }

button,
input {
  overflow: visible; }

button,
select {
  text-transform: none; }

button,
html [type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button; }

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none; }

input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0; }

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox; }

textarea {
  overflow: auto;
  resize: vertical; }

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0; }

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal; }

progress {
  vertical-align: baseline; }

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto; }

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none; }

[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button; }

output {
  display: inline-block; }

summary {
  display: list-item;
  cursor: pointer; }

template {
  display: none; }

[hidden] {
  display: none !important; }

.icon {
  display: inline-block;
  vertical-align: middle;
  background-size: cover;
  background-repeat: no-repeat; }

.icon-payway-alipay-sm {
  width: 19px;
  height: 19px;
  background: url(../detail-steam.png) -105px -205px; }

.icon-payway-alipay {
  width: 23px;
  height: 24px;
  background: url(./detail-steam.png) -237px 0px; }

.comm-header {
  position: relative;
  flex-wrap: nowrap !important;
  width: 100%;
  height: 45px;
  padding: 0 2.34vw;
  font-size: 12px;
  background: #292B2D; }
  .comm-header button {
    padding: 0;
    border: none;
    outline: none;
    background: none;
    cursor: pointer; }
  .comm-header > div {
    height: 100%; }
  .comm-header .logo {
    display: block;
    height: 60%;
    margin-right: calc(10px + 3.125vw); }
    .comm-header .logo img {
      height: 100%; }
    .comm-header .logo h1 {
      margin: 0;
      font-size: 0; }
  .comm-header .nav {
    display: none; }
    .comm-header .nav a {
      position: relative;
      margin: 0 2.08vw;
      padding: 5px;
      color: #D5D5D5; }
      .comm-header .nav a:hover {
        color: #ffffff; }
      .comm-header .nav a.on {
        color: #FFFEFE; }
        .comm-header .nav a.on::after {
          content: '';
          position: absolute;
          display: block;
          left: 50%;
          top: 100%;
          width: 18px;
          height: 3px;
          transform: translateX(-50%);
          background: #f7cd46;
          border-radius: 2px; }
      .comm-header .nav a.secondary {
        display: none; }
        @media only screen and (min-width: 1460px) {
          .comm-header .nav a.secondary {
            display: inline-flex; } }
  .comm-header .search {
    display: none;
    margin-right: 3.125vw; }
    .comm-header .search .dropmenu {
      max-width: 200px;
      height: 29px;
      color: #333333; }
      .comm-header .search .dropmenu .drop-title input {
        color: #333333; }
        .comm-header .search .dropmenu .drop-title input::-webkit-input-placeholder {
          color: #333333; }
      .comm-header .search .dropmenu .drop-title .edge {
        display: inline-block;
        width: 0;
        height: 0;
        border: 0 solid transparent;
        overflow: hidden;
        border-bottom: none;
        border-top-color: #333333; }
      .comm-header .search .dropmenu .drop-menu dd {
        line-height: 29px; }
      .comm-header .search .dropmenu .drop-title {
        position: relative; }
        .comm-header .search .dropmenu .drop-title .symbolIcon {
          position: absolute;
          left: 3px;
          top: 4px;
          font-size: 21px;
          color: #D5D5D5;
          pointer-events: none;
          transition: .2s all; }
        .comm-header .search .dropmenu .drop-title input {
          width: 29px;
          padding: 4px 0 4px 29px;
          background: transparent;
          transition: .2s all .1s;
          color: #D5D5D5; }
          .comm-header .search .dropmenu .drop-title input:focus {
            width: 200px;
            padding-right: 4px;
            background-color: #373940;
            border-radius: 3px; }
            .comm-header .search .dropmenu .drop-title input:focus ~ .symbolIcon {
              font-size: 17px;
              left: 5px;
              top: 6px; }
          .comm-header .search .dropmenu .drop-title input::-webkit-input-placeholder {
            color: #6e6e6f; }
      .comm-header .search .dropmenu .drop-menu {
        max-height: unset;
        border: none;
        border-top: solid 5px transparent; }
  .comm-header .aside-main-menu-btn .symbolIcon {
    font-size: 22px;
    color: #F7CD46; }
  .comm-header .un-login {
    padding-right: 2vw; }
    .comm-header .un-login button {
      color: #D7D7D7; }
    .comm-header .un-login .split-line {
      display: inline-block;
      vertical-align: middle;
      width: 1px;
      height: 15px;
      background: rgba(105, 105, 105, 0.4);
      margin: 0 10px; }
    .comm-header .un-login .symbolIcon {
      margin-right: 10px;
      font-size: 27px; }
  .comm-header .login-ed {
    height: 100%; }
    .comm-header .login-ed .msg {
      position: relative;
      margin-right: 1.458vw;
      font-size: 0;
      color: #D5D5D5; }
      .comm-header .login-ed .msg .symbolIcon {
        font-size: 20px; }
      .comm-header .login-ed .msg .amount {
        display: none;
        position: absolute;
        right: 0;
        top: 0;
        min-width: 22px;
        height: 22px;
        line-height: 22px;
        padding: 0 6px;
        text-align: center;
        border-radius: 11px;
        font-size: 12px;
        background: #EC5947;
        transform: scale(0.8333) translate(75%, -50%); }
      .comm-header .login-ed .msg.has-new .amount {
        display: block; }
    .comm-header .login-ed .user {
      position: relative;
      height: 100%;
      width: 180px; }
      .comm-header .login-ed .user > a {
        width: 100%; }
        .comm-header .login-ed .user > a img {
          width: 32px;
          margin-right: 10px;
          border-radius: 50%; }
        .comm-header .login-ed .user > a span {
          max-width: 120px;
          font-size: 14px;
          color: #D5D5D5;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis; }
      .comm-header .login-ed .user .sub-menu {
        position: absolute;
        display: none;
        z-index: 2;
        top: 100%;
        right: 0;
        width: 100%;
        padding-top: 20px;
        background: rgba(41, 43, 45, 0.97);
        color: #B5B5B5;
        font-size: 14px; }
        .comm-header .login-ed .user .sub-menu .balance {
          padding: 0 18px;
          margin-bottom: 20px; }
          .comm-header .login-ed .user .sub-menu .balance span {
            color: #FF7522; }
          .comm-header .login-ed .user .sub-menu .balance .buttons {
            margin-top: 15px; }
            .comm-header .login-ed .user .sub-menu .balance .buttons a, .comm-header .login-ed .user .sub-menu .balance .buttons button {
              padding: 0;
              border: none;
              outline: none;
              background: none;
              cursor: pointer;
              display: inline-block;
              height: auto;
              max-width: 999px;
              padding: 6px 18px;
              text-align: center;
              line-height: 100%;
              white-space: nowrap;
              border-radius: 2px;
              color: #040404;
              font-size: 14px;
              background: #F7CD46; }
              .comm-header .login-ed .user .sub-menu .balance .buttons a.ghost, .comm-header .login-ed .user .sub-menu .balance .buttons button.ghost {
                color: #FF6F00;
                background: transparent;
                box-shadow: inset 0 0 0 1px #FF6F00; }
              .comm-header .login-ed .user .sub-menu .balance .buttons a:disabled, .comm-header .login-ed .user .sub-menu .balance .buttons button:disabled {
                border: none;
                cursor: not-allowed;
                color: #ffffff;
                background: #CCCCCC; }
              .comm-header .login-ed .user .sub-menu .balance .buttons a.ghost, .comm-header .login-ed .user .sub-menu .balance .buttons button.ghost {
                margin-left: 10px;
                color: #B5B5B5;
                box-shadow: inset 0 0 0 1px #7A7A7A; }
        .comm-header .login-ed .user .sub-menu .menu {
          position: relative;
          list-style: none;
          margin: 0;
          padding: 12px 0; }
          .comm-header .login-ed .user .sub-menu .menu::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: calc(100% - 36px);
            height: 1px;
            background: rgba(255, 255, 255, 0.15); }
          .comm-header .login-ed .user .sub-menu .menu li a {
            display: block;
            padding: 0 18px;
            line-height: 32px;
            color: inherit; }
            .comm-header .login-ed .user .sub-menu .menu li a:hover {
              color: #ffffff;
              background: rgba(255, 255, 255, 0.15); }
      .comm-header .login-ed .user:hover .sub-menu {
        display: block; }
  .comm-header .game-categories {
    display: block;
    height: 100%;
    background: #F7CD46; }
    .comm-header .game-categories .btn {
      position: relative;
      height: 100%;
      padding: 0 10px;
      text-align: center;
      color: #040404; }
      .comm-header .game-categories .btn .symbolIcon {
        font-size: 25px; }
      .comm-header .game-categories .btn::after {
        content: '';
        position: absolute;
        z-index: 3;
        display: block;
        width: 100%;
        height: 5px;
        top: 100%;
        left: 0;
        margin-top: -1px;
        background: #F7CD46; }
    .comm-header .game-categories .content {
      display: none;
      position: absolute;
      z-index: 2;
      left: 0;
      top: 100%;
      width: 100vw;
      height: 395px;
      background: rgba(4, 4, 4, 0.8);
      overflow-y: auto; }
      .comm-header .game-categories .content .wrap {
        height: 100%;
        padding-top: 3%;
        padding-bottom: 3%; }
        .comm-header .game-categories .content .wrap .games {
          display: inline-block;
          width: 100%;
          vertical-align: top; }
          .comm-header .game-categories .content .wrap .games.client li {
            width: 20%; }
          .comm-header .game-categories .content .wrap .games.mobile li {
            width: 33.33%; }
          .comm-header .game-categories .content .wrap .games .tit {
            color: #ffffff;
            font-size: 18px; }
            .comm-header .game-categories .content .wrap .games .tit .symbolIcon {
              margin-right: 10px; }
          .comm-header .game-categories .content .wrap .games .game-list {
            width: 100%;
            padding: 0;
            font-size: 0;
            list-style: none;
            margin: 0 -2vw; }
            .comm-header .game-categories .content .wrap .games .game-list li {
              display: inline-block;
              padding: 10px 2vw 0; }
              .comm-header .game-categories .content .wrap .games .game-list li a {
                position: relative;
                display: block;
                width: 100%;
                max-width: 76px;
                line-height: 76px;
                margin-bottom: 30px;
                text-align: center;
                color: #E3E3E3; }
                .comm-header .game-categories .content .wrap .games .game-list li a img {
                  display: inline-block;
                  width: 100%;
                  border-radius: 16px;
                  background: #ffffff; }
                .comm-header .game-categories .content .wrap .games .game-list li a span {
                  position: absolute;
                  display: block;
                  top: 100%;
                  left: 50%;
                  transform: translateX(-50%);
                  white-space: nowrap;
                  line-height: 30px;
                  font-size: 14px; }
                .comm-header .game-categories .content .wrap .games .game-list li a .symbolIcon {
                  position: absolute;
                  font-size: 25px;
                  top: -10px;
                  right: -25px; }
                .comm-header .game-categories .content .wrap .games .game-list li a:hover {
                  color: #F7CD46; }
        .comm-header .game-categories .content .wrap .split-line {
          display: inline-block;
          vertical-align: middle;
          width: 1px;
          height: 100%;
          background: rgba(255, 255, 255, 0.2);
          display: none;
          margin: 0 8% 0 5%; }
        @media only screen and (min-width: 990px) {
          .comm-header .game-categories .content .wrap .games .game-list li {
            padding-top: 30px; }
          .comm-header .game-categories .content .wrap .games.client {
            width: 50.5%; }
          .comm-header .game-categories .content .wrap .games.mobile {
            width: 30.5%; }
          .comm-header .game-categories .content .wrap .split-line {
            display: inline-block; } }
        @media only screen and (min-width: 1460px) {
          .comm-header .game-categories .content .wrap {
            padding-top: 50px;
            padding-bottom: 50px; }
            .comm-header .game-categories .content .wrap .games .game-list {
              margin: 0 -35px; }
              .comm-header .game-categories .content .wrap .games .game-list li {
                padding-left: 35px;
                padding-right: 35px; } }
    .comm-header .game-categories:hover, .comm-header .game-categories.active {
      background: #ffc200; }
      .comm-header .game-categories:hover .btn::after, .comm-header .game-categories.active .btn::after {
        background: #ffc200; }
      .comm-header .game-categories:hover .content, .comm-header .game-categories.active .content {
        display: block; }
  @media only screen and (min-width: 768px) {
    .comm-header.hasOpacity {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      background: rgba(4, 4, 4, 0.7);
      box-shadow: 0 5px 9px 0 rgba(0, 0, 0, 0.15); }
      .comm-header.hasOpacity .login-ed .user .sub-menu {
        background: rgba(4, 4, 4, 0.7); } }
  @media only screen and (min-width: 990px) {
    .comm-header {
      padding-right: 0;
      height: 70px;
      font-size: 16px; }
      .comm-header .aside-main-menu-btn {
        display: none; }
      .comm-header .nav {
        display: flex; }
      .comm-header .game-categories .btn {
        padding: 0 25px; }
        .comm-header .game-categories .btn .symbolIcon {
          font-size: 35px; } }
  @media only screen and (min-width: 1460px) {
    .comm-header .logo {
      margin-right: 120px; }
    .comm-header .search {
      display: flex; } }

.comm-footer {
  bottom: 0;
  left: 0;
  width: 100%;
  background: #292B2D;
  padding-top: 2%;
  margin-top: 2%; }
  .comm-footer .guarantee {
    padding: 0;
    display: none; }
    .comm-footer .guarantee > li {
      list-style: none; }
      .comm-footer .guarantee > li .symbolIcon {
        font-size: 81px; }
      .comm-footer .guarantee > li > div {
        margin-left: 1rem; }
        .comm-footer .guarantee > li > div p {
          margin: 0;
          color: #888888;
          font-size: 13px; }
          .comm-footer .guarantee > li > div p:first-child {
            color: #F7CD46;
            font-size: 18px; }
  .comm-footer .help {
    margin-bottom: 24px;
    padding: 0;
    font-size: 0;
    border-bottom: 1px solid rgba(33, 36, 43, 0.05);
    display: none; }
    .comm-footer .help > li {
      display: inline-block;
      width: 25%;
      padding: 44px 0 48px;
      font-size: 12px;
      text-align: left;
      color: #59656d;
      vertical-align: top;
      text-align: center; }
      .comm-footer .help > li .tit {
        font-size: 14px;
        color: #fff;
        margin-bottom: 32px;
        line-height: 100%; }
      .comm-footer .help > li > a {
        display: block;
        color: #888888;
        line-height: 100%; }
        .comm-footer .help > li > a:not(:last-child) {
          margin-bottom: 18px; }
      .comm-footer .help > li .hover-pop p {
        margin: 0 !important; }
    .comm-footer .help .contact {
      margin-top: 44px;
      padding: 0;
      height: 101px;
      text-align: center;
      width: 20%;
      border-left: 1px solid rgba(248, 248, 248, 0.05);
      border-right: 1px solid rgba(248, 248, 248, 0.05);
      display: none; }
      .comm-footer .help .contact span, .comm-footer .help .contact a {
        display: inline-flex; }
        .comm-footer .help .contact span.hotline, .comm-footer .help .contact a.hotline {
          margin-bottom: 14px;
          width: 138px;
          font-size: 20px;
          height: 20px;
          color: #F7CD46; }
        .comm-footer .help .contact span.service-hours, .comm-footer .help .contact a.service-hours {
          display: block;
          margin-bottom: 19px;
          font-size: 12px;
          color: #888888; }
        .comm-footer .help .contact span.advisory, .comm-footer .help .contact a.advisory {
          width: 128px;
          height: 32px;
          border: 1px solid #F7CD46;
          font-size: 14px;
          color: #F7CD46; }
          .comm-footer .help .contact span.advisory .symbolIcon, .comm-footer .help .contact a.advisory .symbolIcon {
            margin-right: 5px;
            font-size: 17px; }
    .comm-footer .help .tools {
      text-align: center;
      display: none; }
      .comm-footer .help .tools li {
        display: inline-block; }
        .comm-footer .help .tools li .symbolIcon {
          font-size: 57px;
          color: #646464; }
          .comm-footer .help .tools li .symbolIcon:hover {
            color: #d6d6d4; }
        .comm-footer .help .tools li p {
          margin-top: 12px;
          color: #666666; }
        .comm-footer .help .tools li:nth-child(4) {
          margin-right: 0; }
        .comm-footer .help .tools li:hover {
          position: relative; }
          .comm-footer .help .tools li:hover .hover-pop {
            display: block;
            padding: 15px; }
            .comm-footer .help .tools li:hover .hover-pop > div, .comm-footer .help .tools li:hover .hover-pop > ul, .comm-footer .help .tools li:hover .hover-pop::before {
              background: #fff;
              border: solid 1px #EFF0EF; }
            .comm-footer .help .tools li:hover .hover-pop > div, .comm-footer .help .tools li:hover .hover-pop > ul {
              width: 130px;
              padding: 15px; }
            .comm-footer .help .tools li:hover .hover-pop img {
              max-width: 100%; }
  .comm-footer .friend-links {
    font-size: 12px;
    color: #666666;
    white-space: nowrap;
    margin-bottom: 30px;
    display: none; }
    .comm-footer .friend-links > span {
      white-space: normal; }
      .comm-footer .friend-links > span:first-child {
        margin-right: 20px; }
      .comm-footer .friend-links > span > a {
        display: inline-block;
        margin-right: 25px;
        color: #666666; }
        .comm-footer .friend-links > span > a img {
          width: 16px;
          vertical-align: sub; }
  .comm-footer .copyright {
    padding-bottom: 2%;
    color: #555555;
    font-size: 12px;
    text-align: center; }
    .comm-footer .copyright span {
      display: inline-block;
      margin-right: 30px;
      vertical-align: middle; }
    .comm-footer .copyright a {
      color: #555555; }
  @media only screen and (min-width: 990px) {
    .comm-footer {
      display: block; }
      .comm-footer .guarantee {
        display: flex; }
      .comm-footer .help {
        display: block; }
        .comm-footer .help > li {
          width: 12%;
          text-align: left; }
        .comm-footer .help .contact {
          display: none; }
        .comm-footer .help .tools {
          width: 40%;
          display: inline-block; }
      .comm-footer .friend-links {
        display: block; }
      .comm-footer .copyright {
        padding-bottom: 44px;
        text-align: left; } }
  @media only screen and (min-width: 1460px) {
    .comm-footer .help > li:nth-child(4) {
      width: 10.4%; }
    .comm-footer .help .contact {
      display: inline-block; }
    .comm-footer .help .tools {
      width: 31.6%; } }

.h5-footer {
  background: #fff;
  font-size: 12px;
  text-align: center;
  padding: 12px 47px;
  color: #666; }
  @media only screen and (min-width: 990px) {
    .h5-footer {
      display: none; } }

.small-menu-bg, .black-bg {
  position: fixed;
  z-index: 3;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
  background: rgba(0, 0, 0, 0.6);
  transition: visibility 0.5s, opacity 0.5s; }
  .small-menu-bg.visible, .black-bg.visible {
    visibility: visible;
    opacity: 1.0;
    transition: visibility 0s, opacity 0.5s; }

.aside-main-menu {
  position: fixed;
  z-index: 4;
  top: 0;
  right: -65vw;
  visibility: hidden;
  width: 65vw;
  height: 100%;
  overflow: hidden;
  background: #171a21;
  transition: all .5s; }
  .aside-main-menu.visible {
    visibility: visible;
    right: 0; }
  .aside-main-menu ul {
    width: 100%;
    height: calc(100% - 140px);
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto; }
    .aside-main-menu ul li {
      line-height: 55px;
      padding: 0 10px;
      border-top: 1px solid #2f3138;
      border-bottom: 1px solid #000000;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #bdbdbd;
      font-size: 20px; }
      .aside-main-menu ul li:first-child {
        padding: 0 0; }
      .aside-main-menu ul li a {
        display: block;
        color: inherit; }
      .aside-main-menu ul li p {
        margin-bottom: 0; }
      .aside-main-menu ul li > div {
        display: none; }
        .aside-main-menu ul li > div a {
          line-height: 40px;
          font-size: 18px;
          color: #FF6F00; }
          .aside-main-menu ul li > div a span {
            display: inline-block;
            width: 175px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis; }
          .aside-main-menu ul li > div a.msg-btn {
            line-height: 30px;
            font-size: 12px;
            color: #ffffff; }
            .aside-main-menu ul li > div a.msg-btn span {
              width: auto;
              font-size: 12px;
              line-height: 12px;
              color: #FF6F00;
              vertical-align: middle; }
      .aside-main-menu ul li .to-login {
        display: block;
        padding: 0 10px; }
      .aside-main-menu ul li .login-ed {
        position: relative;
        display: block;
        padding: 20px 20px 10px;
        box-shadow: inset 0 -4px 8px 0 #000000;
        background: -webkit-linear-gradient(107deg, #24313f 0%, #171a21 33%);
        background: linear-gradient(107deg, #24313f 0%, #171a21 33%); }
        .aside-main-menu ul li .login-ed img {
          width: 34px;
          height: 34px;
          padding: 1px;
          margin-right: 10px;
          background: linear-gradient(to bottom, #515151 5%, #474747 95%);
          vertical-align: middle; }
        .aside-main-menu ul li .login-ed span {
          vertical-align: middle;
          font-size: 22px;
          color: #898989; }
          .aside-main-menu ul li .login-ed span:hover {
            color: #FF6F00; }
        .aside-main-menu ul li .login-ed .cart, .aside-main-menu ul li .login-ed .logout {
          position: absolute;
          display: inline-block;
          bottom: 5px;
          right: 10px;
          line-height: normal;
          color: #898989;
          font-size: 16px; }
  .aside-main-menu .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    overflow-y: hidden;
    padding: 20px 10px 0;
    text-align: left;
    font-size: 10px;
    color: #8a8a8a; }
    .aside-main-menu .footer img {
      height: 45px; }

.aside-minor-menu-btn {
  padding: 0;
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
  display: inline-block;
  height: auto;
  max-width: 999px;
  padding: 5px 15px;
  text-align: center;
  line-height: 100%;
  white-space: nowrap;
  border-radius: 2px;
  color: #040404;
  font-size: 14px;
  background: #F7CD46;
  position: fixed;
  right: 0;
  top: 120px;
  z-index: 3;
  display: block;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }
  .aside-minor-menu-btn.ghost {
    color: #FF6F00;
    background: transparent;
    box-shadow: inset 0 0 0 1px #FF6F00; }
  .aside-minor-menu-btn:disabled {
    border: none;
    cursor: not-allowed;
    color: #ffffff;
    background: #CCCCCC; }
  @media only screen and (min-width: 768px) {
    .aside-minor-menu-btn {
      top: 148px;
      padding: 8px 15px; } }
  @media only screen and (min-width: 1460px) {
    .aside-minor-menu-btn {
      display: none; } }

.aside-tools {
  display: none;
  position: fixed;
  right: 0;
  top: 250px;
  width: 80px;
  padding: 1px 0 0 0;
  margin: 0;
  list-style: none;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.06); }
  .aside-tools > li {
    position: relative;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    background: #fff;
    border-bottom: 1px solid #ECECEC; }
    .aside-tools > li:nth-last-child(1) {
      border: none; }
    .aside-tools > li > a {
      display: block;
      padding: 15px 0;
      color: #bbbbbb; }
      .aside-tools > li > a i {
        display: block;
        margin: 0 auto 6px; }
      .aside-tools > li > a .symbolIcon {
        display: block;
        margin: 0 auto 6px;
        font-size: 24px; }
    .aside-tools > li:first-child {
      border-top-left-radius: 4px; }
    .aside-tools > li:last-child {
      border-bottom-left-radius: 4px; }
    .aside-tools > li:hover {
      background-color: #F7CD46; }
      .aside-tools > li:hover a {
        color: #020001; }
  @media only screen and (min-width: 990px) {
    .aside-tools {
      display: block; } }

#goTop {
  padding: 0;
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
  position: fixed;
  left: calc(1444px + (100% - 1444px)/2 + 35px);
  bottom: 11px;
  display: none;
  width: 58px;
  line-height: 58px;
  text-align: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  visibility: hidden;
  pointer-events: none; }
  #goTop .symbolIcon {
    font-size: 30px;
    vertical-align: middle; }
  #goTop:hover {
    background: rgba(0, 0, 0, 0.5); }
  @media only screen and (min-width: 1640px) {
    #goTop {
      visibility: visible;
      pointer-events: unset; } }

.aside-advertisement {
  position: fixed;
  top: 50%;
  display: none;
  overflow: hidden;
  transform: translateY(-50%); }
  @media only screen and (min-width: 1800px) {
    .aside-advertisement {
      display: block; } }
  .aside-advertisement.left {
    left: calc(((100% - 1444px)/2 - 150px)/2); }
  .aside-advertisement.right {
    right: calc(((100% - 1444px)/2 - 150px)/2); }

.comm-form {
  font-size: 14px;
  color: #333333; }
  .comm-form p {
    margin: 20px 0;
    font-size: 12px;
    text-align: left; }
    .comm-form p strong {
      color: #FF6F00; }
    .comm-form p.tip {
      margin: 5px 0 0;
      color: #b0b6b9; }
      .comm-form p.tip i {
        margin-right: 5px; }
  .comm-form label {
    margin-bottom: 0; }
  .comm-form input[type="submit"] {
    display: block;
    width: 100%;
    max-width: 320px;
    height: 40px;
    margin: 20px auto;
    font-size: 17px;
    border: none;
    color: #040404;
    border-radius: 4px;
    background: #F7CD46;
    cursor: pointer; }
    .comm-form input[type="submit"].small {
      max-width: 135px;
      font-size: 14px; }
    .comm-form input[type="submit"]:disabled {
      background: #EFF0EF;
      color: #bbbbbb; }
  .comm-form .inline {
    display: inline-block !important;
    vertical-align: middle; }
    .comm-form .inline + .inline {
      margin-left: 30px; }
  .comm-form .small, .comm-form .middle, .comm-form .large {
    max-width: 100%; }
    .comm-form .small.small, .comm-form .middle.small, .comm-form .large.small {
      width: 250px; }
    .comm-form .small.middle, .comm-form .middle.middle, .comm-form .large.middle {
      width: 320px; }
    .comm-form .small.large, .comm-form .middle.large, .comm-form .large.large {
      width: 575px; }
  .comm-form .form-item {
    position: relative;
    margin-bottom: 20px;
    line-height: 36px;
    font-size: 14px; }
    .comm-form .form-item.no-margin {
      margin: 0; }
    .comm-form .form-item input, .comm-form .form-item textarea, .comm-form .form-item .dropmenu {
      display: block;
      width: 100%;
      height: 36px;
      background: #f6f6f6;
      border-radius: 4px;
      border: solid 1px #EFF0EF; }
      .comm-form .form-item input.disabled, .comm-form .form-item textarea.disabled, .comm-form .form-item .dropmenu.disabled {
        padding-left: 0;
        border: none;
        background: transparent; }
        .comm-form .form-item input.disabled.dropmenu, .comm-form .form-item textarea.disabled.dropmenu, .comm-form .form-item .dropmenu.disabled.dropmenu {
          pointer-events: none; }
    .comm-form .form-item input {
      padding: 0 20px; }
      .comm-form .form-item input::-webkit-input-placeholder {
        color: #bbbbbb; }
      .comm-form .form-item input[name="dragImgCaptcha"] {
        width: 0 !important;
        height: 0 !important;
        border: 0 !important;
        padding: 0 !important; }
      .comm-form .form-item input.error {
        border-color: #FF9600; }
    .comm-form .form-item textarea {
      height: auto;
      padding: 10px 20px;
      line-height: normal;
      resize: vertical; }
      .comm-form .form-item textarea::-webkit-input-placeholder {
        color: #bbbbbb; }
    .comm-form .form-item .dropmenu {
      max-width: 100%;
      height: 36px;
      color: #333333; }
      .comm-form .form-item .dropmenu .drop-title input {
        color: #333333; }
        .comm-form .form-item .dropmenu .drop-title input::-webkit-input-placeholder {
          color: #333333; }
      .comm-form .form-item .dropmenu .drop-title .edge {
        display: inline-block;
        width: 0;
        height: 0;
        border: 4px solid transparent;
        overflow: hidden;
        border-bottom: none;
        border-top-color: #333333; }
      .comm-form .form-item .dropmenu .drop-menu dd {
        line-height: 36px; }
      .comm-form .form-item .dropmenu .drop-title input::-webkit-input-placeholder {
        color: #bbbbbb; }
    .comm-form .form-item .pos-before, .comm-form .form-item .pos-after {
      display: block;
      position: absolute;
      top: 0;
      padding: 0 10px;
      line-height: 34px;
      background-clip: padding-box;
      background-origin: padding-box;
      border: solid 1px transparent; }
      .comm-form .form-item .pos-before a, .comm-form .form-item .pos-after a {
        display: inline-block;
        vertical-align: middle;
        line-height: 20px;
        cursor: pointer;
        color: #FF6F00;
        font-size: 14px; }
      .comm-form .form-item .pos-before.pos-before, .comm-form .form-item .pos-after.pos-before {
        left: 0; }
        .comm-form .form-item .pos-before.pos-before.dropmenu, .comm-form .form-item .pos-after.pos-before.dropmenu {
          max-width: 60px;
          padding: 0;
          line-height: normal;
          border-right: none; }
          .comm-form .form-item .pos-before.pos-before.dropmenu .drop-title input, .comm-form .form-item .pos-after.pos-before.dropmenu .drop-title input {
            display: inline-block;
            padding: 0 10px;
            border: none; }
          .comm-form .form-item .pos-before.pos-before.dropmenu ~ input, .comm-form .form-item .pos-after.pos-before.dropmenu ~ input {
            padding-left: 60px; }
      .comm-form .form-item .pos-before.pos-after, .comm-form .form-item .pos-after.pos-after {
        right: 0;
        max-width: 80%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis; }
        .comm-form .form-item .pos-before.pos-after.has-border::before, .comm-form .form-item .pos-after.pos-after.has-border::before {
          content: '';
          margin-right: 10px;
          display: inline-block;
          vertical-align: middle;
          width: 1px;
          height: 20px;
          background: #EFF0EF; }
    .comm-form .form-item .font15 {
      font-size: 15px; }
    .comm-form .form-item .font18 {
      font-size: 18px; }
    .comm-form .form-item .money {
      color: #FF6F00;
      font-size: 14px;
      font-weight: 400; }
      .comm-form .form-item .money span {
        font-size: 18px;
        font-weight: 700; }
    .comm-form .form-item .relative-words {
      padding: 0 2px;
      line-height: 20px;
      font-size: 12px;
      color: #999999; }
      .comm-form .form-item .relative-words strong {
        font-weight: 400;
        color: #FF6F00; }
    .comm-form .form-item.w-eight-two {
      text-align: left; }
      .comm-form .form-item.w-eight-two .pull-left {
        display: inline-block;
        width: 72%; }
      .comm-form .form-item.w-eight-two .pull-right {
        display: inline-block;
        float: right;
        width: 26%;
        height: 36px;
        line-height: 34px;
        text-align: center;
        border-radius: 4px; }
        .comm-form .form-item.w-eight-two .pull-right.get-captcha, .comm-form .form-item.w-eight-two .pull-right.copy-textarea {
          cursor: pointer;
          color: #333333;
          background: #F7CD46;
          border: solid 1px #F7CD46; }
          .comm-form .form-item.w-eight-two .pull-right.get-captcha.disabled, .comm-form .form-item.w-eight-two .pull-right.copy-textarea.disabled {
            pointer-events: none;
            color: #bbbbbb;
            background: #EFF0EF;
            border-color: #EFF0EF; }
    .comm-form .form-item.amount-editor {
      line-height: 36px; }
      .comm-form .form-item.amount-editor button, .comm-form .form-item.amount-editor input {
        padding: 0;
        border: none;
        outline: none;
        background: none;
        cursor: pointer;
        display: inline-block;
        height: 30px;
        line-height: 28px !important;
        vertical-align: middle;
        text-align: center;
        border-radius: 2px;
        border: solid 1px #E7E8E9; }
      .comm-form .form-item.amount-editor input {
        width: 80px;
        cursor: text; }
      .comm-form .form-item.amount-editor button {
        width: 30px;
        font: normal 24px cursive;
        cursor: pointer;
        user-select: none; }
        .comm-form .form-item.amount-editor button:disabled {
          color: #bbbbbb;
          pointer-events: none; }
  .comm-form .form-item-after {
    display: inline-block;
    vertical-align: top;
    line-height: 36px;
    font-size: 12px;
    color: #bbbbbb; }
    .comm-form .form-item-after strong, .comm-form .form-item-after a {
      color: #FF6F00; }
  .comm-form .form-item-kv {
    font-size: 0; }
    .comm-form .form-item-kv > label {
      display: inline-block;
      vertical-align: top;
      width: 72px;
      margin-right: 20px;
      line-height: 36px;
      white-space: nowrap;
      text-align: right;
      font-size: 13px;
      color: #bbbbbb; }
      .comm-form .form-item-kv > label.long-txt {
        width: 140px; }
        .comm-form .form-item-kv > label.long-txt + .form-item {
          width: calc(100% - 176px); }
    .comm-form .form-item-kv > .form-item {
      display: inline-block;
      vertical-align: top; }
      .comm-form .form-item-kv > .form-item .fr {
        margin: 5px 0;
        line-height: normal; }
        .comm-form .form-item-kv > .form-item .fr.light {
          color: #bbbbbb; }
    .comm-form .form-item-kv > label + .form-item {
      width: calc(100% - 95px); }
    .comm-form .form-item-kv.has-border {
      margin-bottom: 20px;
      border-bottom: solid 1px #EFF0EF; }
  .comm-form .file-box {
    margin-bottom: 0;
    font-size: 0; }
    .comm-form .file-box .file-item {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: 80px;
      height: 80px;
      line-height: 80px;
      margin-right: 20px;
      background: url("/static/img/file-item-bg-white.png") no-repeat center center/contain; }
      .comm-form .file-box .file-item input[type="file"] {
        width: 100%;
        height: 100%;
        vertical-align: middle;
        opacity: 0;
        cursor: pointer; }
      .comm-form .file-box .file-item .preview-item {
        position: relative;
        width: 100%;
        height: 100%; }
        .comm-form .file-box .file-item .preview-item img {
          display: block;
          width: 100%;
          height: 100%; }
        .comm-form .file-box .file-item .preview-item button {
          padding: 0;
          border: none;
          outline: none;
          background: none;
          cursor: pointer;
          position: absolute;
          right: 0;
          top: 0;
          display: block;
          width: 16px;
          line-height: 16px;
          font-size: 16px;
          color: #ffffff;
          background: #FF9600;
          font-family: monospace;
          border-radius: 50%;
          -webkit-transform: translate(50%, -50%);
          -moz-transform: translate(50%, -50%);
          -ms-transform: translate(50%, -50%);
          -o-transform: translate(50%, -50%);
          transform: translate(50%, -50%); }
  .comm-form p.error {
    position: absolute;
    top: 100%;
    left: 0;
    margin: 0;
    padding-left: 20px;
    line-height: 20px;
    color: #FF9600;
    font-size: 14px;
    white-space: nowrap;
    background: url("/static/img/error-head-bg.png") no-repeat left center; }
    .comm-form p.error a {
      color: #00aaff;
      cursor: pointer; }

.dragImgCaptcha {
  display: none;
  height: 40px;
  -webkit-user-select: none;
  user-select: none; }
  .dragImgCaptcha .track {
    position: relative;
    height: 40px;
    background: #f6f6f6;
    border: 1px solid #e9e9e9;
    border-radius: 2px;
    text-align: center;
    line-height: 40px; }
    .dragImgCaptcha .track .captchaTooltip {
      color: #bbbbbb; }
    .dragImgCaptcha .track .progress {
      position: absolute;
      left: -1px;
      top: -1px;
      display: block;
      height: 40px;
      border-style: solid;
      border-width: 1px 0 1px 1px;
      border-radius: 2px; }
      .dragImgCaptcha .track .progress .captchaButton {
        display: block;
        width: 40px;
        height: 38px;
        line-height: 40px;
        text-align: center;
        border-radius: 1px;
        box-shadow: 0 0 3px rgba(0, 0, 0, 0.3); }
        .dragImgCaptcha .track .progress .captchaButton .buttonOverlay {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 40px;
          height: 40px;
          z-index: 2; }
        .dragImgCaptcha .track .progress .captchaButton .captchaIcon {
          display: inline;
          font-size: 19.5px;
          font-family: "Courier New", Courier, sans-serif; }
      .dragImgCaptcha .track .progress .captchaButtonInitial {
        background: #ffffff; }
        .dragImgCaptcha .track .progress .captchaButtonInitial:hover {
          cursor: pointer;
          background: #F7CD46; }
      .dragImgCaptcha .track .progress .captchaButtonProgress {
        background: #F7CD46; }
      .dragImgCaptcha .track .progress .captchaButtonSuccess {
        background: #3ecfb0; }
      .dragImgCaptcha .track .progress .captchaButtonError {
        background: #FF9600; }
      .dragImgCaptcha .track .progress .captchaTooltip {
        display: inline;
        font-size: 20px;
        color: black;
        font-family: "Times New Roman", sans-serif; }
    .dragImgCaptcha .track .progressInitial {
      border-color: #e9e9e9;
      background-color: rgba(25, 145, 250, 0.2); }
    .dragImgCaptcha .track .progressSuccess {
      border-color: #3ecfb0;
      background: rgba(62, 207, 176, 0.1); }
    .dragImgCaptcha .track .progressError {
      border-color: #FF9600;
      background: rgba(255, 150, 0, 0.04); }
  .dragImgCaptcha .captchaImageWrapper {
    display: block;
    position: absolute;
    bottom: 40px;
    z-index: 1;
    height: 0;
    width: 100%;
    border-radius: 2px;
    overflow: hidden;
    transition: height 0.2s linear .2s; }
    .dragImgCaptcha .captchaImageWrapper > img {
      width: 100%; }
    .dragImgCaptcha .captchaImageWrapper .captchaReload {
      position: absolute;
      z-index: 1;
      top: 0;
      right: 2px;
      font-size: 20px;
      color: black;
      font-family: "Trebuchet MS", Helvetica, sans-serif; }
      .dragImgCaptcha .captchaImageWrapper .captchaReload:hover {
        cursor: pointer; }

.hover-pop {
  position: absolute;
  z-index: 2;
  display: none;
  top: 50%; }
  .hover-pop::before {
    content: '';
    position: absolute;
    z-index: 1;
    top: 50%;
    width: 10px;
    height: 10px; }
  .hover-pop.left {
    left: 0;
    transform: translate(-100%, -50%); }
    .hover-pop.left::before {
      right: 0;
      border-left: none !important;
      border-bottom: none !important;
      transform: translate(-100%, -50%) rotate(45deg); }
  .hover-pop.right {
    right: 0;
    transform: translate(100%, -50%); }
    .hover-pop.right::before {
      left: 0;
      border-top: none !important;
      border-right: none !important;
      transform: translate(100%, -50%) rotate(45deg); }
  .hover-pop.top {
    top: 0;
    bottom: auto;
    left: 50%;
    transform: translate(-50%, -100%); }
    .hover-pop.top::before {
      top: auto;
      bottom: 0;
      left: 50%;
      border-top: none !important;
      border-left: none !important;
      transform: translate(-50%, -100%) rotate(45deg); }
  .hover-pop.bottom {
    top: auto;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 100%); }
    .hover-pop.bottom::before {
      top: 0;
      left: 50%;
      border-right: none !important;
      border-bottom: none !important;
      transform: translate(-50%, 100%) rotate(45deg); }
  .hover-pop.left-top {
    left: 0;
    transform: translate(-100%, -20%); }
    .hover-pop.left-top::before {
      top: 20%;
      right: 0;
      border-left: none !important;
      border-bottom: none !important;
      transform: translate(-100%, -50%) rotate(45deg); }
  .hover-pop.right-top {
    right: 0;
    transform: translate(100%, -20%); }
    .hover-pop.right-top::before {
      top: 20%;
      left: 0;
      border-top: none !important;
      border-right: none !important;
      transform: translate(100%, -50%) rotate(45deg); }
  .hover-pop.right-bottom {
    top: auto;
    bottom: 0;
    right: 0;
    transform: translate(100%, 15%); }
    .hover-pop.right-bottom::before {
      top: auto;
      bottom: 15%;
      left: 0;
      border-top: none !important;
      border-right: none !important;
      transform: translate(100%, -50%) rotate(45deg); }

.dropmenu {
  position: relative;
  display: inline-block;
  vertical-align: middle; }
  .dropmenu .drop-title, .dropmenu .drop-menu {
    border-radius: 4px; }
  .dropmenu .drop-title {
    display: block;
    width: 100%;
    height: 100%;
    white-space: nowrap; }
    .dropmenu .drop-title input, .dropmenu .drop-title a {
      width: 100%;
      height: 100%;
      padding: 0 10px;
      font-size: 14px;
      border: none; }
      .dropmenu .drop-title input:read-only, .dropmenu .drop-title a:read-only {
        cursor: pointer; }
    .dropmenu .drop-title i:first-child {
      position: absolute;
      top: 50%;
      left: 10px;
      -webkit-transform: translateY(-50%);
      -moz-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%); }
    .dropmenu .drop-title i + input, .dropmenu .drop-title i + a {
      padding-left: 35px; }
    .dropmenu .drop-title input + i, .dropmenu .drop-title a + i {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%); }
      .dropmenu .drop-title input + i.edge, .dropmenu .drop-title a + i.edge {
        transition: all .3s;
        cursor: pointer; }
  .dropmenu .drop-menu {
    position: absolute;
    display: none;
    z-index: 2;
    top: 100%;
    width: 100%;
    max-height: 35vh;
    overflow-y: auto;
    margin: 0;
    padding: 5px 0;
    font-size: 12px;
    background-color: #fff;
    background-origin: padding-box !important;
    background-clip: padding-box !important;
    border: solid 1px #EFF0EF; }
    .dropmenu .drop-menu dd {
      margin-bottom: 0;
      padding-left: 10px; }
      .dropmenu .drop-menu dd > a {
        display: block;
        color: inherit;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis; }
      .dropmenu .drop-menu dd span {
        vertical-align: middle; }
      .dropmenu .drop-menu dd.drop-tips {
        opacity: .35;
        cursor: default; }
      .dropmenu .drop-menu dd.disabled {
        pointer-events: none; }
      .dropmenu .drop-menu dd.selected:not(.drop-tips) {
        color: #FF6F00; }
      .dropmenu .drop-menu dd:not(.drop-tips):hover {
        color: #040404;
        background-color: #F7CD46; }
  .dropmenu.disabled {
    pointer-events: none; }
    .dropmenu.disabled .drop-title .edge {
      display: none !important; }
    .dropmenu.disabled .drop-menu {
      display: none !important; }
  .dropmenu.active .drop-title .edge {
    transform: translateY(-50%) rotate(180deg); }
  .dropmenu.search-drop input::placeholder {
    color: transparent !important; }

.checkbox, .radiobox {
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
  cursor: pointer;
  font-size: 0;
  line-height: normal; }
  .checkbox ~ label, .radiobox ~ label {
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }
  .checkbox input, .radiobox input {
    display: none !important; }
  .checkbox i, .radiobox i {
    display: block;
    position: relative;
    width: 14px;
    height: 14px;
    border-radius: 2px;
    border: solid 1px #bbbbbb; }
    .checkbox i:after, .radiobox i:after {
      content: '';
      position: absolute;
      left: 3px;
      top: 0;
      display: table;
      width: 5px;
      height: 10px;
      border-width: 2px;
      border-style: solid;
      border-top: 0;
      border-left: 0;
      color: transparent;
      transform: rotate(45deg); }
  .checkbox i.roundness, .radiobox i.roundness {
    position: relative;
    display: block;
    border-radius: 50%; }
    .checkbox i.roundness:after, .radiobox i.roundness:after {
      content: '';
      position: absolute;
      display: block;
      left: 50%;
      top: 50%;
      width: 8px;
      height: 8px;
      border: none;
      color: transparent;
      border-radius: 50%;
      transform: translate(-50%, -50%); }
  .checkbox input:checked + i, .radiobox input:checked + i {
    border-color: #F7CD46;
    background: #F7CD46; }
    .checkbox input:checked + i:after, .radiobox input:checked + i:after {
      color: #fff; }
  .checkbox input:checked + i.roundness, .radiobox input:checked + i.roundness {
    background: none; }
    .checkbox input:checked + i.roundness:after, .radiobox input:checked + i.roundness:after {
      background-color: #F7CD46; }
  .checkbox input:disabled, .radiobox input:disabled {
    border-color: rgba(0, 0, 0, 0.26);
    pointer-events: none;
    color: rgba(0, 0, 0, 0.26) !important; }
    .checkbox input:disabled ~ span, .radiobox input:disabled ~ span {
      color: rgba(0, 0, 0, 0.26); }
    .checkbox input:disabled:checked + i, .radiobox input:disabled:checked + i {
      border-color: rgba(0, 0, 0, 0.26);
      background: rgba(0, 0, 0, 0.26); }

.pagination {
  padding: 0;
  margin: 60px auto;
  list-style: none;
  text-align: center;
  font-size: 14px; }
  .pagination li {
    display: none;
    margin: 0 2px;
    color: #333333;
    background: #ffffff; }
    .pagination li i {
      margin: 0 10px;
      color: #7b92a4; }
    .pagination li a {
      display: block;
      padding: 6px 12px;
      color: inherit; }
    .pagination li.on {
      color: #040404;
      background: #F7CD46; }
    .pagination li.page-next, .pagination li.page-prev, .pagination li.page-first, .pagination li.page-last {
      display: inline-block; }
    .pagination li.disabled {
      pointer-events: none;
      background: rgba(0, 0, 0, 0.05);
      border-color: transparent;
      color: #bbbbbb; }
    @media only screen and (min-width: 768px) {
      .pagination li {
        display: inline-block; } }

.section-tit {
  position: relative;
  font-size: 0; }
  .section-tit > h2 {
    position: relative;
    display: inline-block;
    margin: 0;
    padding-left: 14px;
    font-size: 20px;
    color: #282828;
    font-weight: 400; }
    .section-tit > h2::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      display: block;
      width: 4px;
      height: 20px;
      margin-top: 1.5px;
      border-radius: 2px;
      background: #F7CD46;
      transform: translateY(-50%); }
  .section-tit .more {
    position: absolute;
    display: block;
    right: 0;
    top: 50%;
    line-height: 25px;
    text-align: center;
    transform: translateY(-50%); }
    .section-tit .more span {
      display: inline-block;
      margin-right: 6px;
      vertical-align: middle;
      font-size: 14px;
      font-family: PingFang SC, "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
      font-weight: 500;
      color: #8c8c8c; }
  .section-tit .categories {
    float: right;
    position: static;
    display: none;
    padding: 0;
    margin: 0;
    line-height: 33px;
    white-space: normal;
    font-size: 12px;
    list-style: none; }
    .section-tit .categories li {
      display: inline-block;
      padding: 2px 12px;
      margin-right: 15px;
      line-height: normal;
      cursor: pointer;
      border-radius: 12px;
      color: #333333;
      border: solid 1px #bbbbbb; }
      .section-tit .categories li:last-child {
        margin: 0; }
      .section-tit .categories li.on {
        color: #333333;
        background: #F7CD46;
        border-color: #F7CD46; }

.section-content {
  padding: 0; }

@media only screen and (min-width: 768px) {
  .section-tit {
    position: relative; }
    .section-tit > h2 {
      font-size: 28px;
      padding-left: 22px; }
      .section-tit > h2::before {
        width: 6px;
        height: 24px;
        border-radius: 3px; }
    .section-tit .categories {
      position: absolute;
      display: inline-block;
      top: 0;
      left: 244px; } }
*::-webkit-scrollbar {
  width: 4px;
  height: 4px; }
  @media only screen and (min-width: 990px) {
    *::-webkit-scrollbar {
      width: 6px;
      height: 6px; } }
*::-webkit-scrollbar-thumb {
  background-color: #DFDFDF; }
*::-webkit-scrollbar-track {
  background-color: transparent; }

.symbolIcon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden; }

body {
  overflow-x: hidden; }
  body .fr-view {
    font-size: 15px; }
  body p[data-f-id="pbf"] {
    display: none !important; }
  body #blockbyte-bs-indicator {
    display: none !important; }
  body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) {
    width: 460px;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
    background-color: #fff; }
    body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-title {
      height: 50px;
      line-height: 49px;
      padding: 0 30px;
      font-size: 15px;
      color: #333333;
      background: inherit;
      border-bottom: solid 1px #EFF0EF; }
    body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-content {
      text-align: center; }
      body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-content iframe {
        background: #ffffff; }
      body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-content .template > .tip {
        width: 100%;
        padding: 10px;
        text-align: center;
        color: #666666;
        font-size: 14px;
        background: #FDEDDB; }
        body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-content .template > .tip img {
          vertical-align: unset; }
        body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-content .template > .tip strong {
          color: #FF9600;
          font-weight: normal; }
      body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-content .confirm-msg {
        padding: 30px 0;
        text-align: center;
        color: #FF6F00;
        font-size: 18px;
        border-bottom: solid 1px #EFF0EF; }
        body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-content .confirm-msg .description {
          margin: 20px 0 0;
          font-size: 14px;
          color: #666666;
          font-weight: normal; }
    body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-imgbar, body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-imguide {
      display: block; }
    body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-btn {
      padding-bottom: 30px; }
      body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-btn a {
        display: inline-block;
        height: auto;
        max-width: 999px;
        padding: 10px 50px;
        text-align: center;
        line-height: 100%;
        white-space: nowrap;
        border-radius: 2px;
        color: #040404;
        font-size: 14px;
        background: #F7CD46;
        font-size: 14px;
        border-color: #F7CD46; }
        body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-btn a.ghost {
          color: #FF6F00;
          background: transparent;
          box-shadow: inset 0 0 0 1px #FF6F00; }
        body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-btn a:disabled {
          border: none;
          cursor: not-allowed;
          color: #ffffff;
          background: #CCCCCC; }
        body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-btn a.layui-layer-btn1 {
          background: none;
          color: #333333;
          border-color: rgba(0, 0, 0, 0.2); }
    body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-close1, body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-close2 {
      position: absolute;
      right: -5px;
      top: -5px; }
    body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-close1 {
      width: 30px;
      height: 30px;
      margin-left: 0;
      background-position: -149px -31px; }
    body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading).layui-layer-dialog .layui-layer-content {
      padding: 30px 2.5%;
      color: #333333; }
    @media only screen and (min-width: 990px) {
      body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) {
        border-radius: 4px;
        border: solid 1px #000; }
        body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-imgbar, body .layui-layer:not(.layui-layer-msg):not(.layui-layer-tips):not(.layui-layer-loading) .layui-layer-imguide {
          display: none; } }

.hor {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap; }

.hor-center {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center; }

.ver-center {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center; }

.center-center {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center; }

.center-center-column {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center; }

.space-between {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between; }

.space-between-column {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between; }

.space-around-column {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-around; }

.space-around {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-around; }

.fl {
  float: left; }

.fr {
  float: right; }

.cf:before, .cf:after {
  content: '';
  display: table; }
.cf:after {
  clear: both; }

.w-response {
  width: 100%;
  padding: 0 2%;
  margin: auto; }
  @media only screen and (min-width: 990px) {
    .w-response {
      max-width: 95%;
      padding: 0; } }
  @media only screen and (min-width: 1460px) {
    .w-response {
      max-width: 1444px; } }

.font13 {
  font-size: 14px !important; }

.font14 {
  font-size: 14px !important; }

.font15 {
  font-size: 15px !important; }

.font16 {
  font-size: 16px !important; }

.font17 {
  font-size: 17px !important; }

.font18 {
  font-size: 18px !important; }

.def-none {
  padding: 50px 0;
  border: none !important;
  text-align: center; }
  .def-none button {
    padding: 0;
    border: none;
    outline: none;
    background: none;
    cursor: pointer; }
  .def-none p {
    margin-top: 30px;
    font-size: 14px;
    text-align: center;
    color: #bbbbbb; }
    .def-none p a, .def-none p button {
      color: #FF6F00; }
    .def-none p strong {
      color: #666666;
      font-size: 16px;
      font-weight: 400; }
  .def-none > a, .def-none > button {
    display: inline-block;
    width: 120px;
    line-height: 36px;
    font-size: 14px;
    border-radius: 2px;
    color: #333333;
    border: solid 1px #bbbbbb; }
  .def-none.space-between {
    padding: 0 4vw;
    background: rgba(0, 0, 0, 0.02); }
    .def-none.space-between p {
      margin: 0;
      padding-left: 2.5%;
      text-align: left; }

.explain {
  display: inline-block;
  cursor: default; }
  .explain .about {
    display: inline-block;
    margin-right: 3px;
    vertical-align: middle; }
  .explain .hover-pop {
    text-align: left;
    white-space: normal;
    line-height: normal;
    padding: 15px; }
    .explain .hover-pop > div, .explain .hover-pop > ul, .explain .hover-pop::before {
      background: #fff;
      border: solid 1px rgba(0, 0, 0, 0.08); }
    .explain .hover-pop > div, .explain .hover-pop > ul {
      width: 200px;
      padding: 15px; }
    .explain .hover-pop > div {
      padding: 10px; }
    .explain .hover-pop .tit {
      margin-bottom: 15px;
      color: #999999; }
    .explain .hover-pop .items {
      margin: 0;
      padding: 0;
      list-style: none;
      font-size: 13px;
      color: #666666; }
      .explain .hover-pop .items li {
        position: relative; }
        .explain .hover-pop .items li:not(:only-child) {
          padding-left: 15px; }
          .explain .hover-pop .items li:not(:only-child)::before {
            content: '';
            position: absolute;
            display: block;
            left: 0;
            top: 7px;
            width: 6px;
            height: 6px;
            border-radius: 2px;
            background: #FF6F00; }
  .explain:hover {
    position: relative; }
    .explain:hover .hover-pop {
      display: block; }

.coupon-layer {
  position: fixed;
  display: block;
  left: 50%;
  top: 50%;
  z-index: 3;
  width: 100%;
  transform: translate(-50%, -50%);
  font-size: 0; }
  .coupon-layer img {
    display: block;
    max-width: 100%;
    margin: 0 auto;
    user-select: none;
    -webkit-user-drag: none; }
  .coupon-layer a {
    position: absolute;
    left: 50%;
    display: block;
    transform: translateX(-50%); }
    .coupon-layer a.to-use {
      top: 68%;
      height: 10%;
      width: 298px;
      max-width: 50%; }
    .coupon-layer a.close {
      bottom: 0;
      width: 46px;
      height: 6.5%;
      max-width: 8%; }

.template {
  display: none;
  font-size: 18px;
  text-align: left; }
  .template > div, .template > .comm-form {
    max-width: 100%; }
  .template > .comm-form {
    margin: 40px auto; }
    .template > .comm-form .drop-menu {
      max-height: 150px; }
    .template > .comm-form.w-340 {
      width: 340px; }
      .template > .comm-form.w-340 input[type="submit"].small {
        margin-left: 92px; }
  .template .forget-password {
    line-height: 20px;
    font-size: 14px;
    cursor: pointer;
    color: #bbbbbb; }
  .template .buttons {
    text-align: center; }
    .template .buttons button, .template .buttons a {
      padding: 0;
      border: none;
      outline: none;
      background: none;
      cursor: pointer;
      margin: 0 15px; }
      .template .buttons button:nth-child(1), .template .buttons a:nth-child(1) {
        display: inline-block;
        height: auto;
        max-width: 999px;
        padding: 9px 30px;
        text-align: center;
        line-height: 100%;
        white-space: nowrap;
        border-radius: 2px;
        color: #040404;
        font-size: 14px;
        background: transparent;
        border: solid 1px rgba(0, 0, 0, 0.2);
        color: #333333; }
        .template .buttons button:nth-child(1).ghost, .template .buttons a:nth-child(1).ghost {
          color: #FF6F00;
          background: transparent;
          box-shadow: inset 0 0 0 1px #FF6F00; }
        .template .buttons button:nth-child(1):disabled, .template .buttons a:nth-child(1):disabled {
          border: none;
          cursor: not-allowed;
          color: #ffffff;
          background: #CCCCCC; }
      .template .buttons button:only-child, .template .buttons button:nth-child(2), .template .buttons a:only-child, .template .buttons a:nth-child(2) {
        display: inline-block;
        height: auto;
        max-width: 999px;
        padding: 10px 30px;
        text-align: center;
        line-height: 100%;
        white-space: nowrap;
        border-radius: 2px;
        color: #040404;
        font-size: 14px;
        background: #F7CD46;
        border: none; }
        .template .buttons button:only-child.ghost, .template .buttons button:nth-child(2).ghost, .template .buttons a:only-child.ghost, .template .buttons a:nth-child(2).ghost {
          color: #FF6F00;
          background: transparent;
          box-shadow: inset 0 0 0 1px #FF6F00; }
        .template .buttons button:only-child:disabled, .template .buttons button:nth-child(2):disabled, .template .buttons a:only-child:disabled, .template .buttons a:nth-child(2):disabled {
          border: none;
          cursor: not-allowed;
          color: #ffffff;
          background: #CCCCCC; }
  .template.result-box {
    padding: 50px 0 35px;
    text-align: center; }
    .template.result-box .tit {
      font-size: 20px;
      color: #333333; }
      .template.result-box .tit span, .template.result-box .tit strong {
        display: inline-block;
        vertical-align: middle;
        margin: 0 15px; }
      .template.result-box .tit.warning {
        color: #F7CD46; }
    .template.result-box .result-message {
      width: 100%;
      padding: 20px;
      margin-bottom: 30px;
      font-size: 14px;
      color: #333333;
      text-align: center;
      border-bottom: solid 1px #EFF0EF; }
      .template.result-box .result-message a, .template.result-box .result-message strong {
        color: #FF6F00; }
  .template.rules-box {
    padding: 30px 40px 50px; }
    .template.rules-box .fr-view {
      height: 400px;
      margin-bottom: 40px;
      padding: 25px 30px;
      border: solid 1px #EFF0EF;
      overflow-y: auto; }
  .template.pay-box > div {
    width: 320px;
    margin: 0 auto;
    font-size: 14px;
    color: #999999; }
     strong {
      color: #FF6F00;
      font-size: 13px; }
       strong span {
        font-size: 16px; }
       strong.dark {
        color: #333333; }
    .pay-ways {
      margin: 20px 0; }
      .pay-ways ul {
        margin-top: 10px;
        padding: 0;
        list-style: none; }
        .pay-ways ul li {
          position: relative;
          display: inline-block;
          line-height: 36px;
          padding: 0 12px;
          margin-right: 10px;
          vertical-align: middle;
          color: #333333;
          font-size: 14px;
          border-radius: 2px;
          cursor: pointer;
          border: solid 1px #EFF0EF; }
          .pay-ways ul li i {
            margin-right: 10px; }
            .pay-ways ul li i.icon-payway-selected {
              position: absolute;
              display: none;
              margin: 0;
              right: 0;
              bottom: 0; }
          .pay-ways ul li span {
            display: inline-block;
            vertical-align: middle; }
          .pay-ways ul li.on {
            border-color: #FF9600; }
            .pay-ways ul li.on .icon-payway-selected {
              display: block; }
    .tip-text {
        width: 100%;
        padding: 10px;
        text-align: center;
        color: #666666;
        font-size: 14px;
        background: #FDEDDB; 
    }
    
     .balance {
      margin: 20px 0; }
       .balance > span:first-child {
        margin-right: 20px; }
       .balance > label {
        margin-bottom: 5px; }
     .need-pay {
      text-align: center;
      padding: 8px 0;
      background: #EFF0EF;
      border-radius: 4px; }
     .qrcode {
      min-height: 320px;
      margin: 30px 0;
      text-align: center; }
       .qrcode p {
        margin-top: 20px; }
         .qrcode p span {
          color: #c9c9c9; }
  .template.attention-box .content {
    margin-bottom: 0;
    padding: 30px 2.5%;
    text-align: left;
    border-bottom: none; }
    @media only screen and (min-width: 768px) {
      .template.attention-box .content {
        padding: 30px; } }
    .template.attention-box .content .main {
      margin-bottom: 25px;
      border-bottom: 1px solid #EFF0EF; }
      .template.attention-box .content .main h2 {
        font-size: 18px; }
      .template.attention-box .content .main .tips {
        margin-bottom: 20px;
        font-size: 14px; }
      .template.attention-box .content .main .href a {
        display: block;
        margin-bottom: 20px;
        line-height: 100%;
        font-size: 14px;
        color: #FF9600; }
    .template.attention-box .content .buttons {
      font-size: 0;
      color: #bbbbbb;
      text-align: left; }
      .template.attention-box .content .buttons > label {
        margin-bottom: 0;
        line-height: 40px;
        font-size: 12px;
        cursor: pointer; }
        .template.attention-box .content .buttons > label + a {
          margin: 0;
          font-size: 12px;
          vertical-align: middle; }
      .template.attention-box .content .buttons button {
        padding: 0;
        border: none;
        outline: none;
        background: none;
        cursor: pointer;
        display: inline-block;
        height: auto;
        max-width: 999px;
        padding: 12px 42px;
        text-align: center;
        line-height: 100%;
        white-space: nowrap;
        border-radius: 2px;
        color: #040404;
        font-size: 14px;
        background: #F7CD46;
        margin: 0;
        font-size: 12px; }
        .template.attention-box .content .buttons button.ghost {
          color: #FF6F00;
          background: transparent;
          box-shadow: inset 0 0 0 1px #FF6F00; }
        .template.attention-box .content .buttons button:disabled {
          border: none;
          cursor: not-allowed;
          color: #ffffff;
          background: #CCCCCC; }
  .template.login-box, .template.register-box, .template.findPassword-box {
    padding: 5%;
    color: #bbbbbb; }
    .template.login-box .tab-head, .template.register-box .tab-head, .template.findPassword-box .tab-head {
      position: relative;
      list-style: none;
      padding: 0; }
      .template.login-box .tab-head::after, .template.register-box .tab-head::after, .template.findPassword-box .tab-head::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        display: block;
        width: 100%;
        height: 1px;
        background: linear-gradient(90deg, #f2f2f2, #f2f2f2 50%, transparent); }
      .template.login-box .tab-head li, .template.register-box .tab-head li, .template.findPassword-box .tab-head li {
        display: inline-block;
        margin-right: 45px;
        padding-bottom: 5px;
        font-size: 18px;
        border-bottom: solid 3px transparent;
        cursor: pointer; }
        .template.login-box .tab-head li.on, .template.login-box .tab-head li:only-child, .template.register-box .tab-head li.on, .template.register-box .tab-head li:only-child, .template.findPassword-box .tab-head li.on, .template.findPassword-box .tab-head li:only-child {
          color: #303030;
          font-weight: 700;
          border-bottom-color: #303030; }
        .template.login-box .tab-head li:only-child:not(.none), .template.register-box .tab-head li:only-child:not(.none), .template.findPassword-box .tab-head li:only-child:not(.none) {
          position: relative;
          margin-left: 25px; }
          .template.login-box .tab-head li:only-child:not(.none)::before, .template.register-box .tab-head li:only-child:not(.none)::before, .template.findPassword-box .tab-head li:only-child:not(.none)::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 0;
            display: block;
            width: 12px;
            height: 26px;
            background: url("/static/img/icon-back.png") no-repeat center; }
    .template.login-box .content, .template.register-box .content, .template.findPassword-box .content {
      padding: 50px 0 10px; }
      .template.login-box .content .comm-form .form-item, .template.register-box .content .comm-form .form-item, .template.findPassword-box .content .comm-form .form-item {
        line-height: 40px; }
        .template.login-box .content .comm-form .form-item > input, .template.login-box .content .comm-form .form-item .dropmenu, .template.register-box .content .comm-form .form-item > input, .template.register-box .content .comm-form .form-item .dropmenu, .template.findPassword-box .content .comm-form .form-item > input, .template.findPassword-box .content .comm-form .form-item .dropmenu {
          height: 40px; }
        .template.login-box .content .comm-form .form-item .dropmenu, .template.register-box .content .comm-form .form-item .dropmenu, .template.findPassword-box .content .comm-form .form-item .dropmenu {
          line-height: 40px; }
        .template.login-box .content .comm-form .form-item .pos-before, .template.login-box .content .comm-form .form-item .pos-after, .template.register-box .content .comm-form .form-item .pos-before, .template.register-box .content .comm-form .form-item .pos-after, .template.findPassword-box .content .comm-form .form-item .pos-before, .template.findPassword-box .content .comm-form .form-item .pos-after {
          line-height: 38px; }
        .template.login-box .content .comm-form .form-item.w-eight-two .pull-right, .template.register-box .content .comm-form .form-item.w-eight-two .pull-right, .template.findPassword-box .content .comm-form .form-item.w-eight-two .pull-right {
          height: 40px;
          line-height: 38px; }
    @media only screen and (min-width: 990px) {
      .template.login-box, .template.register-box, .template.findPassword-box {
        padding: 10%; } }
  .template.login-box .content .comm-form {
    margin-bottom: 60px; }
    .template.login-box .content .comm-form > div input {
      padding-left: 35px; }
  .template.login-box .content .other-ways {
    font-size: 14px;
    text-align: center; }
    .template.login-box .content .other-ways button {
      padding: 0;
      border: none;
      outline: none;
      background: none;
      cursor: pointer;
      margin: 0 20px;
      width: 48px;
      height: 48px;
      text-align: center;
      background: #EFF0EF;
      border-radius: 50%; }
      .template.login-box .content .other-ways button:hover[data-type="WEIXIN"] {
        background: #58BC46; }
      .template.login-box .content .other-ways button:hover[data-type="QQ"] {
        background: #3fa6ee; }
  .template.register-box .content .comm-form .user-agree, .template.findPassword-box .content .comm-form .user-agree {
    position: relative; }
    .template.register-box .content .comm-form .user-agree label, .template.findPassword-box .content .comm-form .user-agree label {
      margin-bottom: 0; }
    .template.register-box .content .comm-form .user-agree a, .template.findPassword-box .content .comm-form .user-agree a {
      vertical-align: middle; }
  .template.startGame-box > img {
    width: 100%;
    min-height: 250px; }
  .template.startGame-box .bottom {
    padding: 20px 30px;
    white-space: nowrap;
    background: #F7CD46; }
    .template.startGame-box .bottom .logo, .template.startGame-box .bottom .info {
      display: inline-block;
      vertical-align: middle; }
    .template.startGame-box .bottom .info {
      padding-left: 20px;
      color: #333333;
      font-size: 14px; }
      .template.startGame-box .bottom .info .tools {
        margin-top: 10px; }
        .template.startGame-box .bottom .info .tools a, .template.startGame-box .bottom .info .tools .share-code {
          padding: 0;
          border: none;
          outline: none;
          background: none;
          cursor: pointer;
          display: inline-block;
          height: auto;
          max-width: 999px;
          padding: 7px 18px;
          text-align: center;
          line-height: 100%;
          white-space: nowrap;
          border-radius: 2px;
          color: #040404;
          font-size: 14px;
          background: transparent;
          font-size: 12px;
          border: solid 1px rgba(0, 0, 0, 0.1); }
          .template.startGame-box .bottom .info .tools a.ghost, .template.startGame-box .bottom .info .tools .share-code.ghost {
            color: #FF6F00;
            background: transparent;
            box-shadow: inset 0 0 0 1px #FF6F00; }
          .template.startGame-box .bottom .info .tools a:disabled, .template.startGame-box .bottom .info .tools .share-code:disabled {
            border: none;
            cursor: not-allowed;
            color: #ffffff;
            background: #CCCCCC; }
          .template.startGame-box .bottom .info .tools a:not(:last-child), .template.startGame-box .bottom .info .tools .share-code:not(:last-child) {
            margin-right: 10px; }
          .template.startGame-box .bottom .info .tools a:first-child, .template.startGame-box .bottom .info .tools .share-code:first-child {
            color: #fff;
            background: #333333; }
        .template.startGame-box .bottom .info .tools .share-code {
          position: relative; }
          .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop {
            padding: 15px;
            display: block;
            right: 0;
            left: unset;
            padding-left: 0;
            padding-right: 0;
            border-radius: 30px;
            transform: translate(0, -100%); }
            .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop > div, .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop > ul, .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop::before {
              background: #fff;
              border: solid 1px rgba(0, 0, 0, 0.1); }
            .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop > div, .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop > ul {
              width: 335px;
              padding: 15px; }
            .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop::before {
              left: unset;
              right: 45px; }
            .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop > div {
              border-radius: 30px; }
            .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop .copy-key {
              padding-left: 20px;
              line-height: 30px;
              background: #F7F7F2;
              color: #666666;
              font-size: 0;
              border: none;
              border-radius: 15px; }
              .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop .copy-key label, .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop .copy-key input, .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop .copy-key button {
                display: inline-block;
                vertical-align: middle;
                margin: 0;
                font-size: 12px; }
              .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop .copy-key input {
                min-width: 140px;
                font-size: 16px;
                color: #FF6F00;
                border: none;
                background: transparent; }
              .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop .copy-key button {
                padding: 0;
                border: none;
                outline: none;
                background: none;
                cursor: pointer;
                display: inline-block;
                height: auto;
                max-width: 999px;
                padding: 5px 20px;
                text-align: center;
                line-height: 100%;
                white-space: nowrap;
                border-radius: 2px;
                color: #040404;
                font-size: 14px;
                background: #F7CD46;
                border-radius: 15px; }
                .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop .copy-key button.ghost {
                  color: #FF6F00;
                  background: transparent;
                  box-shadow: inset 0 0 0 1px #FF6F00; }
                .template.startGame-box .bottom .info .tools .share-code:hover .hover-pop .copy-key button:disabled {
                  border: none;
                  cursor: not-allowed;
                  color: #ffffff;
                  background: #CCCCCC; }
  .template.idcard-box {
    padding: 20px 25px 0; }
    .template.idcard-box .tips p {
      margin-bottom: 5px;
      font-size: 13px;
      color: #333333; }
      .template.idcard-box .tips p a, .template.idcard-box .tips p strong {
        color: #FF6F00; }
    .template.idcard-box .form-item-kv > label {
      color: #666666; }
      .template.idcard-box .form-item-kv > label::before {
        content: '*';
        margin-right: 10px;
        color: #FF6F00; }
    .template.idcard-box .form-item-kv .form-item input {
      color: #666666;
      background: #fff; }
    .template.idcard-box .form-item-kv .form-item .orange {
      color: #FF6F00; }
    .template.idcard-box .form-item-kv .form-item .tip {
      line-height: normal; }

/*# sourceMappingURL=comm.css.map */

.swiper-container {
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  border-radius: 4px;
  font-size: 0; }
  .swiper-container .swiper-wrapper {
    margin: 0;
    padding: 0; }
    .swiper-container .swiper-wrapper .swiper-slide {
      display: inline-block;
      vertical-align: middle; }
      .swiper-container .swiper-wrapper .swiper-slide img {
        max-width: 100%;
        max-height: 100%; }

.breadcrumb {
  line-height: 32px;
  font-size: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 96%;
  padding: 0; }
  .breadcrumb a, .breadcrumb h1, .breadcrumb span, .breadcrumb i {
    display: inline-block;
    padding: 0;
    margin: 0;
    font-size: 12px;
    vertical-align: middle;
    font-weight: 400; }
  .breadcrumb a {
    color: #999999; }
  .breadcrumb i {
    margin: 0 15px;
    font-style: normal; }
  .breadcrumb h1, .breadcrumb span {
    color: #bbbbbb; }
  .breadcrumb a:hover, .breadcrumb a.fr {
    color: #FF6F00; }
  @media only screen and (min-width: 768px) {
    .breadcrumb {
      line-height: 48px; } }

.account-item {
  display: inline-block;
  width: 100%;
  padding: 4% 5.5%;
  border-radius: 4px;
  background: #ffffff; }
  .account-item .content {
    position: relative;
    flex-wrap: nowrap;
    height: 76px;
    margin-top: 50px; }
    .account-item .content > div {
      height: 100%; }
    .account-item .content .content-left {
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      height: 44px;
      flex-wrap: wrap;
      flex-direction: row;
      align-items: flex-start;
      transform: translateY(-100%); }
    .account-item .content .content-right {
      position: absolute;
      right: 0;
      bottom: 0;
      align-items: flex-end;
      flex-wrap: nowrap;
      height: 100%; }
  .account-item .tit {
    display: -webkit-box;
    height: 48px;
    font-size: 16px;
    color: #282828;
    white-space: normal;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; }
    .account-item .tit .tag {
      display: inline-block;
      margin-right: 4px;
      padding: 4.5px 9px;
      line-height: 100%;
      vertical-align: text-bottom;
      font-size: 12px;
      color: #ffffff;
      border-radius: 4px; }
      .account-item .tit .tag.android {
        background-color: #5C9B56; }
      .account-item .tit .tag.ios {
        background-color: #434343; }
      .account-item .tit .tag.recommend {
        background-color: #F15E30; }
  .account-item .img {
    height: 100%;
    margin-right: 18px;
    border-radius: 4px; }
  .account-item .game-region {
    margin-right: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: #999999; }
    .account-item .game-region span {
      color: #B6A57C; }
  .account-item .tags span {
    display: inline-block;
    line-height: 100%;
    padding: 4px 5px;
    margin: 1px 5px 1px 0;
    font-size: 12px;
    border-radius: 2px;
    color: #BFAF88;
    background: rgba(240, 232, 204, 0.8); }
  .account-item .renter {
    display: none;
    font-size: 14px;
    color: #8B8B8B; }
    .account-item .renter .symbolIcon {
      font-size: 18px; }
    .account-item .renter .symbolIcon, .account-item .renter i {
      margin-right: 8px; }
    .account-item .renter span {
      display: inline-block;
      max-width: 140px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis; }
  .account-item .price {
    white-space: nowrap;
    color: #F15E30;
    font-size: 16px;
    vertical-align: baseline; }
    .account-item .price strong {
      font-size: 26px; }
  .account-item .rent-count {
    white-space: nowrap;
    font-size: 14px;
    color: #999999; }
    .account-item .rent-count span {
      color: #F15E30; }
  @media only screen and (min-width: 990px) {
    .account-item .content {
      height: 96px;
      margin-top: 20px; }
      .account-item .content .content-left {
        position: static;
        transform: none;
        display: flex;
        height: 100%;
        flex-direction: column;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: space-between; }
      .account-item .content .content-right {
        padding: 18px 0 8px; }
    .account-item .tit {
      height: 60px;
      font-size: 20px; }
      .account-item .tit .tag {
        font-size: 14px; }
    .account-item .renter {
      display: flex; }
    .account-item:hover {
      box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.06); } }

.aside-account-list {
  list-style: none;
  padding: 0;
  margin: 0; }
  .aside-account-list li {
    padding: 20px 0; }
    .aside-account-list li:not(:last-child) {
      border-bottom: solid 1px #ECECEC; }
    .aside-account-list li > a {
      display: block; }
      .aside-account-list li > a .account-img {
        display: inline-block;
        width: 65px;
        margin-right: 10px;
        vertical-align: middle; }
      .aside-account-list li > a .account-info {
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 75px);
        font-size: 14px; }
        .aside-account-list li > a .account-info .tit {
          display: -webkit-box;
          height: 34px;
          line-height: 18px;
          color: #333333;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical; }
        .aside-account-list li > a .account-info .sec-tit {
          margin-top: 13px; }
          .aside-account-list li > a .account-info .sec-tit .price {
            color: #FF6F00; }

.account-list {
  list-style: none;
  padding: 0;
  margin: -5px;
  font-size: 0; }
  .account-list li {
    display: inline-block;
    vertical-align: top;
    width: 100%;
    padding: 5px; }
  @media only screen and (min-width: 768px) {
    .account-list {
      margin: -10px; }
      .account-list li {
        width: 50%;
        padding: 10px; } }
  .account-list.small li {
    width: 100%; }
    .account-list.small li .account-item .sec-tit {
      margin-top: 10px;
      white-space: nowrap;
      overflow: hidden; }
    .account-list.small li .account-item .content {
      margin-top: 20px; }
      .account-list.small li .account-item .content .content-right {
        padding: 0; }
  @media only screen and (min-width: 768px) {
    .account-list.small li {
      width: 50%; } }
  @media only screen and (min-width: 990px) {
    .account-list.small li {
      width: 33.33%; } }
  @media only screen and (min-width: 1460px) {
    .account-list.small li {
      width: 25%; } }

.detail-top {
  margin-bottom: 20px;
  background-color: #ffffff; }
  .detail-top .media, .detail-top .info {
    display: block;
    width: 100%; }
  .detail-top .media {
    padding: 10px 0; }
    .detail-top .media-swiper .media-main {
      margin: 0 auto;
      width: calc(100% - 20px);
      max-width: calc(480px + 20px);
      text-align: center;
      background: #F4F4F4; }
      .detail-top .media-swiper .media-main .swiper-slide {
        width: 100%;
        cursor: pointer; }
        .detail-top .media-swiper .media-main .swiper-slide img {
          width: 100%;
          max-width: 480px;
          max-height: 480px;
          border-radius: 4px; }
    .detail-top .media-swiper .thumbs {
      position: relative;
      margin: 0 auto 10px;
      text-align: center;
      width: calc(100% - 20px);
      max-width: 480px; }
      .detail-top .media-swiper .thumbs .media-thumbs {
        display: inline-block;
        margin-left: 6px;
        padding-top: 10px;
        width: calc(100% - 34px - 6px); }
        .detail-top .media-swiper .thumbs .media-thumbs .swiper-slide {
          position: relative;
          padding-top: calc((100% - 17px - 6px * 5) / 4.85);
          height: 0;
          background: #F4F4F4;
          border: 2px solid transparent; }
          .detail-top .media-swiper .thumbs .media-thumbs .swiper-slide img {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            width: 100%;
            border-radius: 4px; }
          .detail-top .media-swiper .thumbs .media-thumbs .swiper-slide.active {
            position: relative;
            border-color: #FF9600;
            border-radius: 2px; }
            .detail-top .media-swiper .thumbs .media-thumbs .swiper-slide.active img {
              border-radius: 0; }
            .detail-top .media-swiper .thumbs .media-thumbs .swiper-slide.active:before {
              display: inline-block;
              width: 0;
              height: 0;
              border: 8px solid transparent;
              overflow: hidden;
              content: '';
              position: absolute;
              top: 0;
              left: 50%;
              -webkit-transform: translate(-50%, -100%);
              -moz-transform: translate(-50%, -100%);
              -ms-transform: translate(-50%, -100%);
              -o-transform: translate(-50%, -100%);
              transform: translate(-50%, -100%);
              border-top: 0;
              border-bottom-color: #FF9600; }
      .detail-top .media-swiper .thumbs .media-btn-prev, .detail-top .media-swiper .thumbs .media-btn-next {
        display: inline-block;
        position: absolute;
        top: 10px;
        height: calc(100% - 15px);
        width: 17px;
        background: #F4F4F4;
        border-radius: 4px;
        cursor: pointer; }
        .detail-top .media-swiper .thumbs .media-btn-prev.swiper-button-disabled, .detail-top .media-swiper .thumbs .media-btn-next.swiper-button-disabled {
          cursor: default; }
        .detail-top .media-swiper .thumbs .media-btn-prev i, .detail-top .media-swiper .thumbs .media-btn-next i {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          margin: auto; }
      .detail-top .media-swiper .thumbs .media-btn-prev {
        left: 0; }
    .detail-top .media .tools {
      display: none;
      margin: 0 auto;
      width: calc(100% - 20px);
      max-width: 480px;
      font-size: 12px;
      color: #bbbbbb; }
      .detail-top .media .tools .up > div {
        display: inline-block;
        margin-right: 25px;
        cursor: pointer; }
        .detail-top .media .tools .up > div > i {
          margin-right: 5px;
          vertical-align: initial; }
        .detail-top .media .tools .up > div.share {
          position: relative; }
          .detail-top .media .tools .up > div.share:hover .hover-pop {
            padding: 15px;
            display: block;
            text-align: center; }
            .detail-top .media .tools .up > div.share:hover .hover-pop > div, .detail-top .media .tools .up > div.share:hover .hover-pop > ul, .detail-top .media .tools .up > div.share:hover .hover-pop::before {
              background: #fff;
              border: solid 1px #EFF0EF; }
            .detail-top .media .tools .up > div.share:hover .hover-pop > div, .detail-top .media .tools .up > div.share:hover .hover-pop > ul {
              width: 230px;
              padding: 15px; }
            .detail-top .media .tools .up > div.share:hover .hover-pop > div a {
              display: inline-block;
              width: 34px;
              margin: 0 5px;
              line-height: 34px;
              text-align: center;
              vertical-align: middle;
              background: #f4f4f4;
              border-radius: 50%; }
              .detail-top .media .tools .up > div.share:hover .hover-pop > div a:hover {
                background: #FF6F00; }
              .detail-top .media .tools .up > div.share:hover .hover-pop > div a i {
                vertical-align: sub; }
      .detail-top .media .tools .report {
        cursor: pointer; }
  .detail-top .info {
    padding: 10px; }
    .detail-top .info .head {
      flex-wrap: nowrap; }
      .detail-top .info .head .info-tit {
        width: 100%;
        line-height: initial; }
        .detail-top .info .head .info-tit .tit {
          margin-bottom: 5px;
          font-size: 16px;
          color: #282828;
          word-break: break-all; }
        .detail-top .info .head .info-tit .sec-tit {
          margin-bottom: 3px;
          font-size: 14px; }
        .detail-top .info .head .info-tit .tags {
          display: none; }
          .detail-top .info .head .info-tit .tags a, .detail-top .info .head .info-tit .tags span {
            display: inline-block;
            margin: 2px 4px 2px 0;
            padding: 3px 6px;
            font-size: 12px;
            color: #999999;
            background: #F4F4F4;
            border-radius: 2px; }
      .detail-top .info .head .seller {
        display: none;
        padding: 10px;
        margin-left: 30px;
        text-align: center;
        align-self: flex-start;
        color: #B8B8B8;
        border: 1px solid rgba(255, 150, 0, 0.1); }
        .detail-top .info .head .seller div {
          width: 100%;
          max-width: 6em;
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis; }
        .detail-top .info .head .seller i + div {
          color: #FF9600; }
        .detail-top .info .head .seller .symbolIcon {
          font-size: 25px; }
    .detail-top .info .seller-info {
      position: relative;
      display: none;
      margin-bottom: 10px;
      padding: 10px;
      color: #666666;
      background: #F4F4F4;
      border-radius: 4px;
      font-size: 12px; }
      .detail-top .info .seller-info .item-info {
        width: calc(100% - 150px); }
        .detail-top .info .seller-info .item-info > div {
          line-height: 100%;
          white-space: nowrap; }
          .detail-top .info .seller-info .item-info > div:not(:last-child) {
            margin-bottom: 15px; }
          .detail-top .info .seller-info .item-info > div span, .detail-top .info .seller-info .item-info > div a {
            display: inline-block;
            vertical-align: middle; }
            .detail-top .info .seller-info .item-info > div span.light, .detail-top .info .seller-info .item-info > div a.light {
              color: #bbbbbb; }
          .detail-top .info .seller-info .item-info > div a {
            margin-right: 10px; }
          .detail-top .info .seller-info .item-info > div strong {
            color: #FF6F00;
            font-size: 16px; }
      .detail-top .info .seller-info .account-info {
        padding-left: 10px;
        border-left: 1px solid rgba(0, 0, 0, 0.05); }
        .detail-top .info .seller-info .account-info > div {
          display: inline-block;
          text-align: center; }
          .detail-top .info .seller-info .account-info > div:first-child {
            margin-right: 10px; }
          .detail-top .info .seller-info .account-info > div div:first-child {
            line-height: 100%;
            font-size: 14px;
            color: #333333;
            font-weight: 700; }
        @media only screen and (min-width: 768px) {
          .detail-top .info .seller-info .account-info > div div:first-child {
            font-size: 16px; } }
        @media only screen and (min-width: 1460px) {
          .detail-top .info .seller-info .account-info {
            margin-top: 15px; }
            .detail-top .info .seller-info .account-info > div div:first-child {
              font-size: 18px;
              margin-bottom: 10px; } }
    .detail-top .info .comm-form {
      margin: 10px 0; }
      .detail-top .info .comm-form .form-item-kv > label {
        margin-right: 10px;
        text-align: left;
        color: #666666; }
      .detail-top .info .comm-form .form-item-after {
        display: block;
        margin: -20px 0 0 82px; }
        .detail-top .info .comm-form .form-item-after .total-hours {
          color: #FF6F00; }
      .detail-top .info .comm-form .store-coupons {
        margin-bottom: 5px; }
        .detail-top .info .comm-form .store-coupons > label {
          line-height: 30px; }
        .detail-top .info .comm-form .store-coupons .form-item {
          line-height: 30px; }
          .detail-top .info .comm-form .store-coupons .form-item > a {
            display: inline-block;
            min-width: 70px;
            margin-right: 12px;
            padding: 0 5px;
            line-height: 20px;
            vertical-align: middle;
            text-align: center;
            font-size: 12px;
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            background: url("/static/img/coupon-bg.png") no-repeat center center/100% 100%; }
      .detail-top .info .comm-form .activities span {
        position: relative;
        display: inline-block;
        margin-right: 12px;
        padding: 0 18px;
        line-height: 24px;
        vertical-align: middle;
        text-align: center;
        color: #FF4800;
        font-size: 12px;
        overflow: hidden;
        cursor: pointer;
        user-select: none;
        border-radius: 4px;
        border: 1px solid #ff943f; }
        .detail-top .info .comm-form .activities span.on {
          background: rgba(255, 221, 213, 0.88); }
          .detail-top .info .comm-form .activities span.on::after {
            content: '';
            position: absolute;
            right: -1px;
            bottom: -1px;
            display: block;
            width: 22px;
            height: 22px;
            background: url("/static/img/activity-selected.png") no-repeat center/cover; }
        .detail-top .info .comm-form .activities span.disabled {
          cursor: not-allowed;
          color: #ABABAB;
          border-color: #D6D6D6;
          background: rgba(234, 233, 233, 0.88); }
      .detail-top .info .comm-form .rent-type, .detail-top .info .comm-form .rent-time {
        padding: 0;
        margin: 0 0 16px 0;
        font-size: 0; }
        .detail-top .info .comm-form .rent-type li, .detail-top .info .comm-form .rent-time li {
          position: relative;
          display: inline-block;
          width: 20%;
          /* max-width: 180px; */
          padding: 10px;
          margin-right: 2%;
          line-height: 100%;
          vertical-align: top;
          font-size: 12px;
          border: 1px solid rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          cursor: pointer; }
          .detail-top .info .comm-form .rent-type li .type, .detail-top .info .comm-form .rent-time li .type {
            font-size: 12px;
            color: #333333;
            font-weight: 700; }
          .detail-top .info .comm-form .rent-type li .unit, .detail-top .info .comm-form .rent-time li .unit {
            display: none;
            margin: 8px 0;
            color: #bbbbbb; }
          .detail-top .info .comm-form .rent-type li .fee, .detail-top .info .comm-form .rent-time li .fee {
            font-size: 12px;
            color: #FF6F00;
            font-weight: 700; }
            .detail-top .info .comm-form .rent-type li .fee span, .detail-top .info .comm-form .rent-time li .fee span {
              font-size: 20px;
              line-height: 100%; }
          .detail-top .info .comm-form .rent-type li.time, .detail-top .info .comm-form .rent-time li.time {
            font-size: 12px; }
            .detail-top .info .comm-form .rent-type li.time div:first-child, .detail-top .info .comm-form .rent-time li.time div:first-child {
              margin-bottom: 10px;
              font-weight: 700;
              color: #333333; }
            .detail-top .info .comm-form .rent-type li.time div:nth-child(2), .detail-top .info .comm-form .rent-time li.time div:nth-child(2) {
              color: #666666; }
          .detail-top .info .comm-form .rent-type li:last-child, .detail-top .info .comm-form .rent-time li:last-child {
            margin-right: 0; }
          .detail-top .info .comm-form .rent-type li:hover, .detail-top .info .comm-form .rent-time li:hover {
            position: relative; }
            .detail-top .info .comm-form .rent-type li:hover .hover-pop, .detail-top .info .comm-form .rent-time li:hover .hover-pop {
              display: block;
              padding: 15px; }
              .detail-top .info .comm-form .rent-type li:hover .hover-pop > div, .detail-top .info .comm-form .rent-type li:hover .hover-pop > ul, .detail-top .info .comm-form .rent-type li:hover .hover-pop::before, .detail-top .info .comm-form .rent-time li:hover .hover-pop > div, .detail-top .info .comm-form .rent-time li:hover .hover-pop > ul, .detail-top .info .comm-form .rent-time li:hover .hover-pop::before {
                background: #fff;
                border: solid 1px rgba(0, 0, 0, 0.1); }
              .detail-top .info .comm-form .rent-type li:hover .hover-pop > div, .detail-top .info .comm-form .rent-type li:hover .hover-pop > ul, .detail-top .info .comm-form .rent-time li:hover .hover-pop > div, .detail-top .info .comm-form .rent-time li:hover .hover-pop > ul {
                width: 164px;
                padding: 15px; }
              .detail-top .info .comm-form .rent-type li:hover .hover-pop > .pop-box, .detail-top .info .comm-form .rent-time li:hover .hover-pop > .pop-box {
                border-radius: 2px;
                padding: 11px 18px;
                white-space: nowrap; }
          .detail-top .info .comm-form .rent-type li.on, .detail-top .info .comm-form .rent-time li.on {
            border-color: #ff9600; }
            .detail-top .info .comm-form .rent-type li.on .type, .detail-top .info .comm-form .rent-type li.on .unit, .detail-top .info .comm-form .rent-time li.on .type, .detail-top .info .comm-form .rent-time li.on .unit {
              color: #FF9600; }
            .detail-top .info .comm-form .rent-type li.on.time div, .detail-top .info .comm-form .rent-time li.on.time div {
              color: #FF9600; }
            .detail-top .info .comm-form .rent-type li.on::before, .detail-top .info .comm-form .rent-time li.on::before {
              content: '';
              position: absolute;
              display: block;
              right: -1px;
              bottom: -1px;
              width: 24px;
              height: 24px;
              background: url(/static/img/icon-selected.png) no-repeat center center;
              border-bottom-right-radius: 4px; }
          .detail-top .info .comm-form .rent-type li.disabled, .detail-top .info .comm-form .rent-time li.disabled {
            opacity: .5; }
            .detail-top .info .comm-form .rent-type li.disabled:hover .hover-pop, .detail-top .info .comm-form .rent-time li.disabled:hover .hover-pop {
              display: none !important; }
      .detail-top .info .comm-form .rent-timeQuantum input, .detail-top .info .comm-form .rent-startTime input {
        padding-left: 10px;
        color: #666666;
        background: #fff;
        cursor: pointer;
        font-size: 12px;
        user-select: none; }
      .detail-top .info .comm-form .rent-timeQuantum .drop-menu {
        max-height: 156px; }
      .detail-top .info .comm-form .rent-sure {
        padding: 0;
        border: none;
        outline: none;
        background: none;
        cursor: pointer;
        display: inline-block;
        height: auto;
        max-width: 999px;
        padding: 16px 94px;
        text-align: center;
        line-height: 100%;
        white-space: nowrap;
        border-radius: 2px;
        color: #040404;
        font-size: 14px;
        background: #F7CD46;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 2;
        width: 100%;
        margin-top: 20px;
        font-size: 16px;
        border-radius: 0;
        font-weight: bold; }
        .detail-top .info .comm-form .rent-sure.ghost {
          color: #FF6F00;
          background: transparent;
          box-shadow: inset 0 0 0 1px #FF6F00; }
        .detail-top .info .comm-form .rent-sure:disabled {
          border: none;
          cursor: not-allowed;
          color: #ffffff;
          background: #CCCCCC; }
  .detail-top .comm-form .form-item {
    margin-bottom: 14px; }
  @media only screen and (min-width: 424px) {
    .detail-top .media-swiper .thumbs .media-thumbs .swiper-slide {
      padding-top: calc((100% - 17px - 6px * 3) / 3.85); } }
  @media only screen and (min-width: 768px) {
    .detail-top .media {
      display: inline-block;
      width: 37%;
      vertical-align: top; }
      .detail-top .media-swiper .thumbs .media-thumbs .swiper-slide {
        padding-top: calc((100% - 17px - 6px * 5) / 4.85); }
      .detail-top .media .tools {
        display: block; }
    .detail-top .info {
      display: inline-block;
      width: calc(100% - 37% - 10px); }
      .detail-top .info .head .info-tit .tit {
        font-size: 18px; }
      .detail-top .info .head .info-tit .tags {
        display: block; }
      .detail-top .info .seller-info {
        display: block; }
      .detail-top .info .comm-form .form-item-after {
        display: inline-block;
        margin: 0; }
      .detail-top .info .comm-form .rent-type li .unit, .detail-top .info .comm-form .rent-time li .unit {
        display: block; }
      .detail-top .info .comm-form .rent-sure {
        position: static;
        width: auto;
        border-radius: 4px; } }
  @media only screen and (min-width: 990px) {
    .detail-top .media {
      padding: 30px 0 30px 30px; }
    .detail-top .info {
      padding: 30px 30px 30px 20px; }
      .detail-top .info .head .seller {
        display: block; }
      .detail-top .info .head .info-tit .tit {
        margin-bottom: 10px; }
      .detail-top .info .head .info-tit .sec-tit {
        line-height: 100%;
        margin-bottom: 10px; }
      .detail-top .info .head .info-tit .tags {
        line-height: 100%;
        margin-bottom: 20px; }
        .detail-top .info .head .info-tit .tags span {
          margin-right: 8px; }
      .detail-top .info .seller-info {
        padding: 18px; }
        .detail-top .info .seller-info .item-info {
          width: calc(100% - 215px); }
        .detail-top .info .seller-info .account-info {
          padding-left: 36px; }
          .detail-top .info .seller-info .account-info > div:first-child {
            margin-right: 36px; }
      .detail-top .info .comm-form .rent-type li, .detail-top .info .comm-form .rent-time li {
        display: inline-flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-around;
        padding: 16px 0; }
        .detail-top .info .comm-form .rent-type li .type, .detail-top .info .comm-form .rent-time li .type {
          font-size: 16px; }
        .detail-top .info .comm-form .rent-type li .fee, .detail-top .info .comm-form .rent-time li .fee {
          font-size: 14px; }
          .detail-top .info .comm-form .rent-type li .fee span, .detail-top .info .comm-form .rent-time li .fee span {
            font-size: 28px; }
        .detail-top .info .comm-form .rent-type li.time, .detail-top .info .comm-form .rent-time li.time {
          justify-content: flex-start;
          padding: 12px 0 12px 20px;
          font-size: 14px; } }

.detail-content {
  margin-bottom: 20px;
  padding-bottom: 30px;
  background-color: #ffffff; }
  .detail-content .tab-head {
    margin-bottom: 20px;
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
    font-size: 0;
    border-bottom: 1px solid #EFF0EF; }
    .detail-content .tab-head li {
      display: inline-block;
      margin-right: 30px;
      height: 100%;
      font-size: 16px;
      color: #999999;
      cursor: pointer; }
      .detail-content .tab-head li.on {
        color: #333333;
        border-bottom: 2px solid #333333; }
      @media only screen and (min-width: 768px) {
        .detail-content .tab-head li {
          margin-right: 59px; } }
    .detail-content .tab-head.account-items-switch {
      padding: 0; }
  .detail-content .mainTab-contents {
    padding: 0 10px; }
    .detail-content .mainTab-contents ul {
      margin: 0;
      padding: 0;
      list-style: none; }
    .detail-content .mainTab-contents > ul li {
      display: inline-block;
      font-size: 12px; }
    .detail-content .mainTab-contents .tit {
      position: relative;
      padding-left: 20px;
      margin-bottom: 20px;
      font-size: 20px;
      color: #333333;
      font-weight: 700; }
      .detail-content .mainTab-contents .tit::before {
        content: '';
        position: absolute;
        width: 10px;
        height: 10px;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        background: url("/static/img/detail-section-tit-bg.png") no-repeat center center/contain; }
    .detail-content .mainTab-contents .gameDescribe-img {
      width: 100%;
      margin-bottom: 50px; }
    .detail-content .mainTab-contents .statistics {
      font-size: 0;
      margin: -10px -10px 20px -10px; }
      .detail-content .mainTab-contents .statistics > li {
        display: inline-block;
        width: 50%;
        padding: 10px; }
        .detail-content .mainTab-contents .statistics > li .statistics-item {
          position: relative;
          padding: 12% 10%;
          background-color: #F4F4F4;
          border: 1px solid rgba(0, 0, 0, 0.06);
          border-radius: 4px; }
          .detail-content .mainTab-contents .statistics > li .statistics-item .item-txt {
            position: relative;
            z-index: 1; }
            .detail-content .mainTab-contents .statistics > li .statistics-item .item-txt > div:nth-child(1) {
              color: #666666;
              font-size: 18px;
              font-weight: 700; }
            .detail-content .mainTab-contents .statistics > li .statistics-item .item-txt > div:nth-child(2) {
              color: #999999;
              font-size: 12px; }
          .detail-content .mainTab-contents .statistics > li .statistics-item > img {
            position: absolute;
            right: 10%;
            top: 50%;
            height: 50px;
            transform: translateY(-50%); }
    .detail-content .mainTab-contents .account-desc {
      margin-bottom: 30px;
      font-size: 13px; }
      .detail-content .mainTab-contents .account-desc p {
        display: inline-block;
        min-width: 50%;
        min-height: 50px;
        margin-bottom: 0;
        padding: 12px;
        line-height: 16px;
        color: #474747;
        border-radius: 4px;
        background: rgba(253, 246, 240, 0.5);
        border: 1px solid rgba(255, 183, 80, 0.5); }
      .detail-content .mainTab-contents .account-desc img {
        display: block;
        max-width: 100%;
        margin: 10px auto 0; }
    .detail-content .mainTab-contents .account-info {
      margin-bottom: 30px;
      font-size: 12px; }
      .detail-content .mainTab-contents .account-info > span {
        padding: 0;
        margin-top: 20px;
        margin-bottom: 10px;
        margin-right: 50px;
        color: #666666; }
    .detail-content .mainTab-contents .account-items {
      margin-bottom: 20px; }
      .detail-content .mainTab-contents .account-items .tab-head {
        margin-bottom: 30px; }
      .detail-content .mainTab-contents .account-items .tab-contents .filter-box {
        margin-bottom: 20px; }
        .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul {
          line-height: 30px; }
          .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul li {
            display: inline-block;
            margin-right: 10px;
            font-size: 12px;
            color: #555555;
            cursor: pointer;
            vertical-align: bottom; }
            @media only screen and (min-width: 1460px) {
              .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul li {
                margin-right: 30px; } }
            .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul li::before, .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul li span {
              display: inline-block;
              vertical-align: middle; }
            .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul li:hover span {
              color: #FF6F00; }
            .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul li::before {
              content: '';
              width: 12px;
              height: 12px;
              margin-right: 5px;
              border-radius: 2px;
              background: #ffffff;
              border: solid 2px #ffffff;
              box-shadow: 0 0 1px 1px #CCCCCC; }
            .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul li.on {
              color: #333333; }
              .detail-content .mainTab-contents .account-items .tab-contents .filter-box ul li.on::before {
                background: #FF6F00;
                box-shadow: 0 0 1px 1px #FF6F00; }
        .detail-content .mainTab-contents .account-items .tab-contents .filter-box .search {
          display: inline-block;
          width: 15%;
          height: 30px;
          min-width: 140px;
          overflow: hidden;
          font-size: 0;
          white-space: nowrap;
          background: #fff;
          border-radius: 4px;
          border: 1px solid rgba(0, 0, 0, 0.05); }
          .detail-content .mainTab-contents .account-items .tab-contents .filter-box .search input, .detail-content .mainTab-contents .account-items .tab-contents .filter-box .search button {
            display: inline-block;
            height: 100%;
            vertical-align: middle; }
          .detail-content .mainTab-contents .account-items .tab-contents .filter-box .search input {
            width: calc(100% - 36px);
            padding: 0 10px;
            font-size: 12px;
            background: none;
            border: none; }
            .detail-content .mainTab-contents .account-items .tab-contents .filter-box .search input::-webkit-input-placeholder {
              color: #bbbbbb; }
          .detail-content .mainTab-contents .account-items .tab-contents .filter-box .search button {
            padding: 0;
            border: none;
            outline: none;
            background: none;
            cursor: pointer;
            width: 36px;
            text-align: center; }
      .detail-content .mainTab-contents .account-items .tab-contents .result-content .items {
        margin: 0 -.8%;
        font-size: 0; }
        .detail-content .mainTab-contents .account-items .tab-contents .result-content .items li {
          display: inline-block;
          padding: 1%;
          width: 33.33%; }
          .detail-content .mainTab-contents .account-items .tab-contents .result-content .items li > div img {
            display: block;
            width: 100%; }
          .detail-content .mainTab-contents .account-items .tab-contents .result-content .items li > div .title {
            margin-top: 10px;
            color: #474747;
            font-size: 12px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis; }
            .detail-content .mainTab-contents .account-items .tab-contents .result-content .items li > div .title strong {
              color: #FF6F00;
              font-weight: 400; }
      .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items {
        font-size: 0;
        margin: 0 -0.4%; }
        .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li {
          display: inline-block;
          width: 16.6%;
          padding: 0.5%;
          position: relative; }
          .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li > .cover {
            width: 100%;
            position: relative; }
            .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li > .cover img {
              display: block;
              width: 100%; }
          .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .skin-count {
            position: absolute;
            bottom: 0;
            right: 0;
            font-size: 0; }
            .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .skin-count > i {
              text-align: center;
              line-height: 16px;
              font-size: 12px; }
          .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .title {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 12px; }
          .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .hover-pop {
            padding: 0;
            overflow: hidden;
            display: block;
            box-sizing: border-box; }
            .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .hover-pop > div {
              width: 0; }
              .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .hover-pop > div .hover-tit {
                font-size: 15px;
                color: #474747;
                font-weight: bold;
                margin-bottom: 14px;
                padding-left: 5px; }
              .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .hover-pop > div .items {
                max-height: 370px;
                overflow-y: auto; }
                .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .hover-pop > div .items > li {
                  width: 50%; }
                  .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li .hover-pop > div .items > li > img {
                    width: 100%; }
        .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items .filter-show {
          display: inline-block; }
      @media only screen and (min-width: 768px) {
        .detail-content .mainTab-contents .account-items .tab-contents .result-content .items li {
          width: 20%; }
        .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li {
          width: 12.5%; }
          .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li:hover .hover-pop {
            display: block;
            transition: all 0.1s 0.3s;
            padding: 15px; }
            .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li:hover .hover-pop > div, .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li:hover .hover-pop > ul, .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li:hover .hover-pop::before {
              background: #fff;
              border: solid 1px #EFF0EF; }
            .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li:hover .hover-pop > div, .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li:hover .hover-pop > ul {
              width: 370px;
              padding: 15px; }
            .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li:hover .hover-pop > div {
              transition: all 0.1s 0.3s; } }
      @media only screen and (min-width: 990px) {
        .detail-content .mainTab-contents .account-items .tab-contents .result-content .items li {
          width: 12.5%; }
        .detail-content .mainTab-contents .account-items .tab-contents .result-content .hoverPop-items > li {
          width: 7.14%; } }
      .detail-content .mainTab-contents .account-items .tab-contents li {
        display: inline-block; }
        .detail-content .mainTab-contents .account-items .tab-contents li .title {
          line-height: normal;
          font-size: 12px;
          color: #474747;
          text-align: center;
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis; }
    .detail-content .mainTab-contents .statement, .detail-content .mainTab-contents .guide {
      color: #474747; }
      .detail-content .mainTab-contents .statement > p, .detail-content .mainTab-contents .guide > p {
        margin-bottom: 18px;
        font-size: 13px; }
      .detail-content .mainTab-contents .statement > ul, .detail-content .mainTab-contents .guide > ul {
        margin: 0;
        padding: 0; }
        .detail-content .mainTab-contents .statement > ul li, .detail-content .mainTab-contents .guide > ul li {
          list-style: none;
          margin-bottom: 9px;
          font-size: 12px; }
          .detail-content .mainTab-contents .statement > ul li a, .detail-content .mainTab-contents .statement > ul li strong, .detail-content .mainTab-contents .statement > ul li span, .detail-content .mainTab-contents .guide > ul li a, .detail-content .mainTab-contents .guide > ul li strong, .detail-content .mainTab-contents .guide > ul li span {
            color: #FF9600; }
          .detail-content .mainTab-contents .statement > ul li i, .detail-content .mainTab-contents .guide > ul li i {
            margin-right: 6px; }
    .detail-content .mainTab-contents #guide-swiper {
      padding-bottom: 30px; }
      .detail-content .mainTab-contents #guide-swiper .swiper-pagination {
        position: absolute;
        bottom: 4px;
        display: block;
        width: 100%;
        height: 6px;
        border-radius: 2px;
        background: #f2f2f2; }
        .detail-content .mainTab-contents #guide-swiper .swiper-pagination-progressbar {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          transform: scale(0);
          transform-origin: left top;
          background: linear-gradient(90deg, #ff9600, #ff5500); }
      .detail-content .mainTab-contents #guide-swiper .swiper-button-prev, .detail-content .mainTab-contents #guide-swiper .swiper-button-next {
        padding: 0;
        border: none;
        outline: none;
        background: none;
        cursor: pointer;
        display: none;
        position: absolute;
        top: 50%;
        width: 38px;
        line-height: 70px;
        text-align: center;
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.3);
        transform: translateY(-50%); }
        .detail-content .mainTab-contents #guide-swiper .swiper-button-prev:not(.swiper-button-disabled), .detail-content .mainTab-contents #guide-swiper .swiper-button-next:not(.swiper-button-disabled) {
          display: block; }
        .detail-content .mainTab-contents #guide-swiper .swiper-button-prev.swiper-button-prev, .detail-content .mainTab-contents #guide-swiper .swiper-button-next.swiper-button-prev {
          left: 0; }
        .detail-content .mainTab-contents #guide-swiper .swiper-button-prev.swiper-button-next, .detail-content .mainTab-contents #guide-swiper .swiper-button-next.swiper-button-next {
          right: 0; }
    .detail-content .mainTab-contents .cf .account-items img {
      height: 85px;
      opacity: 0; }
    .detail-content .mainTab-contents .lol .account-items img {
      height: 85px;
      opacity: 0; }
    .detail-content .mainTab-contents .pubg .result-content .items > li {
      width: 8.3%; }
      .detail-content .mainTab-contents .pubg .result-content .items > li img {
        background: radial-gradient(circle, #272727, #595959); }
    .detail-content .mainTab-contents .glory .account-items img {
      height: 85px;
      opacity: 0; }
  @media only screen and (min-width: 768px) {
    .detail-content .mainTab-contents .statistics > li {
      width: 25%;
      max-width: 240px; }
      .detail-content .mainTab-contents .statistics > li .statistics-item .item-txt > div:nth-child(2) {
        margin-bottom: 5%; }
      .detail-content .mainTab-contents .statistics > li .statistics-item > img {
        height: auto; }
    .detail-content .mainTab-contents .account-items {
      display: block; }
      .detail-content .mainTab-contents .account-items .tit {
        margin-bottom: 6px; }
      .detail-content .mainTab-contents .account-items .item-head {
        display: block; }
    .detail-content .mainTab-contents .glory .statistics > li {
      width: 33.33%; } }
  @media only screen and (min-width: 990px) {
    .detail-content {
      margin-bottom: 45px;
      padding-bottom: 70px; }
      .detail-content .mainTab-head {
        margin-bottom: 30px;
        padding: 0 30px;
        height: 52px;
        line-height: 52px; }
      .detail-content .mainTab-contents {
        padding: 0 30px; }
        .detail-content .mainTab-contents .account-items .item-head .tab-head li {
          margin-right: 38px; } }

.recommend .section-tit {
  margin-bottom: 10px; }
@media only screen and (min-width: 990px) {
  .recommend .section-tit {
    margin-bottom: 20px; } }

.template.report-box .content {
  padding: 30px 30px 70px; }
  .template.report-box .content > p {
    font-size: 14px;
    color: #999999; }
  .template.report-box .content > div {
    font-size: 0; }
    .template.report-box .content > div span {
      display: inline-block;
      margin-right: 11px;
      width: 80px;
      height: 30px;
      line-height: 28px;
      text-align: center;
      font-size: 14px;
      color: #666666;
      background: rgba(18, 20, 23, 0.04);
      border: 1px solid rgba(40, 40, 40, 0.05);
      cursor: pointer; }
      .template.report-box .content > div span.on {
        color: #333333;
        background: #F7CD46;
        border-color: #F7CD46; }
.template.report-box .buttons {
  margin-bottom: 40px; }
  .template.report-box .buttons button {
    display: inline-block;
    height: auto;
    max-width: 999px;
    padding: 12px 45px;
    text-align: center;
    line-height: 100%;
    white-space: nowrap;
    border-radius: 2px;
    color: #040404;
    font-size: 14px;
    background: #F7CD46;
    font-size: 14px;
    line-height: 100%;
    color: #333333; }
    .template.report-box .buttons button.ghost {
      color: #FF6F00;
      background: transparent;
      box-shadow: inset 0 0 0 1px #FF6F00; }
    .template.report-box .buttons button:disabled {
      border: none;
      cursor: not-allowed;
      color: #ffffff;
      background: #CCCCCC; }
.template.timePicker-box {
  height: 305px; }
  .template.timePicker-box .tab-head {
    margin-bottom: 0;
    padding: 0 20px;
    height: 57px;
    list-style: none;
    border-bottom: 1px solid #EFF0EF; }
    .template.timePicker-box .tab-head li {
      cursor: pointer; }
      .template.timePicker-box .tab-head li span {
        font-size: 12px;
        color: #999999; }
        .template.timePicker-box .tab-head li span:first-child {
          font-size: 18px;
          color: #717171; }
          @media only screen and (min-width: 768px) {
            .template.timePicker-box .tab-head li span:first-child {
              font-size: 20px; } }
      .template.timePicker-box .tab-head li.on span {
        color: #333333; }
        .template.timePicker-box .tab-head li.on span:first-child {
          color: #FF6F00; }
  .template.timePicker-box .tab-content {
    height: 220px;
    margin-bottom: 10px; }
    .template.timePicker-box .tab-content .calendar {
      padding: 20px 0;
      font-size: 0; }
      .template.timePicker-box .tab-content .calendar .hour, .template.timePicker-box .tab-content .calendar .minute {
        position: relative;
        display: inline-block;
        width: 50%;
        vertical-align: top; }
        .template.timePicker-box .tab-content .calendar .hour::before, .template.timePicker-box .tab-content .calendar .hour::after, .template.timePicker-box .tab-content .calendar .minute::before, .template.timePicker-box .tab-content .calendar .minute::after {
          content: '';
          position: absolute;
          display: block;
          left: 0;
          pointer-events: none;
          width: 100%;
          height: 14px; }
        .template.timePicker-box .tab-content .calendar .hour::before, .template.timePicker-box .tab-content .calendar .minute::before {
          top: 36px;
          background: linear-gradient(white, rgba(255, 255, 255, 0.1)); }
        .template.timePicker-box .tab-content .calendar .hour::after, .template.timePicker-box .tab-content .calendar .minute::after {
          bottom: 0;
          background: linear-gradient(rgba(255, 255, 255, 0.1), white); }
        .template.timePicker-box .tab-content .calendar .hour .calendar-tit, .template.timePicker-box .tab-content .calendar .minute .calendar-tit {
          display: block;
          margin-bottom: 25px;
          font-size: 12px;
          line-height: 100%;
          color: #999999; }
          .template.timePicker-box .tab-content .calendar .hour .calendar-tit strong, .template.timePicker-box .tab-content .calendar .minute .calendar-tit strong {
            font-size: 14px;
            color: #333333; }
        .template.timePicker-box .tab-content .calendar .hour > ul, .template.timePicker-box .tab-content .calendar .minute > ul {
          height: 160px;
          margin: 0;
          padding: 0;
          list-style: none;
          overflow-y: scroll; }
          .template.timePicker-box .tab-content .calendar .hour > ul::-webkit-scrollbar, .template.timePicker-box .tab-content .calendar .minute > ul::-webkit-scrollbar {
            width: 4px;
            height: 4px;
            border-radius: 2px; }
          .template.timePicker-box .tab-content .calendar .hour > ul li, .template.timePicker-box .tab-content .calendar .minute > ul li {
            display: block;
            width: 54px;
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            color: #818181;
            text-align: center;
            border: 1px solid transparent;
            cursor: pointer; }
            .template.timePicker-box .tab-content .calendar .hour > ul li.disabled, .template.timePicker-box .tab-content .calendar .minute > ul li.disabled {
              color: rgba(187, 187, 187, 0.5);
              cursor: not-allowed; }
            .template.timePicker-box .tab-content .calendar .hour > ul li:not(.disabled):hover, .template.timePicker-box .tab-content .calendar .minute > ul li:not(.disabled):hover {
              color: #FF6F00; }
            .template.timePicker-box .tab-content .calendar .hour > ul li:not(.disabled).on, .template.timePicker-box .tab-content .calendar .minute > ul li:not(.disabled).on {
              position: relative;
              z-index: 1;
              font-size: 18px;
              color: #ffffff;
              background: #FF9600; }
      .template.timePicker-box .tab-content .calendar .hour {
        text-align: left;
        border-right: 1px solid #EFF0EF; }
        .template.timePicker-box .tab-content .calendar .hour .calendar-tit {
          padding-left: 50px; }
        .template.timePicker-box .tab-content .calendar .hour ul {
          padding-left: 50px; }
      .template.timePicker-box .tab-content .calendar .minute {
        text-align: right; }
        .template.timePicker-box .tab-content .calendar .minute .calendar-tit {
          padding-right: 50px; }
        .template.timePicker-box .tab-content .calendar .minute ul {
          display: inline-block;
          padding-right: 50px; }
  @media only screen and (min-width: 990px) {
    .template.timePicker-box {
      width: 435px;
      background: #ffffff;
      border-radius: 4px;
      border: solid 1px #FF9600; } }
.template.timeout-box .content {
  padding: 50px 0;
  text-align: center; }
  .template.timeout-box .content > div {
    line-height: 100%;
    font-size: 14px;
    color: #666666; }
    .template.timeout-box .content > div:first-child {
      margin-bottom: 18px;
      font-size: 20px;
      color: #FF9600; }
      .template.timeout-box .content > div:first-child > span {
        display: inline-block;
        vertical-align: middle; }
      .template.timeout-box .content > div:first-child i {
        margin-right: 12px; }
.template.timeout-box .buttons button {
  display: inline-block;
  height: auto;
  max-width: 999px;
  padding: 12px 45px;
  text-align: center;
  line-height: 100%;
  white-space: nowrap;
  border-radius: 2px;
  color: #040404;
  font-size: 14px;
  background: #F7CD46;
  font-size: 14px;
  line-height: 100%;
  color: #333333; }
  .template.timeout-box .buttons button.ghost {
    color: #FF6F00;
    background: transparent;
    box-shadow: inset 0 0 0 1px #FF6F00; }
  .template.timeout-box .buttons button:disabled {
    border: none;
    cursor: not-allowed;
    color: #ffffff;
    background: #CCCCCC; }
.template.timeout-box .buttons span {
  line-height: 100%;
  font-size: 12px;
  color: #bbbbbb; }
.template.sureOrder-box .content {
  padding-top: 18px; }
  .template.sureOrder-box .content .comm-form .tit {
    width: 100%;
    margin-bottom: 5px;
    font-size: 16px;
    color: #282828;
    font-weight: 700;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; }
  .template.sureOrder-box .content .comm-form .sec-tit {
    margin-bottom: 10px;
    font-size: 14px;
    color: #333333; }
  .template.sureOrder-box .content .comm-form .form-item-kv > label {
    margin-right: 10px;
    text-align: left; }
  .template.sureOrder-box .content .comm-form .form-item-kv .form-item {
    margin-bottom: 0; }
    .template.sureOrder-box .content .comm-form .form-item-kv .form-item input {
      color: #666666;
      background: #fff; }

/*# sourceMappingURL=detail.css.map */


