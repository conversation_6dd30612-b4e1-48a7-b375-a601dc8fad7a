<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>第三方账号绑定列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>  
<script>
layui.use(['table', 'form'], function () {
    var $ = layui.jquery,
        form = layui.form,
        table = layui.table;
    
    // 保存原始数据
    var originalData = [];
    
    // 表格渲染
    table.render({
        elem: '#currentTableId',
        url: '/admin/index/getThirdPartyBindings',
        toolbar: '#toolbarDemo',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: "checkbox", fixed: "left", width: 50},
            {field: 'id', title: 'ID', sort: true},
            {field: 'nickname', title: '用户昵称', templet: function(d){
                return '<a class="layui-text-blue" href="javascript:;">'+ d.nickname +'</a>';
            }},
            {field: 'user_id', title: '用户ID'},
            {field: 'third_party', title: '平台', templet: function(d){
                var className = '';
                switch(d.third_party) {
                    case 'weixin': className = 'layui-bg-green'; break;
                    case 'qq': className = 'layui-bg-blue'; break;
                    case 'weibo': className = 'layui-bg-red'; break;
                }
                return '<span class="layui-badge '+ className +'">'+ d.third_party +'</span>';
            }},
            {field: 'social_uid', title: '第三方UID'},
            {field: 'faceimg', title: '头像', templet: function(d){
                return '<img src="'+ d.faceimg +'" class="layui-circle" style="width:30px; height:30px;">';
            }},
            {field: 'gender', title: '性别'},
            {field: 'ip', title: '登录IP'},
            {field: 'create_time', title: '绑定时间', sort: true}
        ]],
        limits: [10, 15, 20, 25, 50, 100],
        limit: 15,
        page: true,
        skin: 'line',
        even: true,
        size: 'lg',
        done: function(res) {
            // 保存原始数据
            originalData = res.data;
        }
    });

    // 监听搜索操作
    form.on('submit(data-search-btn)', function (data) {
        var searchNickname = data.field.nickname.toLowerCase();
        var searchPlatform = data.field.third_party;
        
        // 基于原始数据进行筛选
        var filteredData = originalData.filter(function(item) {
            var matchNickname = !searchNickname || 
                              item.nickname.toLowerCase().indexOf(searchNickname) !== -1;
            var matchPlatform = !searchPlatform || 
                              item.third_party === searchPlatform;
            return matchNickname && matchPlatform;
        });

        // 重载表格
        table.reload('currentTableId', {
            data: filteredData,
            page: {
                curr: 1
            }
        });
        return false;
    });
});
</script>

<style>
.layuimini-container {
    padding: 15px;
    background-color: #F2F2F2;
}
.layuimini-main {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
}
.layui-table-cell {
    height: auto;
    line-height: 28px;
}
.layui-circle {
    border-radius: 50%;
    border: 1px solid #eee;
}
.layui-text-blue {
    color: #1E9FFF;
}
</style>
</body>
</html> 