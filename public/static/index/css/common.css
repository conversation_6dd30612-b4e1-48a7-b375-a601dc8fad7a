blockquote,
body,
button,
dd,
dl,
dt,
fieldset,
form,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
input,
legend,
li,
ol,
p,
pre,
td,
textarea,
th,
ul {
  margin: 0;
  padding: 0;
}
body,
button,
input,
select,
table,
textarea {
  border-collapse: collapse;
  border-spacing: 0;
}
body {
  font: normal 14px microsoft Yahei, Verdana, Arial, Helvetica, sans-serif;
  color: #000;
  -webkit-text-size-adjust: none;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
}
fieldset,
img {
  border: 0;
}
li,
ol,
ul {
  list-style: none;
}
address,
em {
  font-style: normal;
}
table {
  border-collapse: collapse;
}
em,
i {
  font-style: normal;
}
b,
strong {
  font-weight: 400;
}
img {
  border: none;
}
img,
input {
  vertical-align: middle;
}
input {
  outline: none;
}
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
textarea:focus {
  outline: 0;
}
a {
  text-decoration: none;
}
.clearfix:after {
  display: block;
  clear: both;
  visibility: hidden;
  height: 0;
  content: ' ';
  font-size: 0;
}
.clearfix {
  zoom: 1;
}
button,
input,
textarea:focus {
  outline: 0;
  border: none;
  background: transparent;
}
.bc {
  margin-left: auto;
  margin-right: auto;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
@-webkit-keyframes zhuan {
  0% {
    -webkit-transform: rotateY(90deg);
    transform: rotateY(90deg);
  }
  40% {
    -webkit-transform: rotateY(-20deg);
    transform: rotateY(-20deg);
  }
  60% {
    -webkit-transform: rotateY(10deg);
    transform: rotateY(10deg);
  }
  80% {
    -webkit-transform: rotateY(-5deg);
    transform: rotateY(-5deg);
  }
  to {
    -webkit-transform: rotateY(0);
    transform: rotateY(0);
  }
}
@-moz-keyframes zhuan {
  0% {
    -moz-transform: rotateY(90deg);
    transform: rotateY(90deg);
  }
  40% {
    -moz-transform: rotateY(-20deg);
    transform: rotateY(-20deg);
  }
  60% {
    -moz-transform: rotateY(10deg);
    transform: rotateY(10deg);
  }
  80% {
    -moz-transform: rotateY(-5deg);
    transform: rotateY(-5deg);
  }
  to {
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }
}
@keyframes zhuan {
  0% {
    -webkit-transform: rotateY(90deg);
    -moz-transform: rotateY(90deg);
    transform: rotateY(90deg);
  }
  40% {
    -webkit-transform: rotateY(-20deg);
    -moz-transform: rotateY(-20deg);
    transform: rotateY(-20deg);
  }
  60% {
    -webkit-transform: rotateY(10deg);
    -moz-transform: rotateY(10deg);
    transform: rotateY(10deg);
  }
  80% {
    -webkit-transform: rotateY(-5deg);
    -moz-transform: rotateY(-5deg);
    transform: rotateY(-5deg);
  }
  to {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }
}
.nologin {
  text-align: center;
}
.nologin .nologin-title {
  font-size: 22px;
  color: #333;
  height: 50px;
  line-height: 3;
  font-weight: 700;
}
.nologin .nologin-content {
  font-size: 16px;
  font-weight: 400;
  color: #333;
  margin-top: 15px;
  padding: 0 20px 25px;
  border-bottom: 1px solid #d8d9d8;
}
.nologin .nologin-btn {
  width: 100%;
  height: 60px;
  line-height: 60px;
  position: relative;
}
.nologin .nologin-btn:after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 1px;
  height: 100%;
  background-color: #d8d9d8;
}
.nologin .nologin-btn > div {
  width: 50%;
  height: 100%;
  font-size: 18px;
  cursor: pointer;
}
.nologin .nologin-btn .refund-btn {
  float: left;
  color: #f92a2b;
}
.nologin .nologin-btn .confirm-btn {
  float: right;
  color: #333;
}
.nologin .nologin-btn.nobalance .refund-btn,
.nologin .nologin-btn.nobalance:after {
  display: none;
}
.nologin .nologin-btn.nobalance .confirm-btn {
  width: 100%;
  color: #f92a2b;
}
.refund {
  padding: 24px 24px 32px;
}
.refund .clearbox:after {
  content: '';
  display: block;
  clear: both;
}
.refund .refund-msg-status {
  padding: 20px;
  border-radius: 4px;
  background: #f5f5f5;
}
.refund .nodispose {
  color: #737373;
  display: none;
}
.refund .nodispose.show {
  display: block;
}
.refund .nodispose .now-status span {
  color: #4d4d4d;
  font-weight: 700;
  font-size: 14px;
}
.refund .processed {
  color: #737373;
  display: none;
}
.refund .processed.show {
  display: block;
}
.refund .processed .now-status {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #ebebeb;
}
.refund .processed .now-status .now-status-msg span {
  color: #4d4d4d;
  font-weight: 700;
  font-size: 14px;
}
.refund .processed .now-status .now-status-time {
  margin-left: 30px;
}
.refund .dispose-result {
  margin-bottom: 16px;
}
.refund .dispose-result .dispose-result-time {
  margin-left: 40px;
}
.refund .dispose-msg .dispose-leave-msg-label {
  float: left;
  width: 70px;
}
.refund .dispose-msg .dispose-leave-msg-content {
  float: left;
  width: 375px;
}
.refund .submit-time {
  margin-top: 16px;
}
.refund .refund-user-msg {
  margin-top: 32px;
  font-size: 12px;
  height: 16px;
  color: #737373;
  text-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
  padding-bottom: 24px;
  border-bottom: 1px solid #f5f5f5;
}
.refund .refund-user-box > div {
  float: left;
  width: 30%;
}
.refund .refund-user-box .refund-user-phone {
  width: 40%;
}
.refund .refund-merchant {
  font-size: 12px;
  color: #737373;
  text-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
}
.refund .refund-merchant > div {
  margin-top: 24px;
}
.refund .merchant-user-msg {
  height: 16px;
}
.refund .merchant-user-msg > div {
  float: left;
  width: 30%;
}
.refund .merchant-user-msg .merchant-user-phone {
  width: 40%;
}
.refund .merchant-leave-msg:after {
  content: '';
  display: block;
  clear: both;
}
.refund .merchant-leave-msg .merchant-leave-msg-label {
  float: left;
  width: 72px;
  text-align: right;
}
.refund .merchant-leave-msg .merchant-leave-msg-content {
  width: 415px;
  float: left;
}
.refund .refund-msg .confirm-btn {
  width: 170px;
  height: 42px;
  line-height: 42px;
  background: #f6472f;
  border-radius: 4px;
  text-align: center;
  color: #fff;
  font-size: 18px;
  margin: 32px auto 0;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
}
.refund .refund-msg .confirm-btn:hover {
  -webkit-box-shadow: 0 6px 18px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 6px 18px 0 rgba(0, 0, 0, 0.2);
}
.apply-refund-form {
  padding: 24px 24px 32px;
}
.apply-refund-form textarea {
  resize: none;
}
.apply-refund-form .apply-label {
  width: 80px;
  color: #737373;
  font-size: 12px;
  text-align: right;
  position: relative;
}
.apply-refund-form .input-required:before {
  content: '*';
  position: absolute;
  top: 0;
  left: 0;
  color: #fc3737;
}
.apply-refund-form .apply-user-msg {
  padding-bottom: 24px;
  border-bottom: 1px solid #f5f5f5;
}
.apply-refund-form .apply-user-msg > div {
  width: 100%;
  height: 20px;
  line-height: 20px;
  margin-bottom: 16px;
}
.apply-refund-form .apply-user-msg > div:last-child {
  margin: 0;
}
.apply-refund-form .apply-user-msg .apply-content {
  font-size: 12px;
  font-weight: 700;
  color: #262626;
  text-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
}
.apply-refund-form .apply-form {
  margin-top: 24px;
}
.apply-refund-form .apply-form input[type='text'] {
  min-width: 129px;
  height: 36px;
  line-height: 36px;
  outline: none;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  -webkit-transition: border 0.5s;
  -moz-transition: border 0.5s;
  transition: border 0.5s;
}
.apply-refund-form .apply-form .apply-contact-way input[type='text'] {
  width: 129px;
}
.apply-refund-form .apply-form input:focus {
  border-color: #b3b3b3;
}
.apply-refund-form .form-input-item {
  height: 38px;
  line-height: 38px;
  margin-bottom: 24px;
}
.apply-refund-form .apply-merchant .apply-merchant-order {
  position: relative;
}
.apply-refund-form .apply-merchant-order .merchant-order {
  width: 261px;
  padding-right: 132px !important;
}
.apply-refund-form .merchant-order-hint-msg {
  position: absolute;
  top: 0;
  right: 0;
  width: 90px;
  padding: 0 30px 0 12px;
  height: 38px;
  cursor: pointer;
  color: #737373;
  font-size: 12px;
}
.apply-refund-form .merchant-order-hint-msg:after {
  content: '';
  position: absolute;
  top: 12px;
  right: 12px;
  width: 14px;
  height: 14px;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
  background-image: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/refund-form-question.png);
}
.apply-refund-form .merchant-order-hint-msg:hover:after {
  background-image: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/refund-form-question-hover.png);
}
.apply-refund-form .apply-pay .apply-pay-channel {
  position: relative;
  height: 100%;
  overflow: hidden;
}
.apply-refund-form .apply-pay-channel .pay-channel {
  height: 100%;
  overflow: hidden;
}
.apply-refund-form .pay-channel span {
  position: relative;
  display: inline-block;
  height: 28px;
  line-height: 28px;
  background: #fff;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  padding: 0 12px 0 34px;
  overflow: hidden;
  margin-right: 12px;
  margin-top: 4px;
}
.apply-refund-form .pay-channel span.active {
  border-color: #ff2828;
}
.apply-refund-form .pay-channel span.active:before {
  content: '';
  position: absolute;
  right: -1px;
  bottom: 0;
  width: 13px;
  height: 13px;
  background-image: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/refund-form-pay-checked.png);
}
.apply-refund-form .pay-channel span:after {
  content: '';
  position: absolute;
  left: -8px;
  top: -6px;
  width: 54px;
}
.apply-refund-form .pay-channel span.pay-channel-wx:after {
  content: '';
  height: 52px;
  background-image: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/refund-form-wx.png);
}
.apply-refund-form .pay-channel span.pay-channel-zfb:after {
  content: '';
  height: 54px;
  top: -7px;
  background-image: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/refund-form-zfb.png);
}
.apply-refund-form .apply-leave {
  height: auto;
  margin-bottom: 0;
}
.apply-refund-form .apply-leave .apply-leave-msg .input-required:before {
  right: 38px;
}
.apply-refund-form .apply-leave-msg,
.apply-refund-form .apply-leave-msg .leave-msg-text {
  position: relative;
}
.apply-refund-form .apply-leave-msg .leave-msg-text textarea {
  border: 1px solid #d9d9d9;
  padding: 12px;
  width: 381px;
  height: 72px;
}
.apply-refund-form .apply-leave-msg .confirm-checkbox-box {
  margin-left: 80px;
}
.apply-refund-form .apply-leave-msg .confirm-checkbox-box .confirm-checkbox {
  display: none;
}
.apply-refund-form .leave-msg-count {
  position: absolute;
  right: 8px;
  bottom: 8px;
}
.apply-refund-form .leave-msg-count span {
  color: #fc3737;
}
.apply-refund-form .submit-btn {
  width: 170px;
  height: 42px;
  line-height: 42px;
  background: #f6472f;
  border-radius: 4px;
  text-align: center;
  color: #fff;
  font-size: 18px;
  margin: 32px auto 0;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
}
.apply-refund-form .submit-btn:hover {
  -webkit-box-shadow: 0 6px 18px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 6px 18px 0 rgba(0, 0, 0, 0.2);
}
.apply-refund-form .confirm-checkbox-box label {
  position: relative;
  padding-left: 16px;
  cursor: pointer;
}
.apply-refund-form .confirm-checkbox-box label:after {
  content: '';
  position: absolute;
  left: 0;
  top: 3px;
  width: 8px;
  height: 8px;
  border-radius: 2px;
  border: 2px solid #999;
}
.apply-refund-form .confirm-checkbox-box.confirm-checked label:after {
  display: none;
}
.apply-refund-form .confirm-checkbox-box.confirm-checked label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 12px;
  height: 12px;
  background-image: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/refund-form-checked.png);
}
.apply-refund-form .clearbox:after {
  content: '';
  display: block;
  clear: both;
}
.get-order-hint img {
  float: left;
  margin-right: 20px;
}
.get-order-hint img:last-child {
  margin-right: 0;
}
.hb-popup-box .hb-popup {
  width: 100%;
  height: 100%;
}
.hb-popup-box .hb-popup .hb-bg {
  width: 276px;
  height: 342px;
  background: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/hb_popup_bg.png)
    no-repeat;
  overflow: hidden;
  text-align: center;
}
.hb-popup-box .hb-popup .hb-top {
  color: #e74125;
  margin-top: 34px;
  font-weight: 700;
  line-height: 1;
}
.hb-popup-box .hb-popup .hb-top span.hb-top-txt {
  display: block;
  font-size: 16px;
  color: #a37332;
  margin-bottom: 18px;
}
.hb-popup-box .hb-popup .hb-top p i.icon-symbol {
  font-size: 20px;
}
.hb-popup-box .hb-popup .hb-top p .hb-money {
  font-size: 40px;
}
.hb-popup-box .hb-popup .hb-bottom {
  margin-top: 110px;
}
.hb-popup-box .hb-popup .hb-bottom .hb-btn {
  width: 183px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  background: url(https://zuhaowan.zuhaowan.com/shangv1/default_v2/image/hb_popup_btn.png)
    no-repeat;
  margin: 15px auto 0;
}
.hb-popup-box .hb-popup .hb-bottom .hb-btn a {
  display: block;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  cursor: pointer;
}
.hb-popup-box .hb-popup .hb-bottom .hb-bottom-txt {
  font-size: 14px;
  font-weight: 400;
  color: #f0a68d;
}
.red-pack-bg {
  -webkit-box-shadow: none;
  box-shadow: none;
  background: none;
}
