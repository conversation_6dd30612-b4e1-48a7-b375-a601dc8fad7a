

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">推广设置</div>
          <div class="layui-card-body" pad15>
            
            <div class="layui-form" wid100 lay-filter="">
              <div class="layui-form-item">
                <label class="layui-form-label">推广开关</label>
                <div class="layui-input-block">
                      <select name="pro_switch" lay-search="" value="" id="select">
                          <option value="1">开启</option>
                          <option value="0">关闭</option>
                       </select>
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">返现比例(百分比)</label>
                <div class="layui-input-block">
                  <input type="text" name="pro_money" lay-verify="" value="{$config.pro_money}" class="layui-input" placeholder="返现百分比(无需填百分号，直接填数字即可)">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="set_website">确认保存</button>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  document.querySelector("#select").value="{$config.pro_switch}";
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
    
  }).use(['index','set', 'form','upload'], function(){
     //图片上传
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload ;
    //图片上传
    upload.render({
      elem: '#layuiadmin-upload-useradmin'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });
  // 提交事件
  form.on('submit(set_website)', function(data){
    var field = data.field; // 获取表单字段值
    $.ajax({
        url: '/admin/Promotion',
        method: 'post',
        data: data.field,
        success: function(res){
        layer.msg('保存成功');
        }
    });
    return false; // 阻止默认 form 跳转
  });
    
    
  });
  </script>
</body>
</html>