<!DOCTYPE html>
<html>
<head>
    <title>离线账号状态修复测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        button.success { background: #28a745; }
        button.success:hover { background: #218838; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .status-available { color: #28a745; font-weight: bold; }
        .status-unavailable { color: #dc3545; font-weight: bold; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 3px; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>离线账号状态修复测试</h1>
        
        <div class="test-section">
            <h3>1. 问题描述</h3>
            <div class="result warning">
                <h4>🐛 发现的BUG：</h4>
                <p>离线账号在分配给用户后，状态被错误地设置为不可用（ac_states = 0），但根据业务需求：</p>
                <ul>
                    <li><strong>离线账号应该永远保持可用状态</strong></li>
                    <li><strong>不应该因为分配而变成不可用</strong></li>
                    <li><strong>只有管理员手动设置才能改变状态</strong></li>
                </ul>
                
                <h4>🔍 问题影响：</h4>
                <ul>
                    <li>用户无法正常使用已分配的离线账号</li>
                    <li>系统显示账号不可用，造成用户困惑</li>
                    <li>影响离线账号的正常业务流程</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. 离线账号状态检查</h3>
            <button onclick="checkOfflineAccountStatus()">检查离线账号状态</button>
            <button onclick="fixOfflineAccountStatus()" class="success">修复离线账号状态</button>
            <div id="statusResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 修复方案</h3>
            <div class="result info">
                <h4>修复的代码位置：</h4>
                
                <h5>1. 卡密兑换时（CdkExchange.php:342）：</h5>
                <div class="code-block">// 修复前
'ac_states' => 0, // 已分配状态

// 修复后
'ac_states' => 1, // 离线账号永远保持可用状态</div>
                
                <h5>2. 在线卡密兑换时（CdkExchange.php:610）：</h5>
                <div class="code-block">// 修复前
Account::where('ac_name', $acName)->update([...]);

// 修复后
Account::where('ac_name', $acName)
    ->where('goods_Type', 1) // 只更新在线账号
    ->update([...]);</div>
                
                <h5>3. 永久版账号提取时（Index.php:526）：</h5>
                <div class="code-block">// 修复前
$account->ac_states = 0;

// 修复后
if ($account->goods_Type == 0) {
    $account->ac_states = 1; // 离线账号永远保持可用状态
} else {
    $account->ac_states = 0; // 在线账号设为不可用
}</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. 测试结果说明</h3>
            <div class="result">
                <h4>状态说明：</h4>
                <ul>
                    <li><span class="status-available">可用（ac_states = 1）</span>：正确状态，离线账号应该保持此状态</li>
                    <li><span class="status-unavailable">不可用（ac_states = 0）</span>：错误状态，需要修复</li>
                </ul>
                
                <h4>预期结果：</h4>
                <ul>
                    <li>所有离线账号（goods_Type = 0）的状态都应该是可用（ac_states = 1）</li>
                    <li>即使账号已分配给用户（ac_uid 不为空），状态仍应该是可用</li>
                    <li>只有在线账号（goods_Type = 1）才应该在分配后变为不可用</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function checkOfflineAccountStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在检查离线账号状态...';
            
            fetch('/admin/TestController/testOfflineAccountStatus')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        const stats = data.data.statistics;
                        const issues = data.data.issues;
                        const problematicAccounts = data.data.problematic_accounts;
                        const normalAccounts = data.data.normal_accounts;
                        
                        let statusClass = issues.length > 0 ? 'warning' : 'success';
                        
                        resultDiv.className = `result ${statusClass}`;
                        resultDiv.innerHTML = `
                            <h4>📊 离线账号状态统计：</h4>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">${stats.total}</div>
                                    <div class="stat-label">总离线账号数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number status-available">${stats.available}</div>
                                    <div class="stat-label">可用状态</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number status-unavailable">${stats.unavailable}</div>
                                    <div class="stat-label">不可用状态</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.assigned}</div>
                                    <div class="stat-label">已分配</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.unassigned}</div>
                                    <div class="stat-label">未分配</div>
                                </div>
                            </div>
                            
                            ${issues.length > 0 ? `
                                <h5>⚠️ 发现的问题：</h5>
                                <ul>
                                    ${issues.map(issue => `<li>${issue}</li>`).join('')}
                                </ul>
                            ` : '<h5>✅ 没有发现问题，所有离线账号状态正常</h5>'}
                            
                            ${problematicAccounts.length > 0 ? `
                                <h5>🔧 需要修复的账号示例：</h5>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>账号名</th>
                                            <th>用户ID</th>
                                            <th>状态</th>
                                            <th>商品ID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${problematicAccounts.slice(0, 10).map(account => `
                                            <tr>
                                                <td>${account.id}</td>
                                                <td>${account.ac_name}</td>
                                                <td>${account.ac_uid || '未分配'}</td>
                                                <td class="status-unavailable">不可用</td>
                                                <td>${account.ac_goods}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            ` : ''}
                            
                            ${normalAccounts.length > 0 ? `
                                <h5>✅ 正常的离线账号示例：</h5>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>账号名</th>
                                            <th>用户ID</th>
                                            <th>状态</th>
                                            <th>商品ID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${normalAccounts.slice(0, 5).map(account => `
                                            <tr>
                                                <td>${account.id}</td>
                                                <td>${account.ac_name}</td>
                                                <td>${account.ac_uid || '未分配'}</td>
                                                <td class="status-available">可用</td>
                                                <td>${account.ac_goods}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            ` : ''}
                            
                            <h5>📋 修复信息：</h5>
                            <p><strong>描述：</strong>${data.data.fix_info.description}</p>
                            <p><strong>预期行为：</strong>${data.data.fix_info.expected_behavior}</p>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `<p>❌ 检查失败：${data.msg}</p>`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<p>❌ 请求失败：${error.message}</p>`;
                });
        }
        
        function fixOfflineAccountStatus() {
            if (!confirm('确定要修复所有离线账号的状态吗？这将把所有状态为不可用的离线账号改为可用状态。')) {
                return;
            }
            
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在修复离线账号状态...';
            
            fetch('/admin/TestController/fixOfflineAccountStatus')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <h4>✅ 离线账号状态修复完成！</h4>
                            <p><strong>发现问题账号：</strong>${data.data.total_problematic} 个</p>
                            <p><strong>成功修复：</strong>${data.data.fixed_count} 个</p>
                            <p><strong>说明：</strong>${data.data.note}</p>
                            
                            ${data.data.fixed_accounts.length > 0 ? `
                                <h5>修复的账号示例：</h5>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>账号名</th>
                                            <th>用户ID</th>
                                            <th>修复状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.data.fixed_accounts.map(account => `
                                            <tr>
                                                <td>${account.id}</td>
                                                <td>${account.ac_name}</td>
                                                <td>${account.ac_uid || '未分配'}</td>
                                                <td class="status-available">已修复为可用</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            ` : ''}
                            
                            <p><em>建议：修复完成后，请重新检查离线账号状态以确认修复效果。</em></p>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `<p>❌ 修复失败：${data.msg}</p>`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<p>❌ 请求失败：${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
