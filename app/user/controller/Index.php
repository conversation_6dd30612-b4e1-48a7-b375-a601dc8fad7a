<?php
/**
 * 用户中心控制器
 *
 * @package app\user\controller
 * <AUTHOR> Name <<EMAIL>>
 * @copyright Copyright (c) 2023 Your Company Name
 * @license MIT License
 * @version 1.0.0
 */

namespace app\user\controller;

use app\admin\model\Offline;
use app\BaseController;
use app\user\controller\Verify;
use app\admin\model\System;
use app\admin\model\Config;
use think\facade\Request;
use think\facade\View;
use app\admin\model\User as Us;
use think\facade\Cookie;
use app\admin\model\Combo;
use app\admin\model\Goods;
use app\admin\model\Order;
use app\user\model\Coupons;
use app\admin\model\Notice;
use think\facade\Db;
use app\controller\QrCode;
use app\admin\model\Promotion as Pro;
use app\admin\model\Workorder;
use app\admin\model\VipAccount;
use think\response\Json;
use app\admin\model\Account;

/**
 * 用户中心首页控制器
 *
 * 主要功能:
 * - 用户基础信息管理
 * - VIP账号管理
 * - 订单管理
 * - 钱包/支付管理
 * - 工单/反馈管理
 * - 系统通知管理
 */
class Index extends Verify
{
    /**************************************
     * 用户基础信息管理
     **************************************/

    /**
     * 获取通用通知数据（用于所有ticket/视图）
     * 返回帮助中心和系统公告数据
     */
    private function getNoticeData()
    {
        // 获取帮助中心文章 (not_system=0 表示帮助中心文章)
        $helpNotices = \app\admin\model\Notice::where('not_system', '0')
                         ->order('id desc')
                         ->limit(5)
                         ->select();

        // 获取系统公告
        $systemNotices = \app\admin\model\Notice::where('not_system', '2')
                          ->order('id desc')
                          ->limit(2)
                          ->select();

        return [
            'helpNotices' => $helpNotices,
            'systemNotices' => $systemNotices
        ];
    }

    /**
     * 合并视图数据（添加公告和帮助中心数据）
     * @param array $data 原始数据
     * @return array 合并后的数据
     */
    private function mergeViewData($data)
    {
        return array_merge($data, $this->getNoticeData());
    }

    /**
     * 用户设置页面
     */
    public function setup()
    {
        // 获取用户名
        $username = Cookie::get("username");

        // 用户名不在则跳转至登录
        if (empty($username)) {
            return redirect("login")->with("error", "请先登录！");
        }

        // 查询用户信息
        $user = Us::where("us_username", $username)->find();

        if (!$user) {
            return View::fetch("error/user_not_found");
        }

        // 查询系统信息
        $system = System::find(1);
        if (!$system) {
            return View::fetch("error/system_not_found");
        }

        // 查询用户的第三方绑定记录（如微信、QQ、GitHub等）
        $bindings = Db::table("third_party_bindings")
            ->where("user_id", $user->id)
            ->select();

        return View::fetch("ticket/list7", $this->mergeViewData([
            "user" => $user,
            "system" => $system,
            "bindings" => $bindings,
        ]));
    }

    /**
     * 用户退出登录
     */
    public function exit()
    {
        Cookie::delete("username");
        Cookie::delete("login");
        Cookie::delete("id");
        echo "<script>top.location.href='/user/login'</script>";
    }

    /**
     * 月费/季费/年费会员提取单个VIP账号
     */
    public function extractRegularMemberVipAccount()
    {
        $config = Config::find(1);
        $maxAccountLimit = $config['vip_maxAccountLimit'];

        // 获取请求参数
        $userId = Request::param('id');
        $username = Request::param('username');
        $accountId = Request::param('account_id');
        $accountType = Request::param('account_type', 'online');

        // 参数验证
        if (empty($userId) || empty($username) || empty($accountId)) {
            return json(['code' => 1, 'msg' => '缺少必要参数'], 400);
        }

        // 验证账号类型参数
        if (!in_array($accountType, ['online', 'offline'])) {
            return json(['code' => 1, 'msg' => '无效的账号类型'], 400);
        }

        // 验证用户身份
        $user = Us::where([
            'id' => $userId,
            'us_username' => $username
        ])->find();

        if (!$user) {
            return json(['code' => 1, 'msg' => '用户未登录或不存在'], 401);
        }

        // 验证用户是否为月费/季费/年费会员
        $allowedLevels = ["月费会员", "季费会员", "年费会员", "至尊会员"];
        if (!in_array($user->membership_level, $allowedLevels)) {
            return json(['code' => 1, 'msg' => '当前账号权限不足'], 403);
        }

        // 查询商品信息，判断是否允许提取
        $goods = Db::table('tk_goods')->where('id', $accountId)->find();
        if (!$goods) {
            return json(['code' => 1, 'msg' => '商品不存在'], 404);
        }

        // 判断商品是否允许会员提取
        if ($goods['limited'] == 0) {
            return json(['code' => 1, 'msg' => '该商品不允许提取，请联系客服'], 403);
        }

        // 检查用户当前持有的VIP账号数量（只统计在线账号）
        $currentAccountCount = Account::where([
            'ac_uid' => $user->id,
            'ac_vip' => 1,
            'goods_Type' => 1  // 只统计在线账号
        ])->count();

        if ($currentAccountCount >= $maxAccountLimit) {
            return json([
                'code' => 1,
                'msg' => "已达到最大提取限制({$maxAccountLimit}个),请先释放部分账号后再提取"
            ], 403);
        }

        // 查询账号
        $query = Account::where('ac_goods', $accountId)
            ->where('ac_states', 1);

        if ($accountType === 'online') {
            // 在线账号额外检查
            $query = $query->where('goods_Type', 1)
                ->where('ac_sell', 1);

            // 检查是否已租用该游戏的在线账号
            $existingAccount = Account::where([
                'ac_uid' => $user->id,
                'ac_goods' => $accountId,
                'ac_vip' => 1,
                'goods_Type' => 1
            ])->find();

            if ($existingAccount) {
                return json([
                    'code' => 1,
                    'msg' => '您已租用过此游戏的在线账号,每个游戏只能租用一个在线账号'
                ], 403);
            }
        } else {
            // 离线账号只需验证状态
            $query = $query->where('goods_Type', 0);
        }

        $accounts = $query->select();

        if ($accounts->isEmpty()) {
            $typeText = $accountType === 'online' ? '在线' : '离线';
            return json(['code' => 1, 'msg' => "未找到可用的{$typeText}账号"], 404);
        }

        // 随机选取一个账号
        $account = $accounts[rand(0, count($accounts) - 1)];
        $ac_name = $account->ac_name;

        try {
            if ($accountType === 'online') {
                // ✅ 使用统一的账号分配方法
                \app\controller\Api::assignAccount(
                    $ac_name,
                    $user->id,
                    time() + $config['delay_duration'] * 60 * 60,
                    [
                        'ac_sell' => 0,
                        'ac_vip' => 1
                    ]
                );
            }

            // 查询商品信息以获取正确的游戏名称
            $goods = Db::name('goods')->where('id', $account->ac_goods)->find();
            $gameName = $goods ? $goods['goods_name'] : '未知游戏';

            // 创建订单记录
            $orderData = [
                'ord_bbh' => date('YmdHis') . rand(1000, 9999),
                'ord_type' => 'vip_extract',
                'ord_type2' => $accountType === 'online' ? '1' : '0',
                'ord_name' => $gameName, // 使用具体的游戏名称而不是通用描述
                'ord_uid' => $user->id,
                'ord_ifpay' => 1,
                'GoodType' => $accountType === 'online' ? 1 : 0,
                'is_permanent' => 2, // 会员提取账号订单
                'ord_aid' => $account->id,
                'time' => date('Y-m-d H:i:s'),
                'account_status' => 1,
                'expiry_date' => date('Y-m-d H:i:s', time() + $config['delay_duration'] * 60 * 60),
                'or_maturity' => 1, // 设置为未到期
                'ord_combo' => $account->ac_goods, // 设置为账号的ac_goods值
            ];

            Db::table('tk_order')->insert($orderData);

            // 记录日志
            $typeText = $accountType === 'online' ? '在线' : '离线';
            Db::table('tk_userlogo')->insert([
                'uid' => $user->id,
                'content' => "成功提取{$typeText}VIP账号组: " . $ac_name,
                'ip' => Request::ip(),
                'time' => date('Y-m-d H:i:s')
            ]);

            return json([
                'code' => 0,
                'msg' => "成功提取{$typeText}VIP账号组",
                'data' => Account::where('ac_name', $ac_name)->select()
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('提取VIP账号失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '提取VIP账号失败，请稍后再试', 'erro'=>$e->getMessage()], 500);
        }
    }

    /**
     * 取消租用1 取消租用VIP账号
     */
    public function 取消租用1()
    {
        // 获取POST参数
        $userId = Request::post('id');
        $username = Request::post('username');
        $vip_id = Request::post('vip_id');

        // 参数验证
        if (empty($userId) || empty($username) || empty($vip_id)) {
            return json(['code' => 1, 'msg' => '缺少必要参数'], 400);
        }

        // 获取用户信息
        $user = Us::where([
            'id' => $userId,
            'us_username' => $username
        ])->find();

        if (!$user) {
            return json(['code' => 1, 'msg' => '用户未登录或不存在'], 401);
        }

        // 获取VIP账号信息
        $vipAccount = Account::where('id', $vip_id)->find();

        if (!$vipAccount) {
            return json(['code' => 1, 'msg' => 'VIP账号不存在'], 404);
        }

        // 检查VIP账号是否属于该用户
        if ($vipAccount->ac_uid != $user->id) {
            return json(['code' => 1, 'msg' => 'VIP账号不属于该用户'], 403);
        }

        // 获取ac_name并更新所有相关账号
        $ac_name = $vipAccount->ac_name;
        try {
            // ✅ 使用统一的账号回收方法
            \app\controller\Api::recycleAccount($ac_name);

            // 记录日志到 userlogo 表
            Db::table('tk_userlogo')->insert([
                'uid' => $user->id,
                'content' => '成功取消租用账号组: ' . $ac_name,
                'ip' => Request::ip(),
                'time' => date('Y-m-d H:i:s')
            ]);

            return json(['code' => 200, 'msg' => '取消租用成功'], 200);
        } catch (\Exception $e) {
            \think\facade\Log::error('取消租用VIP账号失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '取消租用VIP账号失败，请稍后再试'], 500);
        }
    }

    /**
     * 延时VIP账号
     */
    public function delayVipAccount()
    {
        // 获取POST参数
        $userId = Request::post('id');
        $username = Request::post('username');
        $vip_id = Request::post('vip_id');

        // 参数验证
        if (empty($userId) || empty($username) || empty($vip_id)) {
            return json(['code' => 1, 'msg' => '缺少必要参数'], 400);
        }

        // 获取用户信息
        $user = Us::where([
            'id' => $userId,
            'us_username' => $username
        ])->find();

        if (!$user) {
            return json(['code' => 1, 'msg' => '用户未登录或不存在'], 401);
        }

        // 获取VIP账号信息
        $vipAccount = Account::where('id', $vip_id)->find();

        if (!$vipAccount) {
            return json(['code' => 1, 'msg' => 'VIP账号不存在'], 404);
        }

        // 检查VIP账号是否属于该用户
        if ($vipAccount->ac_uid != $user->id) {
            return json(['code' => 1, 'msg' => 'VIP账号不属于该用户'], 403);
        }

        // 检查VIP账号的到期时间
        if ($vipAccount->exit_time <= time()) {
            return json(['code' => 1, 'msg' => 'VIP账号已到期，无法延时'], 400);
        } else {
            // 根据ac_goods查询limited值
            $goods = Db::name('goods')->where('id', $vipAccount->ac_goods)->find();

            // 检查是否允许会员提取
            if ($goods && $goods['limited'] == 0) {
                // limited为0，返回禁止操作的提示信息
                $config = Config::find(1);
                return json(['code' => 1, 'msg' => $config->account_operation_disabled_message], 400);
            }

            // 账号未到期且允许操作，继续后续处理...
            // 这里是原有的延长时间逻辑
        }

        $config = Config::find(1);

        // 延长VIP账号的到期时间3小时
        $vipAccount->exit_time = time() + $config['delay_duration'] * 60 * 60; // 当前时间戳 + 3小时（以秒为单位）

        try {
            $vipAccount->save();

            // 记录日志到 userlogo 表
            Db::table('tk_userlogo')->insert([
                'uid' => $user->id,
                'content' => '成功延时账号: ' . $vipAccount->ac_username,
                'ip' => Request::ip(),
                'time' => date('Y-m-d H:i:s')
            ]);

            return json(['code' => 0, 'msg' => 'VIP账号已成功延时'. $config["delay_duration"] .'小时'], 200);
        } catch (\Exception $e) {
            \think\facade\Log::error('延时VIP账号失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '延时VIP账号失败，请稍后再试'], 500);
        }
    }

    /**
     * 提取永久版账号
     */
    public function extractPermanentAccount()
    {
        $config = Config::find(1);
        // 设置最大可提取账号数量
        $maxAccountLimit = $config['vip_maxAccountLimit'];

        // 获取请求参数
        $userId = Request::param('id');
        $username = Request::param('username');
        $accountId = Request::param('account_id'); // 这里的accountId实际上是goods_id

        // 参数验证
        if (empty($userId) || empty($username) || empty($accountId)) {
            return json(['code' => 1, 'msg' => '缺少必要参数'], 400);
        }

        // 验证用户身份
        $user = Us::where([
            'id' => $userId,
            'us_username' => $username
        ])->find();

        if (!$user) {
            return json(['code' => 1, 'msg' => '用户未登录或不存在'], 401);
        }

        // 直接查询goods表获取商品信息
        $goods = Db::table('tk_goods')->where('id', $accountId)->find();
        if (!$goods) {
            return json(['code' => 1, 'msg' => '商品不存在'], 404);
        }

        // 判断商品是否允许会员提取
        if ($goods['limited'] == 0) {
            return json(['code' => 1, 'msg' => '该商品不允许提取，请联系客服'], 403);
        }

        // 验证用户是否购买了永久版套餐（只检查有效订单，排除被封禁的）
        $orders = Order::where([
            'ord_uid' => $userId,
            'is_permanent' => 1,
            'GoodType' => 3,
            'ord_ifpay' => 1,
            'or_maturity' => 1  // 只查询未到期的订单，排除被封禁的
        ])->select();

        $hasValidOrder = false;
        foreach ($orders as $order) {
            // 永久版卡密兑换订单中，ord_combo 直接存储的是商品ID
            if ($order->ord_combo == $accountId) {
                $hasValidOrder = true;
                break;
            }
        }

        if (!$hasValidOrder) {
            return json(['code' => 1, 'msg' => '未购买相应的永久版套餐'], 403);
        }

        // 检查用户当前持有的永久版账号数量
        $currentAccountCount = Account::where([
            'ac_uid' => $user->id,
            'ac_vip' => 2
        ])->count();

        // 如果已达到最大限制,返回错误提示
        if ($currentAccountCount >= $maxAccountLimit) {
            return json([
                'code' => 1,
                'msg' => "已达到最大提取限制({$maxAccountLimit}个),请先释放部分账号后再提取"
            ], 403);
        }

        // 智能分配账号逻辑
        $account = $this->getOptimalPermanentAccount($userId, $accountId);

        if (!$account) {
            return json(['code' => 1, 'msg' => '未找到可用账号'], 404);
        }

        // 更新选中账号的信息（永久版只处理在线账号）
        $account->ac_sell = 0;
        $account->ac_states = 0; // 在线账号设为不可用
        $account->ac_vip = 2;    // 永久版标识
        $account->ac_uid = $user->id;
        $account->exit_time = time() + $config['delay_duration'] * 60 * 60;

        try {
            // 开启事务
            Db::startTrans();

            $account->save();

            // 创建永久版提取订单记录
            $orderData = [
                'ord_bbh' => date('YmdHis') . rand(1000, 9999),
                'ord_type' => 'permanent_extract',
                'ord_type2' => 1, // 永久版订单类型
                'ord_name' => $goods['goods_name'] . '(永久版提取)',
                'ord_uid' => $user->id,
                'ord_ifpay' => 1,
                'GoodType' => 1, // 在线账号（永久版只允许在线账号）
                'is_permanent' => 2, // 永久版提取订单标识
                'ord_aid' => $account->id,
                'time' => date('Y-m-d H:i:s'),
                'account_status' => 1,
                'expiry_date' => date('Y-m-d H:i:s', $account->exit_time),
                'or_maturity' => 1,
                'ord_combo' => $accountId, // 修复：存储商品ID用于永久版权限验证
                'ord_money' => 0, // 提取不收费
                'goods_id' => $accountId, // 新增：明确存储商品ID
            ];

            Order::create($orderData);

            // 记录日志到 userlogo 表
            Db::table('tk_userlogo')->insert([
                'uid' => $user->id,
                'content' => '成功提取永久版账号: ' . $account->ac_name . ' (商品ID: ' . $accountId . ')',
                'ip' => Request::ip(),
                'time' => date('Y-m-d H:i:s')
            ]);

            // 提交事务
            Db::commit();

            return json([
                'code' => 0,
                'msg' => '成功提取永久版账号',
                'data' => $account
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            \think\facade\Log::error('提取永久版账号失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '提取永久版账号失败，请稍后再试', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * 智能分配永久版账号
     *
     * @param int $userId 用户ID
     * @param int $goodsId 商品ID
     * @return Account|null 分配的账号对象
     */
    private function getOptimalPermanentAccount($userId, $goodsId)
    {
        // 1. 查询用户历史提取记录（按时间正序，最早的在前）
        $historyOrders = Order::where([
            'ord_uid' => $userId,
            'is_permanent' => 2, // 永久版提取订单
            'ord_ifpay' => 1,
            'ord_combo' => $goodsId,
            'ord_type' => 'permanent_extract'
        ])->order('id', 'asc')->select(); // 按ID正序，最早提取的在前

        // 2. 如果有历史提取记录，优先检查历史账号
        if (!$historyOrders->isEmpty()) {
            foreach ($historyOrders as $historyOrder) {
                if ($historyOrder['ord_aid']) {
                    $historyAccount = Account::where([
                        'id' => $historyOrder['ord_aid'],
                        'ac_goods' => $goodsId,
                        'ac_sell' => 1,      // 可售
                        'ac_states' => 1,    // 可用
                        'ac_uid' => null,    // 未被占用
                        'goods_Type' => 1    // 只允许在线账号
                    ])->find();

                    if ($historyAccount) {
                        // 找到可用的历史账号，优先分配
                        \think\facade\Log::info("永久版智能分配: 用户{$userId}获得历史账号{$historyAccount['ac_name']}");
                        return $historyAccount;
                    }
                }
            }
        }

        // 3. 没有可用的历史账号，随机分配新的在线账号
        $availableAccounts = Account::where('ac_goods', $goodsId)
            ->where('ac_sell', 1)
            ->where('ac_states', 1)
            ->where('ac_uid', null)    // 确保未被占用
            ->where('goods_Type', 1)   // 只允许在线账号
            ->select();

        if ($availableAccounts->isEmpty()) {
            \think\facade\Log::warning("永久版分配失败: 商品{$goodsId}无可用账号");
            return null;
        }

        // 随机选择一个新账号
        $randomAccount = $availableAccounts[rand(0, count($availableAccounts) - 1)];
        \think\facade\Log::info("永久版智能分配: 用户{$userId}获得随机账号{$randomAccount['ac_name']}");

        return $randomAccount;
    }

    /**************************************
     * 订单管理
     **************************************/

    /**
     * 创建订单接口
     */
    public function order()
    {
        // 生成订单号
        $order = date("YmdHis") . time();

        // 获取请求参数
        $params = Request::param();

        // 参数验证
        if (!isset($params['duration']) || !isset($params['unit']) || !isset($params['goodsId'])) {
            return json(['code' => 1, 'msg' => '缺少必要参数']);
        }

        // 验证时间单位
        if (!in_array($params['unit'], ['day', 'hour'])) {
            return json(['code' => 1, 'msg' => '无效的时间单位']);
        }

        // 验证时长
        $duration = intval($params['duration']);
        if ($duration <= 0) {
            return json(['code' => 1, 'msg' => '时长必须大于0']);
        }

        try {
            // 查询商品信息
            $goods = Goods::find($params['goodsId']);
            if (!$goods) {
                return json(['code' => 1, 'msg' => '商品不存在']);
            }

            // 查询套餐价格信息
            $combo = Combo::where('co_goods', $params['goodsId'])->find();
            if (!$combo) {
                return json(['code' => 1, 'msg' => '套餐价格未设置']);
            }

            // 根据时间单位计算订单金额
            $money = $params['unit'] === 'day' ?
                $duration * $combo['day_price'] :  // 按天单价计算
                $duration * $combo['hour_price'];  // 按小时单价计算

            // 生成订单描述
            $goodsName = $goods['goods_name'] . ' ' . $duration . ($params['unit'] === 'day' ? '天' : '小时');

            // 生成订单链接 - 修复combo_id
            $data = [
                "code" => 0,
                "src" => "/user/index/orderPag?" . http_build_query([
                    'order' => $order,
                    'goodsName' => $goodsName,
                    'duration' => $duration,
                    'unit' => $params['unit'],
                    'money' => $money,
                    'discount' => 0,
                    'yhjid' => $params['yhjId'],
                    'id' => $params['goodsId'],
                    'combo_id' => $combo['id']  // 修复：使用正确的套餐ID
                ])
            ];
            //return $combo['id'];

            return json($data);

        } catch (\Exception $e) {
            \think\facade\Log::error('创建订单失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '创建订单失败，请稍后重试' ]);
        }
    }

    /**
     * 订单页面
     */
    public function orderPag()
    {
        $order = Request::param();
        $order["time"] = date("Y-m-d H:i:s");

        $data = $this->mergeViewData([
            "order" => $order,
            "system" => System::find(1),
            "pay" => Config::find(1),
        ]);

        return View::fetch("/order/index", $data);
    }

    /**
     * 创建离线账号订单接口
     */
    public function offlineOrder()
    {
        // 生成订单号
        $order = date("YmdHis") . time();

        // 获取请求参数
        $params = Request::param();

        // 参数验证
        if (!isset($params['goodsId'])) {
            return json(['code' => 1, 'msg' => '缺少商品ID']);
        }

        try {
            // 查询离线账号商品信息
            $offlineProduct = Db::table('tk_offline')
                ->where('product_id', $params['goodsId'])
                ->find();

            if (!$offlineProduct) {
                return json(['code' => 1, 'msg' => '离线商品不存在']);
            }

            // 获取离线账号固定价格
            $money = $offlineProduct['product_amount'];

            if (!is_numeric($money) || $money <= 0) {
                return json(['code' => 1, 'msg' => '商品价格无效']);
            }

            // 查询商品信息
            $goods = Goods::find($params['goodsId']);
            if (!$goods) {
                return json(['code' => 1, 'msg' => '商品不存在']);
            }

            //查询套餐价格信息
            $of = Offline::where('product_id', $params['goodsId'])->find();
            $combo = Combo::where('co_goods', $params['goodsId'])->find();
            if (!$combo) {
                return json(['code' => 1, 'msg' => '套餐价格未设置']);
            }

            // 生成订单描述
            $goodsName = $offlineProduct['product_name'] ?? $params['goods_name'] . '--离线账号';

            // 生成离线账号订单链接
            $data = [
                "code" => 0,
                "src" => "/user/index/orderPag?" . http_build_query([
                    'order' => $order,
                    'goodsName' => $goodsName,
                    'money' => $money,
                    'discount' => 0,
                    //'yhjid' => $params['yhjId'] ?? '',
                    'id' => $params['goodsId'],
                    'combo_id' => $params['goodsId'],  // 添加套餐ID
                    'is_offline' => true  // 标记为离线账号订单
                ])
            ];

            return json($data);

        } catch (\Exception $e) {
            \think\facade\Log::error('创建离线账号订单失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '创建离线账号订单失败，请稍后重试',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 创建游戏永久版订单接口
     */
    public function createGamePermanentOrder()
    {
        // 生成订单号
        $order = date("YmdHis") . time();

        // 获取请求参数
        $params = Request::param();

        // 参数验证
        if (!isset($params['goodsId'])) {
            return json(['code' => 1, 'msg' => '缺少商品ID']);
        }

        try {
            // 查询游戏信息
            $game = Goods::find($params['goodsId']);
            if (!$game) {
                return json(['code' => 1, 'msg' => '商品不存在']);
            }

            //查询套餐价格信息
            $combo = Combo::where('co_goods', $params['goodsId'])->find();
            if (!$combo) {
                return json(['code' => 1, 'msg' => '套餐价格未设置']);
            }

            // 获取游戏永久版价格
            $permanentPrice = $game['permanent_price2'];

            if (!is_numeric($permanentPrice) || $permanentPrice <= 0) {
                return json(['code' => 1, 'msg' => '游戏价格无效']);
            }

            // 生成订单描述
            $gameName = $game['goods_name'] . ' 永久版';

            // 生成游戏永久版订单链接
            $data = [
                "code" => 0,
                "src" => "/user/index/orderPag?" . http_build_query([
                    'order' => $order,
                    'goodsName' => $gameName,
                    'money' => $permanentPrice,
                    'discount' => 0,
                    'id' => $params['goodsId'],
                    'combo_id' => null,  // 修复：永久版订单不需要套餐ID
                    'goods_id' => $params['goodsId'],  // 新增：明确传递商品ID
                    'is_permanent' => true  // 标记为游戏永久版订单
                ])
            ];

            return json($data);

        } catch (\Exception $e) {
            \think\facade\Log::error('创建游戏永久版订单失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '创建游戏永久版订单失败，请稍后重试',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取会员订单列表
     */
    public function getMemberOrders()
    {
        // 获取当前用户信息
        $user = Us::where([
            "us_username" => Cookie::get("username"),
            "id" => Cookie::get("id"),
        ])->find();

        // 如果用户未找到
        if (!$user) {
            $data = $this->mergeViewData([
                "user" => null,
                "system" => null,
                "vipAccounts" => [],
                "message" => "用户未登录或不存在"
            ]);
            return View::fetch("ticket/vip_list1", $data);
        }

        // 查询系统信息
        $system = System::find(1);

        // 查询会员价格信息
        $membershipPricing = Db::table('tk_membership_pricing')
            ->order('id', 'asc')
            ->select();

        // 判断用户会员等级和到期时间
        $currentTime = date('Y-m-d H:i:s');
        if (empty($user->exit_time) || $user->exit_time < $currentTime) {
            $data = $this->mergeViewData([
                "user" => $user,
                "system" => $system,
                "vipAccounts" => [],
                "membershipPricing" => $membershipPricing,
                "message" => "您的会员已过期,请续费后使用"
            ]);
            return View::fetch("ticket/vip_list1", $data);
        }

        // 判断用户会员等级 - 只排除基础会员
        if ($user->membership_level == "基础会员") {
            $data = $this->mergeViewData([
                "user" => $user,
                "system" => $system,
                "vipAccounts" => [],
                "membershipPricing" => $membershipPricing,
                "message" => "您当前的会员等级无权访问此功能"
            ]);
            return View::fetch("ticket/vip_list1", $data);
        }

        // 允许月费、季费、年费会员访问
        $allowedLevels = ["月费会员", "季费会员", "年费会员", "至尊会员"];
        if (in_array($user->membership_level, $allowedLevels)) {
            // 1. 获取在线VIP账号并处理
            $onlineAccounts = \app\admin\model\Account::where('ac_uid', $user->id)
                ->where('ac_vip', 1)  // VIP用户
                ->where('goods_Type', 1)  // 在线账号
                ->field([
                    'MIN(id) as id',
                    'ac_name',
                    'MIN(ac_password) as ac_password',
                    'MIN(ac_goods) as ac_goods',
                    'MIN(ac_sell) as ac_sell',
                    'MIN(ac_states) as ac_states',
                    'MIN(ac_uid) as ac_uid',
                    'MIN(goods_Type) as goods_Type',
                    'MIN(ac_vip) as ac_vip',
                    'MIN(exit_time) as exit_time',
                    'MIN(time) as time',
                    'MIN(token_limit) as token_limit',
                    'MIN(modify) as modify',
                    'MIN(is_locked) as is_locked'
                ])
                ->group('ac_name')
                ->order('id', 'desc')
                ->select();

            $onlineAccountsWithGoodsName = [];
            foreach ($onlineAccounts as $account) {
                // 根据ac_goods查询商品表获取商品名称
                $goodsInfo = Db::table('tk_goods')
                    ->where('id', $account['ac_goods'])
                    ->field('goods_name')
                    ->find();

                $accountData = $account->toArray();
                $accountData['goods_name'] = $goodsInfo ? $goodsInfo['goods_name'] : '在线账号';
                $accountData['ord_bbh'] = ''; // 添加空的订单号字段
                $onlineAccountsWithGoodsName[] = $accountData;
            }

            // 2. 获取离线账号（通过订单表关联）
            $offlineAccounts = Db::table('tk_order')
                ->alias('o')
                ->join('tk_account a', 'o.ord_aid = a.id')
                ->where([
                    'o.ord_uid' => $user->id,
                    'o.ord_ifpay' => 1,
                    'o.GoodType' => 0,  // 离线账号
                    'o.is_permanent' => 2,  // 会员提取账号订单
                    'o.or_maturity' => 1  // 未到期
                ])
                // 移除错误的过滤条件，因为会员提取的离线账号ord_type就是'vip_extract'
                ->field([
                    'a.id',
                    'a.ac_name',
                    'a.ac_password',
                    'a.ac_goods',
                    'a.ac_sell',
                    'a.ac_states',
                    'a.ac_uid',
                    'a.goods_Type',
                    'a.ac_vip',
                    'o.expiry_date as exit_time',  // 使用订单的到期时间
                    'a.time',
                    'a.token_limit',
                    'a.modify',
                    'a.is_locked',
                    'o.ord_bbh',  // 订单编号
                    'o.time as order_time'  // 订单时间
                ])
                ->order('o.id', 'desc')
                ->select();

            $offlineAccountsWithGoodsName = [];
            foreach ($offlineAccounts as $account) {
                // 根据ac_goods查询商品表获取商品名称
                $goodsInfo = Db::table('tk_goods')
                    ->where('id', $account['ac_goods'])
                    ->field('goods_name')
                    ->find();

                $accountData = $account;
                $accountData['goods_name'] = $goodsInfo ? $goodsInfo['goods_name'] : '离线账号';
                $offlineAccountsWithGoodsName[] = $accountData;
            }

            // 3. 检测账号状态并返回数据
            if (empty($onlineAccountsWithGoodsName) && empty($offlineAccountsWithGoodsName)) {
                $data = $this->mergeViewData([
                    "user" => $user,
                    "system" => $system,
                    "onlineAccounts" => [],
                    "offlineAccounts" => [],
                    "membershipPricing" => $membershipPricing,
                    "message" => "账户没有提取任何VIP账号"
                ]);
            } else {
                $data = $this->mergeViewData([
                    "user" => $user,
                    "system" => $system,
                    "onlineAccounts" => $onlineAccountsWithGoodsName,
                    "offlineAccounts" => $offlineAccountsWithGoodsName,
                    "membershipPricing" => $membershipPricing,
                    "message" => "success"
                ]);
            }
        }
        return View::fetch("ticket/vip_list1", $data);
    }

    /**
     * 获取永久版块专属账号的订单列表
     */
    public function permanent()
    {
        // 获取当前用户信息
        $user = Us::where([
            "us_username" => Cookie::get("username"),
            "id" => Cookie::get("id"),
        ])->find();

        // 如果用户未找到
        if (!$user) {
            // 将错误信息作为数据传递给视图
            $data = $this->mergeViewData([
                "user" => null,
                "system" => null,
                "vipAccounts" => [],
                "message" => "用户未登录或不存在"
            ]);
            return View::fetch("ticket/permanent", $data);
        }

        // 查询系统信息
        $system = System::find(1);

        // 查询用户是否购买了永久版游戏（只查询有效订单，排除被封禁的）
        $permanentOrders = Order::where([
            'ord_uid' => $user->id,
            'is_permanent' => 1,
            'GoodType' => 3,
            'ord_ifpay' => 1,
            'or_maturity' => 1  // 只查询未到期的订单，排除被封禁的
        ])->select();

        $hasPermanentOrder = $permanentOrders->count() > 0;

        // 如果有永久版订单，将订单列表传递给页面
        if ($hasPermanentOrder) {
            // 查询至尊会员专属账号列表
            $vipAccounts = \app\admin\model\Account::where('ac_uid', $user->id)
                ->where('ac_vip', 2)  // 只查询永久版提取标记
                ->order('id', 'desc')  // 按照 ID 降序排列
                ->select();
            // 检测是否有专属账号
            if ($vipAccounts->isEmpty()) {
                // 无专属账号，返回提示信息到视图
                $data = $this->mergeViewData([
                    "user" => $user,
                    "system" => $system,
                    "vipAccounts" => [],
                    "permanentOrders" => $permanentOrders, // 传递订单列表
                    "message" => "账户没有提取永久版账号"
                ]);
            } else {
                // 有专属账号，则正常返回数据
                $data = $this->mergeViewData([
                    "user" => $user,
                    "system" => $system,
                    "vipAccounts" => $vipAccounts,
                    "permanentOrders" => $permanentOrders, // 传递订单列表
                    "message" => "success"
                ]);
            }
        } else {
            // 未购买永久版游戏
            $data = $this->mergeViewData([
                "user" => $user,
                "system" => $system,
                "vipAccounts" => [],
                "permanentOrders" => [], // 空订单列表
                "message" => "当前账号未购买永久版游戏"
            ]);
        }

        return View::fetch("ticket/permanent", $data);
    }

    /**
     * 获取离线账号订单列表
     */
    public function getOfflineAccount()
    {
        // 获取当前用户信息
        $user = Us::where([
            "us_username" => Cookie::get("username"),
            "id" => Cookie::get("id"),
        ])->find();

        // 如果用户未找到
        if (!$user) {
            $data = $this->mergeViewData([
                "user" => null,
                "system" => null,
                "offlineAccounts" => [],
                "message" => "用户未登录或不存在"
            ]);
            return View::fetch("ticket/offline_list", $data);
        }

        // 查询系统信息
        $system = System::find(1);

        // 修改查询逻辑：先从订单表查询，再关联账号表（只查询有效订单，排除被封禁的）
        $offlineAccounts = Db::table('tk_order')
            ->alias('o')
            ->join('tk_account a', 'o.ord_aid = a.id')
            ->where([
                'o.ord_uid' => $user->id,
                'o.ord_ifpay' => 1,
                'o.GoodType' => 0,
                'o.or_maturity' => 1  // 只查询未到期的订单，排除被封禁的
            ])
            // 移除错误的过滤条件，允许显示会员提取的离线账号
            ->field('a.*, o.ord_name, o.ord_type')  // 添加 ord_name 和 ord_type 到查询字段
            ->order('a.id', 'desc')
            ->select();

        // 检测是否有离线账号
        if ($offlineAccounts->isEmpty()) {
            $data = $this->mergeViewData([
                "user" => $user,
                "system" => $system,
                "offlineAccounts" => [],
                "message" => "账户没有离线账号"
            ]);
        } else {
            $data = $this->mergeViewData([
                "user" => $user,
                "system" => $system,
                "offlineAccounts" => $offlineAccounts,
                "message" => "success"
            ]);
        }

        return View::fetch("ticket/offline_list", $data);
    }

    /**
     * 获取普通商品订单列表
     */
    public function onShop()
    {
        // 构建数据集合
        $data = [
            "user" => Us::where([
                "us_username" => Cookie::get("username"),
                "id" => Cookie::get("id"),
            ])->find(),
            "system" => System::find(1),
            // 查询订单时剔除特定ord_name的数据和特定GoodType的数据
            "order" => Order::where([
                "ord_uid" => Cookie::get("id"),
                "ord_ifpay" => 1,
                "or_maturity" => 1,
                "is_permanent" =>[0]//只查询普通订单
            ])
                ->where("ord_name", "<>", "至尊会员") // 过滤掉 "至尊会员"
                ->where("ord_name", "<>", "月费会员") // 过滤掉 "月费会员"
                ->where("ord_name", "<>", "季费会员") // 过滤掉 "季费会员"
                ->where("ord_name", "<>", "年费会员") // 过滤掉 "年费会员"
                ->where("ord_name", "<>", "在线VIP账号提取") // 过滤掉 "在线VIP账号提取"
                ->where("ord_type", "<>", "会员充值") // 过滤掉会员充值订单
                ->where("ord_type", "<>", "离线卡密兑换") // 过滤掉离线卡密兑换订单
                ->whereNotIn("GoodType", [0, 3]) // 过滤掉 GoodType 为 0 和 3 的订单
                ->order("id", "desc")
                ->paginate(10),
        ];

        // 遍历订单数据并转换支付类型显示
        foreach ($data["order"] as $index => $value) {
            switch ($value["ord_type"]) {
                case "wxpay":
                    $data["order"][$index]["ord_type"] = "微信";
                    break;
                case "alipay":
                    $data["order"][$index]["ord_type"] = "支付宝";
                    break;
                case "qqpay":
                    $data["order"][$index]["ord_type"] = "QQ";
                    break;
                case "yepay":
                    $data["order"][$index]["ord_type"] = "余额";
                    break;
                case "CDK兑换":
                case "永久版卡密兑换":
                case "离线卡密兑换":
                case "会员充值":
                    // 保持原始值，不转换
                    $data["order"][$index]["ord_type"] = $value["ord_type"];
                    break;
                default:
                    $data["order"][$index]["ord_type"] = "其他";
                    break;
            }

            // 检查订单是否已支付但未分配账号
            if ($value["ord_ifpay"] == 1 && $value["or_maturity"] == 1 && empty($value["ord_aid"])) {
                // 添加一个标记，表示此订单可以手动提取账号
                $data["order"][$index]["can_extract"] = true;
                // 添加订单备注信息
                $data["order"][$index]["pending_reason"] = $value["pending_reason"] ?? "等待分配账号";
            } else {
                $data["order"][$index]["can_extract"] = false;
            }
        }

        // 将空数组设置为套餐数据（备用字段）
        $data["combo"] = [];

        // 添加通知数据
        $data = $this->mergeViewData($data);

        // 渲染视图并回数据
        return View::fetch("ticket/list1", $data);
    }

    /**************************************
     * 钱包/支付管理
     **************************************/

    /**
     * 我的钱包页面
     */
    public function onmoney()
    {
        $reqData = Request::param("page");
        if (!isset($reqData)) {
            $reqData["page"] = 1;
        }

        $user = Us::where([
            "us_username" => Cookie::get("username"),
            "id" => Cookie::get("id"),
        ])->find();

        // 计算用户已消费金额
        $Consumed = Order::where([
            "ord_uid" => $user["id"],
            "ord_ifpay" => 1,
        ])->select();
        $ConsumedMoney = 0;
        foreach ($Consumed as $val) {
            $ConsumedMoney += $val["ord_money"];
        }

        $data = $this->mergeViewData([
            "user" => $user,
            "system" => System::find(1),
            "order" => Order::where([
                "ord_uid" => $user["id"],
                "ord_ifpay" => 1
            ])
                ->order("id", "desc")
                ->paginate(5),
            "Consumed" => $ConsumedMoney,
        ]);

        return View::fetch("ticket/list3", $data);
    }

    /**
     * 优惠券页面
     */
    public function coupon()
    {
        $user = Us::where([
            "us_username" => Cookie::get("username"),
            "id" => Cookie::get("id"),
        ])->find();

        $data = $this->mergeViewData([
            "user" => $user,
            "system" => System::find(1),
        ]);

        return View::fetch("ticket/list4", $data);
    }

    /**************************************
     * 工单/反馈管理
     **************************************/

    /**
     * 工单列表页面
     */
    public function feedback()
    {
        $work = Workorder::where("work_user", Cookie::get("id"))
            ->order("id", "desc")
            ->paginate(9);

        foreach ($work as $index => $val) {
            // 工单类型转换
            if ($val["work_type"] == 0) {
                $work[$index]["work_type"] = "售前";
            } else if ($val["work_type"] == 1) {
                $work[$index]["work_type"] = "售后";
            }

            // 工单状态转换
            if ($val["work_state"] == 0) {
                $work[$index]["work_state"] = "<b style='color:orange'>正在处理</b>";
            } else if ($val["work_state"] == 1) {
                $work[$index]["work_state"] = "<b style='color:green'>已处理</b>";
            }
        }

        $data = $this->mergeViewData([
            "user" => Us::where("us_username", Cookie::get("username"))->find(),
            "system" => System::find(1),
            "workorder" => $work,
            "order" => Order::where("ord_uid", Cookie::get("id"))->select(),
        ]);

        return View::fetch("ticket/list5", $data);
    }

    /**************************************
     * 系统通知/帮助
     **************************************/

    /**
     * 帮助中心页面
     */
    public function notice()
    {
        // 获取用户信息
        $user = Us::find(Cookie::get('id'));

        // 获取系统设置
        $system = System::find(1);

        // 获取分页参数
        $page = Request::param('page', 1);
        $limit = 10;

        // 获取帮助中心文章 (not_system=0 表示帮助中心文章)
        $helpNotices = \app\admin\model\Notice::where('not_system', '0')
                         ->order('id desc')
                         ->paginate($limit, false, ['page' => $page]);

        // 获取系统公告 (用于侧边栏)
        $systemNotices = \app\admin\model\Notice::where('not_system', '2')
                          ->order('id desc')
                          ->limit(5)
                          ->select();

        // 传递数据到视图
        $data = [
            'user' => $user,
            'system' => $system,
            'notices' => $helpNotices,
            'helpNotices' => $helpNotices,
            'systemNotices' => $systemNotices,
            'currentSection' => 'help'  // 标记当前部分为帮助中心
        ];

        return View::fetch('ticket/list6', $data);
    }

    /**
     * 系统公告页面
     */
    public function systemGG()
    {
        // 获取用户信息
        $user = Us::find(Cookie::get('id'));

        // 获取系统设置
        $system = System::find(1);

        // 获取分页参数
        $page = Request::param('page', 1);
        $limit = 10;

        // 获取系统公告列表
        $systemNotices = \app\admin\model\Notice::where('not_system', '2')
                        ->order('id desc')
                        ->paginate($limit, false, ['page' => $page]);

        // 获取帮助中心文章(用于侧边栏)
        $helpNotices = \app\admin\model\Notice::where('not_system', '0')
                         ->order('id desc')
                         ->limit(5)
                         ->select();

        // 传递数据到视图
        $data = [
            'user' => $user,
            'system' => $system,
            'notices' => $systemNotices,
            'helpNotices' => $helpNotices,
            'systemNotices' => $systemNotices,
            'currentSection' => 'system'  // 标记当前部分为系统公告
        ];

        return View::fetch('ticket/list6', $data);
    }

    /**
     * 公告详情页面
     */
    public function noticeList()
    {
        // 获取公告ID
        $noticeId = Request::param('id');

        if (empty($noticeId)) {
            return redirect('/user/index/notice');
        }

        // 获取用户信息
        $user = Us::find(Cookie::get('id'));

        // 获取系统设置
        $system = System::find(1);

        // 获取公告详情
        $notice = \app\admin\model\Notice::find($noticeId);

        if (!$notice) {
            return redirect('/user/index/notice');
        }

        // 获取帮助中心文章(用于侧边栏)
        $helpNotices = \app\admin\model\Notice::where('not_system', '0')
                         ->order('id desc')
                         ->limit(5)
                         ->select();

        // 获取系统公告(用于侧边栏)
        $systemNotices = \app\admin\model\Notice::where('not_system', '2')
                          ->order('id desc')
                          ->limit(5)
                          ->select();

        // 确定当前查看的是哪种类型的公告
        $currentSection = $notice['not_system'] == '0' ? 'help' : 'system';

        // 传递数据到视图
        $data = [
            'user' => $user,
            'system' => $system,
            'notice' => $notice,
            'helpNotices' => $helpNotices,
            'systemNotices' => $systemNotices,
            'noticeContent' => $notice,
            'currentSection' => $currentSection
        ];

        return View::fetch('ticket/noticeList', $data);
    }

    /**
     * 处理会员开通请求
     */
    public function purchaseMembership()
    {
        try {
            // 获取请求参数
            $memberName = urldecode(Request::param('memberName')); // URL解码会员名称
            $ord_type = urldecode(Request::param('ord_type')); // URL解码会员名称

            // 获取当前用户信息
            $user = Us::where([
                'us_username' => Cookie::get('username'),
                'id' => Cookie::get('id')
            ])->find();

            if (!$user) {
                return json(['code' => 1, 'msg' => '用户未登录或不存在']);
            }

            // 查询会员定价信息
            $membershipInfo = Db::table('tk_membership_pricing')
                ->where('membership_type', $memberName)
                ->find();

            if (!$membershipInfo) {
                return json(['code' => 1, 'msg' => '未找到该会员类型的定价信息']);
            }

            // 生成订单号
            $orderNo = date('YmdHis') . time();

            // 构建订单数据 - 适配支付页面所需格式
            $orderData = [
                'order' => $orderNo,                    // 订单号
                'goodsName' => $memberName,             // 会员名称
                'money' => $membershipInfo['price'],    // 会员价格
                'time' => date('Y-m-d H:i:s'),         // 下单时间
                'discount' => 0,                        // 优惠金额
                'combo_id' => $membershipInfo['id'],    // 会员套餐ID
                'is_membership' => true,                // 标记为会员订单
                'duration' => $membershipInfo['validity_period'], // 会员有效期
                'unit' => 'day',                         // 时间单位(按天)
                'ord_type' => $ord_type,                 // 订单类型
            ];

            // 返回订单页面URL
            $data = [
                'code' => 0,
                'src' => '/user/index/orderPag?' . http_build_query($orderData)
            ];

            return json($data);

        } catch (\Exception $e) {
            \think\facade\Log::error('创建会员订单失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '创建会员订单失败，请稍后重试',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 手动提取订单账号
     * 用于处理已支付但因库存不足未分配账号的订单
     */
    public function extractOrderAccount()
    {
        // 获取订单ID
        $orderId = Request::param('id');

        // 参数验证
        if (empty($orderId)) {
            return json(['code' => 1, 'msg' => '缺少必要参数']);
        }

        // 获取当前用户信息
        $user = Us::where([
            "us_username" => Cookie::get("username"),
            "id" => Cookie::get("id"),
        ])->find();

        // 如果用户未找到
        if (!$user) {
            return json(['code' => 1, 'msg' => '用户未登录或不存在']);
        }

        // 查询订单信息
        $order = Order::where([
            'id' => $orderId,
            'ord_uid' => $user["id"],
            'ord_ifpay' => 1,
            'or_maturity' => 1
        ])->find();

        // 验证订单是否存在
        if (!$order) {
            return json(['code' => 1, 'msg' => '订单不存在或不属于当前用户']);
        }

        // 检查订单是否已分配账号
        if (!empty($order["ord_aid"])) {
            return json(['code' => 1, 'msg' => '该订单已分配账号，无需重新提取']);
        }

        // 获取订单对应的套餐信息
        $combo = Combo::where('id', $order["ord_combo"])->find();
        if (!$combo) {
            return json(['code' => 1, 'msg' => '未找到订单对应的套餐信息']);
        }

        // 查询可用的账号
        $account = Account::where([
            'ac_goods' => $combo["co_goods"],
            'ac_sell' => 1,
            'ac_states' => 1,
            'goods_Type' => 1,
        ])->find();

        // 如果仍然没有可用账号
        if (!$account) {
            return json(['code' => 1, 'msg' => '暂无可用账号，请稍后再试']);
        }

        // 计算到期时间
        $config = Config::find(1);
        $duration = intval($order["duration"] ?? $config['delay_duration']);
        $unit = strtolower($order["unit"] ?? 'hour');
        $seconds = 0;

        if ($unit == 'day') {
            $seconds = $duration * 24 * 3600;
        } elseif ($unit == 'hour') {
            $seconds = $duration * 3600;
        } else {
            $seconds = $config['delay_duration'] * 3600; // 默认使用配置的延迟时间
        }

        $exitTime = time() + $seconds;
        $acName = $account["ac_name"];

        // 开始事务
        Db::startTrans();
        try {
            // ✅ 使用统一的账号分配方法
            \app\controller\Api::assignAccount(
                $acName,
                $user["id"],
                $exitTime,
                ['ac_sell' => 0]
            );

            // 更新订单信息
            $order->ord_aid = $account["id"];
            $order->ord_remarks = '用户手动提取账号成功';
            $order->pending_reason = null;
            $order->save();

            // 记录日志
            Db::table('tk_userlogo')->insert([
                'uid' => $user["id"],
                'content' => '手动提取订单账号成功: ' . $account["id"],
                'ip' => Request::ip(),
                'time' => date('Y-m-d H:i:s')
            ]);

            Db::commit();

            return json([
                'code' => 0,
                'msg' => '提取账号成功',
                'data' => [
                    'account_id' => $account["id"],
                    'account_name' => $account["ac_name"],
                    'account_password' => $account["ac_password"],
                    'exit_time' => date('Y-m-d H:i:s', $exitTime)
                ]
            ]);
        } catch (\Exception $e) {
            Db::rollback();
            \think\facade\Log::error('手动提取账号失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '提取账号失败，请稍后再试']);
        }
    }
}