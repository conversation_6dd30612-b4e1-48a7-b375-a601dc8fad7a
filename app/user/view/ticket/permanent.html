<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$system.sy_title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <script src="/static/js/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        :root {
            --primary: #4f46e5;
            --primary-dark: #4338ca;
            --secondary: #7c3aed;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --gray-light: #f9fafb;
            --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.05);
            --header-height: 72px;
        }

        /* 基础动画 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shimmer {
            0% { background-position: -1000px 0; }
            100% { background-position: 1000px 0; }
        }

        /* 应用动画 */
        .animate-fade-in {
            animation: fadeIn 0.6s ease forwards;
        }

        .animate-slide-up {
            animation: slideUp 0.5s ease-out forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* 交互悬停效果 */
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        .hover-lift:hover {
            transform: translateY(-6px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        /* 玻璃拟态效果 */
        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        /* 精致边框 */
        .border-gradient {
            border: 2px solid transparent;
            background-clip: padding-box, border-box;
            background-origin: padding-box, border-box;
            background-image: linear-gradient(to right, white, white),
                              linear-gradient(to right, var(--primary), var(--secondary));
        }

        /* 3D按钮效果 */
        .button-3d {
            transform-style: preserve-3d;
            transition: all 0.3s ease;
        }

        .button-3d:active {
            transform: translateY(3px);
        }

        /* 标签样式 */
        .tag {
            @apply inline-flex px-3 py-1 text-xs font-medium rounded-full;
            transform: translateZ(0);
            transition: all 0.3s;
        }

        .tag-success {
            @apply bg-green-100 text-green-800;
        }

        .tag-warning {
            @apply bg-yellow-100 text-yellow-800;
        }

        .tag-danger {
            @apply bg-red-100 text-red-800;
        }

        .tag:hover {
            transform: translateY(-1px) scale(1.05);
        }

        /* 卡片样式 */
        .card {
            @apply bg-white rounded-2xl overflow-hidden;
            box-shadow: var(--card-shadow);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            transform: translateY(-4px);
        }

        /* 表格样式 */
        .modern-table {
            table-layout: fixed;
            width: 100%;
            border-spacing: 0;
            background: white;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .modern-table th {
            background: linear-gradient(135deg, #f3f4f6, #f9fafb);
            color: #4b5563;
            font-weight: 600;
            font-size: 0.875rem;
            padding: 1rem 1.25rem;
            text-align: left;
            border-bottom: 2px solid #e5e7eb;
            letter-spacing: 0.025em;
            position: relative;
        }

        .modern-table th:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .modern-table th:hover:after {
            transform: scaleX(1);
        }

        .modern-table th:nth-child(1) {
            width: 25%;
        }

        .modern-table th:nth-child(2) {
            width: 20%;
        }

        .modern-table th:nth-child(3) {
            width: 20%;
        }

        .modern-table th:nth-child(4) {
            width: 35%;
        }

        .modern-table td {
            vertical-align: middle;
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;
        }

        .modern-table tr {
            transition: all 0.2s ease;
        }

        .modern-table tr:hover {
            background-color: #f9fafb;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
        }

        /* 加载状态 */
        .shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 1000px 100%;
            animation: shimmer 2s infinite linear;
        }

        /* 头像样式 */
        .avatar-ring {
            @apply rounded-full border-4 border-white;
            box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.2);
        }

        /* 激活指示器 */
        .active-indicator {
            @apply absolute right-1 bottom-1 w-3 h-3 bg-green-500 rounded-full;
            box-shadow: 0 0 0 2px white;
            z-index: 1;
        }

        /* 彩色背景渐变 */
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        }

        /* 模态框动画 */
        .modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            z-index: 9999 !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
            display: none;
            align-items: center !important;
            justify-content: center !important;
            backdrop-filter: blur(4px);
        }

        .modal.active {
            display: flex !important;
            animation: modalFadeIn 0.3s ease forwards;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .modal-content {
            background: white !important;
            border-radius: 16px !important;
            padding: 24px !important;
            width: 90% !important;
            max-width: 600px !important;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1) !important;
            position: relative !important;
            transform: scale(0.95);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .modal.active .modal-content {
            transform: scale(1) !important;
            opacity: 1 !important;
        }

        /* 环形进度条 */
        .circular-progress {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
        }

        .circular-progress svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .circular-progress circle {
            fill: none;
            stroke-width: 5;
            stroke-linecap: round;
            transform-origin: center;
            transition: stroke-dashoffset 0.3s;
        }

        .circular-bg {
            stroke: #e6e6e6;
        }

        .circular-progress-value {
            stroke: var(--success);
            stroke-dasharray: 283; /* 2πr where r=45 */
            stroke-dashoffset: 0;
        }

        .circular-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            font-weight: 500;
            color: var(--primary-dark);
        }

        /* 账号展示样式 */
        .account-card {
            @apply flex items-center p-4 border border-gray-100 rounded-xl mb-2;
            transition: all 0.3s ease;
        }

        .account-card:hover {
            @apply border-indigo-300 bg-indigo-50 bg-opacity-30;
            transform: translateX(4px);
        }

        .account-img {
            @apply w-16 h-16 rounded-lg object-cover mr-4;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .account-details {
            @apply flex-1;
        }

        .account-extract-btn {
            margin-left: 3px;
            padding: 6px 12px;
            background: #4F46E5;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s;
            box-shadow: 0 2px 5px rgba(79, 70, 229, 0.3);
            white-space: nowrap;
        }

        .account-extract-btn:hover {
            background: #4338CA;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(79, 70, 229, 0.4);
        }

        .account-extract-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 3px rgba(124, 58, 237, 0.4);
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(79, 70, 229, 0.3);
            border-radius: 10px;
            transition: all 0.3s;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(79, 70, 229, 0.5);
        }

        /* 空状态样式 */
        .empty-state {
            @apply py-16 px-6 text-center rounded-xl bg-gray-50;
        }

        .empty-state svg {
            @apply mx-auto w-24 h-24 text-gray-300 mb-4;
        }

        .empty-state h3 {
            @apply text-xl font-medium text-gray-900 mb-2;
        }

        .empty-state p {
            @apply text-gray-500 mb-6 max-w-md mx-auto;
        }

        /* 按钮集合 */
        .btn {
            @apply px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-300;
        }

        .btn-primary {
            @apply bg-indigo-600 text-white;
            box-shadow: 0 4px 6px rgba(79, 70, 229, 0.25);
        }

        .btn-primary:hover {
            @apply bg-indigo-700;
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(79, 70, 229, 0.35);
        }

        .btn-outline {
            @apply border border-gray-300 text-gray-700;
        }

        .btn-outline:hover {
            @apply border-indigo-300 text-indigo-700 bg-indigo-50;
        }

        .btn-danger {
            @apply bg-red-500 text-white;
        }

        .btn-danger:hover {
            @apply bg-red-600;
        }

        .btn-icon {
            @apply inline-flex items-center justify-center;
        }

        .btn-icon svg {
            @apply w-4 h-4 mr-2;
        }

        /* 操作按钮组 */
        .action-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.2s;
            white-space: nowrap;
            margin-bottom: 0.5rem;
            margin-right: 0.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transform: translateY(0);
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .action-btn:active {
            transform: translateY(0);
        }

        .action-blue {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            color: white;
        }

        .action-blue:hover {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
        }

        .action-green {
            background: linear-gradient(135deg, #34d399, #10b981);
            color: white;
        }

        .action-green:hover {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .action-red {
            background: linear-gradient(135deg, #f87171, #ef4444);
            color: white;
        }

        .action-red:hover {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .action-purple {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
        }

        .action-purple:hover {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        /* 自定义搜索框 */
        .search-container {
            @apply relative mb-4;
        }

        .search-input {
            @apply w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-gray-700;
            transition: all 0.3s;
        }

        .search-input:focus {
            @apply border-indigo-300 outline-none;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);
        }

        .search-icon {
            @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400;
        }

        /* 账号信息展示改进 */
        .account-info-container {
            display: flex;
            align-items: center;
        }

        .account-icon {
            width: 42px;
            height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            margin-right: 12px;
            background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
        }

        .account-icon i {
            font-size: 1.2rem;
            color: #4f46e5;
        }

        .account-details .username {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .account-password {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background-color: #f3f4f6;
            border-radius: 9999px;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
            font-size: 0.75rem;
            color: #4b5563;
            cursor: pointer;
            transition: all 0.2s;
        }

        .account-password:hover {
            background-color: #e5e7eb;
        }

        /* 游戏名称和时间显示优化 */
        .game-info, .time-info {
            display: flex;
            align-items: center;
        }

        .game-info i, .time-info i {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            margin-right: 8px;
        }

        .game-info i {
            background-color: #dbeafe;
            color: #3b82f6;
        }

        .time-info i {
            background-color: #f3f4f6;
            color: #6b7280;
        }

        .game-name, .expiry-time {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .game-name {
            color: #1f2937;
        }

        .expiry-time {
            color: #4b5563;
        }

        /* 动画优化 */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-slide-up {
            animation: slideInUp 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }

        /* 修复提取账号按钮样式 */
        .btn.btn-primary.btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            font-weight: 500;
            color: white;
            background-color: #4F46E5;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(79, 70, 229, 0.25);
            transition: all 0.2s;
            border: none;
            outline: none;
            cursor: pointer;
            white-space: nowrap;
        }

        .btn.btn-primary.btn-icon:hover {
            background-color: #4338CA;
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(79, 70, 229, 0.35);
        }

        .btn.btn-primary.btn-icon:active {
            transform: translateY(0);
        }

        .btn.btn-primary.btn-icon .fas {
            margin-right: 0.5rem;
        }

        /* 统一按钮样式 */
        .btn.btn-primary {
            background-color: #4F46E5;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(79, 70, 229, 0.25);
            transition: all 0.2s;
        }

        .btn.btn-primary:hover {
            background-color: #4338CA;
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(79, 70, 229, 0.35);
        }

        /* 响应式导航栏 */
        .responsive-header {
            position: sticky;
            top: 0;
            z-index: 1000;
            background-color: #ffffff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .responsive-header.scrolled {
            padding: 0.75rem 0;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }

        .header-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .logo-section {
            display: flex;
            align-items: center;
        }

        .logo-link {
            display: block;
        }

        .logo-image {
            height: 40px;
            width: auto;
        }

        .main-nav {
            display: flex;
            align-items: center;
        }

        .nav-list {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            margin: 0 0.5rem;
        }

        .nav-link {
            display: block;
            padding: 0.5rem 1rem;
            color: var(--neutral-700);
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
            border-radius: 0.375rem;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary);
            background-color: rgba(79, 70, 229, 0.05);
        }

        .nav-link.active {
            color: var(--primary);
            background-color: rgba(79, 70, 229, 0.1);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 1.5rem;
            height: 2px;
            background-color: var(--primary);
            border-radius: 9999px;
        }

        .user-section {
            display: flex;
            align-items: center;
        }

        .user-dropdown {
            position: relative;
        }

        .user-trigger {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }

        .user-trigger:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .user-avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .user-name {
            margin: 0 0.5rem;
            font-weight: 500;
            color: var(--neutral-800);
            display: none;
        }

        @media (min-width: 768px) {
            .user-name {
                display: block;
            }
        }

        .user-dropdown-menu {
            position: absolute;
            right: 0;
            top: calc(100% + 0.5rem);
            width: 220px;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 0.5rem 0;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s;
            z-index: 100;
            overflow: hidden;
        }

        .user-dropdown:hover .user-dropdown-menu,
        .user-dropdown-menu:hover {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--neutral-700);
            font-size: 0.9375rem;
            transition: all 0.2s;
        }

        .dropdown-item:hover {
            background-color: rgba(0, 0, 0, 0.03);
            color: var(--primary);
        }

        .dropdown-item i {
            margin-right: 0.75rem;
            width: 1.25rem;
            text-align: center;
            color: var(--neutral-500);
        }

        .dropdown-item:hover i {
            color: var(--primary);
        }

        .dropdown-divider {
            height: 1px;
            background-color: rgba(0, 0, 0, 0.1);
            margin: 0.5rem 0;
        }

        /* 移动端导航切换按钮 */
        .nav-toggle {
            display: none;
            flex-direction: column;
            justify-content: space-between;
            width: 30px;
            height: 21px;
            cursor: pointer;
            z-index: 200;
        }

        .nav-toggle span {
            display: block;
            height: 3px;
            width: 100%;
            background-color: var(--neutral-800);
            border-radius: 3px;
            transition: all 0.3s;
        }

        .nav-toggle.active span:first-child {
            transform: translateY(9px) rotate(45deg);
        }

        .nav-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .nav-toggle.active span:last-child {
            transform: translateY(-9px) rotate(-45deg);
        }

        .mobile-user-section {
            display: none;
            padding: 1.5rem;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            margin-top: 1rem;
        }

        .mobile-user-section .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .mobile-user-section .user-avatar {
            width: 3rem;
            height: 3rem;
            margin-right: 1rem;
        }

        .mobile-user-section .user-name {
            display: block;
            font-size: 1.125rem;
        }

        .mobile-user-section .user-actions {
            display: flex;
            gap: 0.5rem;
        }

        .user-action-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem;
            border-radius: 0.375rem;
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--neutral-700);
            font-weight: 500;
            transition: all 0.2s;
        }

        .user-action-btn:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        /* 响应式适配 */
        @media (max-width: 991px) {
            .nav-toggle {
                display: flex;
            }

            .main-nav {
                position: fixed;
                top: 0;
                right: 0;
                width: 280px;
                height: 100vh;
                background-color: white;
                box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
                padding: 5rem 0 2rem;
                flex-direction: column;
                align-items: flex-start;
                transform: translateX(100%);
                transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
                z-index: 100;
                overflow-y: auto;
            }

            .main-nav.active {
                transform: translateX(0);
            }

            .nav-list {
                flex-direction: column;
                width: 100%;
            }

            .nav-item {
                margin: 0;
                width: 100%;
            }

            .nav-link {
                padding: 1rem 1.5rem;
                border-radius: 0;
            }

            .nav-link.active::after {
                left: 1.5rem;
                bottom: 0.75rem;
                transform: none;
            }

            .mobile-user-section {
                display: block;
                width: 100%;
            }

            .nav-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 99;
                backdrop-filter: blur(4px);
                opacity: 0;
                transition: opacity 0.3s;
            }

            .nav-overlay.active {
                display: block;
                opacity: 1;
            }
        }

        /* 整体响应式布局增强 */
        body {
            padding-top: var(--header-height);
        }

        @media (max-width: 767px) {
            :root {
                --header-height: 64px;
            }
        }

        main {
            min-height: calc(100vh - var(--header-height) - 80px); /* 减去头部和大致的页脚高度 */
        }

        @media (max-width: 991px) {
            .container {
                max-width: 100%;
                padding-left: 1rem;
                padding-right: 1rem;
            }

            /* 修改卡片嵌套间距 */
            .card {
                padding: 1.25rem;
            }

            /* 确保表格可滚动 */
            .overflow-x-auto {
                margin: 0 -1.25rem;
                padding: 0 1.25rem;
                width: calc(100% + 2.5rem);
            }
        }

        @media (max-width: 767px) {
            /* 优化移动端卡片和空间 */
            .py-8 {
                padding-top: 1.5rem;
                padding-bottom: 1.5rem;
            }

            .space-y-6 > * + * {
                margin-top: 1rem !important;
            }

            .card {
                border-radius: 0.75rem;
            }

            /* 表格适配 */
            .modern-table th:nth-child(1),
            .modern-table th:nth-child(2),
            .modern-table th:nth-child(3),
            .modern-table th:nth-child(4) {
                width: auto;
            }

            .modern-table td {
                padding: 0.75rem 1rem;
            }

            /* 操作按钮适配 */
            .flex.space-x-2 {
                flex-wrap: wrap;
            }

            .action-btn {
                margin-bottom: 0.5rem;
                padding: 0.375rem 0.625rem;
                font-size: 0.7rem;
                display: flex;
                align-items: center;
                max-width: 80px;
            }

            .action-btn i {
                margin-right: 2px;
            }
        }

        /* 移动端特殊调整 */
        @media (max-width: 576px) {
            /* 用户信息卡片 */
            .gradient-bg {
                height: 24vw;
            }

            .avatar-ring {
                width: 20vw;
                height: 20vw;
                max-width: 80px;
                max-height: 80px;
            }

            .pt-20 {
                padding-top: 3.5rem;
            }

            /* 表格特别处理 */
            .modern-table {
                display: block;
                box-shadow: none;
            }

            .modern-table thead {
                display: none;
            }

            .modern-table tbody, .modern-table tr {
                display: block;
                width: 100%;
            }

            .modern-table tr {
                margin-bottom: 1rem;
                border-radius: 0.5rem;
                border: 1px solid #e5e7eb;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                overflow: hidden;
            }

            .modern-table td {
                display: block;
                padding: 0.75rem 1rem;
                text-align: right;
                border-bottom: none;
                position: relative;
            }

            .modern-table td::before {
                content: attr(data-label);
                position: absolute;
                left: 1rem;
                top: 50%;
                transform: translateY(-50%);
                font-weight: 500;
                color: #4b5563;
            }

            .modern-table td:first-child {
                border-top-left-radius: 0.5rem;
                border-top-right-radius: 0.5rem;
                background-color: #f9fafb;
            }

            .modern-table td:last-child {
                border-bottom-left-radius: 0.5rem;
                border-bottom-right-radius: 0.5rem;
            }

            /* 确保移动端可以正确显示账号信息 */
            .account-info-container, .game-info, .time-info {
                justify-content: flex-end;
            }

            .account-icon, .game-info i, .time-info i {
                order: 2;
                margin-right: 0;
                margin-left: 0.5rem;
            }
        }

        /* 添加到现有style标签中或创建新的style标签 */
        /* 改进模态框样式 */
        .modal-content {
            max-height: 90vh !important; /* 限制最大高度为视口高度的90% */
            display: flex !important;
            flex-direction: column !important;
        }

        .search-container {
            margin-bottom: 1rem !important; /* 增加搜索框与内容区域的间距 */
        }

        #availableAccountsContent {
            flex: 1 !important;
            max-height: 70vh !important; /* 内容区域最大高度 */
            overflow-y: auto !important;
        }

        /* 改进账号卡片网格布局 */
        @media (max-width: 640px) {
            #availableAccountsContent .grid {
                grid-template-columns: 1fr !important; /* 手机上单列显示 */
            }
        }

        /* 确保确认层可以正确显示 */
        .absolute.inset-0.bg-black.bg-opacity-50.z-10 {
            border-radius: 0 !important;
        }

        /* 改进模态框在移动设备上的显示 */
        @media (max-width: 640px) {
            .modal-content {
                width: 95% !important;
                max-width: 95% !important;
                margin: 10px !important;
                padding: 16px !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 custom-scrollbar">
    {include file='../app/user/view/ticket/header.html'}

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-8 animate-fade-in-up">
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 左侧用户信息面板 -->
            <div class="lg:w-1/4 space-y-6">
                <!-- 用户信息卡片 -->
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <div class="h-32 bg-gradient-to-r from-indigo-500 to-purple-600 relative">
                        <div class="absolute -bottom-16 inset-x-0 flex justify-center">
                            <div class="relative">
                                <img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}"
                                     alt="用户头像"
                                     class="w-32 h-32 rounded-full border-4 border-white shadow-lg object-cover">
                                <div class="absolute bottom-2 right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                            </div>
                        </div>
                    </div>
                    <div class="pt-20 pb-6 px-6 text-center">
                        <h3 class="text-xl font-bold text-gray-900 mb-1">{$user.us_username}</h3>
                        <p class="text-sm text-gray-500 mb-4">ID: {$user.id}</p>
                        <div class="inline-flex items-center px-3 py-1 rounded-full bg-indigo-100 text-indigo-800">
                            <i class="fas fa-crown text-yellow-500 mr-2"></i>
                            <span class="font-medium">永久版用户</span>
                        </div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <div class="card animate-slide-up" style="animation-delay: 0.2s">
                    {include file='../app/user/view/ticket/index.html'}
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="lg:w-3/4 space-y-6">
                <!-- 数据概览卡片 -->
                <!-- <div class="grid grid-cols-1 md:grid-cols-3 gap-4 animate-slide-up" style="animation-delay: 0.3s">

                    <div class="card p-6 hover-lift">
                        <div class="flex items-center">
                            <div class="bg-green-100 rounded-full p-3 mr-4">
                                <i class="fas fa-wallet text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">余额</p>
                                <h3 class="text-2xl font-bold text-green-600">¥{$user.us_money|default='0.00'}</h3>
                            </div>
                        </div>
                    </div>
                </div> -->

                <!-- 账号管理区域 -->
                <div class="card animate-slide-up" style="animation-delay: 0.4s">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex justify-between items-center">
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">永久版账号管理</h2>
                                <p class="text-sm text-gray-500 mt-1">管理您的永久版游戏账号</p>
                            </div>

                            {if $message neq '当前未购买永久版游戏'}
                            <button onclick="getAvailableAccounts()"
                                    class="btn btn-primary btn-icon">
                                <i class="fas fa-plus-circle mr-2"></i>
                                提取账号
                            </button>
                            {/if}
                        </div>
                    </div>

                    <div class="p-6">
                        {if $message eq 'success'}
                            {if count($vipAccounts) eq 0}
                                <div class="empty-state">
                                    <i class="fas fa-folder-open text-gray-300 text-7xl"></i>
                                    <h3>暂无账号数据</h3>
                                    <p>您当前还未提取任何永久版账号，点击上方"提取账号"按钮开始使用。</p>
                                    <button onclick="getAvailableAccounts()"
                                            class="btn btn-primary">
                                        <i class="fas fa-plus-circle mr-2"></i>
                                        提取账号
                                    </button>
                                </div>
                            {else}
                                <div class="overflow-x-auto custom-scrollbar">
                                    <table class="modern-table">
                                        <thead>
                                            <tr>
                                                <th>账号信息</th>
                                                <th>游戏名称</th>
                                                <th>到期时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {volist name="vipAccounts" id="account"}
                                            <tr class="animate-slide-up" style="animation-delay: 0.3s">
                                                <td>
                                                    <div class="account-info-container">
                                                        <div class="account-icon">
                                                            <i class="fas fa-user-shield"></i>
                                                        </div>
                                                        <div class="account-details">
                                                            <div class="username">{$account.ac_name}</div>
                                                            <div class="account-password" onclick="copyPassword(this, '{$account.ac_password}')">
                                                                {$account.ac_password}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="game-info">
                                                        <i class="fas fa-gamepad"></i>
                                                        <span class="game-name">
                                                            {php}
                                                            $displayName = '未知游戏';
                                                            $ac_goods = $account['ac_goods'];

                                                            // 尝试从订单中查找游戏名称
                                                            foreach($permanentOrders as $order) {
                                                                if($order['ord_combo'] == $ac_goods) {
                                                                    $displayName = $order['ord_name'];
                                                                    break;
                                                                }
                                                            }

                                                            // 如果找不到匹配，根据ac_goods提供默认名称
                                                            if($displayName == '未知游戏') {
                                                                if($ac_goods == 12) {
                                                                    $displayName = '黑神话悟空 永久版';
                                                                } elseif($ac_goods == 15) {
                                                                    $displayName = '黑神话悟空 永久版';
                                                                }
                                                            }

                                                            echo $displayName;
                                                            {/php}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="time-info">
                                                        <i class="far fa-clock"></i>
                                                        <span class="expiry-time">{:date('Y-m-d H:i', $account.exit_time)}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="flex flex-wrap gap-2">
                                                        <button onclick="getToken('{$account.id}')" class="action-btn action-blue">
                                                            <i class="fas fa-key mr-1"></i>
                                                            提取令牌
                                                        </button>
                                                        <button onclick="ys('{$account.id}')" class="action-btn action-green">
                                                            <i class="fas fa-clock mr-1"></i>
                                                            延长时间
                                                        </button>
                                                        <button onclick="openPermanentSwitchModal('{$account.id}', '{$account.ac_goods}')" class="action-btn action-purple">
                                                            <i class="fas fa-exchange-alt mr-1"></i>
                                                            切换账号
                                                        </button>
                                                        <button onclick="cancelLease('{$account.id}')" class="action-btn action-red">
                                                            <i class="fas fa-trash-alt mr-0.5"></i>
                                                            取消租用
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {/volist}
                                        </tbody>
                                    </table>
                                </div>
                            {/if}
                        {elseif $message eq '当前未购买永久版游戏'}
                            <div class="empty-state">
                                <i class="fas fa-crown text-yellow-300 text-7xl"></i>
                                <h3>尚未购买永久版</h3>
                                <p>购买永久版游戏可获得更多特权和更好的游戏体验</p>
                                <button onclick="purchaseVip()"
                                        class="btn btn-primary">
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    立即购买
                                </button>
                            </div>
                        {/if}
                    </div>
                </div>

                <!-- 常见问题 -->
                <div class="card p-6 animate-slide-up" style="animation-delay: 0.5s">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-question-circle text-indigo-500 mr-2"></i>
                        常见问题
                    </h3>

                    <div class="space-y-4">
                        <div class="border border-gray-100 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <h4 class="font-medium text-gray-900 mb-2">什么是永久版账号？</h4>
                            <p class="text-gray-600 text-sm">永久版账号是指购买后无需续费，可长期使用的游戏账号。相比普通账号，永久版账号提供更稳定的游戏体验。</p>
                        </div>

                        <div class="border border-gray-100 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <h4 class="font-medium text-gray-900 mb-2">如何延长账号使用时间？</h4>
                            <p class="text-gray-600 text-sm">点击账号操作栏中的"延长时间"按钮，系统将自动为您延长3小时的使用时间，每天可延长多次。</p>
                        </div>

                        <div class="border border-gray-100 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <h4 class="font-medium text-gray-900 mb-2">令牌的用途是什么？</h4>
                            <p class="text-gray-600 text-sm">令牌是用于特定游戏登录的临时验证码，点击"提取令牌"后会生成一个30秒有效的验证码，请在有效期内使用。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 令牌模态框 -->
    <div id="tokenModal" class="modal">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-900">账号令牌</h3>
                <button onclick="closeTokenModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="text-center">
                <div class="circular-progress" id="tokenTimer">
                    <svg viewBox="0 0 100 100">
                        <circle class="circular-bg" cx="50" cy="50" r="45" />
                        <circle class="circular-progress-value" cx="50" cy="50" r="45" />
                    </svg>
                    <div class="circular-text"><span id="timerSeconds">30</span>秒</div>
                </div>

                <div class="bg-gray-50 p-5 rounded-xl mb-4 font-mono text-lg tracking-wide text-center" id="tokenValue">
                    令牌加载中...
                </div>

                <p class="text-sm text-gray-500 mb-4">
                    <i class="fas fa-info-circle mr-1"></i>
                    令牌有效期仅有30秒，请及时使用
                </p>

                <button onclick="copyToken()" class="btn btn-primary w-full">
                    <i class="fas fa-copy mr-2"></i>
                    复制令牌
                </button>
            </div>
        </div>
    </div>

    <!-- 可用账号信息模态框 -->
    <div id="availableAccountsModal" class="modal">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-900">选择账号提取</h3>
                <button onclick="closeAvailableAccountsModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" placeholder="搜索游戏名称..." class="search-input" />
            </div>

            <div class="overflow-y-auto custom-scrollbar" id="availableAccountsContent">
                <!-- 动态内容将在此注入 -->
                <div class="py-10 text-center text-gray-400">
                    <i class="fas fa-spinner fa-spin text-3xl mb-3"></i>
                    <p>正在加载可用账号...</p>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/alert.js"></script>
    <script type="text/javascript" charset="utf-8">
        var userId = "{$user.id}";
        var username = "{$user.us_username}";
        let allAccountsData = [];
        let tokenCopyText = '';

        // 全局变量跟踪活动的Toast
        let activeToasts = {};
        let loadingToastId = null;

        // 通用 AJAX 请求函数
        function ajaxRequest(url, type, data, successCallback, errorCallback) {
            $.ajax({
                url: url,
                type: type,
                dataType: "json",
                data: data,
                success: successCallback,
                error: errorCallback || function(xhr) {
                    showToast('错误', '请求失败，请稍后重试', 'error');
                }
            });
        }

        // 更新打开模态框的函数
        function getAvailableAccounts() {
            const loadingToastId = showToast('加载中', '正在获取可用账号...', 'info', true);

            $.ajax({
                url: "/user/Buy/getPurchasedPermanentGames",
                type: "GET",
                dataType: "json",
                data: {},
                success: function(res) {
                    hideToast(loadingToastId);

                    if (res.code === 200) {
                        if (res.data && res.data.length > 0) {
                            allAccountsData = res.data;

                            // 显示模态框
                            openModal('availableAccountsModal');

                            // 渲染数据
                            renderFilteredAccounts(res.data);

                            // 初始化搜索框
                            const searchInput = document.getElementById('searchInput');
                            if(searchInput) {
                                searchInput.value = '';
                                searchInput.focus();

                                searchInput.onkeyup = function() {
                                    const searchTerm = this.value.toLowerCase().trim();
                                    const filteredData = allAccountsData.filter(item =>
                                        item.goods_name.toLowerCase().includes(searchTerm)
                                    );
                                    renderFilteredAccounts(filteredData);
                                };
                            }
                        } else {
                            showToast('提示', '没有可用的永久版游戏', 'warning');
                        }
                    } else {
                        showToast('错误', res.msg || '获取账号列表失败', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    hideToast(loadingToastId);
                    console.error("请求错误:", status, error);
                    showToast('错误', '请求失败，请稍后重试', 'error');
                }
            });
        }

        // 渲染账号列表
        function renderFilteredAccounts(data) {
            const content = document.getElementById('availableAccountsContent');
            if(!content) return;

            // 清空现有内容
            content.innerHTML = '';

            if (data.length === 0) {
                content.innerHTML = `
                    <div class="empty-state py-8 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <h3 class="text-lg font-medium text-gray-700 mb-1">未找到游戏</h3>
                        <p class="text-gray-500 text-sm">尝试使用不同的搜索词</p>
                    </div>
                `;
                return;
            }

            // 使用现代网格布局
            const grid = document.createElement('div');
            grid.className = 'grid grid-cols-1 sm:grid-cols-2 gap-4'; // 修改网格布局类名

            // 添加数据项
            data.forEach((item, index) => {
                const card = document.createElement('div');
                card.className = 'account-card animate-slide-up bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all';
                card.style.animationDelay = `${index * 0.05}s`;

                card.innerHTML = `
                    <div class="p-4">
                        <div class="flex items-center gap-3">
                            <div class="flex-shrink-0">
                                <img src="${item.goods_img || '/static/images/default-game.png'}"
                                     alt="${item.goods_name}"
                                     class="w-14 h-14 rounded-lg object-cover shadow-sm"
                                     onerror="this.src='/static/images/default-game.png'">
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="text-base font-medium text-gray-900 mb-1 truncate">${item.goods_name}</h3>
                                <div class="flex flex-wrap gap-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        永久版
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                        可用: ${item.count}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-3 bg-gray-50 border-t border-gray-100 flex justify-end">
                        <button class="btn-extract" onclick="extractSingleAccount(${item.id})">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            提取账号
                        </button>
                    </div>
                `;

                grid.appendChild(card);
            });

            content.appendChild(grid);

            // 添加提取按钮样式
            const style = document.createElement('style');
            style.textContent = `
                .btn-extract {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0.5rem 1rem;
                    background: linear-gradient(135deg, #4f46e5, #7c3aed);
                    color: white;
                    font-size: 0.875rem;
                    font-weight: 500;
                    border: none;
                    border-radius: 0.375rem;
                    cursor: pointer;
                    transition: all 0.2s;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .btn-extract:hover {
                    background: linear-gradient(135deg, #4338ca, #6d28d9);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
                }

                .btn-extract:active {
                    transform: translateY(0);
                }
            `;

            if(!document.querySelector('style[data-id="extract-button-style"]')) {
                style.setAttribute('data-id', 'extract-button-style');
                document.head.appendChild(style);
            }
        }

        // 提取单个账号
        function extractSingleAccount(accountId) {
            // 创建一个覆盖在当前模态框上的确认层
            const confirmOverlay = document.createElement('div');
            confirmOverlay.className = 'absolute inset-0 bg-black bg-opacity-50 z-10 flex items-center justify-center rounded-xl';
            confirmOverlay.style.backdropFilter = 'blur(4px)';

            confirmOverlay.innerHTML = `
                <div class="bg-white rounded-xl p-6 max-w-md text-center shadow-xl transform transition-all animate-fadeIn">
                    <div class="mb-4 text-indigo-600">
                        <i class="fas fa-question-circle text-5xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">确认提取</h3>
                    <p class="text-gray-600 mb-6">确定要提取此账号吗？提取后账号将与您的用户绑定</p>
                    <div class="flex justify-center space-x-4">
                        <button id="confirm-cancel" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
                            取消
                        </button>
                        <button id="confirm-extract" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                            确认提取
                        </button>
                    </div>
                </div>
            `;

            // 获取模态框内容并添加确认层
            const modalContent = document.querySelector('#availableAccountsModal .modal-content');
            modalContent.appendChild(confirmOverlay);

            // 添加按钮事件
            document.getElementById('confirm-cancel').addEventListener('click', function() {
                modalContent.removeChild(confirmOverlay);
            });

            document.getElementById('confirm-extract').addEventListener('click', function() {
                // 移除确认层
                modalContent.removeChild(confirmOverlay);

                // 显示处理中提示
                const processingOverlay = document.createElement('div');
                processingOverlay.className = 'absolute inset-0 bg-black bg-opacity-50 z-10 flex items-center justify-center rounded-xl';
                processingOverlay.style.backdropFilter = 'blur(4px)';

                processingOverlay.innerHTML = `
                    <div class="bg-white rounded-xl p-6 max-w-md text-center shadow-xl animate-fadeIn">
                        <div class="mb-4 text-blue-600">
                            <i class="fas fa-spinner fa-spin text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">处理中</h3>
                        <p class="text-gray-600">正在提取账号，请稍候...</p>
                    </div>
                `;

                modalContent.appendChild(processingOverlay);

                // 发送AJAX请求
                $.ajax({
                    url: "/user/index/extractPermanentAccount",
                    type: "POST",
                    dataType: "json",
                    data: {
                        id: userId,
                        username: username,
                        account_id: accountId
                    },
                    success: function(res) {
                        // 移除处理中提示
                        modalContent.removeChild(processingOverlay);

                        if(res.code === 0) {
                            // 显示成功提示
                            const successOverlay = document.createElement('div');
                            successOverlay.className = 'absolute inset-0 bg-black bg-opacity-50 z-10 flex items-center justify-center rounded-xl';
                            successOverlay.style.backdropFilter = 'blur(4px)';

                            successOverlay.innerHTML = `
                                <div class="bg-white rounded-xl p-6 max-w-md text-center shadow-xl animate-fadeIn">
                                    <div class="mb-4 text-green-500">
                                        <i class="fas fa-check-circle text-5xl"></i>
                                    </div>
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">账号提取成功!</h3>
                                    <p class="text-gray-600 mb-2">您的账号已成功提取，页面将自动刷新...</p>
                                </div>
                            `;

                            modalContent.appendChild(successOverlay);

                            // 2秒后关闭模态框并刷新页面
                            setTimeout(() => {
                                closeModal('availableAccountsModal');
                                location.reload();
                            }, 2000);
                        } else {
                            // 显示错误提示
                            showErrorOverlay(modalContent, res.msg || '账号提取失败，请稍后重试');
                        }
                    },
                    error: function(xhr, status, error) {
                        // 移除处理中提示
                        modalContent.removeChild(processingOverlay);

                        let errorMsg = '服务器错误，请联系客服';

                        // 检查是否有响应体
                        if (xhr.status === 403) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response && response.msg) {
                                    errorMsg = response.msg;
                                }
                            } catch (e) {
                                // 解析失败，使用默认错误信息
                                console.error('解析错误响应失败:', e);
                            }
                        }

                        // 显示错误提示
                        showErrorOverlay(modalContent, errorMsg);
                    }
                });
            });
        }

        // 显示错误提示弹窗
        function showErrorOverlay(parentElement, errorMessage) {
            const errorOverlay = document.createElement('div');
            errorOverlay.className = 'absolute inset-0 bg-black bg-opacity-50 z-10 flex items-center justify-center rounded-xl';
            errorOverlay.style.backdropFilter = 'blur(4px)';

            errorOverlay.innerHTML = `
                <div class="bg-white rounded-xl p-6 max-w-md text-center shadow-xl animate-fadeIn">
                    <div class="mb-4 text-red-500">
                        <i class="fas fa-times-circle text-5xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">提取失败</h3>
                    <p class="text-gray-600 mb-6">${errorMessage}</p>
                    <button id="error-close" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                        关闭
                    </button>
                </div>
            `;

            parentElement.appendChild(errorOverlay);

            // 2秒后自动关闭错误提示
            setTimeout(() => {
                try {
                    if (errorOverlay.parentElement === parentElement) {
                        parentElement.removeChild(errorOverlay);
                    }
                } catch (e) {
                    console.error('移除错误提示失败:', e);
                }
            }, 2000);

            // 添加关闭按钮事件
            document.getElementById('error-close').addEventListener('click', function() {
                parentElement.removeChild(errorOverlay);
            });
        }

        // 获取令牌
        function getToken(id) {
            const toastId = showToast('加载中', '正在获取令牌...', 'info', true);

            ajaxRequest("/user/base/getVIipAccountToken", "POST", { id: id },
                function(res) {
                    // 成功回调
                    hideToast(toastId);

                    if (res.msg === 1 && res.data && res.data.token) {
                        tokenCopyText = res.data.token;
                        showSuccessTokenModal(res.data.token);
                    } else if (res.msg === 0 && res.content === "已达到令牌提取次数限制") {
                        showErrorTokenModal("已达到令牌提取次数限制");
                    } else {
                        showErrorTokenModal("获取令牌失败：" + (res.content || "请稍后重试"));
                    }
                },
                function(xhr, status, error) {
                    // 错误回调
                    hideToast(toastId);
                    showToast('错误', '获取令牌失败，请检查网络连接', 'error');
                }
            );
        }

        // 延长账号时间
        function ys(vipId) {
            if(!confirm("确定要延长此账号的使用时间吗？将延长3小时")) {
                return;
            }

            const processingToastId = showToast('处理中', '正在延长账号时间...', 'info', true);

            $.ajax({
                url: "/user/index/delayVipAccount",
                type: "POST",
                dataType: "json",
                data: {
                    id: userId,
                    username: username,
                    vip_id: vipId
                },
                success: function(res) {
                    hideToast(processingToastId);
                    if(res.code === 0){
                        showToast('成功', '账号时间已成功延长3小时', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showToast('失败', res.msg || '延长失败', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    hideToast(processingToastId);
                    // 检查是否有响应体
                    try {
                        const responseData = JSON.parse(xhr.responseText);
                        if (responseData && responseData.msg) {
                            // 显示响应体中的错误信息
                            showToast('失败', responseData.msg, 'error');
                        } else {
                            // 没有具体错误信息，显示默认提示
                            showToast('失败', '服务器网络异常，请联系管理员', 'error');
                        }
                    } catch (e) {
                        // 解析JSON失败或没有响应体，显示默认提示
                        showToast('失败', '服务器网络异常，请联系管理员', 'error');
                    }
                }
            });
        }

        // 取消租用
        function cancelLease(vipId) {
            if(!confirm("确定要取消租用该账号吗？此操作不可撤销")) {
                return;
            }

            const cancelToastId = showToast('处理中', '正在取消租用...', 'info', true);

            ajaxRequest("/user/index/取消租用1", "POST", {
                id: userId,
                username: username,
                vip_id: vipId
            }, function(res) {
                hideToast(cancelToastId);
                if(res.code === 200){
                    showToast('成功', '已成功取消租用', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('失败', res.msg || '取消租用失败', 'error');
                }
            }, function(xhr, status, error) {
                hideToast(cancelToastId);
                showToast('失败', '服务器网络异常，请联系管理员', 'error');
            });
        }

        // 购买VIP
        function purchaseVip() {
            var time = Math.floor(Date.now() / 1000);
            var rand = Math.floor(Math.random() * 90000) + 10000;
            var order = '' + time + rand;
            var url = '{$system.sy_url}/user/index/orderPag?id=039&order=' + order + '&goodsName=至尊会员&discount=0&money=288&combo_id=6&duration=3&unit=6';
            window.location.href = url;
        }

        // 修复令牌相关函数
        function showSuccessTokenModal(token) {
            const modal = document.getElementById('tokenModal');
            const tokenValue = document.getElementById('tokenValue');
            const tokenCircle = document.querySelector('.circular-progress-value');

            if(!modal || !tokenValue || !tokenCircle) return;

            // 设置令牌内容
            tokenValue.innerHTML = `
                <div class="flex justify-center items-center p-4 bg-gray-50 rounded-xl font-mono text-lg tracking-wider">
                    <span class="select-all">${token}</span>
                </div>
            `;

            // 设置圆形进度条
            const circumference = 2 * Math.PI * 45; // 与circle r=45对应
            tokenCircle.style.strokeDasharray = circumference;
            tokenCircle.style.stroke = '#10b981';

            // 显示模态框
            openModal('tokenModal');

            // 启动计时器
            startTokenTimer();

            // 设置复制内容
            tokenCopyText = token;
        }

        function showErrorTokenModal(message) {
            const modal = document.getElementById('tokenModal');
            const tokenValue = document.getElementById('tokenValue');
            const timerCircle = document.querySelector('.circular-progress-value');
            const timerText = document.querySelector('.circular-text');

            if(!modal || !tokenValue || !timerCircle || !timerText) return;

            // 设置错误内容
            tokenValue.innerHTML = `
                <div class="flex items-center justify-center p-4 bg-red-50 rounded-xl text-red-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>${message}</span>
                </div>
            `;

            // 设置错误样式
            timerCircle.style.stroke = '#ef4444';
            timerCircle.style.strokeDashoffset = '0';
            timerText.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>';

            // 显示模态框
            openModal('tokenModal');

            // 3秒后自动关闭
            setTimeout(() => {
                closeTokenModal();
            }, 3000);
        }

        function startTokenTimer() {
            let seconds = 30;
            const timerElement = document.getElementById('timerSeconds');
            const timerCircle = document.querySelector('.circular-progress-value');
            const circumference = 2 * Math.PI * 45;

            if(!timerElement || !timerCircle) return;

            timerCircle.style.strokeDasharray = circumference;
            timerCircle.style.stroke = '#10b981';

            if (window.tokenTimer) {
                clearInterval(window.tokenTimer);
            }

            window.tokenTimer = setInterval(() => {
                seconds--;
                if (seconds >= 0) {
                    timerElement.textContent = seconds;
                    const offset = circumference * (1 - seconds / 30);
                    timerCircle.style.strokeDashoffset = offset;

                    // 添加颜色渐变效果
                    if (seconds <= 5) {
                        timerCircle.style.stroke = '#ef4444'; // 红色
                    } else if (seconds <= 10) {
                        timerCircle.style.stroke = '#f59e0b'; // 黄色
                    }
                } else {
                    clearInterval(window.tokenTimer);
                    timerCircle.style.stroke = '#ef4444';
                    document.querySelector('.circular-text').innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>';

                    document.getElementById('tokenValue').innerHTML = `
                        <div class="flex items-center justify-center p-4 bg-red-50 rounded-xl text-red-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>令牌已过期</span>
                        </div>
                    `;

                    // 2秒后自动关闭
                    setTimeout(() => {
                        closeTokenModal();
                    }, 2000);
                }
            }, 1000);
        }

        // 复制令牌
        function copyToken() {
            if(!tokenCopyText) return;

            const tempInput = document.createElement('input');
            tempInput.value = tokenCopyText;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand('copy');
            document.body.removeChild(tempInput);

            // 更新UI以反馈复制成功
            const btnCopy = document.querySelector('.btn-primary.w-full');
            if(btnCopy) {
                const originalHtml = btnCopy.innerHTML;
                btnCopy.innerHTML = '<i class="fas fa-check mr-2"></i>复制成功';
                btnCopy.classList.add('bg-green-500');

                setTimeout(() => {
                    btnCopy.innerHTML = originalHtml;
                    btnCopy.classList.remove('bg-green-500');
                }, 1500);
            }

            showToast('成功', '令牌已复制到剪贴板', 'success');
        }

        // 模态框通用函数
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if(!modal) return;

            // 显示模态框
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // 确保内容显示动画
            const modalContent = modal.querySelector('.modal-content');
            if(modalContent) {
                // 重置变换，确保动画正确播放
                modalContent.style.transform = 'scale(1)';
                modalContent.style.opacity = '1';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if(!modal) return;

            // 设置过渡效果
            const modalContent = modal.querySelector('.modal-content');
            if(modalContent) {
                modalContent.style.transform = 'scale(0.95)';
                modalContent.style.opacity = '0';
            }

            // 延迟300ms后完全隐藏模态框
            setTimeout(function() {
                modal.classList.remove('active');
                document.body.style.overflow = '';

                // 特殊处理令牌模态框
                if(modalId === 'tokenModal') {
                    if(window.tokenTimer) {
                        clearInterval(window.tokenTimer);
                        window.tokenTimer = null;
                    }

                    const timerElement = document.getElementById('timerSeconds');
                    if(timerElement) timerElement.textContent = '30';

                    const progressCircle = document.querySelector('.circular-progress-value');
                    if(progressCircle) {
                        progressCircle.style.strokeDashoffset = '0';
                        progressCircle.style.stroke = '#10b981';
                    }

                    tokenCopyText = '';
                }
            }, 300);
        }

        // 关闭可用账号模态框
        function closeAvailableAccountsModal() {
            closeModal('availableAccountsModal');
        }

        // 关闭令牌模态框
        function closeTokenModal() {
            closeModal('tokenModal');
        }

        // 改进的显示Toast函数
        function showToast(title, message, type, persistent = false) {
            // 如果是加载中提示且已有一个正在显示，先移除旧的
            if (type === 'info' && persistent && loadingToastId) {
                hideToast(loadingToastId);
            }

            // 如果页面中没有toast容器，则创建一个
            if (!document.getElementById('toast-container')) {
                const container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2';
                document.body.appendChild(container);
            }

            const container = document.getElementById('toast-container');
            const id = 'toast-' + Date.now();

            // 确定toast类型样式
            let typeClass = 'bg-gray-800';
            let icon = '<i class="fas fa-info-circle"></i>';

            if (type === 'success') {
                typeClass = 'bg-green-500';
                icon = '<i class="fas fa-check-circle"></i>';
            } else if (type === 'error') {
                typeClass = 'bg-red-500';
                icon = '<i class="fas fa-exclamation-circle"></i>';
            } else if (type === 'warning') {
                typeClass = 'bg-yellow-500';
                icon = '<i class="fas fa-exclamation-triangle"></i>';
            } else if (type === 'info') {
                typeClass = 'bg-blue-500';
                icon = '<i class="fas fa-info-circle"></i>';
            }

            // 创建toast元素
            const toast = document.createElement('div');
            toast.id = id;
            toast.className = `${typeClass} text-white p-4 rounded-lg shadow-lg flex items-start w-80 transform translate-x-full transition-all duration-300`;
            toast.innerHTML = `
                <div class="flex-shrink-0 mr-3">
                    ${icon}
                </div>
                <div class="flex-1">
                    <div class="font-bold">${title}</div>
                    <div class="text-sm">${message}</div>
                </div>
                <button class="ml-4 text-white opacity-70 hover:opacity-100 transition-opacity" onclick="hideToast('${id}')">
                    <i class="fas fa-times"></i>
                </button>
                ${persistent ? '<div class="absolute bottom-0 left-0 h-1 bg-white bg-opacity-30 animate-pulse w-full"></div>' : ''}
            `;

            container.appendChild(toast);

            // 添加动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 记录到活动Toast列表
            activeToasts[id] = {
                type,
                persistent,
                timeoutId: null
            };

            // 对于加载中类型的提示，保存ID以便后续操作
            if (type === 'info' && persistent) {
                loadingToastId = id;

                // 即使是持久性的，也设置一个较长的超时时间(30秒)，防止请求超时导致提示永久存在
                activeToasts[id].timeoutId = setTimeout(() => {
                    hideToast(id);
                    showToast('提示', '操作似乎耗时较长，请检查网络或刷新页面', 'warning');
                }, 30000);
            } else if (!persistent) {
                // 如果不是持久性的，设置自动消失(3秒)
                activeToasts[id].timeoutId = setTimeout(() => {
                    hideToast(id);
                }, 3000);
            }

            return id;
        }

        // 改进的隐藏Toast函数
        function hideToast(id) {
            const toast = document.getElementById(id);
            if (!toast) return;

            // 如果有活动的超时计时器，清除它
            if (activeToasts[id] && activeToasts[id].timeoutId) {
                clearTimeout(activeToasts[id].timeoutId);
            }

            // 如果是当前的加载提示，清除标记
            if (loadingToastId === id) {
                loadingToastId = null;
            }

            // 动画淡出
            toast.style.transform = 'translateX(full)';
            toast.style.opacity = '0';

            // 300ms后移除元素
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                // 从活动列表中移除
                delete activeToasts[id];
            }, 300);
        }

        // 隐藏所有Toast的辅助函数(可在页面卸载时调用)
        function hideAllToasts() {
            Object.keys(activeToasts).forEach(id => {
                hideToast(id);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化游戏卡片样式
            initializeAccountCards();

            // 添加密码复制功能
            addPasswordCopyFeature();

            // 初始化模态框交互
            initializeModalInteractions();

            console.log("页面已加载，初始化完成");
        });

        // 初始化模态框交互
        function initializeModalInteractions() {
            // 为模态框背景添加点击关闭事件
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeModal(this.id);
                    }
                });
            });

            // 为ESC键添加关闭模态框事件
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const activeModal = document.querySelector('.modal.active');
                    if (activeModal) {
                        closeModal(activeModal.id);
                    }
                }
            });
        }

        // 永久版切换账号相关功能
        function openPermanentSwitchModal(currentAccountId, goodsId) {
            showToast('正在加载历史账号...', 'info');

            $.ajax({
                url: "/user/base/getPermanentHistoryAccounts",
                type: "get",
                dataType: "json",
                data: { goodsId: goodsId },
                success: function(res) {
                    hideToast();

                    if (res.msg === 1) {
                        const modal = document.getElementById('permanentSwitchModal');
                        const grid = document.getElementById('permanentSwitchGrid');

                        // 清空之前的内容
                        grid.innerHTML = '';

                        if (res.accounts && res.accounts.length > 0) {
                            // 过滤掉当前正在使用的账号
                            const availableAccounts = res.accounts.filter(account =>
                                !account.is_current && account.can_switch
                            );

                            if (availableAccounts.length > 0) {
                                availableAccounts.forEach(account => {
                                    const accountCard = `
                                        <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-300">
                                            <div class="flex items-center justify-between mb-3">
                                                <div class="flex items-center">
                                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                                        ${account.ac_name.substring(0, 2).toUpperCase()}
                                                    </div>
                                                    <div class="ml-3">
                                                        <h4 class="font-medium text-gray-900">${account.ac_name}</h4>
                                                        <p class="text-sm text-gray-500">历史使用账号</p>
                                                    </div>
                                                </div>
                                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                    ${account.ac_state}
                                                </span>
                                            </div>

                                            <div class="text-sm text-gray-600 mb-3">
                                                <p><i class="far fa-clock mr-1"></i>上次提取: ${account.last_extract_time}</p>
                                            </div>

                                            <button onclick="switchPermanentAccount('${account.id}', '${currentAccountId}', '${goodsId}')"
                                                    class="w-full px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg hover:from-purple-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105">
                                                <i class="fas fa-exchange-alt mr-2"></i>
                                                切换到此账号
                                            </button>
                                        </div>
                                    `;
                                    grid.innerHTML += accountCard;
                                });
                            } else {
                                grid.innerHTML = `
                                    <div class="col-span-full text-center py-8">
                                        <i class="fas fa-info-circle text-gray-300 text-4xl mb-4"></i>
                                        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无可切换账号</h3>
                                        <p class="text-gray-500">您的历史账号都在使用中或不可用</p>
                                    </div>
                                `;
                            }
                        } else {
                            grid.innerHTML = `
                                <div class="col-span-full text-center py-8">
                                    <i class="fas fa-history text-gray-300 text-4xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">暂无历史记录</h3>
                                    <p class="text-gray-500">您还没有提取过其他账号</p>
                                </div>
                            `;
                        }

                        // 显示模态框
                        modal.classList.remove('hidden');
                        setTimeout(() => {
                            modal.querySelector('.transform').classList.remove('scale-95');
                            modal.querySelector('.transform').classList.add('scale-100');
                        }, 10);

                    } else {
                        showToast(res.content || '获取历史账号失败', 'error');
                    }
                },
                error: function() {
                    hideToast();
                    showToast('网络错误，请稍后重试', 'error');
                }
            });
        }

        function closePermanentSwitchModal() {
            const modal = document.getElementById('permanentSwitchModal');
            modal.querySelector('.transform').classList.remove('scale-100');
            modal.querySelector('.transform').classList.add('scale-95');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 200);
        }

        function switchPermanentAccount(accountId, currentAccountId, goodsId) {
            showToast('正在切换账号...', 'info');

            $.ajax({
                url: "/user/base/switchPermanentAccount",
                type: "post",
                dataType: "json",
                data: {
                    accountId: accountId,
                    currentAccountId: currentAccountId,
                    goodsId: goodsId
                },
                success: function(res) {
                    hideToast();

                    if (res.msg === 1) {
                        closePermanentSwitchModal();
                        showToast('账号切换成功！', 'success');

                        // 延迟刷新页面
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showToast(res.content || '切换失败，请稍后再试', 'error');
                    }
                },
                error: function() {
                    hideToast();
                    showToast('网络错误，请稍后重试', 'error');
                }
            });
        }
    </script>

    <!-- 永久版切换账号模态框 -->
    <div id="permanentSwitchModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="transform scale-95 transition-all duration-300 bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-exchange-alt text-white text-xl mr-3"></i>
                        <h3 class="text-xl font-bold text-white">切换历史账号</h3>
                    </div>
                    <button onclick="closePermanentSwitchModal()" class="text-white hover:text-gray-200 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <p class="text-purple-100 text-sm mt-2">选择您之前使用过的账号进行切换</p>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6 overflow-y-auto max-h-[60vh]">
                <div id="permanentSwitchGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- 账号列表将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    {include file='../app/user/view/ticket/footer.html'}
</body>
</html>