<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>添加VIP账号</title>
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">添加VIP账号</div>
        <div class="layui-card-body">
            <form class="layui-form" action="">
                <div class="layui-form-item">
                    <label class="layui-form-label">账号</label>
                    <div class="layui-input-block">
                        <input type="text" name="username" required lay-verify="required" placeholder="请输入账号" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="password" required lay-verify="required" placeholder="请输入密码" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="1" title="可用" checked>
                        <input type="radio" name="status" value="0" title="不可用">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">所属用户ID</label>
                    <div class="layui-input-block">
                        <input type="number" name="user_id" placeholder="请输入所属用户ID" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="vip-account-form-submit">提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
    layui.use(['form', 'layer', 'jquery'], function(){
        var form = layui.form,
            layer = layui.layer,
            $ = layui.$;

        // 监听表单提交
        form.on('submit(vip-account-form-submit)', function(data){
            $.post('/admin/VipAccountController/addVipAccount', data.field, function(res){
                if(res.code === 0){
                    layer.msg('VIP账号添加成功', {icon: 1});
                    // 关闭弹窗
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    // 刷新父页面表格
                    parent.layui.table.reload('vip-account-table');
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');

            return false;
        });
    });
</script>

</body>
</html>
