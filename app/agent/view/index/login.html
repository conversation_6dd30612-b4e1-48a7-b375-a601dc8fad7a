<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>代理商登录系统</title>
  <!-- 使用可靠的CDN链接 -->
  <link rel="stylesheet" href="/static/css/element-plus.css" />
  <link rel="stylesheet" href="/statics/css/all.min.css">
  <link href="/statics/css/nprogress.css" rel="stylesheet">
  <style>
    :root {
      --primary-color: #409EFF;
      --success-color: #67C23A;
      --warning-color: #E6A23C;
      --danger-color: #F56C6C;
      --info-color: #909399;
      --border-radius: 8px;
      --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: "PingFang SC", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
      background-color: #f6f8fa;
      color: #333;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #409EFF, #3654FD);
    }
    
    [v-cloak] { display: none; }
    
    .login-container {
      width: 100%;
      max-width: 420px;
      padding: 40px;
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    .login-logo {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
    }
    
    .login-logo i {
      font-size: 28px;
      margin-right: 10px;
    }
    
    .login-title {
      font-size: 22px;
      color: #333;
      margin-bottom: 10px;
    }
    
    .login-subtitle {
      font-size: 14px;
      color: #909399;
    }
    
    .form-footer {
      margin-top: 30px;
      text-align: center;
      font-size: 13px;
      color: #909399;
    }
    
    .form-footer a {
      color: var(--primary-color);
      text-decoration: none;
    }
    
    .form-footer a:hover {
      text-decoration: underline;
    }
    
    .password-icon {
      cursor: pointer;
    }
    
    @media (max-width: 480px) {
      .login-container {
        padding: 30px 20px;
      }
    }
  </style>
</head>
<body>
  <div id="app" v-cloak>
    <div class="login-container">
      <div class="login-header">
        <div class="login-logo">
          <i class="fas fa-chart-pie"></i>
          <span>代理商后台</span>
        </div>
        <h2 class="login-title">账号登录</h2>
        <p class="login-subtitle">请输入您的账号和密码</p>
      </div>

      <!-- 显示禁用错误信息 -->
      {if condition="session('login_error')"}
      <el-alert
        title="{$Think.session.login_error}"
        type="error"
        :closable="false"
        style="margin-bottom: 20px;">
      </el-alert>
      {/if}
      
      <el-form :model="form" ref="loginForm" :rules="rules" @submit.prevent="handleLogin">
        <input type="hidden" name="__token__" v-model="form.__token__">
        
        <el-form-item prop="username">
          <el-input 
            v-model="form.username"
            placeholder="请输入用户名">
            <template #prefix>
              <i class="fas fa-user"></i>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input 
            v-model="form.password"
            placeholder="请输入密码"
            :type="showPassword ? 'text' : 'password'">
            <template #prefix>
              <i class="fas fa-lock"></i>
            </template>
            <template #suffix>
              <i class="password-icon fas" 
                 :class="showPassword ? 'fa-eye-slash' : 'fa-eye'"
                 @click="showPassword = !showPassword"></i>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            native-type="submit"
            style="width: 100%"
            :loading="submitLoading">
            登录
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- <div class="form-footer">
        <p>登录代表您已同意我们的 <a href="#">服务条款</a> 与 <a href="#">隐私政策</a></p>
        <p style="margin-top: 8px">遇到问题? <a href="#">联系客服支持</a></p>
      </div> -->
    </div>
  </div>

  <!-- 首先加载Vue -->
  <script src="/statics/js/vue.global.prod.js"></script>
  <!-- 加载NProgress -->
  <script src="/statics/js/nprogress.js"></script>
  <!-- 然后加载Element Plus -->
  <script src="/statics/js/element-plus.full.js"></script>
  <!-- 最后加载中文语言包 -->
  <script src="/statics/js/element-plus-locale-zh-cn.min.js"></script>
  <!-- 加载axios -->
  <script src="/statics/js/axios.min.js"></script>

  <script>
    // 获取PHP模板变量
    const csrfToken = "{$token}";
    
    NProgress.start();
    
    const { createApp, ref, reactive } = Vue;
    
    const app = createApp({
      setup() {
        // 表单数据
        const form = reactive({
          username: '',
          password: '',
          __token__: csrfToken
        });
        
        // 表单验证规则
        const rules = {
          username: [
            { required: true, message: '请输入用户名', trigger: 'blur' }
          ],
          password: [
            { required: true, message: '请输入密码', trigger: 'blur' },
            { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
          ]
        };
        
        const loginForm = ref(null);
        const showPassword = ref(false);
        const submitLoading = ref(false);
        
        // 登录方法
        const handleLogin = () => {
          if (!loginForm.value) return;
          
          loginForm.value.validate(async (valid) => {
            if (!valid) return;
            
            submitLoading.value = true;
            
            try {
              const formData = new FormData();
              formData.append('username', form.username);
              formData.append('password', form.password);
              formData.append('__token__', form.__token__);
              
              const res = await axios.post('/agent/index/login', formData, {
                headers: {
                  'X-Requested-With': 'XMLHttpRequest'
                },
                timeout: 10000
              });
              
              if (res.data.code === 1) {
                if (res.data.token) {
                  form.__token__ = res.data.token;
                }
                
                ElementPlus.ElMessage.success('登录成功');
                
                // 登录成功后跳转
                setTimeout(() => {
                  window.location.href = '/agent/';
                }, 500);
              } else {
                ElementPlus.ElMessage.error(res.data.msg || '登录失败');
              }
            } catch (e) {
              ElementPlus.ElMessage.error('网络错误，请稍后重试');
              console.error('Login error:', e);
            } finally {
              submitLoading.value = false;
            }
          });
        };
        
        return {
          form,
          rules,
          loginForm,
          showPassword,
          submitLoading,
          handleLogin
        };
      }
    });
    
    // 获取Element Plus组件和消息通知
    const { ElMessage } = ElementPlus;
    
    // 挂载应用并设置中文语言包
    app.use(ElementPlus, {
      locale: ElementPlusLocaleZhCn
    }).mount('#app');
    
    // 页面加载完成
    window.onload = function() {
      NProgress.done();
    };
  </script>
</body>
</html> 