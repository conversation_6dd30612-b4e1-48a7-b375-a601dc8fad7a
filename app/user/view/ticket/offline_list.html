<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$system.sy_title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <script src="/static/js/jquery.min.js"></script>
    <style>
        /* 动画效果 */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fade-in-up {
            animation: fadeInUp 0.5s ease-out;
        }

        /* 表格动画 */
        .table-row-animate {
            opacity: 0;
            transform: translateY(10px);
            animation: fadeInUp 0.5s ease-out forwards;
        }

        /* 表格样式优化 */
        .modern-table {
            border-spacing: 0;
            width: 100%;
            background: white;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .modern-table th {
            background: linear-gradient(to right, #4f46e5, #6366f1);
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            padding: 1rem;
            text-align: left;
            position: relative;
        }

        .modern-table td {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .modern-table tr:hover td {
            background-color: #f8fafc;
        }

        /* 按钮效果 */
        .btn-token {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-token:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
        }

        .btn-token:active {
            transform: translateY(0);
        }

        .btn-token::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transform: rotate(45deg);
            transition: 0.5s;
        }

        .btn-token:hover::after {
            left: 100%;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 3rem;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .empty-state svg {
            width: 120px;
            height: 120px;
            margin: 0 auto 1.5rem;
            color: #e5e7eb;
        }

        /* 令牌模态框样式 */
        :root {
            --color-primary: #4f46e5;
            --color-primary-dark: #3730a3;
        }

        .scale-in-center {
            transform: scale(0.9);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .scale-in-center.active {
            transform: scale(1);
            opacity: 1;
        }

        /* 令牌模态框样式 */
        #tokenModal {
            opacity: 0;
            transition: opacity 0.3s ease;
            --countdown-size: 110px;
        }

        .countdown-circle {
            position: relative;
            width: var(--countdown-size);
            height: var(--countdown-size);
            margin: 0 auto;
        }

        .countdown-circle svg {
            position: absolute;
            top: 0;
            left: 0;
            width: var(--countdown-size);
            height: var(--countdown-size);
            transform: rotate(-90deg);
        }

        .countdown-circle circle {
            fill: none;
            stroke-width: 8;
            stroke: var(--color-primary);
            stroke-linecap: round;
            stroke-dasharray: 283;
            stroke-dashoffset: 0;
            transition: stroke-dashoffset 1s linear, stroke 0.3s ease;
        }

        .timer-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            font-weight: bold;
            color: var(--color-primary);
        }

        .timer-text span:first-child {
            font-size: 2rem;
            display: block;
            line-height: 1;
        }

        .timer-label {
            font-size: 0.875rem;
            opacity: 0.7;
            margin-top: 0.25rem;
        }

        /* 令牌模态框整体样式优化 */
        #tokenModal .scale-in-center {
            max-width: 480px;
            background: linear-gradient(to bottom, #ffffff, #f8fafc);
            border-radius: 24px;
            overflow: hidden;
            box-shadow:
                0 20px 40px -12px rgba(0, 0, 0, 0.15),
                0 0 1px rgba(0, 0, 0, 0.1);
        }

        /* 模态框头部样式优化 */
        #tokenModal .modal-header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            padding: 1.5rem 2rem;
            position: relative;
            overflow: hidden;
        }

        #tokenModal .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .token-container {
            padding: 1.5rem 2rem 2rem; /* 调整内边距 */
            background: linear-gradient(180deg, #ffffff, #f8fafc);
        }

        .countdown-circle {
            --countdown-size: 160px;
            margin: 0.5rem auto 2rem; /* 调整上边距 */
            padding-top: 0.5rem; /* 添加内边距 */
        }

        .countdown-circle::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: radial-gradient(circle at center, rgba(79, 70, 229, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
        }

        .countdown-circle circle {
            stroke-width: 8;
            filter: drop-shadow(0 0 12px rgba(79, 70, 229, 0.3));
        }

        /* 调整倒计时数字位置 */
        .countdown-circle .timer-text {
            font-size: 1.5rem;
        }

        .countdown-circle .timer-text span:first-child {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 令牌显示区域样式 */
        .token-display {
            margin-top: 1rem;
        }

        .token-label {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }

        .token-value {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem 1.25rem;
            text-align: center;
            letter-spacing: 0.05em;
            word-break: break-all;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .token-value.show {
            animation: tokenReveal 0.6s ease-out;
        }

        @keyframes tokenReveal {
            0% {
                opacity: 0;
                transform: translateY(10px) scale(0.95);
            }
            50% {
                transform: translateY(-2px) scale(1.02);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .token-value::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.6),
                transparent
            );
            animation: shine 2s infinite;
        }

        @keyframes shine {
            to {
                left: 100%;
            }
        }

        .token-hint {
            font-size: 0.75rem;
            color: #6b7280;
            text-align: center;
            line-height: 1.5;
            background: #f9fafb;
            border-radius: 8px;
            padding: 0.75rem;
            border-left: 3px solid #4f46e5;
        }

        /* 模态框底部样式 */
        .modal-footer {
            padding: 1.5rem 2rem;
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            width: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #4338ca, #6d28d9);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- 引用头部 -->
    {include file='../app/user/view/ticket/header.html'}

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-8 animate-fade-in-up">
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 左侧用户信息面板 -->
            <div class="lg:w-1/4 space-y-6">
                <!-- 用户信息卡片 -->
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <div class="h-32 bg-gradient-to-r from-indigo-500 to-purple-600 relative">
                        <div class="absolute -bottom-16 inset-x-0 flex justify-center">
                            <div class="relative">
                                <img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" 
                                     alt="用户头像" 
                                     class="w-32 h-32 rounded-full border-4 border-white shadow-lg object-cover">
                                <div class="absolute bottom-2 right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="pt-20 pb-6 px-6">
                        <div class="text-center">
                            <h3 class="text-xl font-bold text-gray-900">{$user.us_username}</h3>
                            <p class="mt-1 text-sm text-gray-500">ID: {$user.id}</p>
                        </div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <div class="p-4">
                        {include file='../app/user/view/ticket/index.html'}
                    </div>
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="lg:w-3/4">
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <!-- 标题栏 -->
                    <div class="p-6 border-b border-gray-100">
                        <h2 class="text-xl font-semibold text-gray-800">离线账号列表</h2>
                    </div>

                    <!-- 账号列表 -->
                    <div class="p-6">
                        {if $message eq 'success'}
                            {if empty($offlineAccounts)}
                                <div class="empty-state">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                    </svg>
                                    <h3 class="text-xl font-medium text-gray-900">暂无离线账号</h3>
                                    <p class="mt-2 text-gray-500">当前账户下没有可用的离线账号</p>
                                </div>
                            {else}
                                <div class="overflow-x-auto">
                                    <table class="modern-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>名称</th>
                                                <th>账号</th>
                                                <th>密码</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach $offlineAccounts as $key=>$account}
                                            <tr class="table-row-animate" style="animation-delay: {$key * 0.1}s">
                                                <td class="font-mono">{$account.id}</td>
                                                <td>{$account.ord_name}</td>
                                                <td class="font-mono">{$account.ac_name}</td>
                                                <td class="font-mono">
                                                    <span class="px-3 py-1 bg-gray-100 rounded-full">
                                                        {$account.ac_password}
                                                    </span>
                                                </td>
                                                <td>
                                                    <button class="btn-token" data-id="{$account.id}">
                                                        <span class="flex items-center">
                                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                                                      d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                                                            </svg>
                                                            提取令牌
                                                        </span>
                                                    </button>
                                                </td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            {/if}
                        {else}
                            <div class="empty-state">
                                <svg class="w-16 h-16 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                </svg>
                                <h3 class="mt-4 text-xl font-medium text-gray-900">{$message}</h3>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 令牌模态框 -->
    <div id="tokenModal" class="fixed inset-0 bg-gray-900 bg-opacity-75 backdrop-blur-sm flex items-center justify-center hidden z-50 transition-opacity duration-300">
        <div class="scale-in-center">
            <!-- 模态框头部 -->
            <div class="modal-header">
                <h2 class="text-xl font-bold text-white flex items-center relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                    </svg>
                    令牌信息
                </h2>
            </div>

            <!-- 模态框内容 -->
            <div class="token-container">
                <!-- 倒计时区域 -->
                <div class="countdown-wrapper">
                    <div class="countdown-circle">
                        <svg>
                            <circle cx="80" cy="80" r="70"></circle>
                        </svg>
                        <div class="timer-text">
                            <span id="timerSeconds">30</span>
                            <span class="timer-label">秒</span>
                        </div>
                    </div>
                </div>

                <!-- 令牌显示区域 -->
                <div class="token-display">
                    <div class="token-label">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                        </svg>
                        令牌代码
                    </div>
                    <div class="token-value" id="tokenValue">
                        令牌内容将在此显示
                    </div>
                    <div class="token-hint">
                        令牌有效期30秒，请及时使用
                        <br>令牌用于验证账号身份，请勿泄露给他人
                    </div>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="modal-footer">
                <button class="btn btn-primary flex items-center justify-center" onclick="closeTokenModal()">
                    <span>关闭</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script src="/static/js/alert.js"></script>
    <script type="text/javascript" charset="utf-8">
        // 获取令牌
        function getToken(id) {
            // 显示加载提示
            if (typeof showAlert === 'function') {
                showAlert("#16b777", "正在获取令牌", 1);
            }

            $.ajax({
                url: "/user/base/getOfflineToken",
                type: "POST",
                dataType: "json",
                data: {
                    id: id
                },
                success: function(res) {
                    // 隐藏加载提示
                    if (typeof hideAlert === 'function') {
                        hideAlert();
                    }

                    if (res.msg === 1 && res.data && res.data.token) {
                        const modal = document.getElementById('tokenModal');
                        modal.classList.remove('hidden');
                        setTimeout(() => {
                            modal.style.opacity = 1;
                            modal.querySelector('.scale-in-center').classList.add('active');

                            // 添加令牌显示动画
                            const tokenValue = document.getElementById('tokenValue');
                            tokenValue.textContent = res.data.token;
                            tokenValue.classList.add('show');

                            // 启动倒计时
                            startTokenCountdown();
                        }, 10);
                    } else {
                        showMessageModal('error', res.content || "已达到令牌提取次数限制");
                    }
                },
                error: function(xhr, status, error) {
                    // 隐藏加载提示
                    if (typeof hideAlert === 'function') {
                        hideAlert();
                    }

                    let errorMsg = "请求失败，请检查网络连接";
                    if (xhr.status === 404) {
                        errorMsg = "获取令牌功能未找到，请联系管理员";
                    } else if (xhr.status === 500) {
                        errorMsg = "服务器内部错误，请稍后再试";
                    }

                    showMessageModal('error', errorMsg);
                }
            });
        }

        // 启动令牌倒计时
        function startTokenCountdown() {
            const timerElement = document.getElementById('timerSeconds');
            const circleElement = document.querySelector('.countdown-circle circle');
            const circumference = 2 * Math.PI * 70; // r=70

            let timeLeft = 30;
            timerElement.textContent = timeLeft;

            if (window.tokenTimer) {
                clearInterval(window.tokenTimer);
            }

            // 初始化圆环动画
            circleElement.style.strokeDasharray = `${circumference}`;
            circleElement.style.strokeDashoffset = '0';

            window.tokenTimer = setInterval(() => {
                timeLeft--;
                timerElement.textContent = timeLeft;

                // 计算圆环进度
                const progress = (30 - timeLeft) / 30;
                const offset = circumference * progress;
                circleElement.style.strokeDashoffset = offset;

                // 根据剩余时间改变颜色
                if (timeLeft <= 10) {
                    circleElement.style.stroke = '#ef4444'; // 红色警告
                } else if (timeLeft <= 20) {
                    circleElement.style.stroke = '#f59e0b'; // 橙色提醒
                } else {
                    circleElement.style.stroke = 'var(--color-primary)'; // 默认蓝色
                }

                if (timeLeft <= 0) {
                    clearInterval(window.tokenTimer);

                    // 添加消失动画
                    const modal = document.getElementById('tokenModal');
                    modal.querySelector('.token-value').style.transform = 'translateY(20px) scale(0.95)';
                    modal.querySelector('.token-value').style.opacity = '0';

                    setTimeout(() => {
                        closeTokenModal();
                    }, 300);
                }
            }, 1000);
        }

        // 关闭令牌模态框
        function closeTokenModal() {
            const modal = document.getElementById('tokenModal');
            modal.querySelector('.scale-in-center').classList.remove('active');

            // 清除计时器
            if (window.tokenTimer) {
                clearInterval(window.tokenTimer);
                window.tokenTimer = null;
            }

            setTimeout(() => {
                modal.classList.add('hidden');
                modal.style.opacity = 0;

                // 重置UI状态
                document.getElementById('tokenValue').textContent = '令牌内容将在此显示';
                document.getElementById('timerSeconds').textContent = '30';

                const circleElement = document.querySelector('.countdown-circle circle');
                if (circleElement) {
                    circleElement.style.strokeDasharray = '';
                    circleElement.style.strokeDashoffset = '';
                    circleElement.style.stroke = 'var(--color-primary)';
                }

                // 移除动画类
                const tokenValue = document.getElementById('tokenValue');
                tokenValue.classList.remove('show');
                tokenValue.style.transform = '';
                tokenValue.style.opacity = '';
            }, 300);
        }

        // 显示消息模态框
        function showMessageModal(type, message) {
            // 简单的消息提示，可以根据需要扩展
            if (typeof showAlert === 'function') {
                const color = type === 'error' ? '#ef4444' : '#10b981';
                showAlert(color, message, 3);
            } else {
                alert(message);
            }
        }

        // 初始化模态框事件监听
        function initModalListeners() {
            // 令牌模态框点击外部关闭
            document.getElementById('tokenModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeTokenModal();
                }
            });
        }

        $(document).ready(function() {
            // 初始化事件监听
            initModalListeners();

            // 绑定提取令牌按钮事件
            $(document).on('click', '.btn-token', function() {
                const id = $(this).data('id');
                getToken(id);
            });
        });
    </script>
    
    <!-- 引用页脚 -->
    {include file='../app/user/view/ticket/footer.html'}
</body>
</html>

