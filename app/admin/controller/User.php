<?php
namespace app\admin\controller;
use app\BaseController;
use app\admin\model\User as us;
use think\facade\Request;
class User extends BaseController
{
    /**
     * 用户列表接口 - 支持分页和搜索
     *
     * @description 获取用户列表，支持Layui表格的分页、搜索和排序功能
     * @return \think\response\Json 返回符合Layui表格格式的用户列表数据
     */
    public function userList(){
        try {
            $reqData = Request::param();

            // 分页参数验证和处理 - 兼容Layui表格传参
            $page = max(1, intval($reqData['page'] ?? 1));
            $limit = max(1, min(100, intval($reqData['limit'] ?? 10)));  // 正确处理limit参数

            // 搜索参数处理
            $searchUsername = trim($reqData['us_username'] ?? '');
            $searchEmail = trim($reqData['us_email'] ?? '');
            $searchName = trim($reqData['us_name'] ?? '');
            $isSearch = isset($reqData['search']) && $reqData['search'];

            // 构建查询条件 - 使用简化的ThinkPHP语法
            $whereConditions = [];

            // 应用搜索条件
            if ($isSearch && !empty($searchUsername)) {
                $whereConditions[] = ['us_username', 'like', '%' . $searchUsername . '%'];
            }

            if ($isSearch && !empty($searchEmail)) {
                $whereConditions[] = ['us_email', 'like', '%' . $searchEmail . '%'];
            }

            if ($isSearch && !empty($searchName)) {
                $whereConditions[] = ['us_name', 'like', '%' . $searchName . '%'];
            }

            // 获取总记录数（应用搜索条件后）
            if (!empty($whereConditions)) {
                $total = Us::where($whereConditions)->count();
                $dataList = Us::where($whereConditions)
                    ->limit($limit * ($page - 1), $limit)
                    ->order('id', 'desc')
                    ->select();
            } else {
                $total = Us::count();
                $dataList = Us::limit($limit * ($page - 1), $limit)
                    ->order('id', 'desc')
                    ->select();
            }

            // 数据处理 - 格式化显示字段
            $processedData = [];
            foreach ($dataList as $user) {
                $userData = $user->toArray();

                // 格式化注册时间
                if (!empty($userData['time'])) {
                    $userData['formatted_time'] = date('Y-m-d H:i:s', strtotime($userData['time']));
                } else {
                    $userData['formatted_time'] = '-';
                }

                // 格式化会员状态
                if (!empty($userData['exit_time'])) {
                    $exitTime = strtotime($userData['exit_time']);
                    $currentTime = time();
                    if ($exitTime > $currentTime) {
                        $userData['membership_status'] = 'active';
                        $userData['membership_days'] = ceil(($exitTime - $currentTime) / (24 * 60 * 60));
                        $userData['membership_text'] = '会员有效 (剩余' . $userData['membership_days'] . '天)';
                    } else {
                        $userData['membership_status'] = 'expired';
                        $userData['membership_days'] = 0;
                        $userData['membership_text'] = '会员已过期';
                    }
                } else {
                    $userData['membership_status'] = 'none';
                    $userData['membership_days'] = 0;
                    $userData['membership_text'] = '普通用户';
                }

                // 格式化余额显示
                $userData['formatted_money'] = '¥' . number_format($userData['us_money'], 2);

                // 处理头像路径
                if (empty($userData['us_logo'])) {
                    $userData['us_logo'] = '/static/images/default-avatar.png';
                }

                // 安全处理 - 不返回密码字段
                unset($userData['us_password']);

                $processedData[] = $userData;
            }

            // 返回符合Layui表格格式的数据
            return json([
                "code" => 0,                    // 0表示成功
                "msg" => "获取成功",
                "count" => $total,              // 总记录数（应用搜索条件后）
                "data" => $processedData,       // 当前页数据
                "page" => $page,                // 当前页码
                "limit" => $limit,              // 每页显示数量
                "total_pages" => ceil($total / $limit)  // 总页数
            ]);

        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('获取用户列表失败: ' . $e->getMessage());

            // 返回错误信息
            return json([
                "code" => 1,
                "msg" => "获取用户列表失败: " . $e->getMessage(),
                "count" => 0,
                "data" => []
            ], 500);
        }
    }
    /**
     * 添加用户
     *
     * @description 创建新用户账号
     * @return \think\response\Json 返回创建结果
     */
    public function addUser(){
        try {
            $reqData = Request::param();

            // 验证必填字段
            if (empty($reqData['us_username'])) {
                return json(['code' => 1, 'msg' => '用户名不能为空']);
            }

            if (empty($reqData['us_password'])) {
                return json(['code' => 1, 'msg' => '密码不能为空']);
            }

            // 检查用户名是否已存在
            $existUser = Us::where('us_username', $reqData['us_username'])->find();
            if ($existUser) {
                return json(['code' => 1, 'msg' => '用户名已存在']);
            }

            // 检查邮箱是否已存在（如果提供了邮箱）
            if (!empty($reqData['us_email'])) {
                $existEmail = Us::where('us_email', $reqData['us_email'])->find();
                if ($existEmail) {
                    return json(['code' => 1, 'msg' => '邮箱已被使用']);
                }
            }

            // 设置默认值
            $reqData["time"] = date("Y-m-d H:i:s");
            $reqData["us_money"] = $reqData["us_money"] ?? 0;
            $reqData["us_name"] = $reqData["us_name"] ?? $reqData['us_username'];

            // 密码加密（如果需要）
            // $reqData["us_password"] = password_hash($reqData["us_password"], PASSWORD_DEFAULT);

            // 创建用户
            $newUser = Us::create($reqData);

            if ($newUser) {
                return json(['code' => 0, 'msg' => '用户创建成功', 'data' => $newUser]);
            } else {
                return json(['code' => 1, 'msg' => '用户创建失败']);
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('添加用户失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '添加用户失败: ' . $e->getMessage()]);
        }
    }
    /**
     * 更新用户信息
     *
     * @description 更新指定用户的信息
     * @return \think\response\Json 返回更新结果
     */
    public function upUser(){
        try {
            $reqData = Request::param();

            // 验证用户ID
            if (empty($reqData['id'])) {
                return json(['code' => 1, 'msg' => '用户ID不能为空']);
            }

            // 检查用户是否存在
            $user = Us::find($reqData['id']);
            if (!$user) {
                return json(['code' => 1, 'msg' => '用户不存在']);
            }

            // 处理密码字段
            if (!empty($reqData["us_password"])) {
                // 如果提供了新密码，保留密码字段
                // 可以在这里添加密码加密逻辑
                // $reqData["us_password"] = password_hash($reqData["us_password"], PASSWORD_DEFAULT);
            } else {
                // 如果密码为空，则不更新密码字段
                unset($reqData["us_password"]);
            }

            // 检查用户名唯一性（如果要更新用户名）
            if (!empty($reqData['us_username']) && $reqData['us_username'] !== $user['us_username']) {
                $existUser = Us::where('us_username', $reqData['us_username'])
                    ->where('id', '<>', $reqData['id'])
                    ->find();
                if ($existUser) {
                    return json(['code' => 1, 'msg' => '用户名已存在']);
                }
            }

            // 检查邮箱唯一性（如果要更新邮箱）
            if (!empty($reqData['us_email']) && $reqData['us_email'] !== $user['us_email']) {
                $existEmail = Us::where('us_email', $reqData['us_email'])
                    ->where('id', '<>', $reqData['id'])
                    ->find();
                if ($existEmail) {
                    return json(['code' => 1, 'msg' => '邮箱已被使用']);
                }
            }

            // 更新用户信息
            $result = Us::update($reqData);

            if ($result) {
                return json(['code' => 0, 'msg' => '用户信息更新成功']);
            } else {
                return json(['code' => 1, 'msg' => '用户信息更新失败']);
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('更新用户失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '更新用户失败: ' . $e->getMessage()]);
        }
    }
    /**
     * 删除用户（支持批量删除）
     *
     * @description 删除指定的用户，支持单个删除和批量删除
     * @return \think\response\Json 返回删除结果
     */
    public function delUser(){
        try {
            $ids = Request::param("id");

            // 验证参数
            if (empty($ids)) {
                return json(['code' => 1, 'msg' => '请选择要删除的用户']);
            }

            // 确保$ids是数组格式
            if (!is_array($ids)) {
                $ids = [$ids];
            }

            // 验证用户是否存在
            $existUsers = Us::whereIn('id', $ids)->select();
            if ($existUsers->isEmpty()) {
                return json(['code' => 1, 'msg' => '要删除的用户不存在']);
            }

            if (count($existUsers) !== count($ids)) {
                return json(['code' => 1, 'msg' => '部分用户不存在，请刷新页面后重试']);
            }

            // 检查是否有用户正在使用中（可以根据业务需求添加检查逻辑）
            // 例如：检查用户是否有未完成的订单等

            // 执行删除操作
            $deletedCount = Us::destroy($ids);

            if ($deletedCount > 0) {
                $message = count($ids) === 1 ? '用户删除成功' : "成功删除 {$deletedCount} 个用户";
                return json(['code' => 0, 'msg' => $message, 'deleted_count' => $deletedCount]);
            } else {
                return json(['code' => 1, 'msg' => '删除失败，请重试']);
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('删除用户失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '删除用户失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取用户统计数据
     *
     * @description 获取用户总数、VIP用户数、今日新增等统计信息
     * @return \think\response\Json 返回统计数据
     */
    public function getStatistics()
    {
        try {
            // 获取总用户数
            $totalUsers = Us::count();

            // 获取VIP用户数（会员未过期的用户）
            $vipUsers = Us::where('exit_time', '>', date('Y-m-d H:i:s'))->count();

            // 获取今日新增用户数
            $todayStart = date('Y-m-d 00:00:00');
            $todayEnd = date('Y-m-d 23:59:59');
            $todayNewUsers = Us::where('time', 'between', [$todayStart, $todayEnd])->count();

            // 获取活跃用户数（最近7天有登录记录的用户）
            $sevenDaysAgo = date('Y-m-d H:i:s', strtotime('-7 days'));
            $activeUsers = Us::where('time', '>', $sevenDaysAgo)->count();

            return json([
                'code' => 0,
                'msg' => '获取统计数据成功',
                'data' => [
                    'total' => $totalUsers,
                    'vip' => $vipUsers,
                    'today_new' => $todayNewUsers,
                    'active' => $activeUsers
                ]
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('获取用户统计数据失败: ' . $e->getMessage());

            return json([
                'code' => 1,
                'msg' => '获取统计数据失败: ' . $e->getMessage(),
                'data' => [
                    'total' => 0,
                    'vip' => 0,
                    'today_new' => 0,
                    'active' => 0
                ]
            ], 500);
        }
    }
}
