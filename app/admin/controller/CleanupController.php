<?php
namespace app\admin\controller;

use think\facade\Db;
use think\facade\View;
use think\facade\Request;

class CleanupController
{
    /**
     * 数据清理工具页面
     */
    public function index()
    {
        return View::fetch('/cleanup/index');
    }

    /**
     * 清理错误的离线账号记录
     */
    public function cleanOfflineAccounts()
    {
        if (Request::isPost()) {
            try {
                // 开始事务
                Db::startTrans();

                // 查找所有以 OFFLINE_ 开头的账号记录（这些是错误创建的）
                $wrongAccounts = Db::name('account')
                    ->where('ac_name', 'like', 'OFFLINE_%')
                    ->where('goods_Type', 0)
                    ->select();

                if (empty($wrongAccounts)) {
                    return json(['code' => 0, 'msg' => '没有发现需要清理的错误账号记录']);
                }

                $cleanedCount = 0;
                $errorCount = 0;
                $details = [];

                foreach ($wrongAccounts as $account) {
                    try {
                        // 记录详情
                        $details[] = [
                            'id' => $account['id'],
                            'ac_name' => $account['ac_name'],
                            'ac_uid' => $account['ac_uid'],
                            'time' => $account['time']
                        ];

                        // 删除错误的账号记录
                        $deleteResult = Db::name('account')->where('id', $account['id'])->delete();
                        
                        if ($deleteResult) {
                            $cleanedCount++;
                        } else {
                            $errorCount++;
                        }
                    } catch (\Exception $e) {
                        $errorCount++;
                        \think\facade\Log::error('清理账号记录失败', [
                            'account_id' => $account['id'],
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                // 提交事务
                Db::commit();

                return json([
                    'code' => 1,
                    'msg' => "清理完成！成功清理 {$cleanedCount} 条错误记录，失败 {$errorCount} 条",
                    'data' => [
                        'cleaned_count' => $cleanedCount,
                        'error_count' => $errorCount,
                        'details' => $details
                    ]
                ]);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                
                return json([
                    'code' => 0,
                    'msg' => '清理失败：' . $e->getMessage()
                ]);
            }
        }

        return json(['code' => 0, 'msg' => '请求方法错误']);
    }

    /**
     * 检查错误的离线账号记录
     */
    public function checkOfflineAccounts()
    {
        try {
            // 查找所有以 OFFLINE_ 开头的账号记录
            $wrongAccounts = Db::name('account')
                ->where('ac_name', 'like', 'OFFLINE_%')
                ->where('goods_Type', 0)
                ->field('id, ac_name, ac_uid, ac_goods, time')
                ->order('id desc')
                ->select();

            $count = count($wrongAccounts);

            return json([
                'code' => 1,
                'msg' => "发现 {$count} 条错误的离线账号记录",
                'data' => [
                    'count' => $count,
                    'accounts' => $wrongAccounts
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '检查失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查正常的离线账号记录
     */
    public function checkNormalOfflineAccounts()
    {
        try {
            // 查找正常的离线账号记录（不以OFFLINE_开头）
            $normalAccounts = Db::name('account')
                ->where('ac_name', 'not like', 'OFFLINE_%')
                ->where('goods_Type', 0)
                ->field('id, ac_name, ac_states, ac_uid, ac_goods, time')
                ->order('id desc')
                ->limit(50)
                ->select();

            $totalCount = Db::name('account')
                ->where('ac_name', 'not like', 'OFFLINE_%')
                ->where('goods_Type', 0)
                ->count();

            $availableCount = Db::name('account')
                ->where('ac_name', 'not like', 'OFFLINE_%')
                ->where('goods_Type', 0)
                ->where('ac_states', 1)
                ->count();

            $assignedCount = Db::name('account')
                ->where('ac_name', 'not like', 'OFFLINE_%')
                ->where('goods_Type', 0)
                ->where('ac_states', 0)
                ->count();

            return json([
                'code' => 1,
                'msg' => '正常离线账号统计',
                'data' => [
                    'total_count' => $totalCount,
                    'available_count' => $availableCount,
                    'assigned_count' => $assignedCount,
                    'accounts' => $normalAccounts
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '检查失败：' . $e->getMessage()
            ]);
        }
    }
}
