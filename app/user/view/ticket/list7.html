<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>{$system.sy_title} -- 个人资料</title>
	<meta name="description" content="{$system.sy_des}" />
	<meta name="keywords" content="{$system.sy_key}" />
	<link href="/static/css/tailwind.min.css" rel="stylesheet">
	<script src="/static/js/jquery.min.js"></script>
	<style>
		/* 卡片动画效果 */
		.profile-card {
			transition: all 0.3s ease;
		}
		
		.profile-card:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
		}

		/* 按钮动画 */
		.btn-animate {
			transition: all 0.3s ease;
			background-size: 200% auto;
		}
		
		.btn-animate:hover {
			background-position: right center;
			transform: translateY(-1px);
		}

		/* 头像边框动画 */
		.avatar-border {
			position: relative;
			padding: 4px;
			background: linear-gradient(45deg, #4f46e5, #7c3aed);
			border-radius: 50%;
		}

		.avatar-border::after {
			content: '';
			position: absolute;
			top: -2px;
			left: -2px;
			right: -2px;
			bottom: -2px;
			background: linear-gradient(45deg, #4f46e5, #7c3aed);
			border-radius: 50%;
			z-index: -1;
			opacity: 0;
			transition: all 0.3s ease;
		}

		.avatar-border:hover::after {
			opacity: 1;
			transform: scale(1.1);
		}

		/* 绑定状态标签 */
		.bind-status {
			padding: 0.25rem 0.75rem;
			border-radius: 9999px;
			font-size: 0.875rem;
			font-weight: 500;
		}

		.status-bound {
			background-color: #dcfce7;
			color: #16a34a;
		}

		.status-unbound {
			background-color: #fee2e2;
			color: #dc2626;
		}
	</style>
</head>

<body class="bg-gray-50 text-gray-800">

	<!-- 引用头部 -->
	{include file='../app/user/view/ticket/header.html'}	

	<main class="container mx-auto px-4 py-8">
		<div class="flex flex-col lg:flex-row gap-6">
			<!-- 左侧面板 -->
			<div class="lg:w-1/4 space-y-6">
				<!-- 用户信息卡片 -->
				<div class="bg-white rounded-2xl shadow-lg overflow-hidden profile-card">
					<div class="h-32 bg-gradient-to-r from-indigo-500 to-purple-600 relative">
						<div class="absolute -bottom-16 inset-x-0 flex justify-center">
							<div class="avatar-border">
								<img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" 
									 class="w-32 h-32 rounded-full object-cover bg-white"
									 alt="用户头像">
							</div>
						</div>
					</div>
					<div class="pt-20 pb-6 px-6 text-center">
						<h3 class="text-xl font-bold text-gray-900">{$user.us_username}</h3>
						<p class="mt-1 text-sm text-gray-500">个人资料设置</p>
					</div>
				</div>

				<!-- 导航菜单 -->
				<div class="bg-white rounded-2xl shadow-lg overflow-hidden">
					{include file='../app/user/view/ticket/index.html'}
				</div>
			</div>

			<!-- 右侧内容区 -->
			<div class="lg:w-3/4 space-y-6">
				<!-- 基础信息卡片 -->
				<div class="bg-white rounded-2xl shadow-lg overflow-hidden profile-card">
					<div class="p-6 border-b border-gray-100">
						<h2 class="text-xl font-bold text-gray-800">基础信息</h2>
					</div>
					<div class="p-6">
						<div class="space-y-4">
							<div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
								<div>
									<h3 class="text-sm font-medium text-gray-900">用户名</h3>
									<p class="text-sm text-gray-500">您的登录账号/邮箱</p>
								</div>
								<div class="text-base font-medium text-gray-900">
									{$user.us_username|default="未知"}
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 账号绑定卡片 -->
				<div class="bg-white rounded-2xl shadow-lg overflow-hidden profile-card">
					<div class="p-6 border-b border-gray-100">
						<h2 class="text-xl font-bold text-gray-800">账号绑定</h2>
					</div>
					<div class="p-6">
						{if count($bindings) === 0}
							<div class="text-center py-8">
								<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
										  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
								</svg>
								<h3 class="mt-2 text-sm font-medium text-gray-900">暂未绑定第三方账号</h3>
								<p class="mt-1 text-sm text-gray-500">绑定后可以快速登录</p>
								<div class="mt-6 space-y-4">
									<button onclick="bindLogin()" 
											class="btn-animate w-full bg-gradient-to-r from-indigo-600 to-purple-600 
												   text-white py-3 px-4 rounded-lg font-medium hover:from-indigo-700 
												   hover:to-purple-700 focus:outline-none focus:ring-2 
												   focus:ring-offset-2 focus:ring-indigo-500">
										 绑定微信账号
									</button>
									<button onclick="bindLogin_qq()" 
											class="btn-animate w-full bg-gradient-to-r from-blue-600 to-cyan-600 
												   text-white py-3 px-4 rounded-lg font-medium hover:from-blue-700 
												   hover:to-cyan-700 focus:outline-none focus:ring-2 
												   focus:ring-offset-2 focus:ring-blue-500">
										 绑定QQ账号
									</button>
								</div>
							</div>
						{else}
							<div class="space-y-4">
								{volist name="bindings" id="binding"}
								<div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
									<div class="flex items-center space-x-3">
										<span class="text-base font-medium text-gray-900">
											{$binding.nickname|default="未知昵称"}
										</span>
										<span class="bind-status status-bound">已绑定</span>
									</div>
									<div class="text-sm text-gray-500">
										绑定时间：{$binding.create_time|default="未知时间"}
									</div>
								</div>
								{/volist}
								<button onclick="unbindLogin()" 
										class="btn-animate w-full bg-gradient-to-r from-red-600 to-pink-600 
											   text-white py-3 px-4 rounded-lg font-medium hover:from-red-700 
											   hover:to-pink-700 focus:outline-none focus:ring-2 
											   focus:ring-offset-2 focus:ring-red-500">
									解除绑定
								</button>
							</div>
						{/if}
					</div>
				</div>

				<!-- 安全设置卡片 -->
				<div class="bg-white rounded-2xl shadow-lg overflow-hidden profile-card">
					<div class="p-6 border-b border-gray-100">
						<h2 class="text-xl font-bold text-gray-800">安全设置</h2>
					</div>
					<div class="p-6">
						<button onclick="changePassword()" 
								class="btn-animate w-full bg-gradient-to-r from-green-600 to-teal-600 
									   text-white py-3 px-4 rounded-lg font-medium hover:from-green-700 
									   hover:to-teal-700 focus:outline-none focus:ring-2 
									   focus:ring-offset-2 focus:ring-green-500">
							修改登录密码
						</button>
					</div>
				</div>
			</div>
		</div>
	</main>

	<!-- 引用页脚 -->
	{include file='../app/user/view/ticket/footer.html'}

	<script>
		// 显示成功或错误消息（来自后端）
		$(document).ready(function () {
			const successMsg = "{$Think.session.success}";
			const errorMsg = "{$Think.session.error}";
			if (successMsg) {
				alert(successMsg);
			}
			if (errorMsg) {
				alert(errorMsg);
			}
		});

		// 点击绑定快捷登录
		function bindLogin() {
			window.location.href = "/user/BindingController/bind"; // 跳转到绑定接口
		}

		function bindLogin_qq() {
			window.location.href = "/user/BindingController/bind?type=qq"; // 跳转到绑定接口
		}

		// 点击解绑快捷登录
		function unbindLogin() {
			if (confirm("确定解绑当前绑定的快捷登录吗？")) {
				window.location.href = "/user/BindingController/unbind"; // 跳转到解绑接口
			}
		}

		// 点击修改密码按钮
		function changePassword() {
			window.location.href = "/user/login/changePasswordPage";
		}
	</script>
</body>
</html>
