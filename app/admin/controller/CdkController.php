<?php
namespace app\admin\controller;

use think\facade\View;
use think\facade\Request;
use app\BaseController;
use app\admin\model\Cdk;
use app\admin\model\Goods; // 假设有一个Goods模型
use think\response\Json;
use think\facade\Db;

class CdkController extends BaseController
{
    // 默认页面
    public function index()
    {
        return View::fetch('/cdk/index');
    }

    // 获取CDK列表
    public function getList(): Json
    {
        $page = Request::param('page', 1, 'intval');
        $limit = Request::param('limit', 10, 'intval');
        $filters = Request::only(['search', 'agent_username', 'status', 'cdk_type']);

        $query = Db::table('cdk_cards')
            ->alias('c')
            ->leftJoin('goods g', 'c.shop = g.id')
            ->leftJoin('agents a', 'c.agent_id = a.id')
            ->field('c.*, g.goods_name as shop, a.username as agent_username, c.is_banned');

        // 模糊搜索：支持搜索卡密代码和商品名称
        if (!empty($filters['search'])) {
            $searchTerm = trim($filters['search']);
            $query->where(function($q) use ($searchTerm) {
                $q->where('c.cdk_code', 'like', "%{$searchTerm}%")
                  ->whereOr('g.goods_name', 'like', "%{$searchTerm}%");
            });
        }

        // 代理账号筛选
        if (!empty($filters['agent_username'])) {
            $query->where('a.username', 'like', "%{$filters['agent_username']}%");
        }

        // 状态筛选
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'banned') {
                $query->where('c.is_banned', 1);
            } else {
                $query->where('c.is_banned', 0)->where('c.status', $filters['status']);
            }
        }

        // 卡密类型筛选
        if (!empty($filters['cdk_type'])) {
            if ($filters['cdk_type'] === 'membership') {
                // 会员卡密以 member_ 开头
                $query->where('c.cdk_type', 'like', 'member_%');
            } else {
                $query->where('c.cdk_type', $filters['cdk_type']);
            }
        }

        $count = $query->count();
        $cdkList = $query->page($page, $limit)->select();

        // 处理cdk_type中的member_前缀
        foreach ($cdkList as &$cdk) {
            if (isset($cdk['cdk_type']) && strpos($cdk['cdk_type'], 'member_') === 0) {
                // 使用str_replace替换前缀，或者使用substr截取
                $cdk['cdk_type'] = str_replace('member_', '', $cdk['cdk_type']);
                // 也可以使用下面的方式
                // $cdk['cdk_type'] = substr($cdk['cdk_type'], 7); // 7是'member_'的长度
            }
        }

        return json(["code" => 0, "msg" => "", "count" => $count, "data" => $cdkList]);
    }

    // 新增CDK
    public function add()
    {
        if (Request::isPost()) {
            $reqData = Request::param();

            if (isset($reqData['cdk_type']) && isset($reqData['goods_id'])) {
                // 获取生成数量，默认为1
                $num = isset($reqData['num']) ? intval($reqData['num']) : 1;

                // 验证生成数量
                if ($num < 1 || $num > 100) {
                    return json(['msg' => 0, 'content' => '生成数量必须在1-100之间']);
                }

                $cdkCodes = [];
                $insertData = [];
                $time = date('Y-m-d H:i:s');

                // 批量生成卡密
                for ($i = 0; $i < $num; $i++) {
                    // 生成唯一 CDK 码，长度为12位
                    $prefix_type = $reqData['cdk_type'];
                    $random_string = substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 5)), 0, 12);
//                    $cdk_code = "Kongzue-{$prefix_type}-{$random_string}";
                    $cdk_code = $random_string;

//                    $cdkCodes[] = $cdk_code;
                    $cdkCodes[] = $random_string;

                    // 准备插入数据
                    $insertData[] = [
                        'cdk_code' => $cdk_code,
                        'cdk_type' => $reqData['cdk_type'],
                        'shop' => $reqData['goods_id'],
                        'expiry_date' => null, // 激活时设置
                        'expiry_date2' => $reqData['expiry_date2'], // 卡密时效数字控制
                        'status' => 'unused', // 默认未使用
                        'created_at' => $time, // 记录生成时间
                    ];
                }

                // 批量插入数据
                Cdk::insertAll($insertData);

                if ($num == 1) {
                    return json(['msg' => 1, 'content' => '卡密创建成功', 'cdk_code' => $cdkCodes[0]]);
                } else {
                    return json(['msg' => 1, 'content' => '批量生成卡密成功', 'cdk_codes' => $cdkCodes]);
                }
            }

            return json(['msg' => 0, 'content' => '参数不完整']);
        }
        return View::fetch('/cdk/add');
    }

    public function delete()
    {
        $id = Request::post('id');

        if (!$id) {
            return json(['msg' => 0, 'content' => '缺少ID参数']);
        }

        if (Cdk::destroy($id)) {
            return json(['msg' => 1, 'content' => '删除成功']);
        }
        return json(['msg' => 0, 'content' => '删除失败']);
    }

    // 批量删除CDK
    public function deleteBatch()
    {
        $ids = Request::param('ids');
        if (empty($ids)) {
            return json(['msg' => 0, 'content' => '请选择要删除的项']);
        }

        if (Cdk::destroy($ids)) {
            return json(['msg' => 1, 'content' => '批量删除成功']);
        }
        return json(['msg' => 0, 'content' => '批量删除失败']);
    }

    // 新增会员卡密生成方法
    public function addMembershipCdk()
    {
        if (Request::isPost()) {
            $reqData = Request::param();

            if (isset($reqData['membership_type'])) {
                // 获取会员价格和有效期
                $membershipInfo = Db::name('membership_pricing')
                    ->where('id', $reqData['membership_type'])
                    ->find();

                if (!$membershipInfo) {
                    return json(['msg' => 0, 'content' => '会员类型不存在']);
                }

                // 获取生成数量，默认为1
                $num = isset($reqData['num']) ? intval($reqData['num']) : 1;
                if ($num <= 0 || $num > 100) {
                    return json(['msg' => 0, 'content' => '生成数量必须在1-100之间']);
                }

                $cdkCodes = [];
                $insertData = [];

                for ($i = 0; $i < $num; $i++) {
                    // 生成唯一 CDK 码，长度为12位
                    $prefix_type = 'member';
                    $random_string = substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 5)), 0, 12);
//                    $cdk_code = "Kongzue-{$prefix_type}-{$random_string}";
                    $cdk_code = $random_string;
//                    $cdkCodes[] = $cdk_code;
                    $cdkCodes[] = $random_string;

                    // 插入数据库的初始数据
                    $insertData[] = [
                        'cdk_code' => $cdk_code,
                        'cdk_type' => 'member_' . $membershipInfo['membership_type'], // 使用会员类型作为cdk_type的一部分
                        'shop' => $membershipInfo['id'], // 存储会员类型ID，用于兑换时查询
                        'expiry_date' => null, // 激活时设置
                        'expiry_date2' => $membershipInfo['validity_period'], // 使用数据库中定义的有效期（天数）
                        'status' => 'unused', // 默认未使用
                        'cost' => 0, // 成本设为0
                        'agent_id' => null, // 不指定代理
                        'created_at' => date('Y-m-d H:i:s'), // 记录生成时间
                    ];
                }

                // 批量插入数据
                Cdk::insertAll($insertData);

                if ($num == 1) {
                    return json(['msg' => 1, 'content' => '会员卡密创建成功', 'cdk_code' => $cdkCodes[0]]);
                } else {
                    return json(['msg' => 1, 'content' => '批量生成会员卡密成功', 'cdk_codes' => $cdkCodes]);
                }
            }

            return json(['msg' => 0, 'content' => '参数不完整']);
        }

        // 获取所有会员类型
        $membershipTypes = Db::name('membership_pricing')->select();

        return View::fetch('/cdk/add_membership', [
            'membershipTypes' => $membershipTypes
        ]);
    }

    // 新增永久版卡密生成方法
    public function addPermanentCdk()
    {
        if (Request::isPost()) {
            $reqData = Request::param();

            if (isset($reqData['goods_id'])) {
                // 获取生成数量，默认为1
                $num = isset($reqData['num']) ? intval($reqData['num']) : 1;

                // 验证生成数量
                if ($num < 1 || $num > 100) {
                    return json(['msg' => 0, 'content' => '生成数量必须在1-100之间']);
                }

                // 获取商品信息
                $goodsInfo = Db::name('goods')->where('id', $reqData['goods_id'])->find();

                if (!$goodsInfo) {
                    return json(['msg' => 0, 'content' => '商品不存在']);
                }

                $cdkCodes = [];
                $insertData = [];
                $time = date('Y-m-d H:i:s');

                // 批量生成永久版卡密
                for ($i = 0; $i < $num; $i++) {
                    // 生成唯一 CDK 码，长度为12位
                    $prefix_type = 'perm';
                    $random_string = substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 5)), 0, 12);
                    $cdk_code = $random_string;
//                    $cdk_code = "Kongzue-{$prefix_type}-{$random_string}";

//                    $cdkCodes[] = $cdk_code;
                    $cdkCodes[] = $random_string;

                    // 准备插入数据
                    $data = [
                        'cdk_code' => $cdk_code,
                        'cdk_type' => 'permanent',
                        'shop' => $reqData['goods_id'],
                        'expiry_date' => null, // 永久版不设置到期时间
                        'expiry_date2' => 99, // 99表示永久
                        'status' => 'unused', // 默认未使用
                        'cost' => isset($reqData['cost']) ? $reqData['cost'] : 0, // 可选成本
                        'created_at' => $time, // 记录生成时间
                    ];

                    if (isset($reqData['agent_id']) && !empty($reqData['agent_id'])) {
                        $data['agent_id'] = $reqData['agent_id'];
                    }

                    $insertData[] = $data;
                }

                // 批量插入数据
                Cdk::insertAll($insertData);

                if ($num == 1) {
                    return json(['msg' => 1, 'content' => '永久版卡密创建成功', 'cdk_code' => $cdkCodes[0]]);
                } else {
                    return json(['msg' => 1, 'content' => '批量生成永久版卡密成功', 'cdk_codes' => $cdkCodes]);
                }
            }

            return json(['msg' => 0, 'content' => '参数不完整']);
        }

        // 获取所有商品
        $goodsList = Db::name('goods')->select();

        return View::fetch('/cdk/add_permanent', [
            'goodsList' => $goodsList
        ]);
    }

    // 批量生成卡密
    public function batchGenerate()
    {
        if (Request::isPost()) {
            $reqData = Request::param();

            if (!isset($reqData['type']) || !isset($reqData['num']) || !isset($reqData['target_id'])) {
                return json(['msg' => 0, 'content' => '参数不完整']);
            }

            $type = $reqData['type']; // membership、permanent 或 offline
            $num = intval($reqData['num']); // 生成数量
            $targetId = $reqData['target_id']; // 会员类型ID或商品ID
            $validityPeriod = isset($reqData['validity_period']) ? $reqData['validity_period'] : 0;
            $agentId = isset($reqData['agent_id']) ? $reqData['agent_id'] : null;

            if ($num <= 0 || $num > 100) {
                return json(['msg' => 0, 'content' => '生成数量必须在1-100之间']);
            }

            $cdkCodes = [];
            $insertData = [];

            for ($i = 0; $i < $num; $i++) {
                $prefix_type = ($type == 'membership') ? 'member' : (($type == 'offline') ? 'offline' : 'perm');
                $random_string = substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 5)), 0, 12);
                $cdk_code = $random_string;
                $cdkCodes[] = $random_string;

                $cost = 0;
                $cdk_type = $type;
                $shopValue = $targetId; // 默认使用targetId

                if ($type == 'membership') {
                    $membershipInfo = Db::name('membership_pricing')->where('id', $targetId)->find();
                    if ($membershipInfo) {
                        $cost = $membershipInfo['price'];
                        $cdk_type = 'member_' . $membershipInfo['membership_type']; // 使用会员类型作为cdk_type
                        $shopValue = $targetId; // 会员卡密：shop存储membership_type_id
                    }
                } elseif ($type == 'offline') {
                    // 离线卡密处理
                    $offlineInfo = Db::name('offline')->where('product_id', $targetId)->find();
                    if ($offlineInfo) {
                        $cost = $offlineInfo['product_amount'];
                        $cdk_type = 'offline'; // 离线卡密类型
                        $shopValue = $targetId; // 离线卡密：shop存储goods_id
                    }
                } elseif ($type == 'permanent') {
                    // 永久版卡密处理
                    $goodsInfo = Db::name('goods')->where('id', $targetId)->find();
                    if ($goodsInfo) {
                        $cost = $goodsInfo['goods_price'] ?? 0;
                        $cdk_type = 'permanent'; // 永久版卡密类型
                        $shopValue = $targetId; // 永久版卡密：shop存储goods_id
                    }
                }

                $insertData[] = [
                    'cdk_code' => $cdk_code,
                    'cdk_type' => $cdk_type,
                    'shop' => $shopValue,
                    'expiry_date' => null,
                    'expiry_date2' => ($type == 'membership') ? $validityPeriod : (($type == 'offline') ? 0 : 99), // 永久版设置为99
                    'status' => 'unused',
                    'cost' => $cost,
                    'agent_id' => $agentId,
                    'created_at' => date('Y-m-d H:i:s'),
                ];
            }

            // 记录生成日志
            \think\facade\Log::info('批量生成卡密', [
                'type' => $type,
                'target_id' => $targetId,
                'num' => $num,
                'agent_id' => $agentId,
                'insert_data_sample' => $insertData[0] ?? null
            ]);

            // 批量插入数据
            Cdk::insertAll($insertData);

            return json(['msg' => 1, 'content' => '批量生成成功', 'cdk_codes' => $cdkCodes]);
        }

        // 获取会员类型和商品列表
        $membershipTypes = Db::name('membership_pricing')->select();
        $goodsList = Db::name('goods')->select();

        return View::fetch('/cdk/batch_generate', [
            'membershipTypes' => $membershipTypes,
            'goodsList' => $goodsList
        ]);
    }

    // 新增离线卡密生成方法
    public function addOfflineCdk()
    {
        if (Request::isPost()) {
            $reqData = Request::param();

            if (isset($reqData['goods_id'])) {
                // 获取生成数量，默认为1
                $num = isset($reqData['num']) ? intval($reqData['num']) : 1;

                // 验证生成数量
                if ($num < 1 || $num > 100) {
                    return json(['msg' => 0, 'content' => '生成数量必须在1-100之间']);
                }

                // 获取商品信息
                $goodsInfo = Db::name('goods')->where('id', $reqData['goods_id'])->find();
                if (!$goodsInfo) {
                    return json(['msg' => 0, 'content' => '商品不存在']);
                }

                // 检查是否设置了离线价格
                $offlinePrice = Db::name('offline')->where('product_id', $reqData['goods_id'])->find();
                if (!$offlinePrice) {
                    return json(['msg' => 0, 'content' => '该商品未设置离线价格，请先在商品管理中设置离线价格']);
                }

                $cdkCodes = [];
                $insertData = [];
                $time = date('Y-m-d H:i:s');

                // 批量生成离线卡密
                for ($i = 0; $i < $num; $i++) {
                    // 生成唯一 CDK 码，长度为12位
                    $random_string = substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 5)), 0, 12);
                    $cdk_code = $random_string;

                    $cdkCodes[] = $random_string;

                    // 准备插入数据
                    $data = [
                        'cdk_code' => $cdk_code,
                        'cdk_type' => 'offline', // 离线卡密类型
                        'shop' => $reqData['goods_id'],
                        'expiry_date' => null, // 离线卡密激活后永久有效
                        'expiry_date2' => 0, // 0表示永久
                        'status' => 'unused', // 默认未使用
                        'cost' => $offlinePrice['product_amount'], // 使用离线价格作为成本
                        'created_at' => $time, // 记录生成时间
                    ];

                    if (isset($reqData['agent_id']) && !empty($reqData['agent_id'])) {
                        $data['agent_id'] = $reqData['agent_id'];
                    }

                    $insertData[] = $data;
                }

                // 批量插入数据
                Cdk::insertAll($insertData);

                if ($num == 1) {
                    return json(['msg' => 1, 'content' => '离线卡密创建成功', 'cdk_code' => $cdkCodes[0]]);
                } else {
                    return json(['msg' => 1, 'content' => '批量生成离线卡密成功', 'cdk_codes' => $cdkCodes]);
                }
            }

            return json(['msg' => 0, 'content' => '参数不完整']);
        }

        // 获取所有有离线价格设置的商品
        $goodsList = Db::name('goods')
            ->alias('g')
            ->join('offline o', 'g.id = o.product_id', 'INNER')
            ->field('g.id, g.goods_name, o.product_amount')
            ->select();

        // 获取代理列表
        $agentsList = Db::name('agents')->select();

        return View::fetch('/cdk/add_offline', [
            'goodsList' => $goodsList,
            'agentsList' => $agentsList
        ]);
    }

    // 卡密调试工具
    public function debug()
    {
        if (Request::isPost()) {
            $cdkCode = trim(Request::param('cdk_code', ''));

            if (empty($cdkCode)) {
                return json(['code' => 0, 'msg' => '请输入卡密码']);
            }

            // 查询卡密详细信息
            $cdkCard = Db::table('cdk_cards')
                ->where('cdk_code', $cdkCode)
                ->find();

            if (!$cdkCard) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            // 获取关联的商品信息
            $goods = null;
            if ($cdkCard['shop']) {
                $goods = Db::name('goods')->where('id', $cdkCard['shop'])->find();
            }

            // 获取代理信息
            $agent = null;
            if ($cdkCard['agent_id']) {
                $agent = Db::name('agents')->where('id', $cdkCard['agent_id'])->find();
            }

            // 获取会员类型信息（如果是会员卡密）
            $membershipInfo = null;
            if (strpos($cdkCard['cdk_type'], 'member_') === 0) {
                $membershipInfo = Db::name('membership_pricing')->where('id', $cdkCard['shop'])->find();
            }

            // 获取离线价格信息（如果是离线卡密）
            $offlineInfo = null;
            if ($cdkCard['cdk_type'] === 'offline') {
                $offlineInfo = Db::name('offline')->where('product_id', $cdkCard['shop'])->find();
            }

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'cdk_card' => $cdkCard,
                    'goods' => $goods,
                    'agent' => $agent,
                    'membership_info' => $membershipInfo,
                    'offline_info' => $offlineInfo
                ]
            ]);
        }

        return View::fetch('/cdk/debug');
    }

    // 简化版调试工具
    public function debugSimple()
    {
        if (Request::isPost()) {
            $cdkCode = trim(Request::param('cdk_code', ''));

            if (empty($cdkCode)) {
                return json(['code' => 0, 'msg' => '请输入卡密码']);
            }

            // 查询卡密详细信息
            $cdkCard = Db::table('cdk_cards')
                ->where('cdk_code', $cdkCode)
                ->find();

            if (!$cdkCard) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            // 获取关联的商品信息
            $goods = null;
            if ($cdkCard['shop']) {
                $goods = Db::name('goods')->where('id', $cdkCard['shop'])->find();
            }

            // 获取代理信息
            $agent = null;
            if ($cdkCard['agent_id']) {
                $agent = Db::name('agents')->where('id', $cdkCard['agent_id'])->find();
            }

            // 获取会员类型信息（如果是会员卡密）
            $membershipInfo = null;
            if (strpos($cdkCard['cdk_type'], 'member_') === 0) {
                $membershipInfo = Db::name('membership_pricing')->where('id', $cdkCard['shop'])->find();
            }

            // 获取离线价格信息（如果是离线卡密）
            $offlineInfo = null;
            if ($cdkCard['cdk_type'] === 'offline') {
                $offlineInfo = Db::name('offline')->where('product_id', $cdkCard['shop'])->find();
            }

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'cdk_card' => $cdkCard,
                    'goods' => $goods,
                    'agent' => $agent,
                    'membership_info' => $membershipInfo,
                    'offline_info' => $offlineInfo
                ]
            ]);
        }

        return View::fetch('/cdk/debug_simple');
    }

    // 快速调试接口 - 直接返回JSON
    public function quickDebug()
    {
        $cdkCode = trim(Request::param('cdk_code', ''));

        if (empty($cdkCode)) {
            return json(['code' => 0, 'msg' => '请输入卡密码']);
        }

        try {
            // 查询卡密详细信息 - 直接使用表名
            $cdkCard = Db::table('cdk_cards')
                ->where('cdk_code', $cdkCode)
                ->find();

            if (!$cdkCard) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            // 获取关联的商品信息
            $goods = null;
            if ($cdkCard['shop']) {
                $goods = Db::name('goods')->where('id', $cdkCard['shop'])->find();
            }

            // 获取代理信息
            $agent = null;
            if ($cdkCard['agent_id']) {
                $agent = Db::name('agents')->where('id', $cdkCard['agent_id'])->find();
            }

            // 获取会员类型信息（如果是会员卡密）
            $membershipInfo = null;
            if (strpos($cdkCard['cdk_type'], 'member_') === 0) {
                $membershipInfo = Db::name('membership_pricing')->where('id', $cdkCard['shop'])->find();
            }

            // 获取离线价格信息（如果是离线卡密）
            $offlineInfo = null;
            if ($cdkCard['cdk_type'] === 'offline') {
                $offlineInfo = Db::name('offline')->where('product_id', $cdkCard['shop'])->find();
            }

            // 一致性检查
            $consistency = [];
            if ($cdkCard['cdk_type'] === 'permanent') {
                $consistency[] = $goods ? '✅ 永久版卡密关联商品正常' : '❌ 永久版卡密未找到关联商品';
            } elseif (strpos($cdkCard['cdk_type'], 'member_') === 0) {
                $consistency[] = $membershipInfo ? '✅ 会员卡密关联会员类型正常' : '❌ 会员卡密未找到关联会员类型';
            } elseif ($cdkCard['cdk_type'] === 'offline') {
                $consistency[] = ($goods && $offlineInfo) ? '✅ 离线卡密关联商品和离线价格正常' : '❌ 离线卡密关联信息不完整';
            }

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'cdk_card' => $cdkCard,
                    'goods' => $goods,
                    'agent' => $agent,
                    'membership_info' => $membershipInfo,
                    'offline_info' => $offlineInfo,
                    'consistency_check' => $consistency,
                    'ban_status' => [
                        'is_banned' => $cdkCard['is_banned'] ?? 0,
                        'banned_at' => $cdkCard['banned_at'] ?? null,
                        'banned_reason' => $cdkCard['banned_reason'] ?? null,
                        'banned_by' => $cdkCard['banned_by'] ?? null
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]
            ]);
        }
    }

    // 测试会员离线账号显示问题
    public function testMemberOfflineAccounts()
    {
        $userId = Request::param('user_id');

        if (empty($userId)) {
            return json(['code' => 0, 'msg' => '请提供用户ID']);
        }

        try {
            // 查询用户信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return json(['code' => 0, 'msg' => '用户不存在']);
            }

            // 查询会员提取的离线账号订单
            $offlineOrders = Db::table('tk_order')
                ->alias('o')
                ->join('tk_account a', 'o.ord_aid = a.id')
                ->where([
                    'o.ord_uid' => $userId,
                    'o.ord_ifpay' => 1,
                    'o.GoodType' => 0,  // 离线账号
                    'o.is_permanent' => 2,  // 会员提取账号订单
                    'o.ord_type' => 'vip_extract'  // 会员提取类型
                ])
                ->field([
                    'o.id as order_id',
                    'o.ord_bbh',
                    'o.ord_type',
                    'o.ord_name',
                    'o.GoodType',
                    'o.is_permanent',
                    'o.or_maturity',
                    'o.expiry_date',
                    'a.id as account_id',
                    'a.ac_name',
                    'a.ac_password',
                    'a.ac_goods',
                    'a.goods_Type'
                ])
                ->order('o.id', 'desc')
                ->select();

            // 查询所有离线账号订单（不限制ord_type）
            $allOfflineOrders = Db::table('tk_order')
                ->alias('o')
                ->join('tk_account a', 'o.ord_aid = a.id')
                ->where([
                    'o.ord_uid' => $userId,
                    'o.ord_ifpay' => 1,
                    'o.GoodType' => 0  // 离线账号
                ])
                ->field([
                    'o.id as order_id',
                    'o.ord_bbh',
                    'o.ord_type',
                    'o.ord_name',
                    'o.GoodType',
                    'o.is_permanent',
                    'o.or_maturity',
                    'o.expiry_date',
                    'a.id as account_id',
                    'a.ac_name',
                    'a.ac_password',
                    'a.ac_goods',
                    'a.goods_Type'
                ])
                ->order('o.id', 'desc')
                ->select();

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'user' => $user,
                    'vip_extract_offline_orders' => $offlineOrders,
                    'all_offline_orders' => $allOfflineOrders,
                    'vip_extract_count' => count($offlineOrders),
                    'all_offline_count' => count($allOfflineOrders)
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 封禁卡密功能
     */
    public function banCdk()
    {
        if (Request::isPost()) {
            $cdkCode = trim(Request::param('cdk_code', ''));
            $reason = trim(Request::param('reason', ''));
            $adminId = session('admin_id'); // 获取当前管理员ID

            if (empty($cdkCode)) {
                return json(['code' => 0, 'msg' => '请输入卡密码']);
            }

            // 封禁原因不是必填的，可以为空

            try {
                // 检查数据库字段是否存在，如果不存在则先添加
                $this->ensureBanFields();

                // 查询卡密信息
                $cdkCard = Db::table('cdk_cards')
                    ->where('cdk_code', $cdkCode)
                    ->find();

                if (!$cdkCard) {
                    return json(['code' => 0, 'msg' => '卡密不存在']);
                }

                // 检查是否已被封禁（如果字段存在）
                if (isset($cdkCard['is_banned']) && $cdkCard['is_banned'] == 1) {
                    return json(['code' => 0, 'msg' => '该卡密已被封禁']);
                }

                // 开始事务
                Db::startTrans();

                // 封禁卡密
                $updateResult = Db::table('cdk_cards')
                    ->where('cdk_code', $cdkCode)
                    ->update([
                        'is_banned' => 1,
                        'banned_at' => date('Y-m-d H:i:s'),
                        'banned_reason' => $reason,
                        'banned_by' => $adminId
                    ]);

                if (!$updateResult) {
                    throw new \Exception('更新卡密状态失败');
                }

                $resultMsg = '卡密封禁成功';

                // 记录卡密状态信息用于调试
                \think\facade\Log::info('封禁卡密状态检查', [
                    'cdk_code' => $cdkCode,
                    'status' => $cdkCard['status'] ?? 'null',
                    'assigned_user' => $cdkCard['assigned_user'] ?? 'null',
                    'cdk_type' => $cdkCard['cdk_type'] ?? 'null'
                ]);

                // 如果卡密已使用，需要扣除对应权益
                // 支持多种状态格式：'used', '已使用', 'activated'
                $isUsed = in_array($cdkCard['status'], ['used', '已使用', 'activated']) &&
                         !empty($cdkCard['assigned_user']);

                if ($isUsed) {
                    $userId = $cdkCard['assigned_user'];
                    $cdkType = $cdkCard['cdk_type'];

                    \think\facade\Log::info('开始撤销卡密权益', [
                        'cdk_code' => $cdkCode,
                        'user_id' => $userId,
                        'cdk_type' => $cdkType,
                        'shop' => $cdkCard['shop']
                    ]);

                    if ($cdkType === 'permanent') {
                        // 永久版卡密：删除用户的永久版账号
                        $this->revokePermanentAccounts($userId, $cdkCard['shop']);
                        $resultMsg .= '，已删除用户的永久版游戏账号';

                    } elseif ($cdkType === 'offline') {
                        // 离线卡密：删除用户的离线账号
                        $this->revokeOfflineAccounts($userId, $cdkCard['shop'], $cdkCard);
                        $resultMsg .= '，已删除用户的离线游戏账号';

                    } elseif (strpos($cdkType, 'member_') === 0) {
                        // 会员卡密：扣除会员时间
                        $this->revokeMembershipTime($userId, $cdkCard);
                        $resultMsg .= '，已扣除用户的会员时间';

                    } else {
                        // 在线卡密：扣除在线时间
                        $this->revokeOnlineTime($userId, $cdkCard);
                        $resultMsg .= '，已扣除用户的在线时间';
                    }

                    \think\facade\Log::info('卡密权益撤销完成', [
                        'cdk_code' => $cdkCode,
                        'user_id' => $userId,
                        'result_msg' => $resultMsg
                    ]);
                } else {
                    \think\facade\Log::info('卡密未使用或无分配用户，跳过权益撤销', [
                        'cdk_code' => $cdkCode,
                        'status' => $cdkCard['status'] ?? 'null',
                        'assigned_user' => $cdkCard['assigned_user'] ?? 'null'
                    ]);
                }

                // 记录操作日志（如果日志表存在）
                try {
                    // 检查日志表是否存在
                    $logTables = Db::query("SHOW TABLES LIKE 'tk_admin_logs'");
                    if (!empty($logTables)) {
                        Db::name('admin_logs')->insert([
                            'admin_id' => $adminId,
                            'action' => 'ban_cdk',
                            'content' => "封禁卡密: {$cdkCode}, 原因: {$reason}",
                            'ip' => Request::ip(),
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    } else {
                        // 如果日志表不存在，记录到系统日志
                        \think\facade\Log::info("管理员封禁卡密", [
                            'admin_id' => $adminId,
                            'cdk_code' => $cdkCode,
                            'reason' => $reason,
                            'ip' => Request::ip()
                        ]);
                    }
                } catch (\Exception $logError) {
                    // 日志记录失败不影响主要功能
                    \think\facade\Log::error('记录封禁日志失败: ' . $logError->getMessage());
                }

                Db::commit();

                return json(['code' => 1, 'msg' => $resultMsg]);

            } catch (\Exception $e) {
                Db::rollback();
                \think\facade\Log::error('封禁卡密失败: ' . $e->getMessage());
                return json(['code' => 0, 'msg' => '封禁失败：' . $e->getMessage()]);
            }
        }

        return View::fetch('/cdk/ban');
    }

    /**
     * 批量封禁卡密功能
     */
    public function batchBanCdk()
    {
        if (Request::isPost()) {
            $cdkCodes = trim(Request::param('cdk_codes', ''));
            $reason = trim(Request::param('reason', ''));
            $adminId = session('admin_id');

            if (empty($cdkCodes)) {
                return json(['code' => 0, 'msg' => '请输入要封禁的卡密码']);
            }

            // 解析卡密列表（一行一个）
            $cdkList = array_filter(array_map('trim', explode("\n", $cdkCodes)));

            if (empty($cdkList)) {
                return json(['code' => 0, 'msg' => '请输入有效的卡密码']);
            }

            if (count($cdkList) > 100) {
                return json(['code' => 0, 'msg' => '单次最多只能封禁100张卡密']);
            }

            try {
                // 检查数据库字段是否存在
                $this->ensureBanFields();

                // 开始事务
                Db::startTrans();

                $results = [
                    'total' => count($cdkList),
                    'success' => 0,
                    'failed' => 0,
                    'details' => []
                ];

                foreach ($cdkList as $index => $cdkCode) {
                    $cdkCode = trim($cdkCode);
                    if (empty($cdkCode)) {
                        continue;
                    }

                    try {
                        $result = $this->processSingleBan($cdkCode, $reason, $adminId);
                        if ($result['success']) {
                            $results['success']++;
                            $results['details'][] = [
                                'cdk_code' => $cdkCode,
                                'status' => 'success',
                                'message' => $result['message']
                            ];
                        } else {
                            $results['failed']++;
                            $results['details'][] = [
                                'cdk_code' => $cdkCode,
                                'status' => 'failed',
                                'message' => $result['message']
                            ];
                        }
                    } catch (\Exception $e) {
                        $results['failed']++;
                        $results['details'][] = [
                            'cdk_code' => $cdkCode,
                            'status' => 'failed',
                            'message' => '处理异常: ' . $e->getMessage()
                        ];
                    }
                }

                // 记录批量操作日志
                try {
                    Db::name('admin_logs')->insert([
                        'admin_id' => $adminId,
                        'action' => 'batch_ban_cdk',
                        'content' => "批量封禁卡密: 总数{$results['total']}, 成功{$results['success']}, 失败{$results['failed']}",
                        'ip' => Request::ip(),
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                } catch (\Exception $logError) {
                    // 日志记录失败不影响主要功能
                    \think\facade\Log::error('记录批量封禁日志失败: ' . $logError->getMessage());
                }

                Db::commit();

                $message = "批量封禁完成！总数: {$results['total']}, 成功: {$results['success']}, 失败: {$results['failed']}";

                return json([
                    'code' => 1,
                    'msg' => $message,
                    'data' => $results
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                \think\facade\Log::error('批量封禁卡密失败: ' . $e->getMessage());
                return json(['code' => 0, 'msg' => '批量封禁失败: ' . $e->getMessage()]);
            }
        }

        return View::fetch('/cdk/batch_ban');
    }

    /**
     * 处理单个卡密封禁
     */
    private function processSingleBan($cdkCode, $reason, $adminId)
    {
        // 查询卡密信息
        $cdkCard = Db::table('cdk_cards')
            ->where('cdk_code', $cdkCode)
            ->find();

        if (!$cdkCard) {
            return ['success' => false, 'message' => '卡密不存在'];
        }

        // 检查是否已被封禁
        if (isset($cdkCard['is_banned']) && $cdkCard['is_banned'] == 1) {
            return ['success' => false, 'message' => '该卡密已被封禁'];
        }

        // 封禁卡密
        $updateResult = Db::table('cdk_cards')
            ->where('cdk_code', $cdkCode)
            ->update([
                'is_banned' => 1,
                'banned_at' => date('Y-m-d H:i:s'),
                'banned_reason' => $reason,
                'banned_by' => $adminId
            ]);

        if (!$updateResult) {
            return ['success' => false, 'message' => '更新卡密状态失败'];
        }

        $resultMsg = '封禁成功';

        // 如果卡密已使用，需要扣除对应权益
        // 支持多种状态格式：'used', '已使用', 'activated'
        $isUsed = in_array($cdkCard['status'], ['used', '已使用', 'activated']) &&
                 !empty($cdkCard['assigned_user']);

        if ($isUsed) {
            $userId = $cdkCard['assigned_user'];
            $cdkType = $cdkCard['cdk_type'];

            \think\facade\Log::info('批量封禁-开始撤销卡密权益', [
                'cdk_code' => $cdkCode,
                'user_id' => $userId,
                'cdk_type' => $cdkType,
                'shop' => $cdkCard['shop']
            ]);

            if ($cdkType === 'permanent') {
                $this->revokePermanentAccounts($userId, $cdkCard['shop']);
                $resultMsg .= '，已撤销永久版权益';
            } elseif ($cdkType === 'offline') {
                $this->revokeOfflineAccounts($userId, $cdkCard['shop'], $cdkCard);
                $resultMsg .= '，已删除离线账号';
            } elseif (strpos($cdkType, 'member_') === 0) {
                $this->revokeMembershipTime($userId, $cdkCard);
                $resultMsg .= '，已扣除会员时间';
            } else {
                $this->revokeOnlineTime($userId, $cdkCard);
                $resultMsg .= '，已扣除在线时间';
            }
        } else {
            \think\facade\Log::info('批量封禁-卡密未使用或无分配用户，跳过权益撤销', [
                'cdk_code' => $cdkCode,
                'status' => $cdkCard['status'] ?? 'null',
                'assigned_user' => $cdkCard['assigned_user'] ?? 'null'
            ]);
        }

        return ['success' => true, 'message' => $resultMsg];
    }

    /**
     * 确保数据库字段存在
     */
    private function ensureBanFields()
    {
        try {
            // 检查 is_banned 字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM `cdk_cards` LIKE 'is_banned'");
            if (empty($columns)) {
                // 添加 is_banned 字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `is_banned` tinyint(1) DEFAULT 0 COMMENT '是否被封禁'");
            }

            // 检查 banned_at 字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM `cdk_cards` LIKE 'banned_at'");
            if (empty($columns)) {
                // 添加 banned_at 字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_at` datetime NULL COMMENT '封禁时间'");
            }

            // 检查 banned_reason 字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM `cdk_cards` LIKE 'banned_reason'");
            if (empty($columns)) {
                // 添加 banned_reason 字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_reason` varchar(255) NULL COMMENT '封禁原因'");
            }

            // 检查 banned_by 字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM `cdk_cards` LIKE 'banned_by'");
            if (empty($columns)) {
                // 添加 banned_by 字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_by` int(11) NULL COMMENT '封禁操作者ID'");
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('检查数据库字段失败: ' . $e->getMessage());
            // 字段检查失败不影响主要功能，继续执行
        }
    }

    /**
     * 撤销永久版账号
     */
    private function revokePermanentAccounts($userId, $goodsId)
    {
        try {
            $revokedCount = 0;

            // 查找用户的永久版账号
            $accounts = Db::name('account')
                ->where([
                    'ac_uid' => $userId,
                    'ac_goods' => $goodsId,
                    'ac_vip' => 2  // 永久版标识
                ])
                ->select();

            foreach ($accounts as $account) {
                // 1. 撤销相关的永久版订单 (永久撤销权限)
                $this->revokePermanentOrders($userId, $account['id']);

                // 2. 账号完全重置 (彻底回收账号 + 账号重置)
                $this->resetAccountCompletely($account['id'], '管理员封禁永久版卡密');

                $revokedCount++;
                \think\facade\Log::info("永久版账号已重置: {$account['id']}, 用户: {$userId}");
            }

            \think\facade\Log::info("成功处理用户 {$userId} 的永久版账号", [
                'goods_id' => $goodsId,
                'processed_count' => $revokedCount,
                'action' => 'permanent_revoke_and_reset',
                'description' => '永久撤销权限 + 彻底回收账号 + 账号重置'
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error("处理永久版账号异常: " . $e->getMessage(), [
                'user_id' => $userId,
                'goods_id' => $goodsId
            ]);
            throw $e; // 重新抛出异常以触发事务回滚
        }
    }

    /**
     * 撤销永久版账号相关的订单 (优化版：永久撤销权限)
     */
    private function revokePermanentOrders($userId, $accountId)
    {
        // 1. 处理普通永久版订单 (GoodType = 2)
        $orders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'ord_aid' => $accountId,
                'GoodType' => 2,  // 永久版账号
                'ord_ifpay' => 1,
                'or_maturity' => 1  // 未到期的订单
            ])
            ->select();

        foreach ($orders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("撤销永久版订单: {$order['id']}");
        }

        // 2. 处理永久版卡密兑换订单 (GoodType = 3)
        $cdkOrders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'GoodType' => 3,  // 永久版卡密
                'ord_ifpay' => 1,
                'or_maturity' => 1
            ])
            ->select();

        foreach ($cdkOrders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("撤销永久版卡密兑换订单: {$order['id']}");
        }

        // 3. 处理通用CDK兑换订单
        $generalCdkOrders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'ord_aid' => $accountId,
                'ord_type' => ['CDK兑换', '永久版卡密兑换'],
                'ord_ifpay' => 1,
                'or_maturity' => 1
            ])
            ->select();

        foreach ($generalCdkOrders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("撤销CDK兑换订单: {$order['id']}");
        }
    }

    /**
     * 撤销离线卡密权限（优化版：无需回收账号，只撤销订单权限）
     */
    private function revokeOfflineAccounts($userId, $goodsId, $cdkCard = null)
    {
        try {
            $revokedCount = 0;

            $this->logCdkDebugInfo('开始撤销离线卡密权限', [
                'user_id' => $userId,
                'goods_id' => $goodsId,
                'cdk_card' => $cdkCard,
                'method' => 'revokeOfflineAccounts'
            ]);

            // 根据用户ID + ord_combo(商品ID) + or_maturity=1 查询有效的离线订单
            $queryConditions = [
                'ord_uid' => $userId,
                'GoodType' => 0,  // 离线账号
                'ord_ifpay' => 1,
                'ord_combo' => $goodsId,  // 使用ord_combo字段匹配商品ID
                'or_maturity' => 1  // 只处理有效的订单
            ];

            $this->logCdkDebugInfo('主查询条件', $queryConditions);

            $orders = Db::name('order')
                ->where($queryConditions)
                ->field('id, ord_bbh, ord_name, ord_combo, ord_aid, ord_type, ord_ifpay, or_maturity')
                ->select();

            $this->logCdkDebugInfo('主查询结果', [
                'found_orders_count' => count($orders),
                'orders' => $orders ? $orders->toArray() : []
            ]);

            foreach ($orders as $order) {
                $this->logCdkDebugInfo('处理订单', [
                    'order_id' => $order['id'],
                    'order_bbh' => $order['ord_bbh'],
                    'order_name' => $order['ord_name'],
                    'before_revoke' => $order
                ]);

                // 撤销订单权限（设置or_maturity=0）
                $this->revokeOrder($order['id']);
                $revokedCount++;

                $this->logCdkDebugInfo('订单撤销完成', [
                    'order_id' => $order['id'],
                    'order_bbh' => $order['ord_bbh'],
                    'goods_id' => $order['ord_combo'],
                    'revoked_count' => $revokedCount
                ]);

                \think\facade\Log::info("撤销离线卡密订单权限: 订单ID={$order['id']}, 订单号={$order['ord_bbh']}, 商品ID={$order['ord_combo']}");
            }

            // 如果没有找到订单，尝试通过ord_type查找（兼容性处理）
            if ($revokedCount === 0) {
                $this->logCdkDebugInfo('主查询未找到订单，开始兼容性查询', [
                    'fallback_reason' => 'no_orders_found_in_main_query'
                ]);

                $fallbackConditions = [
                    'ord_uid' => $userId,
                    'GoodType' => 0,  // 离线账号
                    'ord_ifpay' => 1,
                    'ord_type' => '离线卡密兑换',
                    'or_maturity' => 1
                ];

                $this->logCdkDebugInfo('兼容性查询条件', $fallbackConditions);

                $fallbackOrders = Db::name('order')
                    ->where($fallbackConditions)
                    ->field('id, ord_bbh, ord_name, ord_combo, ord_aid, ord_type, ord_ifpay, or_maturity')
                    ->select();

                $this->logCdkDebugInfo('兼容性查询结果', [
                    'found_orders_count' => count($fallbackOrders),
                    'orders' => $fallbackOrders ? $fallbackOrders->toArray() : []
                ]);

                foreach ($fallbackOrders as $order) {
                    $this->logCdkDebugInfo('检查兼容性订单', [
                        'order_id' => $order['id'],
                        'order_combo' => $order['ord_combo'],
                        'target_goods_id' => $goodsId,
                        'match' => ($order['ord_combo'] == $goodsId)
                    ]);

                    // 检查商品ID是否匹配
                    if ($order['ord_combo'] == $goodsId) {
                        $this->logCdkDebugInfo('兼容性订单匹配，开始撤销', [
                            'order_id' => $order['id'],
                            'order_bbh' => $order['ord_bbh']
                        ]);

                        $this->revokeOrder($order['id']);
                        $revokedCount++;

                        $this->logCdkDebugInfo('兼容性订单撤销完成', [
                            'order_id' => $order['id'],
                            'order_bbh' => $order['ord_bbh'],
                            'revoked_count' => $revokedCount
                        ]);

                        \think\facade\Log::info("通过兼容性查询撤销离线卡密订单: 订单ID={$order['id']}, 订单号={$order['ord_bbh']}");
                    }
                }
            }

            $finalResult = [
                'goods_id' => $goodsId,
                'revoked_orders_count' => $revokedCount,
                'action' => 'revoke_offline_orders_only',
                'description' => '仅撤销订单权限，不回收账号资源'
            ];

            $this->logCdkDebugInfo('离线卡密权限撤销完成', $finalResult);

            \think\facade\Log::info("成功撤销用户 {$userId} 的离线卡密权限", $finalResult);

            return $revokedCount;
        } catch (\Exception $e) {
            $errorInfo = [
                'user_id' => $userId,
                'goods_id' => $goodsId,
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ];

            $this->logCdkDebugInfo('离线卡密权限撤销异常', $errorInfo);

            \think\facade\Log::error("撤销离线卡密权限异常: " . $e->getMessage(), $errorInfo);
            return 0;
        }
    }

    /**
     * 释放账号
     */
    private function releaseAccount($accountId)
    {
        Db::name('account')
            ->where('id', $accountId)
            ->update([
                'ac_uid' => null,
                'ac_vip' => 0,
                'ac_sell' => 1,
                'ac_states' => 1,
                'exit_time' => null
            ]);
    }

    /**
     * 账号完全重置 (用于永久版卡密封禁：彻底回收账号 + 账号重置)
     * @param int $accountId 账号ID
     * @param string $reason 重置原因
     */
    private function resetAccountCompletely($accountId, $reason)
    {
        Db::name('account')
            ->where('id', $accountId)
            ->update([
                'ac_uid' => null,           // 清空用户ID
                'ac_vip' => 0,              // 重置VIP状态
                'ac_sell' => 1,             // 设为可售
                'ac_states' => 1,           // 设为可用状态
                'exit_time' => null,        // 清空到期时间
                'token_limit' => 0,         // 重置token限制
                'modify' => 0,              // 重置修改标记
                'is_locked' => 0            // 解除锁定
            ]);

        \think\facade\Log::info("账号完全重置完成: {$accountId}, 原因: {$reason}");
    }

    /**
     * 撤销订单（设为已到期，用户页面不再显示）
     */
    private function revokeOrder($orderId)
    {
        Db::name('order')
            ->where('id', $orderId)
            ->update([
                'or_maturity' => 0,  // 设为已到期
                'ord_remarks' => '管理员封禁卡密：权益已永久撤销'
            ]);
    }

    /**
     * 删除订单（彻底删除，用于离线卡密等不检查or_maturity的页面）
     */
    private function deleteOrder($orderId)
    {
        Db::name('order')
            ->where('id', $orderId)
            ->delete();

        \think\facade\Log::info("删除订单记录: {$orderId}");
    }

    /**
     * 记录卡密调试信息
     * 用于调试和排查卡密相关问题
     */
    private function logCdkDebugInfo($message, $data = [])
    {
        $debugInfo = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'data' => $data,
            'request_id' => uniqid('cdk_', true),
            'admin_id' => session('admin_id') ?? 'unknown',
            'ip' => request()->ip() ?? 'unknown'
        ];

        // 记录到日志文件
        \think\facade\Log::info('CDK调试信息: ' . $message, $debugInfo);

        // 如果是重要操作，也记录到数据库（如果需要的话）
        if (in_array($message, ['卡密封禁', '卡密解封', '卡密生成', '卡密删除'])) {
            try {
                Db::name('admin_logs')->insert([
                    'admin_id' => session('admin_id') ?? 0,
                    'action' => 'cdk_debug',
                    'content' => json_encode($debugInfo, JSON_UNESCAPED_UNICODE),
                    'ip' => request()->ip(),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } catch (\Exception $e) {
                // 如果数据库记录失败，只记录到文件日志
                \think\facade\Log::warning('CDK调试信息数据库记录失败: ' . $e->getMessage());
            }
        }
    }

    /**
     * 撤销会员时间
     */
    private function revokeMembershipTime($userId, $cdkCard)
    {
        try {
            // 解析会员类型
            $membershipType = str_replace('member_', '', $cdkCard['cdk_type']);

            // 获取会员类型信息
            $membershipInfo = Db::name('membership_pricing')
                ->where('membership_type', $membershipType)
                ->find();

            if (!$membershipInfo) {
                \think\facade\Log::warning("未找到会员类型信息: {$membershipType}");
                return;
            }

            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                \think\facade\Log::warning("未找到用户: {$userId}");
                return;
            }

            // 检查用户是否有会员时间
            if (empty($user['exit_time'])) {
                \think\facade\Log::info("用户 {$userId} 没有会员时间，无需扣除");
                return;
            }

            // 计算要扣除的时间（使用卡密的有效期）
            $deductMonths = (int)$cdkCard['expiry_date2']; // 卡密的有效期（月数）

            // 计算新的到期时间
            $currentExpiry = strtotime($user['exit_time']);
            $deductSeconds = $deductMonths * 30 * 24 * 60 * 60; // 月数转换为秒
            $newExpiry = max(time(), $currentExpiry - $deductSeconds); // 不能早于当前时间

            // 更新用户会员时间
            $updateResult = Db::name('user')
                ->where('id', $userId)
                ->update([
                    'exit_time' => date('Y-m-d H:i:s', $newExpiry)
                ]);

            if ($updateResult) {
                \think\facade\Log::info("成功扣除用户 {$userId} 的会员时间", [
                    'original_expiry' => $user['exit_time'],
                    'new_expiry' => date('Y-m-d H:i:s', $newExpiry),
                    'deducted_months' => $deductMonths
                ]);
            } else {
                \think\facade\Log::error("扣除用户 {$userId} 会员时间失败");
            }
        } catch (\Exception $e) {
            \think\facade\Log::error("撤销会员时间异常: " . $e->getMessage(), [
                'user_id' => $userId,
                'cdk_card' => $cdkCard
            ]);
        }
    }

    /**
     * 撤销在线时间
     */
    private function revokeOnlineTime($userId, $cdkCard)
    {
        try {
            $revokedCount = 0;

            // 方法1：通过卡密的account_id字段查找（如果存在）
            if (isset($cdkCard['account_id']) && $cdkCard['account_id']) {
                $account = Db::name('account')->where('id', $cdkCard['account_id'])->find();
                if ($account && $account['ac_uid'] == $userId && $account['ac_goods'] == $cdkCard['shop']) {
                    $this->revokeOnlineAccount($account, $cdkCard);
                    // 查找并撤销相关订单
                    $this->revokeOnlineOrders($userId, $account['id']);
                    $revokedCount++;
                    \think\facade\Log::info("通过卡密account_id撤销在线账号: {$account['id']}");
                }
            }

            // 方法2：查找用户通过在线卡密获得的账号
            $accounts = Db::name('account')
                ->where([
                    'ac_uid' => $userId,
                    'ac_goods' => $cdkCard['shop'],
                    'ac_vip' => 1  // 在线VIP账号
                ])
                ->select();

            foreach ($accounts as $account) {
                $this->revokeOnlineAccount($account, $cdkCard);
                // 查找并撤销相关订单
                $this->revokeOnlineOrders($userId, $account['id']);
                $revokedCount++;
                \think\facade\Log::info("撤销在线账号: {$account['id']}");
            }

            \think\facade\Log::info("成功撤销用户 {$userId} 的在线时间", [
                'cdk_code' => $cdkCard['cdk_code'],
                'cdk_type' => $cdkCard['cdk_type'],
                'revoked_count' => $revokedCount
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error("撤销在线时间异常: " . $e->getMessage(), [
                'user_id' => $userId,
                'cdk_card' => $cdkCard
            ]);
        }
    }

    /**
     * 撤销单个在线账号的时间
     */
    private function revokeOnlineAccount($account, $cdkCard)
    {
        // 根据卡密类型扣除时间
        if ($cdkCard['cdk_type'] === 'day') {
            // 日卡：扣除天数
            $deductDays = (int)$cdkCard['expiry_date2'];
            $currentExpiry = $account['exit_time'];
            $newExpiry = max(time(), $currentExpiry - ($deductDays * 24 * 60 * 60));

            Db::name('account')
                ->where('id', $account['id'])
                ->update(['exit_time' => $newExpiry]);

        } elseif ($cdkCard['cdk_type'] === 'hour') {
            // 小时卡：扣除小时数
            $deductHours = (int)$cdkCard['expiry_date2'];
            $currentExpiry = $account['exit_time'];
            $newExpiry = max(time(), $currentExpiry - ($deductHours * 60 * 60));

            Db::name('account')
                ->where('id', $account['id'])
                ->update(['exit_time' => $newExpiry]);
        } else {
            // 其他类型的在线卡密，直接设置为已到期
            Db::name('account')
                ->where('id', $account['id'])
                ->update(['exit_time' => time()]);
        }
    }

    /**
     * 撤销在线账号相关的订单
     */
    private function revokeOnlineOrders($userId, $accountId)
    {
        // 查找相关的在线订单
        $orders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'ord_aid' => $accountId,
                'GoodType' => 1,  // 在线账号
                'ord_ifpay' => 1,
                'or_maturity' => 1  // 未到期的订单
            ])
            ->select();

        foreach ($orders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("撤销在线订单: {$order['id']}");
        }

        // 也查找CDK兑换类型的订单
        $cdkOrders = Db::name('order')
            ->where([
                'ord_uid' => $userId,
                'ord_aid' => $accountId,
                'ord_type' => 'CDK兑换',
                'ord_ifpay' => 1,
                'or_maturity' => 1
            ])
            ->select();

        foreach ($cdkOrders as $order) {
            $this->revokeOrder($order['id']);
            \think\facade\Log::info("撤销CDK兑换订单: {$order['id']}");
        }
    }

}
