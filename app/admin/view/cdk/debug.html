<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>卡密调试工具</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/layui/css/layui.css" media="all">
    <style>
        .debug-container {
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .debug-form {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result-box {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        .info-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid #3b82f6;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #1f2937;
            font-weight: bold;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
        }
        .info-table td {
            padding: 8px;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: top;
        }
        .info-table td:first-child {
            font-weight: bold;
            width: 150px;
            color: #6b7280;
        }
        .info-table td:last-child {
            color: #1f2937;
        }
        .status-used {
            color: #ef4444;
            font-weight: bold;
        }
        .status-unused {
            color: #10b981;
            font-weight: bold;
        }
        .warning-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
        .error-box {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #dc2626;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-form">
            <h2 style="text-align: center; margin-bottom: 30px; color: #1f2937;">🔧 卡密调试工具</h2>
            
            <form class="layui-form" lay-filter="debugForm">
                <div class="layui-form-item">
                    <label class="layui-form-label">卡密码</label>
                    <div class="layui-input-block">
                        <input type="text" name="cdk_code" placeholder="请输入要调试的卡密码" autocomplete="off" class="layui-input" required>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-primary" lay-submit lay-filter="debug">🔍 调试分析</button>
                        <button type="reset" class="layui-btn layui-btn-primary">🔄 重置</button>
                    </div>
                </div>
            </form>
        </div>
        
        <div id="resultBox" class="result-box">
            <div id="resultContent"></div>
        </div>
    </div>

    <script src="/layuiadmin/layui/layui.js"></script>
    <script>
    // 添加调试信息
    console.log('页面加载完成');

    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;

        console.log('layui加载完成');

        // 监听提交
        form.on('submit(debug)', function(data){
            console.log('表单提交', data);
            var cdkCode = data.field.cdk_code.trim();
            
            if (!cdkCode) {
                layer.msg('请输入卡密码', {icon: 2});
                return false;
            }
            
            // 显示加载
            var loadIndex = layer.load(1, {shade: [0.1,'#fff']});
            
            console.log('发送AJAX请求', {cdk_code: cdkCode});

            $.ajax({
                url: '/admin/CdkController/debug',
                type: 'POST',
                data: {cdk_code: cdkCode},
                success: function(res) {
                    console.log('AJAX响应', res);
                    layer.close(loadIndex);

                    var resultBox = $('#resultBox');
                    var resultContent = $('#resultContent');

                    if (res.code === 1) {
                        // 调试成功
                        resultBox.show();
                        
                        var html = '<h3>🔍 卡密调试结果</h3>';
                        
                        // 卡密基本信息
                        html += '<div class="info-card">';
                        html += '<h4>📋 卡密基本信息</h4>';
                        html += '<table class="info-table">';
                        html += '<tr><td>卡密码:</td><td>' + res.data.cdk_card.cdk_code + '</td></tr>';
                        html += '<tr><td>卡密类型:</td><td>' + res.data.cdk_card.cdk_type + '</td></tr>';
                        html += '<tr><td>状态:</td><td><span class="' + (res.data.cdk_card.status === 'used' ? 'status-used' : 'status-unused') + '">' + (res.data.cdk_card.status === 'used' ? '已使用' : '未使用') + '</span></td></tr>';
                        html += '<tr><td>Shop字段:</td><td>' + (res.data.cdk_card.shop || '无') + '</td></tr>';
                        html += '<tr><td>成本:</td><td>¥' + (res.data.cdk_card.cost || '0') + '</td></tr>';
                        html += '<tr><td>创建时间:</td><td>' + (res.data.cdk_card.created_at || '未知') + '</td></tr>';
                        
                        if (res.data.cdk_card.assigned_user) {
                            html += '<tr><td>使用用户ID:</td><td>' + res.data.cdk_card.assigned_user + '</td></tr>';
                        }
                        
                        if (res.data.cdk_card.expiry_date) {
                            html += '<tr><td>到期时间:</td><td>' + res.data.cdk_card.expiry_date + '</td></tr>';
                        }
                        
                        if (res.data.cdk_card.expiry_date2) {
                            html += '<tr><td>有效期参数:</td><td>' + res.data.cdk_card.expiry_date2 + '</td></tr>';
                        }
                        
                        html += '</table>';
                        html += '</div>';
                        
                        // 关联商品信息
                        if (res.data.goods) {
                            html += '<div class="info-card">';
                            html += '<h4>🎮 关联商品信息</h4>';
                            html += '<table class="info-table">';
                            html += '<tr><td>商品ID:</td><td>' + res.data.goods.id + '</td></tr>';
                            html += '<tr><td>商品名称:</td><td>' + res.data.goods.goods_name + '</td></tr>';
                            html += '<tr><td>商品价格:</td><td>¥' + (res.data.goods.goods_price || '0') + '</td></tr>';
                            html += '</table>';
                            html += '</div>';
                        } else {
                            html += '<div class="error-box">';
                            html += '<strong>⚠️ 警告：</strong> 未找到关联的商品信息！Shop字段值: ' + (res.data.cdk_card.shop || '无');
                            html += '</div>';
                        }
                        
                        // 代理信息
                        if (res.data.agent) {
                            html += '<div class="info-card">';
                            html += '<h4>👤 代理信息</h4>';
                            html += '<table class="info-table">';
                            html += '<tr><td>代理ID:</td><td>' + res.data.agent.id + '</td></tr>';
                            html += '<tr><td>代理用户名:</td><td>' + res.data.agent.username + '</td></tr>';
                            html += '<tr><td>代理状态:</td><td>' + (res.data.agent.status == 1 ? '正常' : '禁用') + '</td></tr>';
                            html += '</table>';
                            html += '</div>';
                        }
                        
                        // 会员类型信息
                        if (res.data.membership_info) {
                            html += '<div class="info-card">';
                            html += '<h4>👑 会员类型信息</h4>';
                            html += '<table class="info-table">';
                            html += '<tr><td>会员类型ID:</td><td>' + res.data.membership_info.id + '</td></tr>';
                            html += '<tr><td>会员类型:</td><td>' + res.data.membership_info.membership_type + '</td></tr>';
                            html += '<tr><td>价格:</td><td>¥' + res.data.membership_info.price + '</td></tr>';
                            html += '<tr><td>有效期:</td><td>' + res.data.membership_info.validity_period + '个月</td></tr>';
                            html += '</table>';
                            html += '</div>';
                        }
                        
                        // 离线价格信息
                        if (res.data.offline_info) {
                            html += '<div class="info-card">';
                            html += '<h4>💿 离线价格信息</h4>';
                            html += '<table class="info-table">';
                            html += '<tr><td>产品ID:</td><td>' + res.data.offline_info.product_id + '</td></tr>';
                            html += '<tr><td>离线价格:</td><td>¥' + res.data.offline_info.product_amount + '</td></tr>';
                            html += '</table>';
                            html += '</div>';
                        }
                        
                        // 一致性检查
                        html += '<div class="info-card">';
                        html += '<h4>🔍 一致性检查</h4>';
                        
                        if (res.data.cdk_card.cdk_type === 'permanent') {
                            if (res.data.goods) {
                                html += '<p style="color: #10b981;">✅ 永久版卡密关联商品正常</p>';
                            } else {
                                html += '<p style="color: #ef4444;">❌ 永久版卡密未找到关联商品</p>';
                            }
                        } else if (res.data.cdk_card.cdk_type.startsWith('member_')) {
                            if (res.data.membership_info) {
                                html += '<p style="color: #10b981;">✅ 会员卡密关联会员类型正常</p>';
                            } else {
                                html += '<p style="color: #ef4444;">❌ 会员卡密未找到关联会员类型</p>';
                            }
                        } else if (res.data.cdk_card.cdk_type === 'offline') {
                            if (res.data.goods && res.data.offline_info) {
                                html += '<p style="color: #10b981;">✅ 离线卡密关联商品和离线价格正常</p>';
                            } else {
                                html += '<p style="color: #ef4444;">❌ 离线卡密关联信息不完整</p>';
                            }
                        }
                        
                        html += '</div>';
                        
                        resultContent.html(html);
                    } else {
                        // 调试失败
                        resultBox.show();
                        resultContent.html('<div class="error-box"><h3>❌ ' + res.msg + '</h3></div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX错误', {xhr: xhr, status: status, error: error});
                    layer.close(loadIndex);
                    layer.msg('调试失败：' + error, {icon: 2});

                    // 显示错误详情
                    var resultBox = $('#resultBox');
                    var resultContent = $('#resultContent');
                    resultBox.show();
                    resultContent.html('<div class="error-box"><h3>❌ 请求失败</h3><p>状态: ' + status + '</p><p>错误: ' + error + '</p><p>响应: ' + (xhr.responseText || '无') + '</p></div>');
                }
            });
            
            return false;
        });
    });
    </script>
</body>
</html>
