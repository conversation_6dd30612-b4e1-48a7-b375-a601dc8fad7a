    <style>
        *{
            padding: 0;
            margin: 0;
            list-style: none;
            text-decoration: none;
        }
        .fixed{
            color: aliceblue;
            position: fixed;
            z-index: 999;
            display:flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            right: 0;
            top: 0;
        }
        .float_right ul{
            border-radius: 10px;
            background-color: rgb(24,24,28);
            border: 1px solid rgba(225,225,225,.09);
        }
        .float_right img{
            display: block;
            margin:5px auto;
            width: 20px;
        }
        .float_right li{
            text-align: center;
            font-size: 12px;
            font-weight: normal;
            color: aliceblue;
            padding: 10px 0;
            margin: 5px 5px;
            border-bottom: 1px dotted rgba(255,255,255,.09);
        }
        .float_right li>a:hover{
            color: rgb(22,104,220);
        }
        .float_right li:last-child{
            border-bottom: none
        }
        .gzh_box{
             position: fixed;
    width: 240px;
    height: 300px;
    left: 50%;
    top: 50%;
    z-index: 99999;
    padding: 20px;
    margin-left: -150px;
    margin-top: -180px;
            background: rgb(44,44,50);
            text-align: center;
            border-radius: 10px;
        }
        .gzh_box>div{
            margin: 5px 0;
        }
        #gzh{
width: 200px;
height: 200px;
border-radius: 10px;
        }
        #clos{
            position: relative;
            z-index: 9999;
               margin: 3px;
    padding: 10px 100px;
    background: rgb(40,62,90);
    border-radius: 10px;
        }
                /* 遮蔽罩 */
        .hide {
            display: none;
            position: fixed;
            width: 100%;
            height: 100%;
            z-index: 1;
            top: 0;
            left: 0;
            background-color: rgba(0, 0, 0, 0.8);
        }
    </style>
    </div>
    <script>
        function gzh(){
            $(".hide").fadeIn()
            $.get("/user/base/systemData",function (e){
                $("body").append(`
                    <div class="gzh_box">
                    <div>长按图片或扫码关注</div>
                    <div><img src="${e.sy_gzh}" id="gzh"></div>
                    <div>关注公众号接收最新优惠信息</div>
                    <div id="clos">关闭</div>
                    </div>
                `)
                        // 关闭
                        $("#clos").click(function(){
                            $(".hide").fadeOut()
                            $(".gzh_box").fadeOut(300)
                            $(".gzh_box").remove()
                            
                        })
                        $(".hide").click(function(){
                            $(this).fadeOut()
                            $(".gzh_box").fadeOut(300)
                            $(".gzh_box").remove()
                        })
            })
        }
        function qlj(){
            $.get("/user/base/systemData",function (e){
                window.location.href=e.sy_qlj
            })
        }
    </script>
    
    <script type="text/javascript" charset="utf-8">
        $("#right_list1").click(function(){
            $('.alert_box2').fadeIn(300)
            $('body').append("<div id='box'></div>")
            $('#box').css({background:"rgba(0,0,0,0.5)",position:"fixed",width:"100%",height:"100%",top:"0"})
            
          $('#box').click(function(){
            $(".alert_box2").fadeOut(300)
            this.remove()
        })
        })
    </script>