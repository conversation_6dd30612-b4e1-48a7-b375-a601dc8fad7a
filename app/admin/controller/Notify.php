<?php
namespace app\admin\controller;

use app\BaseController;
use app\admin\model\Config;
use app\admin\model\Goods;
use app\admin\model\Combo;
use app\admin\model\Account;
use app\admin\model\User as Us;
use think\facade\Request;
use app\admin\model\Order;
use app\admin\model\Coupons;
use think\facade\Cookie;
use think\facade\Db;
use app\admin\model\Promotion as Pro;

/**
 * Notify Class
 * 用于处理支付通知的控制器，负责更新订单状态、处理余额充值、销毁优惠券、分配账号、计算佣金等功能。
 */
class Notify extends BaseController
{
    /**
     * 支付通知入口
     * 接收支付平台回调请求并处理相关逻辑。
     *
     * @return void
     */
    public function index()
    {
        // 获取支付平台回调的参数
        $order = Request::param();
        $out_trade_no = $order['out_trade_no']; // 商户订单号
        $trade_no = $order['trade_no']; // 支付平台交易号
        $trade_status = $order['trade_status']; // 支付状态

        // 获取支付方式
        $type = $this->getPaymentType($order['type']);

        // 支付金额
        $money = $order['money'];

        // 如果支付成功，处理业务逻辑
        if ($trade_status === 'TRADE_SUCCESS') {
            $uid = Cookie::get("id");

            // 如果订单是余额充值
            if ($order["name"] === "余额充值") {
                $this->processBalanceRecharge($out_trade_no, $trade_no, $uid, $money);
                return header("Location:/user/index/onmoney");
            }

            // **新增逻辑：如果订单名称为至尊会员**
            if ($order["name"] === "至尊会员") {
                // 根据订单号获取订单信息（确保upOrder获取正确）
                $upOrder = Order::where("ord_bbh", $out_trade_no)->find();

                // 获取用户ID（ord_uid 即下单用户ID）
                $orderUserId = $upOrder['ord_uid'];

                // 在用户表中查找该用户
                $user = Us::where('id', $orderUserId)->find();

                // 若用户存在，则更新membership_level字段为'至尊会员'
                if ($user) {
                    $user->membership_level = '至尊会员';
                    $user->save();
                }
                return header("Location:/user/index/getPurchasedComboWithVip");
            }

            // 销毁已使用的优惠券
            $this->invalidateCoupon();

            // 更新订单状态
            $this->updateOrderStatus($out_trade_no, $trade_no, $order['type']);

            // 获取更新后的订单和对应套餐信息
            $upOrder = Order::where("ord_bbh", $out_trade_no)->find();

            // 修复：区分普通订单和永久版订单的套餐查询
            if ($upOrder["is_permanent"] && in_array($upOrder["is_permanent"], [1, 2, 3])) {
                // 永久版订单：ord_combo存储的是商品ID，需要特殊处理
                $combo = null; // 永久版订单不需要套餐信息
            } else {
                // 普通订单：ord_combo存储的是套餐ID
                $combo = Combo::where("id", $upOrder["ord_combo"])->find();
            }

            // 处理佣金逻辑
            //$this->processCommission($uid, $combo);

            // 根据订单类型处理商品或套餐
            //if ($upOrder["ord_type2"] == 1) {
                $this->processSinglePurchase($upOrder, $combo, $out_trade_no);
            // } else {
            //     $this->processGroupPurchase($combo);
            // }

            // 跳转到成功页面
            header("Location:/user/index/onshop");
        }

    }

    /**
     * 获取支付方式名称
     *
     * @param string $type 支付方式标识
     * @return string 支付方式名称
     */
    private function getPaymentType($type)
    {
        switch ($type) {
            case 'alipay': return "支付宝";
            case 'wxpay': return "微信";
            case 'qqpay': return "QQ";
            default: return "未知";
        }
    }

    /**
     * 处理余额充值
     *
     * @param string $out_trade_no 商户订单号
     * @param string $trade_no 支付平台交易号
     * @param int $uid 用户ID
     * @param float $money 充值金额
     * @return void
     */
    private function processBalanceRecharge($out_trade_no, $trade_no, $uid, $money)
    {
        $data = [
            "ord_ybh" => $trade_no,
            "ord_ifpay" => 1,
            "time" => date("Y-m-d H:i:s"),
        ];
        Order::where("ord_bbh", $out_trade_no)->update($data);

        // 更新用户余额
        $currentMoney = Us::where("id", $uid)->value("us_money");
        Us::where("id", $uid)->update(["us_money" => $currentMoney + $money]);
    }

    /**
     * 销毁优惠券
     *
     * @return void
     */
    private function invalidateCoupon()
    {
        $couponId = Cookie::get("yhjId");
        if ($couponId) {
            Coupons::where("id", $couponId)->update(["coupons_use" => 1]);
            Cookie::delete('yhjId');
        }
    }

    /**
     * 更新订单状态
     *
     * @param string $out_trade_no 商户订单号
     * @param string $trade_no 支付平台交易号
     * @param string $type 支付方式
     * @return void
     */
    private function updateOrderStatus($out_trade_no, $trade_no, $type)
    {
        $data = [
            "ord_ybh" => $trade_no,
            "ord_ifpay" => 1,
            "time" => date("Y-m-d H:i:s"),
        ];
        Order::where(["ord_bbh" => $out_trade_no, "ord_type" => $type])->update($data);
    }

    /**
     * 处理佣金逻辑
     *
     * @param int $uid 用户ID
     * @param array $combo 套餐信息
     * @return void
     */
    private function processCommission($uid, $combo)
    {
        $user = Us::where("id", $uid)->find();
        $config = Config::find(1);

        // 判断是否需要分佣
        if ($user["superior"] != 0 && $config["pro_switch"] == 1) {
            $commissionAmount = $combo["co_money"] * ($config["pro_money"] / 100);

            // 更新上级佣金
            $superiorId = $user["superior"];
            $currentCommission = Us::where("id", $superiorId)->value("commission");
            Us::where("id", $superiorId)->update(["commission" => $currentCommission + $commissionAmount]);

            // 更新推广记录
            $currentProMoney = Pro::where("pro_subuser", $uid)->value("pro_money");
            Pro::where("pro_subuser", $uid)->update(["pro_money" => $currentProMoney + $commissionAmount]);
        }
    }

    /**
     * 处理单买逻辑
     *
     * @param array $upOrder 当前订单信息
     * @param array $combo 套餐信息
     * @param string $out_trade_no 商户订单号
     * @return void
     */
    private function processSinglePurchase($upOrder, $combo, $out_trade_no)
    {
        // 修复：处理永久版订单的特殊情况
        if ($upOrder["is_permanent"] && in_array($upOrder["is_permanent"], [1, 2, 3])) {
            // 永久版订单不需要分配账号，直接返回
            return;
        }

        // 确保combo不为空（普通订单必须有套餐信息）
        if (!$combo) {
            throw new \Exception('套餐信息不存在');
        }

        // 判断是否为离线账号订单
        if ($upOrder['GoodType'] == 0) {
            // 查找所有符合条件的离线账号
            $accounts = Account::where([
                "ac_goods" => $combo["co_goods"],
                "goods_Type" => 0,
            ])->select();

            $validAccount = null;

            // 遍历所有找到的离线账号
            foreach ($accounts as $offlineAccount) {
                // 获取该离线账号的账号名称
                $acName = $offlineAccount['ac_name'];

                // 检查是否存在同名的在线账号
                $onlineAccount = Account::where([
                    'ac_name' => $acName,
                    'goods_Type' => 1
                ])->find();

                // 只有当存在同名在线账号且其状态不是0（未被占用）时，才可以分配
                if ($onlineAccount && $onlineAccount['ac_states'] != 0) {
                    $validAccount = $offlineAccount;
                    break; // 找到第一个符合条件的账号即可
                }
            }

            // 如果找到了有效账号，进行分配
            if ($validAccount) {
                // 只更新订单信息，不修改账号状态
                Order::where("ord_bbh", $out_trade_no)->update([
                    "ord_aid" => $validAccount["id"],
                    "or_maturity" => 1
                ]);

                // 更新商品销量
                $currentSales = Goods::where("id", $validAccount["ac_goods"])->value("goods_xl");
                Goods::where("id", $validAccount["ac_goods"])->update([
                    "goods_xl" => $currentSales + 1
                ]);
            } else {
                // 没有可用账号，更新订单状态
                Order::where("ord_bbh", $out_trade_no)->update([
                    "ord_ifpay" => 1,
                    "account_status" => 0,
                    "or_maturity" => 1,
                    "ord_remarks" => "暂无可用离线账号，系统将在账号可用时自动分配",
                    "pending_reason" => "相应的在线账号正在被使用中或不存在"
                ]);
            }
            return;
        }

        // 判断是否为永久版订单
        if ($upOrder['GoodType'] == 3) {

                // 只更新订单信息，不修改账号状态
                Order::where("ord_bbh", $out_trade_no)->update([
                    "or_maturity" => 1
                ]);

            return;
        }

        // 获取配置，检查是否允许分配到期未回收的账号
        $config = Config::find(1);
        $allowExpireNoOffline = $config['allow_expire_no_offline'];

        $account = null;
        $isExpiredReassignment = false;

        if ($allowExpireNoOffline == 1) {
            // ✅ 优先查找标记为过期等待重新分配的账号
            $account = Account::where([
                "ac_goods" => $combo["co_goods"],
                "ac_states" => 0,
                "goods_Type" => 1,
                "expire_status" => 1  // 只查找标记过期的账号
            ])->where('exit_time', '<', time())->find();

            if ($account) {
                $isExpiredReassignment = true;

                // ✅ 分配时才执行改密操作
                try {
                    $apiController = new \app\controller\Api();
                    $apiController->changePasswordForReassignment($account);
                } catch (\Exception $e) {
                    // 改密失败，记录日志但不阻止分配
                    Db::table('monitor_logs')->insert([
                        'account_name' => $account['ac_name'],
                        'action' => '重新分配改密失败',
                        'monitor_type' => '支付分配',
                        'timestamp' => date('Y-m-d H:i:s'),
                        'status' => '警告',
                        'error_message' => '改密失败但继续分配: ' . $e->getMessage()
                    ]);
                }
            }
        }

        if (!$account) {
            // 正常的在线账号处理逻辑
            $account = Account::where([
                "ac_goods" => $combo["co_goods"],
                "ac_sell" => 1,
                "ac_states" => 1,
                "goods_Type" => 1,
            ])->find();
        }

        // 修改：处理没有可用账号的情况
        if (!$account) {
            // 更新订单状态，标记为已支付但未分配账号
            Order::where("ord_bbh", $out_trade_no)->update([
                "ord_ifpay" => 1,
                "account_status" => 0, // 0表示未分配账号
                "or_maturity" => 1,//订单未到期
                "ord_remarks" => "暂无可用账号，系统将在账号可用时自动分配",
                "pending_reason" => "库存不足"
            ]);

            // 更新商品销量
            $currentSales = Goods::where("id", $combo["co_goods"])->value("goods_xl");
            Goods::where("id", $combo["co_goods"])->update(["goods_xl" => $currentSales + 1]);

            return;
        }

        if ($account) {
            $accountId = $account["id"];
            // 计算到期时间
            $duration = intval($upOrder['duration']);
            $unit = strtolower($upOrder['unit']);
            $seconds = 0;

            if ($unit == 'day') {
                $seconds = $duration * 24 * 3600;
            } elseif ($unit == 'hour') {
                $seconds = $duration * 3600;
            }

            $exitTime = time() + $seconds;

            // 获取账号名称
            $acName = Account::where('id', $accountId)->value('ac_name');

            // ✅ 使用统一的账号分配方法
            $assignResult = \app\controller\Api::assignAccount(
                $acName,
                $upOrder['ord_uid'],
                $exitTime
            );

            if (!$assignResult) {
                throw new \Exception("账号绑定失败");
            }

            // 更新订单中的账号信息
            Order::where("ord_bbh", $out_trade_no)->update(["ord_aid" => $accountId, "or_maturity" => 1]);

            // 更新商品销量
            $currentSales = Goods::where("id", $account["ac_goods"])->value("goods_xl");
            Goods::where("id", $account["ac_goods"])->update(["goods_xl" => $currentSales + 1]);
        }
    }

    /**
     * 处理团购逻辑
     *
     * @param array $combo 套餐信息
     * @return void
     */
    private function processGroupPurchase($combo)
    {
        $currentSales = Goods::where("id", $combo["co_goods"])->value("goods_xl");
        Goods::where("id", $combo["co_goods"])->update(["goods_xl" => $currentSales + 1]);
    }
}
