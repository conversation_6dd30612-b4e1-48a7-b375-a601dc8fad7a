<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>数据清理工具</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/layui/css/layui.css" media="all">
    <style>
        .cleanup-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .cleanup-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .danger-zone {
            border: 2px solid #ff4757;
            background: #fff5f5;
        }
        .info-zone {
            border: 2px solid #3742fa;
            background: #f8f9ff;
        }
        .warning-text {
            color: #ff4757;
            font-weight: bold;
        }
        .result-box {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .result-success {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        .result-error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        .account-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 10px;
            background: #f8fafc;
        }
        .account-item {
            padding: 8px;
            border-bottom: 1px solid #e2e8f0;
            font-family: monospace;
            font-size: 12px;
        }
        .account-item:last-child {
            border-bottom: none;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
        }
        .stat-label {
            color: #6b7280;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="cleanup-container">
        <h1 style="text-align: center; margin-bottom: 30px; color: #1f2937;">🛠️ 数据清理工具</h1>
        
        <!-- 检查区域 -->
        <div class="cleanup-card info-zone">
            <h3>📊 数据检查</h3>
            <p>检查系统中的离线账号数据状态</p>
            
            <div class="layui-btn-group">
                <button class="layui-btn layui-btn-primary" onclick="checkWrongAccounts()">🔍 检查错误记录</button>
                <button class="layui-btn layui-btn-primary" onclick="checkNormalAccounts()">📈 检查正常记录</button>
            </div>
            
            <div id="checkResult" class="result-box"></div>
        </div>

        <!-- 清理区域 -->
        <div class="cleanup-card danger-zone">
            <h3>⚠️ 危险操作区域</h3>
            <p class="warning-text">以下操作将永久删除数据，请谨慎操作！</p>
            <p>此工具用于清理离线卡密兑换BUG产生的错误账号记录（以OFFLINE_开头的账号）</p>
            
            <div style="margin: 20px 0;">
                <h4>问题说明：</h4>
                <ul style="margin-left: 20px; color: #666;">
                    <li>离线卡密兑换时错误地创建了新账号记录</li>
                    <li>这些账号以"OFFLINE_卡密码"格式命名</li>
                    <li>正确的逻辑应该是分配现有的离线账号</li>
                    <li>此工具将清理这些错误创建的账号记录</li>
                </ul>
            </div>
            
            <button class="layui-btn layui-btn-danger" onclick="cleanOfflineAccounts()">🗑️ 清理错误的离线账号记录</button>
            
            <div id="cleanResult" class="result-box"></div>
        </div>
    </div>

    <script src="/static/layui/layui.js"></script>
    <script>
    layui.use(['layer'], function(){
        var layer = layui.layer;

        // 检查错误的账号记录
        window.checkWrongAccounts = function() {
            var loadIndex = layer.load(1, {shade: [0.1,'#fff']});
            
            $.ajax({
                url: '/admin/CleanupController/checkOfflineAccounts',
                type: 'GET',
                success: function(res) {
                    layer.close(loadIndex);
                    
                    var resultBox = $('#checkResult');
                    
                    if (res.code === 1) {
                        resultBox.removeClass('result-error').addClass('result-success').show();
                        
                        var html = '<h4>🔍 错误记录检查结果</h4>';
                        html += '<p>发现 <strong>' + res.data.count + '</strong> 条错误的离线账号记录</p>';
                        
                        if (res.data.count > 0) {
                            html += '<div class="account-list">';
                            res.data.accounts.forEach(function(account) {
                                html += '<div class="account-item">';
                                html += 'ID: ' + account.id + ' | 账号: ' + account.ac_name + ' | 用户ID: ' + (account.ac_uid || '未分配') + ' | 创建时间: ' + account.time;
                                html += '</div>';
                            });
                            html += '</div>';
                        }
                        
                        resultBox.html(html);
                    } else {
                        resultBox.removeClass('result-success').addClass('result-error').show();
                        resultBox.html('<h4>❌ 检查失败</h4><p>' + res.msg + '</p>');
                    }
                },
                error: function() {
                    layer.close(loadIndex);
                    layer.msg('检查失败，请稍后重试', {icon: 2});
                }
            });
        };

        // 检查正常的账号记录
        window.checkNormalAccounts = function() {
            var loadIndex = layer.load(1, {shade: [0.1,'#fff']});
            
            $.ajax({
                url: '/admin/CleanupController/checkNormalOfflineAccounts',
                type: 'GET',
                success: function(res) {
                    layer.close(loadIndex);
                    
                    var resultBox = $('#checkResult');
                    
                    if (res.code === 1) {
                        resultBox.removeClass('result-error').addClass('result-success').show();
                        
                        var html = '<h4>📈 正常离线账号统计</h4>';
                        html += '<div class="stats-grid">';
                        html += '<div class="stat-card"><div class="stat-number">' + res.data.total_count + '</div><div class="stat-label">总账号数</div></div>';
                        html += '<div class="stat-card"><div class="stat-number">' + res.data.available_count + '</div><div class="stat-label">可用账号</div></div>';
                        html += '<div class="stat-card"><div class="stat-number">' + res.data.assigned_count + '</div><div class="stat-label">已分配账号</div></div>';
                        html += '</div>';
                        
                        if (res.data.accounts.length > 0) {
                            html += '<h5 style="margin-top: 20px;">最近50条记录：</h5>';
                            html += '<div class="account-list">';
                            res.data.accounts.forEach(function(account) {
                                var statusText = account.ac_states == 1 ? '可用' : '已分配';
                                var statusColor = account.ac_states == 1 ? '#10b981' : '#ef4444';
                                html += '<div class="account-item">';
                                html += 'ID: ' + account.id + ' | 账号: ' + account.ac_name + ' | 状态: <span style="color:' + statusColor + '">' + statusText + '</span> | 用户ID: ' + (account.ac_uid || '未分配');
                                html += '</div>';
                            });
                            html += '</div>';
                        }
                        
                        resultBox.html(html);
                    } else {
                        resultBox.removeClass('result-success').addClass('result-error').show();
                        resultBox.html('<h4>❌ 检查失败</h4><p>' + res.msg + '</p>');
                    }
                },
                error: function() {
                    layer.close(loadIndex);
                    layer.msg('检查失败，请稍后重试', {icon: 2});
                }
            });
        };

        // 清理错误的账号记录
        window.cleanOfflineAccounts = function() {
            layer.confirm('⚠️ 确定要清理错误的离线账号记录吗？<br><br>此操作将永久删除以"OFFLINE_"开头的账号记录，<strong>不可撤销</strong>！', {
                icon: 3,
                title: '危险操作确认',
                btn: ['确定清理', '取消']
            }, function(index) {
                layer.close(index);
                
                var loadIndex = layer.load(1, {shade: [0.3,'#000']});
                
                $.ajax({
                    url: '/admin/CleanupController/cleanOfflineAccounts',
                    type: 'POST',
                    success: function(res) {
                        layer.close(loadIndex);
                        
                        var resultBox = $('#cleanResult');
                        
                        if (res.code === 1) {
                            resultBox.removeClass('result-error').addClass('result-success').show();
                            
                            var html = '<h4>✅ 清理完成</h4>';
                            html += '<p>' + res.msg + '</p>';
                            
                            if (res.data && res.data.details.length > 0) {
                                html += '<h5>清理详情：</h5>';
                                html += '<div class="account-list">';
                                res.data.details.forEach(function(detail) {
                                    html += '<div class="account-item">';
                                    html += '已删除 - ID: ' + detail.id + ' | 账号: ' + detail.ac_name + ' | 用户ID: ' + (detail.ac_uid || '未分配') + ' | 创建时间: ' + detail.time;
                                    html += '</div>';
                                });
                                html += '</div>';
                            }
                            
                            resultBox.html(html);
                            layer.msg('清理成功！', {icon: 1});
                        } else {
                            resultBox.removeClass('result-success').addClass('result-error').show();
                            resultBox.html('<h4>❌ 清理失败</h4><p>' + res.msg + '</p>');
                            layer.msg('清理失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('清理失败，请稍后重试', {icon: 2});
                    }
                });
            });
        };
    });
    </script>
</body>
</html>
