<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>{$system.sy_title} - 控制台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary: #3B82F6;
      --primary-light: #93C5FD;
      --primary-dark: #1D4ED8;
      --success: #10B981;
      --warning: #F59E0B;
      --danger: #EF4444;
      --info: #06B6D4;
      --gray-50: #F9FAFB;
      --gray-100: #F3F4F6;
      --gray-200: #E5E7EB;
      --gray-300: #D1D5DB;
      --gray-400: #9CA3AF;
      --gray-500: #6B7280;
      --gray-600: #4B5563;
      --gray-700: #374151;
      --gray-800: #1F2937;
      --gray-900: #111827;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      background-color: var(--gray-50);
      color: var(--gray-800);
      font-size: 14px;
      line-height: 1.5;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    .layui-fluid {
      padding: 24px;
      max-width: 1600px;
      margin: 0 auto;
    }
    
    /* 数据卡片 */
    .dashboard-card {
      background-color: #fff;
      border-radius: 12px;
      box-shadow: var(--shadow);
      margin-bottom: 24px;
      transition: all 0.25s;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      border: 1px solid var(--gray-100);
    }
    
    .card-header {
      padding: 16px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid var(--gray-100);
      background-color: #fff;
    }
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-800);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .card-title .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 8px;
      background-color: rgba(59, 130, 246, 0.1);
      color: var(--primary);
    }
    
    .card-body {
      padding: 20px;
      flex: 1;
      position: relative;
    }
    
    /* 数据统计网格 */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
    }
    
    .stat-card {
      background-color: #fff;
      border-radius: 10px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      transition: all 0.25s;
      border: 1px solid var(--gray-100);
      position: relative;
      overflow: hidden;
    }
    
    .stat-card:hover {
      box-shadow: var(--shadow-md);
      border-color: var(--primary-light);
    }
    
    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(to right, var(--primary), var(--primary-light));
      opacity: 0;
      transition: opacity 0.2s;
    }
    
    .stat-card:hover::before {
      opacity: 1;
    }
    
    .stat-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 42px;
      height: 42px;
      border-radius: 10px;
      background-color: rgba(59, 130, 246, 0.1);
      color: var(--primary);
      margin-bottom: 8px;
    }
    
    .stat-icon.success {
      background-color: rgba(16, 185, 129, 0.1);
      color: var(--success);
    }
    
    .stat-icon.warning {
      background-color: rgba(245, 158, 11, 0.1);
      color: var(--warning);
    }
    
    .stat-icon.info {
      background-color: rgba(6, 182, 212, 0.1);
      color: var(--info);
    }
    
    .stat-icon.danger {
      background-color: rgba(239, 68, 68, 0.1);
      color: var(--danger);
    }
    
    .stat-label {
      font-size: 14px;
      color: var(--gray-600);
      margin-bottom: 4px;
    }
    
    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: var(--gray-900);
      line-height: 1.2;
    }
    
    .stat-trend {
      font-size: 12px;
      color: var(--gray-500);
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 4px;
    }
    
    .stat-trend.up {
      color: var(--success);
    }
    
    .stat-trend.down {
      color: var(--danger);
    }
    
    /* 图表容器 */
    .chart-container {
      height: 320px;
      width: 100%;
    }
    
    /* 标签 */
    .badge {
      display: inline-flex;
      align-items: center;
      padding: 4px 10px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      line-height: 1;
    }
    
    .badge.primary {
      color: var(--primary);
      background-color: rgba(59, 130, 246, 0.1);
    }
    
    .badge.success {
      color: var(--success);
      background-color: rgba(16, 185, 129, 0.1);
    }
    
    .badge.warning {
      color: var(--warning);
      background-color: rgba(245, 158, 11, 0.1);
    }
    
    .badge.danger {
      color: var(--danger);
      background-color: rgba(239, 68, 68, 0.1);
    }
    
    .badge.info {
      color: var(--info);
      background-color: rgba(6, 182, 212, 0.1);
    }
    
    /* 进度条 */
    .progress {
      height: 8px;
      background-color: var(--gray-100);
      border-radius: 4px;
      overflow: hidden;
      margin-top: 6px;
    }
    
    .progress-bar {
      height: 100%;
      border-radius: 4px;
      background: linear-gradient(to right, var(--primary), var(--primary-light));
    }
    
    .progress-bar.success {
      background: linear-gradient(to right, var(--success), #34D399);
    }
    
    .progress-bar.warning {
      background: linear-gradient(to right, var(--warning), #FBBF24);
    }
    
    /* 背景图案 */
    .bg-pattern {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.02;
      pointer-events: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cg fill='%233B82F6' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/svg%3E");
    }
    
    /* 动画 */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .fade-in {
      animation: fadeIn 0.4s ease forwards;
    }
    
    /* 响应式调整 */
    @media (max-width: 1200px) {
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (max-width: 768px) {
      .layui-fluid {
        padding: 16px;
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
      }
      
      .chart-container {
        height: 260px;
      }
    }

    /* 系统介绍对话框 */
    .system-intro-modal {
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      backdrop-filter: blur(5px);
      opacity: 0;
      transition: opacity 0.5s ease;
      pointer-events: auto;
    }

    .system-intro-dialog {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.95);
      width: 90%;
      max-width: 700px;
      background-color: white;
      border-radius: 12px;
      box-shadow: var(--shadow-lg);
      overflow: hidden;
      transition: all 0.5s ease;
      opacity: 0;
    }

    .modal-open {
      display: block;
      opacity: 1;
    }

    .modal-open .system-intro-dialog {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }

    .intro-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      background: linear-gradient(to right, var(--primary), var(--primary-dark));
      color: white;
    }

    .intro-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 18px;
      font-weight: 600;
    }

    .intro-title-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .intro-close {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
      cursor: pointer;
      transition: all 0.2s;
    }

    .intro-close:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .intro-body {
      padding: 24px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .intro-content {
      color: var(--gray-700);
      line-height: 1.6;
    }

    .intro-section {
      margin-bottom: 24px;
    }

    .intro-section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .intro-feature-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-bottom: 20px;
    }

    .intro-feature-item {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      padding: 14px;
      border-radius: 10px;
      background-color: var(--gray-50);
      border: 1px solid var(--gray-100);
      transition: all 0.2s;
    }

    .intro-feature-item:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
      border-color: var(--primary-light);
      background-color: white;
    }

    .intro-feature-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 8px;
      background-color: rgba(59, 130, 246, 0.1);
      color: var(--primary);
      flex-shrink: 0;
    }

    .intro-feature-text {
      flex: 1;
    }

    .intro-feature-title {
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: 2px;
    }

    .intro-feature-desc {
      font-size: 13px;
      color: var(--gray-600);
    }

    .intro-footer {
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid var(--gray-100);
      background-color: var(--gray-50);
    }

    .intro-dev-info {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 13px;
      color: var(--gray-600);
    }

    .intro-dev-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 6px;
      background-color: rgba(59, 130, 246, 0.1);
      color: var(--primary);
    }

    .intro-version {
      font-size: 13px;
      color: var(--gray-600);
      font-weight: 500;
      background-color: var(--gray-100);
      padding: 4px 10px;
      border-radius: 20px;
    }

    @media (max-width: 768px) {
      .intro-feature-grid {
        grid-template-columns: 1fr;
      }
      
      .intro-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
    }

    .system-intro-modal {
      transition: opacity 0.5s ease;
    }

    .system-intro-dialog {
      transition: all 0.5s ease;
    }
  </style>
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space20">
      <div class="layui-col-md8">
        <!-- 数据概览 -->
        <div class="dashboard-card fade-in" style="animation-delay: 0s;">
          <div class="card-header">
            <h3 class="card-title">
              <span class="icon"><i class="layui-icon layui-icon-chart-screen"></i></span>
              数据概览
            </h3>
            <span class="badge success">实时</span>
          </div>
          <div class="card-body">
            <div class="bg-pattern"></div>
            <div class="stats-grid">
              <!-- 账号总数 -->
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="layui-icon layui-icon-username"></i>
                </div>
                <div class="stat-label">账号总数</div>
                <div class="stat-value">{$data.accounts.total|default='0'}</div>
                <div class="stat-trend up">
                  <i class="layui-icon layui-icon-up"></i>
                  <span>本月新增 {$data.accounts.monthly_increase|default='0'} 个</span>
                </div>
              </div>
              
              <!-- 在线账号 -->
              <div class="stat-card">
                <div class="stat-icon success">
                  <i class="layui-icon layui-icon-circle-dot"></i>
                </div>
                <div class="stat-label">在线账号</div>
                <div class="stat-value">{$data.accounts.online|default='0'}</div>
                <div class="stat-trend">
                  <span>使用率 {$data.accounts.usage_rate|default='0'}%</span>
                </div>
              </div>
              
              <!-- 用户总数 -->
              <div class="stat-card">
                <div class="stat-icon info">
                  <i class="layui-icon layui-icon-user"></i>
                </div>
                <div class="stat-label">用户总数</div>
                <div class="stat-value">{$data.users.total|default='0'}</div>
                <div class="stat-trend up">
                  <i class="layui-icon layui-icon-up"></i>
                  <span>今日新增 {$data.users.today|default='0'} 人</span>
                </div>
              </div>
              
              <!-- VIP用户 -->
              <div class="stat-card">
                <div class="stat-icon warning">
                  <i class="layui-icon layui-icon-diamond"></i>
                </div>
                <div class="stat-label">VIP用户</div>
                <div class="stat-value">{$data.users.vip|default='0'}</div>
                <div class="stat-trend">
                  <span>占比 {$data.users.vip_percentage|default='0'}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 订单与收入 -->
        <div class="dashboard-card fade-in" style="animation-delay: 0.1s;">
          <div class="card-header">
            <h3 class="card-title">
              <span class="icon"><i class="layui-icon layui-icon-rmb"></i></span>
              今日数据
            </h3>
            <span class="badge info">每日更新</span>
          </div>
          <div class="card-body">
            <div class="bg-pattern"></div>
            <div class="stats-grid">
              <!-- 今日收入 -->
              <div class="stat-card">
                <div class="stat-icon primary">
                  <i class="layui-icon layui-icon-rmb"></i>
                </div>
                <div class="stat-label">今日收入</div>
                <div class="stat-value">{$data.income.today|default='0'}</div>
                <div class="stat-trend">
                  <span>环比 {$data.income.daily_change|default='0'}%</span>
                </div>
              </div>
              
              <!-- 今日订单 -->
              <div class="stat-card">
                <div class="stat-icon info">
                  <i class="layui-icon layui-icon-cart"></i>
                </div>
                <div class="stat-label">今日订单</div>
                <div class="stat-value">{$data.orders.today|default='0'}</div>
                <div class="stat-trend">
                  <span>完成率 {$data.orders.completion_rate|default='0'}%</span>
                </div>
              </div>
              
              <!-- 已租用账号 -->
              <div class="stat-card">
                <div class="stat-icon success">
                  <i class="layui-icon layui-icon-release"></i>
                </div>
                <div class="stat-label">已租用账号</div>
                <div class="stat-value">{$data.accounts.rented|default='0'}</div>
                <div class="stat-trend">
                  <span>总数占比 {$data.accounts.rented_percentage|default='0'}%</span>
                </div>
              </div>
              
              <!-- 今日新增用户 -->
              <div class="stat-card">
                <div class="stat-icon warning">
                  <i class="layui-icon layui-icon-add-1"></i>
                </div>
                <div class="stat-label">今日新增用户</div>
                <div class="stat-value">{$data.users.today|default='0'}</div>
                <div class="stat-trend">
                  <span>转化率 {$data.users.conversion_rate|default='0'}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="layui-row layui-col-space20">
          <div class="layui-col-md7">
            <!-- 年度流水统计 -->
            <div class="dashboard-card fade-in" style="animation-delay: 0.2s;">
              <div class="card-header">
                <h3 class="card-title">
                  <span class="icon"><i class="layui-icon layui-icon-chart"></i></span>
                  年度流水统计
                </h3>
                <div class="toggle-group">
                  <button class="toggle-btn active" id="currentYear">本年</button>
                  <button class="toggle-btn" id="lastYear">上年</button>
                </div>
              </div>
              <div class="card-body">
                <div class="bg-pattern"></div>
                <div id="yearlyChart" class="chart-container"></div>
              </div>
            </div>
          </div>
          
          <div class="layui-col-md5">
            <!-- 工单统计 -->
            <div class="dashboard-card fade-in" style="animation-delay: 0.3s;">
              <div class="card-header">
                <h3 class="card-title">
                  <span class="icon"><i class="layui-icon layui-icon-form"></i></span>
                  工单统计
                </h3>
                <span class="badge warning">待处理 {$data.workorders.pending|default='0'}</span>
              </div>
              <div class="card-body">
                <div class="bg-pattern"></div>
                <div class="stats-grid" style="grid-template-columns: repeat(2, 1fr);">
                  <div class="stat-card">
                    <div class="stat-icon warning">
                      <i class="layui-icon layui-icon-dialogue"></i>
                    </div>
                    <div class="stat-label">待处理工单</div>
                    <div class="stat-value">{$data.workorders.pending|default='0'}</div>
                    <div class="progress">
                      <div class="progress-bar warning" style="width: {$data.workorders.pending_percentage|default='0'}%;"></div>
                    </div>
                  </div>
                  
                  <div class="stat-card">
                    <div class="stat-icon success">
                      <i class="layui-icon layui-icon-ok"></i>
                    </div>
                    <div class="stat-label">已处理工单</div>
                    <div class="stat-value">{$data.workorders.processed|default='0'}</div>
                    <div class="progress">
                      <div class="progress-bar success" style="width: {$data.workorders.processed_percentage|default='0'}%;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="layui-col-md4">
        <!-- 系统信息 -->
        <div class="dashboard-card fade-in" style="animation-delay: 0.4s;">
          <div class="card-header">
            <h3 class="card-title">
              <span class="icon"><i class="layui-icon layui-icon-console"></i></span>
              系统信息
            </h3>
            <span class="badge success">运行正常</span>
          </div>
          <div class="card-body">
            <div class="bg-pattern"></div>
            <div class="system-info">
              <div class="info-item" style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--gray-100);">
                <div class="info-icon" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border-radius: 8px; background-color: rgba(59, 130, 246, 0.1); margin-right: 12px;">
                  <i class="layui-icon layui-icon-website" style="color: var(--primary);"></i>
                </div>
                <div style="flex: 1;">
                  <div style="font-size: 12px; color: var(--gray-500);">系统名称</div>
                  <div style="font-size: 15px; font-weight: 600; color: var(--gray-800);">{$system.sy_title|default='Steam租号系统'}</div>
                </div>
              </div>
              <div class="info-item" style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--gray-100);">
                <div class="info-icon" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border-radius: 8px; background-color: rgba(16, 185, 129, 0.1); margin-right: 12px;">
                  <i class="layui-icon layui-icon-release" style="color: var(--success);"></i>
                </div>
                <div style="flex: 1;">
                  <div style="font-size: 12px; color: var(--gray-500);">系统版本</div>
                  <div style="font-size: 15px; font-weight: 600; color: var(--gray-800);">{$system.version|default='1.0.0'}</div>
                </div>
              </div>
              <div class="info-item" style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--gray-100);">
                <div class="info-icon" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border-radius: 8px; background-color: rgba(245, 158, 11, 0.1); margin-right: 12px;">
                  <i class="layui-icon layui-icon-server" style="color: var(--warning);"></i>
                </div>
                <div style="flex: 1;">
                  <div style="font-size: 12px; color: var(--gray-500);">服务器信息</div>
                  <div style="font-size: 15px; font-weight: 600; color: var(--gray-800);">{$system.server_info|default='PHP/'.PHP_VERSION}</div>
                </div>
              </div>
              <div class="info-item" style="display: flex; align-items: center; padding: 12px 0;">
                <div class="info-icon" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border-radius: 8px; background-color: rgba(6, 182, 212, 0.1); margin-right: 12px;">
                  <i class="layui-icon layui-icon-time" style="color: var(--info);"></i>
                </div>
                <div style="flex: 1;">
                  <div style="font-size: 12px; color: var(--gray-500);">运行天数</div>
                  <div style="font-size: 15px; font-weight: 600; color: var(--gray-800);">{$system.running_days|default='0'}天</div>
                </div>
              </div>
            </div>
            
            <div style="margin-top: 16px; background-color: var(--gray-50); border-radius: 8px; padding: 16px;">
              <h3 style="font-size: 15px; font-weight: 600; color: var(--gray-800); margin-bottom: 10px; display: flex; align-items: center;">
                <i class="layui-icon layui-icon-about" style="margin-right: 8px; color: var(--primary);"></i>
                系统介绍
              </h3>
              <div style="font-size: 13px; color: var(--gray-600); line-height: 1.6;">
                <p style="margin-bottom: 10px;">
                  <span style="color: var(--primary); font-weight: 500;">Steam租号系统</span>是一个全方位的游戏账号租赁平台，提供安全、便捷的Steam账号租赁服务。
                </p>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                  <div style="display: flex; align-items: flex-start; gap: 8px;">
                    <i class="layui-icon layui-icon-set" style="color: var(--primary); font-size: 13px; padding-top: 3px;"></i>
                    <span>自动化Steam账号登录与验证</span>
                  </div>
                  <div style="display: flex; align-items: flex-start; gap: 8px;">
                    <i class="layui-icon layui-icon-util" style="color: var(--primary); font-size: 13px; padding-top: 3px;"></i>
                    <span>多级会员体系与特权设置</span>
                  </div>
                  <div style="display: flex; align-items: flex-start; gap: 8px;">
                    <i class="layui-icon layui-icon-dollar" style="color: var(--primary); font-size: 13px; padding-top: 3px;"></i>
                    <span>灵活的计费方案与支付方式</span>
                  </div>
                  <div style="display: flex; align-items: flex-start; gap: 8px;">
                    <i class="layui-icon layui-icon-chart" style="color: var(--primary); font-size: 13px; padding-top: 3px;"></i>
                    <span>实时数据统计与分析功能</span>
                  </div>
                  <div style="display: flex; align-items: flex-start; gap: 8px;">
                    <i class="layui-icon layui-icon-form" style="color: var(--primary); font-size: 13px; padding-top: 3px;"></i>
                    <span>工单系统与客户支持</span>
                  </div>
                  <div style="display: flex; align-items: flex-start; gap: 8px;">
                    <i class="layui-icon layui-icon-template-1" style="color: var(--primary); font-size: 13px; padding-top: 3px;"></i>
                    <span>响应式前端与管理界面</span>
                  </div>
                </div>
                <p style="margin-bottom: 0;">
                  系统支持多级权限管理、自动账号状态监控、短信与邮件通知、自定义计费规则，确保账号租赁全流程安全可靠。
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 系统性能监控 -->
        <div class="dashboard-card fade-in" style="animation-delay: 0.5s;">
          <div class="card-header">
            <h3 class="card-title">
              <span class="icon"><i class="layui-icon layui-icon-fire"></i></span>
              系统性能监控
            </h3>
            <span class="badge info">实时</span>
          </div>
          <div class="card-body">
            <div class="bg-pattern"></div>
            <div style="margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <div style="font-size: 14px; color: var(--gray-600);">CPU 使用率</div>
                <div style="font-size: 14px; font-weight: 600; color: var(--gray-800);">{$system.cpu_usage|default='45'}%</div>
              </div>
              <div class="progress">
                <div class="progress-bar" style="width: {$system.cpu_usage|default='45'}%;"></div>
              </div>
            </div>
            <div style="margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <div style="font-size: 14px; color: var(--gray-600);">内存使用率</div>
                <div style="font-size: 14px; font-weight: 600; color: var(--gray-800);">{$system.memory_usage|default='62'}%</div>
              </div>
              <div class="progress">
                <div class="progress-bar" style="width: {$system.memory_usage|default='62'}%;"></div>
              </div>
            </div>
            <div style="margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <div style="font-size: 14px; color: var(--gray-600);">磁盘使用率</div>
                <div style="font-size: 14px; font-weight: 600; color: var(--gray-800);">{$system.disk_usage|default='38'}%</div>
              </div>
              <div class="progress">
                <div class="progress-bar success" style="width: {$system.disk_usage|default='38'}%;"></div>
              </div>
            </div>
            <div>
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <div style="font-size: 14px; color: var(--gray-600);">网络带宽</div>
                <div style="font-size: 14px; font-weight: 600; color: var(--gray-800);">{$system.network_usage|default='75'}%</div>
              </div>
              <div class="progress">
                <div class="progress-bar warning" style="width: {$system.network_usage|default='75'}%;"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 活跃度分析面板 -->
        <div class="dashboard-card fade-in" style="animation-delay: 0.6s;">
          <div class="card-header">
            <h3 class="card-title">
              <span class="icon"><i class="layui-icon layui-icon-chart-screen"></i></span>
              活跃度分析
            </h3>
            <span class="badge info">本周</span>
          </div>
          <div class="card-body">
            <div class="bg-pattern"></div>
            <div style="margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <div style="font-size: 14px; color: var(--gray-600);">账号使用率</div>
                <div style="font-size: 14px; font-weight: 600; color: var(--gray-800);">{$data.activity.account_usage|default='75'}%</div>
              </div>
              <div class="progress">
                <div class="progress-bar" style="width: {$data.activity.account_usage|default='75'}%;"></div>
              </div>
            </div>
            <div style="margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <div style="font-size: 14px; color: var(--gray-600);">用户活跃度</div>
                <div style="font-size: 14px; font-weight: 600; color: var(--gray-800);">{$data.activity.user_activity|default='68'}%</div>
              </div>
              <div class="progress">
                <div class="progress-bar success" style="width: {$data.activity.user_activity|default='68'}%;"></div>
              </div>
            </div>
            <div style="margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <div style="font-size: 14px; color: var(--gray-600);">转化率</div>
                <div style="font-size: 14px; font-weight: 600; color: var(--gray-800);">{$data.activity.conversion_rate|default='52'}%</div>
              </div>
              <div class="progress">
                <div class="progress-bar success" style="width: {$data.activity.conversion_rate|default='52'}%;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 系统介绍对话框 -->
  <div class="system-intro-modal" id="systemIntroModal">
    <div class="system-intro-dialog">
      <div class="intro-header">
        <div class="intro-title">
          <div class="intro-title-icon">
            <i class="layui-icon layui-icon-console"></i>
          </div>
          <span>Steam租号系统 - 完整介绍</span>
        </div>
        <div class="intro-close" id="introCloseBtn">
          <i class="layui-icon layui-icon-close"></i>
        </div>
      </div>
      
      <div class="intro-body">
        <div class="intro-content">
          <div class="intro-section">
            <p>
              <strong style="color: var(--primary); font-size: 16px;">Steam租号系统</strong>
              是一款专业级游戏账号租赁管理平台，为游戏爱好者提供安全、高效、便捷的Steam账号租赁服务。本系统采用前沿技术架构，提供全面的账号管理、用户管理、订单管理和数据分析功能，帮助管理员轻松运营Steam账号租赁业务。
            </p>
          </div>
          
          <div class="intro-section">
            <div class="intro-section-title">
              <i class="layui-icon layui-icon-star-fill" style="color: var(--primary);"></i>
              核心功能
            </div>
            
            <div class="intro-feature-grid">
              <div class="intro-feature-item">
                <div class="intro-feature-icon">
                  <i class="layui-icon layui-icon-username"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">智能账号管理</div>
                  <div class="intro-feature-desc">自动化管理Steam账号库存，实时监控账号状态，智能分配租用资源</div>
                </div>
              </div>
              
              <div class="intro-feature-item">
                <div class="intro-feature-icon">
                  <i class="layui-icon layui-icon-login-wechat"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">多方式登录验证</div>
                  <div class="intro-feature-desc">支持账号密码、短信、邮箱、微信、QQ等多种登录方式，保障账号安全</div>
                </div>
              </div>
              
              <div class="intro-feature-item">
                <div class="intro-feature-icon" style="color: var(--success); background-color: rgba(16, 185, 129, 0.1);">
                  <i class="layui-icon layui-icon-dollar"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">灵活计费系统</div>
                  <div class="intro-feature-desc">支持时段、包天、包周、包月等多种计费方式，灵活设置折扣与优惠</div>
                </div>
              </div>
              
              <div class="intro-feature-item">
                <div class="intro-feature-icon" style="color: var(--success); background-color: rgba(16, 185, 129, 0.1);">
                  <i class="layui-icon layui-icon-diamond"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">会员等级体系</div>
                  <div class="intro-feature-desc">多级会员体系，不同等级享受不同权益，提升用户黏性与收益</div>
                </div>
              </div>
              
              <div class="intro-feature-item">
                <div class="intro-feature-icon" style="color: var(--warning); background-color: rgba(245, 158, 11, 0.1);">
                  <i class="layui-icon layui-icon-cart"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">订单智能处理</div>
                  <div class="intro-feature-desc">自动化订单流程，从下单到账号分配、使用、归还的全流程监控</div>
                </div>
              </div>
              
              <div class="intro-feature-item">
                <div class="intro-feature-icon" style="color: var(--warning); background-color: rgba(245, 158, 11, 0.1);">
                  <i class="layui-icon layui-icon-chart"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">数据可视化</div>
                  <div class="intro-feature-desc">多维度数据统计与分析，直观展示业务状况，辅助经营决策</div>
                </div>
              </div>
              
              <div class="intro-feature-item">
                <div class="intro-feature-icon" style="color: var(--info); background-color: rgba(6, 182, 212, 0.1);">
                  <i class="layui-icon layui-icon-dialogue"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">工单客服系统</div>
                  <div class="intro-feature-desc">完善的工单处理流程，多渠道客服支持，提高用户满意度</div>
                </div>
              </div>
              
              <div class="intro-feature-item">
                <div class="intro-feature-icon" style="color: var(--info); background-color: rgba(6, 182, 212, 0.1);">
                  <i class="layui-icon layui-icon-set"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">多级权限管理</div>
                  <div class="intro-feature-desc">细粒度的权限控制，支持多角色管理，保障系统安全</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="intro-section">
            <div class="intro-section-title">
              <i class="layui-icon layui-icon-diamond" style="color: var(--primary);"></i>
              高级特性
            </div>
            
            <div class="intro-feature-grid">
              <div class="intro-feature-item">
                <div class="intro-feature-icon">
                  <i class="layui-icon layui-icon-share"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">API开放接口</div>
                  <div class="intro-feature-desc">开放标准API接口，支持第三方应用集成与扩展开发</div>
                </div>
              </div>
              
              <!-- <div class="intro-feature-item">
                <div class="intro-feature-icon">
                  <i class="layui-icon layui-icon-notice"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">多渠道通知</div>
                  <div class="intro-feature-desc">支持短信、邮件、站内信、微信等多种通知方式</div>
                </div>
              </div> -->
              
              <!-- <div class="intro-feature-item">
                <div class="intro-feature-icon">
                  <i class="layui-icon layui-icon-theme"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">自定义界面</div>
                  <div class="intro-feature-desc">可定制化的前端界面，适应不同品牌风格需求</div>
                </div>
              </div> -->
              
              <div class="intro-feature-item">
                <div class="intro-feature-icon">
                  <i class="layui-icon layui-icon-survey"></i>
                </div>
                <div class="intro-feature-text">
                  <div class="intro-feature-title">操作日志审计</div>
                  <div class="intro-feature-desc">全面的操作日志记录与审计功能，保障系统安全</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="intro-section">
            <div class="intro-section-title">
              <i class="layui-icon layui-icon-service" style="color: var(--primary);"></i>
              技术支持与服务
            </div>
            <p>我们提供全方位的技术支持与服务：</p>
            <ul style="padding-left: 20px; margin-bottom: 16px;">
              <!-- <li>7×24小时技术支持</li> -->
              <!-- <li>定期系统维护与升级</li> -->
              <!-- <li>数据定期备份与恢复</li>
              <li>安全漏洞及时修复</li> -->
              <li>根据需求定制开发</li>
            </ul>
            <p>如需技术支持，请联系开发者。</p>
          </div>
        </div>
      </div>
      
      <div class="intro-footer">
        <div class="intro-dev-info">
          <div class="intro-dev-icon">
            <i class="layui-icon layui-icon-group"></i>
          </div>
          <span>开发者：空祖(Kongzu)</span>
        </div>
        <div class="intro-version">当前版本：v1.0.0</div>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '/layuiadmin/'
  }).extend({
    index: 'lib/index'
  }).use(['index', 'element', 'echarts'], function(){
    var $ = layui.$
    ,echarts = layui.echarts
    ,element = layui.element;
    
    // 初始化年度流水图表
    var yearlyChart = echarts.init(document.getElementById('yearlyChart'));
    
    // 获取后端传来的年度数据，如果不存在则使用默认数据
    var currentYearData = {
      months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      values: {$data.yearly_data.current_year|json_encode|raw|default='[0,0,0,0,0,0,0,0,0,0,0,0]'}
    };
    
    var lastYearData = {
      months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      values: {$data.yearly_data.last_year|json_encode|raw|default='[0,0,0,0,0,0,0,0,0,0,0,0]'}
    };
    
    // 设置年度流水图表配置
    function setYearlyChartOption(data) {
      var option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: 'rgba(59, 130, 246, 0.2)',
          borderWidth: 1,
          textStyle: {
            color: '#374151'
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(59, 130, 246, 0.05)'
            }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.months,
          axisLine: {
            lineStyle: {
              color: '#E5E7EB'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#6B7280',
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#F3F4F6',
              type: 'dashed'
            }
          },
          axisLabel: {
            color: '#6B7280',
            fontSize: 12,
            formatter: '{value} 元'
          }
        },
        series: [
          {
            name: '流水金额',
            type: 'bar',
            barWidth: '50%',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(59, 130, 246, 0.8)'
                }, {
                  offset: 1, color: 'rgba(59, 130, 246, 0.3)'
                }]
              },
              borderRadius: [4, 4, 0, 0]
            },
            data: data.values
          },
          {
            name: '趋势线',
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              width: 3,
              color: '#3B82F6'
            },
            emphasis: {
              focus: 'series'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(59, 130, 246, 0.2)'
                }, {
                  offset: 1, color: 'rgba(59, 130, 246, 0)'
                }]
              }
            },
            z: 2,
            data: data.values
          }
        ]
      };
      yearlyChart.setOption(option);
    }
    
    // 默认显示本年数据
    setYearlyChartOption(currentYearData);
    
    // 切换年份按钮事件
    $('#currentYear').on('click', function(){
      $(this).addClass('active');
      $('#lastYear').removeClass('active');
      setYearlyChartOption(currentYearData);
    });
    
    $('#lastYear').on('click', function(){
      $(this).addClass('active');
      $('#currentYear').removeClass('active');
      setYearlyChartOption(lastYearData);
    });
    
    // 窗口大小变化时重新渲染图表
    window.addEventListener('resize', function() {
      yearlyChart.resize();
    });
    
    // 添加淡入动画延迟
    $('.fade-in').each(function(index) {
      $(this).css('animation-delay', (index * 0.1) + 's');
    });
    
    // // 系统介绍对话框立即显示，并使用CSS渐入动画
    // $('#systemIntroModal').show();
    // // 使用requestAnimationFrame确保DOM更新后添加class
    // requestAnimationFrame(function() {
    //   $('#systemIntroModal').addClass('modal-open');
    // });
    
    // // 关闭按钮点击事件
    // $('#introCloseBtn').on('click', function(){
    //   $('#systemIntroModal').removeClass('modal-open');
    //   setTimeout(function(){
    //     $('#systemIntroModal').hide();
    //   }, 300);
    // });
    
    // // 点击对话框外部区域关闭
    // $('#systemIntroModal').on('click', function(e){
    //   if($(e.target).is('#systemIntroModal')){
    //     $('#systemIntroModal').removeClass('modal-open');
    //     setTimeout(function(){
    //       $('#systemIntroModal').hide();
    //     }, 300);
    //   }
    // });
  });
  </script>
</body>
</html>