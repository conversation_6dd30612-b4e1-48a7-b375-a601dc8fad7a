<footer >
    <!-- 帮助中心和系统公告区域 -->
    <div class="container mx-auto px-6 pt-10 pb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
            <!-- 帮助中心区域 -->
            <div class="bg-white rounded-lg p-6 transition-all duration-300 hover:shadow-lg border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-700 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        帮助中心
                    </h3>
                    <a href="/user/index/notice" class="text-blue-500 hover:text-blue-600 text-sm flex items-center">
                        查看全部
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
                <div class="space-y-3">
                    <!-- 帮助文章列表，由后端动态生成 -->
                    {notempty name="helpNotices"}
                        {volist name="helpNotices" id="help" length="5"}
                        <a href="/user/index/noticeList?id={$help.id}" class="block group">
                            <div class="flex items-start p-3 rounded-md transition-colors duration-200 hover:bg-gray-50">
                                <span class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <h4 class="text-gray-700 group-hover:text-blue-600 font-medium">{$help.not_title}</h4>
                                    <p class="text-gray-500 text-sm mt-1 group-hover:text-gray-700">{$help.not_des|default="了解更多详情，请点击查看"}</p>
                                </div>
                            </div>
                        </a>
                        {/volist}
                    {else/}
                    <div class="text-center py-4 text-gray-500">
                        <p>暂无帮助内容，请管理员添加</p>
                    </div>
                    {/notempty}
                </div>
            </div>

            <!-- 系统公告区域 -->
            <div class="bg-white rounded-lg p-6 transition-all duration-300 hover:shadow-lg border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-700 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                        </svg>
                        系统公告
                    </h3>
                    <a href="/user/index/systemGG" class="text-blue-500 hover:text-blue-600 text-sm flex items-center">
                        查看全部
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
                <div class="space-y-3">
                    <!-- 系统公告列表，由后端动态生成 -->
                    {notempty name="systemNotices"}
                        {volist name="systemNotices" id="notice" length="2"}
                        <a href="/user/index/noticeList?id={$notice.id}" class="block group">
                            <div class="flex items-start p-3 rounded-md transition-colors duration-200 hover:bg-gray-50">
                                <span class="flex-shrink-0 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <h4 class="text-gray-700 group-hover:text-blue-600 font-medium">{$notice.not_title}</h4>
                                    <p class="text-gray-500 text-sm mt-1 group-hover:text-gray-700">{$notice.not_des|default="查看公告详情"}</p>
                                    <span class="text-xs text-gray-400 mt-1">{$notice.time|date="Y-m-d"}</span>
                                </div>
                            </div>
                        </a>
                        {/volist}
                    {else/}
                    <div class="text-center py-4 text-gray-500">
                        <p>暂无系统公告</p>
                    </div>
                    {/notempty}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 版权信息 -->
    <div class="border-t border-gray-200">
        <div class="container mx-auto px-6 py-4">
            <div class="text-center text-sm text-gray-600">
                © 2025 租号猫 - 版权所有 | 技术支持：<a href="https://wpa.qq.com/msgrd?v=3&uin=726599757&site=qq&menu=yes" target="_blank" class="text-blue-500 hover:text-blue-600">墨渊</a> | {$system.sy_beian|default="备案号"}
            </div>
        </div>
    </div>
</footer> 