!(function (e) {
  var n = {}
  function t(a) {
    if (n[a]) return n[a].exports
    var o = (n[a] = { i: a, l: !1, exports: {} })
    return e[a].call(o.exports, o, o.exports, t), (o.l = !0), o.exports
  }
  ;(t.m = e),
    (t.c = n),
    (t.d = function (e, n, a) {
      t.o(e, n) || Object.defineProperty(e, n, { enumerable: !0, get: a })
    }),
    (t.r = function (e) {
      'undefined' != typeof Symbol &&
        Symbol.toStringTag &&
        Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
        Object.defineProperty(e, '__esModule', { value: !0 })
    }),
    (t.t = function (e, n) {
      if ((1 & n && (e = t(e)), 8 & n)) return e
      if (4 & n && 'object' == typeof e && e && e.__esModule) return e
      var a = Object.create(null)
      if (
        (t.r(a),
        Object.defineProperty(a, 'default', { enumerable: !0, value: e }),
        2 & n && 'string' != typeof e)
      )
        for (var o in e)
          t.d(
            a,
            o,
            function (n) {
              return e[n]
            }.bind(null, o)
          )
      return a
    }),
    (t.n = function (e) {
      var n =
        e && e.__esModule
          ? function () {
              return e.default
            }
          : function () {
              return e
            }
      return t.d(n, 'a', n), n
    }),
    (t.o = function (e, n) {
      return Object.prototype.hasOwnProperty.call(e, n)
    }),
    (t.p = '//zuhaowan.zuhaowan.com/shanghu/www3.0/'),
    t((t.s = 'bdLf'))
})({
  bdLf: function (e, n, t) {
    'use strict'
    t.r(n)
    t('u71c'), t('yUJH'), t('pRGS')
  },
  pRGS: function (e, n) {
    $(function () {
      var e = $('.officialService')
      e.on('click', function () {
        var n = e.data('url'),
          t = (window.screen.availHeight - 30 - 810) / 2,
          a = (window.screen.availWidth - 10 - 1020) / 2
        window.open(
          n,
          'IM',
          'toolbar=yes, location=yes, directories=no, status=no, menubar=yes, scrollbars=yes, resizable=no, copyhistory=yes, width=1020, height=810,top=' +
            t +
            ',left=' +
            a
        )
      })
    })
  },
  u71c: function (e, n) {
    $(function () {
      var e = $('.games p a'),
        n = ($('.games p span'), $('.games ul')),
        t = $('.areas ul'),
        a = $('.game_server ul'),
        o = $('.game_choix .games'),
        i = $('.game_choix .areas'),
        r = $('.game_choix .game_server'),
        c = $('.game_game'),
        l = $('.area_game'),
        s = $('.server_game'),
        u = $("input[name='gameId']"),
        d = $("input[name='gameName']"),
        f = $("input[name='zoneId']"),
        h = $("input[name='zoneName']"),
        v = $("input[name='serverId']"),
        m = $("input[name='serverName']"),
        g = function (e) {
          e.stop(!0, !1).slideDown(200)
        },
        p = function (e) {
          e.fadeOut(200)
        },
        w = function (e) {
          e.is(':hidden') ? g(e) : p(e)
        },
        y = function (e) {
          p(o), p(i), p(r)
        },
        x = function () {
          var e =
              arguments.length > 0 && void 0 !== arguments[0]
                ? arguments[0]
                : '',
            n =
              arguments.length > 1 && void 0 !== arguments[1]
                ? arguments[1]
                : '澶у尯'
          f.val(e), h.val('澶у尯' !== n ? n : ''), l.text(n)
        },
        b = function () {
          var e =
              arguments.length > 0 && void 0 !== arguments[0]
                ? arguments[0]
                : '',
            n =
              arguments.length > 1 && void 0 !== arguments[1]
                ? arguments[1]
                : '鏈嶅姟鍣�'
          v.val(e), m.val('鏈嶅姟鍣�' !== n ? n : ''), s.text(n)
        },
        _ = {
          api: {
            getGame:
              1 == $('input[name="free"]').val()
                ? '/Common/getGame?free=1'
                : '/Common/getGame',
            getArea: '/Common/getGameZone',
            getServer: '/Common/getGameServer'
          },
          showGameList: function (e) {
            var n = $('.games ul li')
            if (null != n && n.length > 0)
              for (var t = 0; t < n.length; t++) {
                var a = $(n[t])
                'Hot' == e
                  ? a['hot' == a.attr('hot') ? 'show' : 'hide']()
                  : a[a.attr('py') == e ? 'show' : 'hide']()
              }
          }
        }
      e.on('click', function (t) {
        var a = $(this)
        a.addClass('cur').siblings().removeClass('cur'),
          t.stopPropagation(),
          e.removeClass('word_nav-slt'),
          a.addClass('word_nav-slt'),
          0 === a.index()
            ? (n.find('li').hide(), n.find('li[hot="hot"]').show())
            : _.showGameList(a.text())
      })
      var k = { data: void 0, inited: !1 },
        S = function (t) {
          y(),
            k.inited && void 0 !== k.data
              ? w(o)
              : ((k.inited = !0),
                zhw.get(_.api.getGame).then(function (t) {
                  var a, i
                  ;(k.data = t = $.parseJSON(t)),
                    (a = k.data),
                    (i = ''),
                    a && a.length
                      ? a.forEach(function (e) {
                          i +=
                            '<li class="chooseGame"data-id="' +
                            e.id +
                            '"py="' +
                            e.pinyincode +
                            '"' +
                            ('1' == e.ishotgame ? ' hot="hot" ' : '') +
                            '>' +
                            e.title +
                            '</li>'
                        })
                      : (i = '<li>鏈幏鍙栧埌浠讳綍鏁版嵁</li>'),
                    n.html(i),
                    e.eq(0).trigger('click'),
                    g(o)
                }))
        }
      c.on('click', S)
        .parents('.game_choix')
        .find('.games.search-choose')
        .on('click', 'ul .chooseGame', function () {
          var e = $(this),
            n = e.data('id'),
            t = e.text()
          if (n === u.val()) return y(), void z()
          !(function () {
            var e =
                arguments.length > 0 && void 0 !== arguments[0]
                  ? arguments[0]
                  : '',
              n =
                arguments.length > 1 && void 0 !== arguments[1]
                  ? arguments[1]
                  : '娓告垙'
            u.val(e), d.val('娓告垙' !== n ? n : ''), c.text(n)
          })(n, t),
            x(),
            b(),
            z()
        })
      var G = { data: {} },
        O = function (e) {
          var n = ''
          e && e.length
            ? e.forEach(function (e) {
                n +=
                  '<li class="chooseZone"data-id="' +
                  e.id +
                  '">' +
                  e.servername +
                  '</li>'
              })
            : (n = '<li>鏈幏鍙栧埌浠讳綍鏁版嵁</li>'),
            t.html(n)
        },
        z = function (e) {
          y()
          var n = u.val()
          if (n)
            return G.data[n]
              ? (O(G.data[n]), void w(i))
              : void zhw.get(_.api.getArea, { gameId: n }).then(function (e) {
                  O((G.data[n] = $.parseJSON(e))), g(i)
                })
          S()
        }
      l.on('click', z)
        .parents('.game_choix')
        .find('.areas.search-choose')
        .on('click', 'ul .chooseZone', function () {
          var e = $(this),
            n = e.attr('data-id'),
            t = e.text()
          if (n === f.val()) return y(), void P()
          x(n, t), b(), P()
        })
      var I = { data: {}, lock: !1 },
        C = function (e) {
          var n = ''
          e && e.length
            ? e.forEach(function (e) {
                n +=
                  '<li class="chooseServer"data-id="' +
                  e.id +
                  '">' +
                  e.servername +
                  '</li>'
              })
            : (n = '<li>鏈幏鍙栧埌浠讳綍鏁版嵁</li>'),
            a.html(n)
        },
        P = function (e) {
          y()
          var n = u.val(),
            t = f.val(),
            a = ''.concat(n).concat(t)
          if (n)
            if (t) {
              if (!I.lock) {
                if (I.data[a]) return C(I.data[a]), void w(r)
                ;(I.lock = !0),
                  zhw
                    .get(_.api.getServer, { gameId: n, zoneId: t })
                    .then(function (e) {
                      C((I.data[a] = $.parseJSON(e))), g(r), (I.lock = !1)
                    })
                    .catch(function () {
                      I.lock = !1
                    })
              }
            } else z()
          else S()
        }
      s
        .on('click', P)
        .parents('.game_choix')
        .find('.game_server.search-choose')
        .on('click', 'ul .chooseServer', function () {
          var e = $(this),
            n = e.attr('data-id'),
            t = e.text()
          b(n, t), p(r)
        }),
        $('#search').on('click', function () {
          y()
          var e = $('input[name="free"]'),
            n = $("form[name='search']")
          1 == e.val() && n.append(e),
            $('input.dzZbPostInput').each(function (e, t) {
              0 == n.find('input[name='.concat(t.name, ']')).length &&
                n.append(t)
            }),
            n.submit()
        }),
        $('.game_choix').on('click', function (e) {
          e.stopPropagation()
        }),
        $(document).on('click', y)
    })
  },
  yUJH: function (e, n) {
    $(function () {
      $('#nav li').on('click', function () {
        var e = $(this).data('tag') || 'index',
          n = $(this).index()
        sessionStorage.setItem('navname', e),
          sessionStorage.setItem('navindex', n)
      })
    })
  }
})
