/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */ ;
layui.define(["table", "form"], function(t) {
	var e = layui.$,
        $ = layui.$,
		i = layui.table,
		n = layui.form;
	i.render({
			elem: "#LAY-app-content-list2",
			url: "/admin/base/couponsList",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 60,
						title: "ID",
						sort: !0,
						align: "center"
					},
					{
						field: "coupon_code",
						minWidth: 175,
						title: "优惠卷码",
						align: "center"
					},
					{
						field: "coupon_type",
						minWidth: 100,
						title: "优惠方式",
						align: "center"
					},
					{
						field: "coupon_value",
						title: "优惠金额",
						minWidth: 80,
						align: "center"
					},
					{
						field: "coupons_date",
						title: "有效期(天)",
						minWidth: 80,
						align: "center"
					},
					{
						field: "coupons_typeuser",
						title: "优惠用户类型",
						minWidth: 80,
						align: "center"
					},
					{
						field: "coupons_use",
						title: "是否使用",
						minWidth: 80,
						align: "center",
						toolbar: "#coupons_use"
					},
					{
						field: "co_receive",
						title: "是否被领取",
						minWidth: 80,
						align: "center",
						toolbar: "#co_receive"
					},
					{
						field: "coupons_time",
						title: "多少天可用",
						minWidth: 80,
						align: "center",
					},
					{
						field: "time",
						title: "添加时间",
						minWidth: 80,
						align: "center"
					},
					{
						title: "操作",
						width: 150,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-com"
					}
				]
			],
			page: !0,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list2)",
			function(t) {
				t.data;
				"del" === t.event ? layer.confirm("确定删除此优惠卷吗？",
					function(e) {
					    console.log(t.data)
						t.del(), layer.close(e)
						
						layui.$.ajax({
                                    url: '/admin/base/delCoupons',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "编辑优惠卷",
					content: "/admin/index/upCoupons?id="+t.data.id,
					area: ["600px", "450px"],
					btn: ["确定", "取消"],
					yes: function(t, e) {
						var n = window["layui-layer-iframe" + t],
							l = "layuiadmin-app-form-submit",
							a = e.find("iframe")
							.contents()
							.find("#" + l);
						n.layui.form.on("submit(" + l + ")",
							function(e) {
								    $.ajax({
                                        url: '/admin/base/upCoupons',
                                        method: 'post',
                                        data: e.field,
                                        success: function(res){
                                        }
                                    });
								e.field;
								layer.msg('保存成功');
								i.reload("LAY-app-content-list2"),
									layer.close(t)
							}), a.trigger("click")
					},
					success: function(t, e) {
                            
					}
				})

			}
			),
		t("coupons", {})
});