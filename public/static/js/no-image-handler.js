/**
 * 优美的无图片处理器
 * 功能：当图片资源不可用时显示优雅的占位内容
 * 用法：自动检测图片加载失败并替换为美观的占位符
 * 创建时间：2025-06-23
 */

class NoImageHandler {
  /**
   * 构造函数
   * @param {Object} options 配置选项
   */
  constructor(options = {}) {
    this.options = {
      // 是否启用调试模式
      debug: options.debug || false,
      // 重试次数
      retryCount: options.retryCount || 2,
      // 重试延迟（毫秒）
      retryDelay: options.retryDelay || 1000,
      // 是否启用交互效果
      interactive: options.interactive || true,
      // 自定义消息
      messages: {
        game: '暂无游戏图片',
        banner: '轮播图暂时无法显示',
        column: '专栏图片加载中',
        generic: '图片暂时无法显示',
        ...options.messages
      },
      ...options
    };

    this.retryMap = new Map();
    this.init();
  }

  /**
   * 初始化处理器
   */
  init() {
    this.setupGlobalErrorHandler();
    this.processExistingImages();
    this.log('NoImageHandler initialized');
  }

  /**
   * 设置全局图片错误处理
   */
  setupGlobalErrorHandler() {
    // 使用事件委托处理所有图片错误
    document.addEventListener('error', (event) => {
      if (event.target.tagName === 'IMG') {
        this.handleImageError(event.target);
      }
    }, true);
  }

  /**
   * 处理现有图片
   */
  processExistingImages() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (!img.complete || img.naturalWidth === 0) {
        // 图片还未加载完成或加载失败
        img.addEventListener('error', () => this.handleImageError(img));
      }
    });
  }

  /**
   * 处理图片加载错误
   * @param {HTMLImageElement} img 图片元素
   */
  handleImageError(img) {
    const src = img.src || img.dataset.src;
    const retryKey = src + img.alt;
    
    // 检查是否需要重试
    if (this.shouldRetry(retryKey)) {
      this.retryImage(img, retryKey);
      return;
    }

    this.log(`Image failed to load: ${src}`);
    this.replaceWithPlaceholder(img);
  }

  /**
   * 检查是否应该重试
   * @param {string} retryKey 重试键
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(retryKey) {
    const retryCount = this.retryMap.get(retryKey) || 0;
    return retryCount < this.options.retryCount;
  }

  /**
   * 重试加载图片
   * @param {HTMLImageElement} img 图片元素
   * @param {string} retryKey 重试键
   */
  retryImage(img, retryKey) {
    const retryCount = (this.retryMap.get(retryKey) || 0) + 1;
    this.retryMap.set(retryKey, retryCount);

    this.log(`Retrying image load (${retryCount}/${this.options.retryCount}): ${img.src}`);

    setTimeout(() => {
      const originalSrc = img.src || img.dataset.src;
      img.src = originalSrc + (originalSrc.includes('?') ? '&' : '?') + 'retry=' + retryCount;
    }, this.options.retryDelay);
  }

  /**
   * 替换为占位符
   * @param {HTMLImageElement} img 图片元素
   */
  replaceWithPlaceholder(img) {
    const container = img.parentElement;
    const imageType = this.detectImageType(img);
    const placeholder = this.createPlaceholder(imageType, img.alt || '');

    // 保持原有的尺寸和样式
    placeholder.className = img.className;
    placeholder.style.width = img.style.width || '100%';
    placeholder.style.height = img.style.height || '100%';

    // 添加交互效果
    if (this.options.interactive) {
      placeholder.classList.add('interactive');
      this.addInteractiveEvents(placeholder, img);
    }

    // 替换图片
    container.replaceChild(placeholder, img);

    // 触发自定义事件
    this.dispatchEvent('imageReplaced', { 
      originalImg: img, 
      placeholder: placeholder, 
      imageType: imageType 
    });
  }

  /**
   * 检测图片类型
   * @param {HTMLImageElement} img 图片元素
   * @returns {string} 图片类型
   */
  detectImageType(img) {
    if (img.classList.contains('game-image') || 
        img.closest('.game-card')) {
      return 'game';
    }
    
    if (img.classList.contains('banner-image') || 
        img.closest('.swiper-slide') || 
        img.closest('[class*="banner"]')) {
      return 'banner';
    }
    
    if (img.classList.contains('column-image') || 
        img.closest('.column-item') || 
        img.closest('[class*="column"]')) {
      return 'column';
    }
    
    return 'generic';
  }

  /**
   * 创建占位符元素
   * @param {string} type 图片类型
   * @param {string} alt 替代文本
   * @returns {HTMLElement} 占位符元素
   */
  createPlaceholder(type, alt) {
    const placeholder = document.createElement('div');
    placeholder.className = `no-image-placeholder ${type}-no-image`;
    
    const content = document.createElement('div');
    content.className = 'content';
    
    const config = this.getPlaceholderConfig(type, alt);
    
    content.innerHTML = `
      <div class="icon">
        <i class="${config.icon}"></i>
      </div>
      <div class="title">${config.title}</div>
      <div class="subtitle">${config.subtitle}</div>
    `;
    
    placeholder.appendChild(content);
    return placeholder;
  }

  /**
   * 获取占位符配置
   * @param {string} type 图片类型
   * @param {string} alt 替代文本
   * @returns {Object} 配置对象
   */
  getPlaceholderConfig(type, alt) {
    const configs = {
      game: {
        icon: 'fas fa-gamepad',
        title: alt || '游戏图片',
        subtitle: this.options.messages.game
      },
      banner: {
        icon: 'fas fa-image',
        title: 'Steam账号租借',
        subtitle: this.options.messages.banner
      },
      column: {
        icon: 'fas fa-newspaper',
        title: alt || '专栏内容',
        subtitle: this.options.messages.column
      },
      generic: {
        icon: 'fas fa-photo',
        title: alt || '图片内容',
        subtitle: this.options.messages.generic
      }
    };

    return configs[type] || configs.generic;
  }

  /**
   * 添加交互事件
   * @param {HTMLElement} placeholder 占位符元素
   * @param {HTMLImageElement} originalImg 原始图片元素
   */
  addInteractiveEvents(placeholder, originalImg) {
    placeholder.addEventListener('click', () => {
      this.handlePlaceholderClick(placeholder, originalImg);
    });

    placeholder.addEventListener('mouseenter', () => {
      placeholder.style.transform = 'translateY(-4px)';
    });

    placeholder.addEventListener('mouseleave', () => {
      placeholder.style.transform = 'translateY(-2px)';
    });
  }

  /**
   * 处理占位符点击
   * @param {HTMLElement} placeholder 占位符元素
   * @param {HTMLImageElement} originalImg 原始图片元素
   */
  handlePlaceholderClick(placeholder, originalImg) {
    // 显示加载状态
    placeholder.classList.add('loading');
    
    // 尝试重新加载图片
    const newImg = document.createElement('img');
    newImg.className = originalImg.className;
    newImg.alt = originalImg.alt;
    
    newImg.onload = () => {
      placeholder.parentElement.replaceChild(newImg, placeholder);
      this.dispatchEvent('imageReloaded', { img: newImg });
    };
    
    newImg.onerror = () => {
      placeholder.classList.remove('loading');
      this.showRetryMessage(placeholder);
    };
    
    // 添加时间戳避免缓存
    const originalSrc = originalImg.src || originalImg.dataset.src;
    newImg.src = originalSrc + (originalSrc.includes('?') ? '&' : '?') + 't=' + Date.now();
  }

  /**
   * 显示重试消息
   * @param {HTMLElement} placeholder 占位符元素
   */
  showRetryMessage(placeholder) {
    const subtitle = placeholder.querySelector('.subtitle');
    const originalText = subtitle.textContent;
    
    subtitle.textContent = '重试失败，请稍后再试';
    subtitle.style.color = '#ef4444';
    
    setTimeout(() => {
      subtitle.textContent = originalText;
      subtitle.style.color = '';
    }, 3000);
  }

  /**
   * 手动处理指定图片
   * @param {HTMLImageElement|string} target 图片元素或选择器
   */
  handleImage(target) {
    const img = typeof target === 'string' ? document.querySelector(target) : target;
    if (img && img.tagName === 'IMG') {
      this.handleImageError(img);
    }
  }

  /**
   * 批量处理图片
   * @param {string} selector 选择器
   */
  handleImages(selector) {
    const images = document.querySelectorAll(selector);
    images.forEach(img => this.handleImage(img));
  }

  /**
   * 触发自定义事件
   * @param {string} eventName 事件名称
   * @param {Object} detail 事件详情
   */
  dispatchEvent(eventName, detail) {
    const event = new CustomEvent(`noImage${eventName.charAt(0).toUpperCase() + eventName.slice(1)}`, {
      detail: detail
    });
    document.dispatchEvent(event);
  }

  /**
   * 销毁处理器
   */
  destroy() {
    this.retryMap.clear();
    this.log('NoImageHandler destroyed');
  }

  /**
   * 调试日志
   * @param {string} message 日志信息
   */
  log(message) {
    if (this.options.debug) {
      console.log(`[NoImageHandler] ${message}`);
    }
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  // 创建全局无图片处理器实例
  window.noImageHandler = new NoImageHandler({
    debug: false, // 生产环境设为false
    retryCount: 2,
    retryDelay: 1000,
    interactive: true
  });
  
  console.log('无图片处理器已启动');
});

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NoImageHandler;
}
