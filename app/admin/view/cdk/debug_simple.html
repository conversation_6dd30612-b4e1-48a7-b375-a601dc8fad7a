<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>卡密调试工具</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .form-card {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .result-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
        }
        .info-table td {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: top;
        }
        .info-table td:first-child {
            font-weight: bold;
            width: 150px;
            color: #6c757d;
        }
        .status-used {
            color: #dc3545;
            font-weight: bold;
        }
        .status-unused {
            color: #28a745;
            font-weight: bold;
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
        .success-text {
            color: #28a745;
        }
        .error-text {
            color: #dc3545;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-card">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">🔧 卡密调试工具</h2>
            
            <form id="debugForm">
                <div class="form-group">
                    <label for="cdkCode">卡密码</label>
                    <input type="text" id="cdkCode" name="cdk_code" placeholder="请输入要调试的卡密码" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn">🔍 调试分析</button>
                    <button type="reset" class="btn btn-secondary">🔄 重置</button>
                </div>
            </form>
        </div>
        
        <div id="resultBox" class="result-box">
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        console.log('页面加载完成');
        
        document.getElementById('debugForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            var cdkCode = document.getElementById('cdkCode').value.trim();
            console.log('提交表单，卡密码:', cdkCode);
            
            if (!cdkCode) {
                alert('请输入卡密码');
                return;
            }
            
            var resultBox = document.getElementById('resultBox');
            var resultContent = document.getElementById('resultContent');
            
            // 显示加载状态
            resultBox.style.display = 'block';
            resultContent.innerHTML = '<div class="loading">🔄 正在分析卡密...</div>';
            
            console.log('发送AJAX请求');
            
            // 发送AJAX请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin/CdkController/debug', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    console.log('AJAX响应状态:', xhr.status);
                    console.log('AJAX响应内容:', xhr.responseText);
                    
                    if (xhr.status === 200) {
                        try {
                            var res = JSON.parse(xhr.responseText);
                            console.log('解析后的响应:', res);
                            
                            if (res.code === 1) {
                                // 调试成功
                                var html = '<h3>🔍 卡密调试结果</h3>';
                                
                                // 卡密基本信息
                                html += '<div class="info-card">';
                                html += '<h4>📋 卡密基本信息</h4>';
                                html += '<table class="info-table">';
                                html += '<tr><td>卡密码:</td><td>' + res.data.cdk_card.cdk_code + '</td></tr>';
                                html += '<tr><td>卡密类型:</td><td>' + res.data.cdk_card.cdk_type + '</td></tr>';
                                html += '<tr><td>状态:</td><td><span class="' + (res.data.cdk_card.status === 'used' ? 'status-used' : 'status-unused') + '">' + (res.data.cdk_card.status === 'used' ? '已使用' : '未使用') + '</span></td></tr>';
                                html += '<tr><td>Shop字段:</td><td>' + (res.data.cdk_card.shop || '无') + '</td></tr>';
                                html += '<tr><td>成本:</td><td>¥' + (res.data.cdk_card.cost || '0') + '</td></tr>';
                                html += '<tr><td>创建时间:</td><td>' + (res.data.cdk_card.created_at || '未知') + '</td></tr>';
                                
                                if (res.data.cdk_card.assigned_user) {
                                    html += '<tr><td>使用用户ID:</td><td>' + res.data.cdk_card.assigned_user + '</td></tr>';
                                }
                                
                                if (res.data.cdk_card.expiry_date) {
                                    html += '<tr><td>到期时间:</td><td>' + res.data.cdk_card.expiry_date + '</td></tr>';
                                }
                                
                                if (res.data.cdk_card.expiry_date2) {
                                    html += '<tr><td>有效期参数:</td><td>' + res.data.cdk_card.expiry_date2 + '</td></tr>';
                                }
                                
                                html += '</table>';
                                html += '</div>';
                                
                                // 关联商品信息
                                if (res.data.goods) {
                                    html += '<div class="info-card">';
                                    html += '<h4>🎮 关联商品信息</h4>';
                                    html += '<table class="info-table">';
                                    html += '<tr><td>商品ID:</td><td>' + res.data.goods.id + '</td></tr>';
                                    html += '<tr><td>商品名称:</td><td>' + res.data.goods.goods_name + '</td></tr>';
                                    html += '<tr><td>商品价格:</td><td>¥' + (res.data.goods.goods_price || '0') + '</td></tr>';
                                    html += '</table>';
                                    html += '</div>';
                                } else {
                                    html += '<div class="error-box">';
                                    html += '<strong>⚠️ 警告：</strong> 未找到关联的商品信息！Shop字段值: ' + (res.data.cdk_card.shop || '无');
                                    html += '</div>';
                                }
                                
                                // 代理信息
                                if (res.data.agent) {
                                    html += '<div class="info-card">';
                                    html += '<h4>👤 代理信息</h4>';
                                    html += '<table class="info-table">';
                                    html += '<tr><td>代理ID:</td><td>' + res.data.agent.id + '</td></tr>';
                                    html += '<tr><td>代理用户名:</td><td>' + res.data.agent.username + '</td></tr>';
                                    html += '<tr><td>代理状态:</td><td>' + (res.data.agent.status == 1 ? '正常' : '禁用') + '</td></tr>';
                                    html += '</table>';
                                    html += '</div>';
                                }
                                
                                // 一致性检查
                                html += '<div class="info-card">';
                                html += '<h4>🔍 一致性检查</h4>';
                                
                                if (res.data.cdk_card.cdk_type === 'permanent') {
                                    if (res.data.goods) {
                                        html += '<p class="success-text">✅ 永久版卡密关联商品正常</p>';
                                    } else {
                                        html += '<p class="error-text">❌ 永久版卡密未找到关联商品</p>';
                                    }
                                } else if (res.data.cdk_card.cdk_type.indexOf('member_') === 0) {
                                    if (res.data.membership_info) {
                                        html += '<p class="success-text">✅ 会员卡密关联会员类型正常</p>';
                                    } else {
                                        html += '<p class="error-text">❌ 会员卡密未找到关联会员类型</p>';
                                    }
                                } else if (res.data.cdk_card.cdk_type === 'offline') {
                                    if (res.data.goods && res.data.offline_info) {
                                        html += '<p class="success-text">✅ 离线卡密关联商品和离线价格正常</p>';
                                    } else {
                                        html += '<p class="error-text">❌ 离线卡密关联信息不完整</p>';
                                    }
                                }
                                
                                html += '</div>';
                                
                                resultContent.innerHTML = html;
                            } else {
                                // 调试失败
                                resultContent.innerHTML = '<div class="error-box"><h3>❌ ' + res.msg + '</h3></div>';
                            }
                        } catch (e) {
                            console.error('JSON解析错误:', e);
                            resultContent.innerHTML = '<div class="error-box"><h3>❌ 响应解析失败</h3><p>原始响应: ' + xhr.responseText + '</p></div>';
                        }
                    } else {
                        console.error('HTTP错误:', xhr.status);
                        resultContent.innerHTML = '<div class="error-box"><h3>❌ 请求失败</h3><p>HTTP状态: ' + xhr.status + '</p><p>响应: ' + xhr.responseText + '</p></div>';
                    }
                }
            };
            
            xhr.send('cdk_code=' + encodeURIComponent(cdkCode));
        });
    </script>
</body>
</html>
