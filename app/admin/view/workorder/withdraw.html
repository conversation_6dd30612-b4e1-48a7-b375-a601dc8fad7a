

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layuiAdmin 工单系统</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-fluid">  
    <div class="layui-card">
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
        <div class="layui-form-item">
          <div class="layui-inline">
            <label class="layui-form-label">工单号</label>
            <div class="layui-input-block">
              <input type="text" name="id" placeholder="工单号" autocomplete="off" class="layui-input">
            </div>
          </div>
        <div class="layui-inline">
            <label class="layui-form-label">提现状态</label>
            <div class="layui-input-inline">
              <select name="wi_state">
                <option value="">请选择标签</option>
                    <option value="0">未转账</option>
                    <option value="1">已转账	</option>
              </select>
            </div>
          </div>
          <div class="layui-inline">
            <button class="layui-btn layuiadmin-btn-order" lay-submit lay-filter="LAY-app-order-search">
              <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
            </button>
          </div>
        </div>
      </div>
      <div class="layui-card-body">
        <table id="LAY-app-system-order" lay-filter="LAY-app-system-order"></table> 
        <script type="text/html" id="progressTpl">
          <div class="layui-progress layuiadmin-order-progress" lay-filter="progress-"+ {{ d.orderid }} +"">
            <div class="layui-progress-bar layui-bg-blue" lay-percent= {{ d.progress }}></div>
          </div>
        </script>
        <script type="text/html" id="buttonTpl">
          {{#  if(d.work_state == '已处理'){ }}
            <button class="layui-btn layui-btn-normal layui-btn-xs">已处理</button>
          {{#  } else if(d.work_state == '未分配'){ }}
            <button class="layui-btn layui-btn-primary layui-btn-xs">未分配</button>
          {{#  } else{ }}
            <button class="layui-btn layui-btn-warm layui-btn-xs">待处理</button>
          {{#  } }}
        </script>
        <script type="text/html" id="table-system-order">
          <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
        </script>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'withdraw', 'table'], function(){
    var $ = layui.$
    ,form = layui.form
    ,table = layui.table;
    
    //监听搜索
    form.on('submit(LAY-app-order-search)', function(data){
      var field = data.field;
      field.search=true
      //执行重载
      table.reload('LAY-app-system-order', {
        where: field
      });
    });
  });
  </script>
</body>
</html>