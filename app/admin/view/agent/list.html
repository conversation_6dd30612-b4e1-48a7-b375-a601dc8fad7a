<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>代理列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">代理列表</div>
            <div class="layui-card-body">
                <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-admin" data-type="add">添加</button>
                        </div>
                    </div>
                </div>

                <table id="LAY-agent-list" lay-filter="LAY-agent-list"></table>

                <script type="text/html" id="table-agent-toolbar">
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
                </script>

                <script type="text/html" id="statusTpl">
                    <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="status" {{ d.status == 1 ? 'checked' : '' }}>
                </script>
            </div>
        </div>
    </div>

    <script src="/layuiadmin/layui/layui.js"></script>
    <script>
    layui.config({
        base: '/layuiadmin/'
    }).extend({
        index: 'lib/index'
    }).use(['index', 'table', 'form'], function(){
        var $ = layui.$
        ,form = layui.form
        ,table = layui.table;

        table.render({
            elem: '#LAY-agent-list'
            ,url: '/admin/index/getAgentList'
            ,cols: [[
                {type: 'numbers', fixed: 'left'}
                ,{field: 'username', title: '代理账号', minWidth: 100}
                ,{field: 'balance', title: '余额', width: 100}
                ,{field: 'discount_rate', title: '折扣比例(%)', width: 100}
                ,{field: 'status', title: '状态', width: 100, templet: '#statusTpl'}
                ,{field: 'created_at', title: '创建时间', width: 180}
                ,{field: 'updated_at', title: '更新时间', width: 180}
                ,{title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-agent-toolbar'}
            ]]
            ,page: true
            ,limit: 15
            ,text: {
                none: '暂无数据'
            }
        });

        //事件
        var active = {
            add: function(){
                layer.open({
                    type: 2
                    ,title: '添加代理'
                    ,content: '/admin/index/agentForm'
                    ,area: ['500px', '480px']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                        ,submitID = 'LAY-agent-submit'
                        ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;

                            //提交数据
                            $.ajax({
                                url: '/admin/index/saveAgent'
                                ,type: 'POST'
                                ,data: field
                                ,success: function(res){
                                    if(res.code == 0){
                                        layer.close(index);
                                        layer.msg(res.msg, {icon: 1});
                                        table.reload('LAY-agent-list');
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            }
        };

        $('.layui-btn.layuiadmin-btn-admin').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        //监听工具条
        table.on('tool(LAY-agent-list)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('确定删除此代理？', function(index){
                    $.ajax({
                        url: '/admin/index/deleteAgent'
                        ,type: 'POST'
                        ,data: {id: data.id}
                        ,success: function(res){
                            if(res.code == 0){
                                obj.del();
                                layer.close(index);
                                layer.msg(res.msg, {icon: 1});
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                        ,error: function(){
                            layer.msg('删除失败', {icon: 2});
                        }
                    });
                });
            } else if(obj.event === 'edit'){
                layer.open({
                    type: 2
                    ,title: '编辑代理'
                    ,content: '/admin/index/agentForm?id=' + data.id
                    ,area: ['500px', '480px']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                        ,submitID = 'LAY-agent-submit'
                        ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            field.id = obj.data.id;

                            $.ajax({
                                url: '/admin/index/saveAgent'
                                ,type: 'POST'
                                ,data: field
                                ,success: function(res){
                                    if(res.code == 0){
                                        layer.close(index);
                                        layer.msg(res.msg, {icon: 1});
                                        table.reload('LAY-agent-list');
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            }
        });

        //监听状态切换
        form.on('switch(status)', function(obj){
            var id = this.value;
            var status = obj.elem.checked ? 1 : 0;

            $.ajax({
                url: '/admin/index/changeAgentStatus'
                ,type: 'POST'
                ,data: {id: id, status: status}
                ,success: function(res){
                    if(res.code != 0){
                        layer.msg(res.msg, {icon: 2});
                        $(obj.elem).prop('checked', !obj.elem.checked);
                        form.render('checkbox');
                    }
                }
            });
        });
    });
    </script>
</body>
</html>