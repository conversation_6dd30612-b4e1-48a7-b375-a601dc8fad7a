<?php
namespace app\home\controller;

use app\home\model\Goods;
use app\admin\model\Goodsclass;
use app\admin\model\User as Us;
use app\BaseController;
use think\facade\View;
use app\admin\model\System;
use app\admin\model\Config;
use think\facade\Request;
use think\facade\Session;
use think\facade\Cookie;
use think\facade\Db;
use app\admin\model\Banner;

/**
 * Class Index
 * 首页控制器，提供首页展示与商品搜索功能。
 *
 * @package app\home\controller
 */
class Index extends BaseController
{
    /**
     * 初始化方法
     * 用于设置Session。
     *
     * @return void
     */
    public function initialize()
    {
        // 设置用户邀请 ID 到 Session
        Session::set("uid", Request::param("uid"));
    }

    /**
     * 首页显示方法
     * 提供首页所需的商品数据、用户信息及其他系统配置。
     *
     * @return \think\response\View 渲染首页视图
     */
    public function index()
    {
        // 接收分类 ID 参数
        $id = Request::param("id");

        // 获取所有已显示的商品数据
        $goodsAll = Goods::where("goods_show", 1)->select();

        // 根据分类 ID 筛选商品
        if ($id) {
            $goodsAll = Goods::where("goods_show", 1)->where("goods_class", $id)->select();

            // 若分类 ID 为 "goods_classList"，则忽略筛选
            if ($id == "goods_classList") {
                $goodsAll = Goods::where("goods_show", 1)->select();
            }
            // 返回筛选后的商品数据
            return $goodsAll;
        }

        // 获取当前登录用户信息
        $email = Cookie::get("username");
        $userId = Cookie::get("id");
        $loginStatus = Cookie::get("login");

        // 更严格的登录状态检查
        $uid = null;
        if (!empty($email) && !empty($userId) && $loginStatus == '1') {
            $uid = Us::where(["us_username" => $email, "id" => $userId])->find();

            // 如果数据库中找不到用户，清除Cookie
            if (!$uid) {
                Cookie::delete('id');
                Cookie::delete('username');
                Cookie::delete('login');
            }
        }

        // 判断用户是否登录并准备论坛跳转所需信息
        $forumLoginInfo = [
            'username' => '',
            'password' => '',
            'isLoggedIn' => false
        ];

        if ($uid) {
            $forumLoginInfo = [
                'username' => $uid['us_username'],
                'password' => $uid['us_password'], // 注意：这里假设密码字段为 us_pwd
                'isLoggedIn' => true
            ];
        }

        // 构造前7个商品的展示数据（循环补全不足7个）
        $goodsCount = count($goodsAll);
        $goodsDisplay = [];
        for ($i = 0; $i < 7; $i++) {
            $goodsDisplay[] = $goodsAll[$i % $goodsCount];
        }

        // 获取论坛板块数据 - 用于精彩专栏
        $forumSections = [];
        try {
            $forumApiUrl = 'https://xiuluo.zuhaom.com/?ajax=1';
            $response = file_get_contents($forumApiUrl);
            $forumData = json_decode($response, true);

            if (isset($forumData['code']) && $forumData['code'] === 0 && isset($forumData['message']['forum'])) {
                $sections = $forumData['message']['forum'];

                foreach ($sections as $section) {
                    $forumSections[] = [
                        'fid' => $section['fid'],
                        'icon_url' => $section['icon_url'],
                        'name' => $section['name']
                    ];
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('获取论坛板块数据失败: ' . $e->getMessage());
        }

        // 获取论坛帖子数据
        $goodsDisplay2 = [];  // 初始化变量

        try {
            $threadApiUrl = 'https://xiuluo.zuhaom.com/?forum-1.htm?ajax=1';
            $response = file_get_contents($threadApiUrl);
            $threadData = json_decode($response, true);

            // 只处理成功响应的数据
            if ($threadData['code'] === '0' && !empty($threadData['message']['threads'])) {
                $threads = $threadData['message']['threads'];

                // 处理每条数据
                foreach ($threads as $thread) {
                    $goodsDisplay2[] = [
                        'id' => $thread['tid'],
                        'title' => $thread['subject'],
                        'content' => $thread['message'],
                        'date' => $thread['create_date_fmt'],
                        'author' => $thread['username'],
                        'avatar' => $thread['user_avatar_url'],
                        'url' => $thread['url'],
                        'img_url' => $thread['img_url'] ?? ''
                    ];

                    // 只取前4条数据
                    if (count($goodsDisplay2) >= 4) {
                        break;
                    }
                }

                // 如果数据不足4条，用已有数据补齐
                $currentCount = count($goodsDisplay2);
                if ($currentCount > 0 && $currentCount < 4) {
                    $i = 0;
                    while (count($goodsDisplay2) < 4) {
                        $goodsDisplay2[] = $goodsDisplay2[$i];
                        $i = ($i + 1) % $currentCount;
                    }
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('获取论坛帖子数据失败: ' . $e->getMessage());
        }

        // 获取帮助中心和系统公告数据
        $helpCenterList = Db::table('tk_notice')
            ->where('not_system', 0)
            ->limit(5)
            ->field('id, not_title')
            ->select();
        $announcementList = Db::table('tk_notice')
            ->where('not_system', 2)
            ->limit(5)
            ->field('id, not_title, not_content, time, time')
            ->select();

        // 获取所有商品的套餐价格和账号状态
        $comboPrices = [];
        $accountStatus = [];
        foreach ($goodsAll as $goods) {
            // 获取套餐价格
            $combo = Db::table('tk_combo')
                ->where('co_goods', $goods['id'])
                ->field('day_price, hour_price')
                ->find();
            $comboPrices[$goods['id']] = $combo;

            // 检查是否有可用的在线和离线账号，并结合商品状态设置
            $hasOnline = Db::table('tk_account')
                ->where('ac_goods', $goods['id'])
                ->where('ac_states', 1)
                ->where('ac_sell', 1)
                ->where('goods_Type', 1)
                ->find();

            $hasOffline = Db::table('tk_account')
                ->where('ac_goods', $goods['id'])
                ->where('goods_Type', 0)
                ->find();

            // 获取商品的在线和离线状态
            $onlineStatus = isset($goods['online_status']) ? (int)$goods['online_status'] : 1;
            $offlineStatus = isset($goods['offline_status']) ? (int)$goods['offline_status'] : 1;

            // 账号状态 - 在线版只判断商品状态，离线版需要同时判断有账号和商品状态
            $accountStatus[$goods['id']] = [
                'online' => $onlineStatus == 1,  // 只判断在线版状态是否允许出售
                'offline' => !empty($hasOffline) && $offlineStatus == 1  // 离线版需要有账号且状态允许销售
            ];
        }

        // 修改论坛帖子URL，添加访问检查
        if (!empty($goodsDisplay2)) {
            foreach ($goodsDisplay2 as &$thread) {
                // 检查URL是否已经是完整URL
                if (strpos($thread['url'], 'https://') === 0 || strpos($thread['url'], 'http://') === 0) {
                    // 如果已经是完整URL，直接使用
                    $thread['target_url'] = $thread['url'];
                } else {
                    // 如果不是完整URL，构建完整的论坛URL
                    $thread['target_url'] = 'https://xiuluo.zuhaom.com/?' . $thread['url'];
                }

                // 修改URL为本地检查URL
                $thread['url'] = url('home/index/checkForumAccess', ['url' => urlencode($thread['target_url'])]);
            }
        }

        // 修改论坛板块URL，添加访问检查
        if (!empty($forumSections)) {
            foreach ($forumSections as &$section) {
                // 构建板块URL
                $sectionUrl = 'https://xiuluo.zuhaom.com/?forum-' . $section['fid'] . '.htm';
                // 添加访问检查
                $section['url'] = url('home/index/checkForumAccess', ['url' => urlencode($sectionUrl)]);
            }
        }

        // 准备数据传递给视图
        $data = [
            "goodsTj" => $goodsAll->where("goods_tj", 1),
            "goods" => $goodsAll,
            "banner" => Banner::select(),
            "goodsDisplay" => $goodsDisplay,
            "forumActivities" => [],  // 论坛活动数据
            "goodsDisplay2" => $goodsDisplay2,      // 轮播图数据
            "system" => System::find(1),
            "config" => Config::find(1),
            "goodsclass" => Goodsclass::select(),
            "user" => $uid,
            "helpCenterList" => $helpCenterList,
            "announcementList" => $announcementList,
            "isLoggedIn" => !empty($uid),
            "comboPrices" => $comboPrices,
            "accountStatus" => $accountStatus,
            "forumSections" => $forumSections,  // 只包含板块ID、图片URL和名称
            "forumLoginInfo" => $forumLoginInfo,
        ];
        //return json($data);
        // 渲染首页视图并返回
        return View::fetch('index/index', $data);
    }

    /**
     * 搜索商品的方法
     * 根据分类或关键字搜索商品并渲染搜索结果页面。
     *
     * @return \think\response\View 渲染搜索页面视图
     */
    public function search()
    {
        // 接收分类 ID 和搜索关键词参数
        $id = Request::param("id");
        $keyWords = Request::param("keyWords");

        // 构建商品查询条件
        $query = Goods::where("goods_show", 1);

        // 如果分类 ID 不为空，则根据分类筛选
        if ($id) {
            if ($id != "goods_classList") {
                $query->where("goods_class", $id);
            }
        }

        // 如果提供了关键词，根据名称模糊搜索
        if ($keyWords) {
            $query->whereLike("goods_name", "%$keyWords%");
        }

        // 获取所有游戏标签
        $gameTags = Db::table('tk_game_tags')->select();

        // 获取筛选后的商品数据
        $goodsAll = $query->select();

        // 获取每个商品关联的标签
        $goodsTags = [];
        foreach ($goodsAll as $goods) {
            // 修复标签查询，使用正确的字段名称
            // 首先检查表结构
            try {
                $tags = Db::table('tk_goods_tags')
                    ->alias('gt')
                    ->join('tk_game_tags t', 'gt.tag_id = t.id', 'LEFT')
                    ->where('gt.goods_id', $goods['id'])
                    ->column('t.name');
            } catch (\Exception $e) {
                // 如果上面的查询失败，尝试使用可能的替代字段名
                try {
                    $tags = Db::table('tk_goods_tags')
                        ->alias('gt')
                        ->join('tk_game_tags t', 'gt.game_tag_id = t.id', 'LEFT')
                        ->where('gt.goods_id', $goods['id'])
                        ->column('t.name');
                } catch (\Exception $e2) {
                    // 如果仍然失败，直接从商品的goods_key字段解析标签
                    $tags = [];
                    if (!empty($goods['goods_key'])) {
                        $tags = explode(',', $goods['goods_key']);
                    }
                }
            }
            $goodsTags[$goods['id']] = $tags;
        }

        // 获取每个商品的账号状态
        $accountStatus = [];
        foreach ($goodsAll as $goods) {
            // 检查是否有可用的离线账号
            $hasOffline = Db::table('tk_account')
                ->where('ac_goods', $goods['id'])
                ->where('goods_Type', 0)
                ->find();

            // 获取商品的在线和离线状态
            $onlineStatus = isset($goods['online_status']) ? (int)$goods['online_status'] : 1;
            $offlineStatus = isset($goods['offline_status']) ? (int)$goods['offline_status'] : 1;

            // 账号状态 - 在线版只判断商品状态，离线版需要同时判断有账号和商品状态
            $accountStatus[$goods['id']] = [
                'online' => $onlineStatus == 1,  // 只判断在线版状态是否允许出售
                'offline' => !empty($hasOffline) && $offlineStatus == 1  // 离线版需要有账号且状态允许销售
            ];
        }

        // 如果没有匹配的商品，跳转到错误页面
        if ($goodsAll->isEmpty()) {
            return View::fetch('index/seracherro');
        }

        // 获取当前登录用户信息
        $email = Cookie::get("username");
        $userId = Cookie::get("id");
        $loginStatus = Cookie::get("login");

        // 更严格的登录状态检查
        $uid = null;
        if (!empty($email) && !empty($userId) && $loginStatus == '1') {
            $uid = Us::where(["us_username" => $email, "id" => $userId])->find();

            // 如果数据库中找不到用户，清除Cookie
            if (!$uid) {
                Cookie::delete('id');
                Cookie::delete('username');
                Cookie::delete('login');
            }
        }

        // 获取帮助中心和系统公告数据
        $helpCenterList = Db::table('tk_notice')
            ->where('not_system', 0)
            ->limit(5)
            ->field('id, not_title')
            ->select();
        $announcementList = Db::table('tk_notice')
            ->where('not_system', 2)
            ->limit(5)
            ->field('id, not_title')
            ->select();

        // 准备数据传递给视图
        $data = [
            "goodsTj" => $goodsAll->where("goods_tj", 1),
            "goods" => $goodsAll,
            "system" => System::find(1),
            "config" => Config::find(1),
            "goodsclass" => Goodsclass::select(),
            "user" => $uid,
            "helpCenterList" => $helpCenterList,
            "announcementList" => $announcementList,
            "gameTags" => $gameTags,
            "goodsTags" => $goodsTags,
            "accountStatus" => $accountStatus,
        ];

        // 渲染搜索结果视图并返回
        return View::fetch('search/index', $data);
    }

    /**
     * 商品详情方法
     * 根据商品 ID 获取商品详情并渲染商品详情页面。
     *
     * @return \think\response\View 渲染商品详情视图
     */
    public function goodsDetail()
    {
        // 接收商品 ID 参数
        $goodsId = Request::param("id");

        // 获取商品详情
        $goodsDetail = Goods::find($goodsId);

        // 如果商品不存在，跳转到错误页面
        if (!$goodsDetail) {
            return View::fetch('index/seracherro');
        }

        // 获取商品的套餐价格
        $combo = Db::table('tk_combo')
            ->where('co_goods', $goodsId)
            ->field('day_price, hour_price')
            ->find();

        // 检查是否有可用的在线和离线账号，并结合商品状态设置
        $hasOnline = Db::table('tk_account')
            ->where('ac_goods', $goodsId)
            ->where('ac_states', 1)
            ->where('ac_sell', 1)
            ->where('goods_Type', 1)
            ->find();

        $hasOffline = Db::table('tk_account')
            ->where('ac_goods', $goodsId)
            ->where('goods_Type', 0)
            ->find();

        // 获取商品的在线和离线状态
        $onlineStatus = isset($goodsDetail['online_status']) ? (int)$goodsDetail['online_status'] : 1;
        $offlineStatus = isset($goodsDetail['offline_status']) ? (int)$goodsDetail['offline_status'] : 1;

        // 账号状态 - 在线版只判断商品状态，离线版需要同时判断有账号和商品状态
        $accountStatus = [
            'online' => $onlineStatus == 1,  // 只判断在线版状态是否允许出售
            'offline' => !empty($hasOffline) && $offlineStatus == 1  // 离线版需要有账号且状态允许销售
        ];

        // 如果存在离线账号，获取离线账号价格
        $offlinePrice = null;
        if (!empty($hasOffline)) {
            $offlinePriceData = Db::table('tk_offline')
                ->where('product_id', $goodsId)
                ->field('product_amount')
                ->find();
            $offlinePrice = $offlinePriceData ? $offlinePriceData['product_amount'] : null;
        }

        // 获取系统公告数据
        $announcementList = Db::table('tk_notice')
            ->where('not_system', 2)
            ->limit(5)
            ->field('id, not_title, not_content, time')
            ->select();

        // 获取会员价格数据
        $membershipPricing = Db::table('tk_membership_pricing')
            ->select();

        // 检查用户是否登录 - 从Cookie中获取登录信息
        $userId = Cookie::get('id');
        $username = Cookie::get('username');
        $loginStatus = Cookie::get('login');

        // 更严格的登录状态检查
        $isLoggedIn = !empty($userId) && !empty($username) && $loginStatus == '1';
        $user = [];

        if ($isLoggedIn) {
            // 从数据库获取用户信息
            $userInfo = Db::table('tk_user')
                ->where('id', $userId)
                ->where('us_username', $username)
                ->find();

            if ($userInfo) {
                $user = $userInfo;
            } else {
                // 如果数据库中找不到用户，清除Cookie并设置为未登录
                $isLoggedIn = false;
                Cookie::delete('id');
                Cookie::delete('username');
                Cookie::delete('login');
            }
        }

        // 准备数据传递给视图
        $data = [
            "goodsDetail" => $goodsDetail,
            "combo" => $combo,
            "accountStatus" => $accountStatus,
            "offlinePrice" => $offlinePrice,
            "system" => System::find(1),
            "config" => Config::find(1),
            "announcementList" => $announcementList,
            "membershipPricing" => $membershipPricing,
            "isLoggedIn" => $isLoggedIn,  // 添加登录状态
            "user" => $user,  // 添加用户信息
        ];
        // 渲染商品详情视图并返回
        return View::fetch('index/goodsdetail', $data);
    }

    /**
     * 根据标签筛选商品
     */
    public function filterByTags()
    {
        if (!$this->request->isPost()) {
            return json(['success' => false, 'msg' => '非法请求']);
        }

        $tagIds = $this->request->post('tagIds/a', []); // 获取标签ID数组

        try {
            $query = Db::name('goods')
                ->alias('g')
                ->field([
                    'g.id',
                    'g.goods_name',
                    'g.goods_img',
                    'g.goods_money',
                    'g.goods_des',
                    'g.goods_xl',
                    'g.permanent_price2'
                ])
                ->where('g.goods_show', 1);

            // 如果有选择标签,使用子查询实现多标签筛选
            if (!empty($tagIds)) {
                // 获取所有选中标签的名称
                $tagNames = Db::name('game_tags')
                    ->whereIn('id', $tagIds)
                    ->column('name');

                if (!empty($tagNames)) {
                    // 使用whereOr和like组合实现多标签匹配
                    $query->where(function($q) use ($tagNames) {
                        foreach ($tagNames as $index => $tagName) {
                            if ($index === 0) {
                                $q->where('g.goods_key', 'like', '%'.$tagName.'%');
                            } else {
                                $q->whereOr('g.goods_key', 'like', '%'.$tagName.'%');
                            }
                        }
                    });
                }
            }

            $goods = $query->select();

            // 处理商品数据
            foreach ($goods as &$item) {
                $item['goods_img'] = get_file_url($item['goods_img']);
            }

            return json([
                'success' => true,
                'data' => $goods
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'msg' => '筛选失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 论坛登录引导页
     * 当未登录用户尝试访问论坛内容时显示此页面
     *
     * @return \think\response\View 渲染登录引导页视图
     */
    public function forumLoginGuide()
    {
        // 获取要跳转的目标URL
        $targetUrl = Request::param("url", "");

        // 获取系统配置
        $system = System::find(1);
        $config = Config::find(1);

        // 准备数据传递给视图
        $data = [
            "system" => $system,
            "config" => $config,
            "targetUrl" => $targetUrl
        ];

        // 渲染登录引导页视图并返回
        return View::fetch('index/forum_login_guide', $data);
    }

    /**
     * 论坛内容访问检查
     * 检查用户是否已登录，未登录则重定向到登录引导页
     *
     * @return \think\response\Redirect|\think\response\Json 重定向或JSON响应
     */
    public function checkForumAccess()
    {
        // 获取目标URL
        $targetUrl = Request::param("url", "");

        // 检查用户是否已登录
        $email = Cookie::get("username");
        $userId = Cookie::get("id");
        $isLoggedIn = !empty($email) && !empty($userId);

        if (!$isLoggedIn) {
            // 未登录，重定向到登录引导页
            $guideUrl = url('home/index/forumLoginGuide', ['url' => urlencode($targetUrl)]);
            return redirect($guideUrl);
        }

        // 已登录，返回成功状态和目标URL
        if (Request::isAjax()) {
            return json(['success' => true, 'url' => $targetUrl]);
        } else {
            return redirect($targetUrl);
        }
    }
}
