<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付订单 - {$system.sy_title}</title>
    <meta name="description" content="{$system.sy_des}"/>
    <meta name="keywords" content="{$system.sy_key}"/>
    <script src="/static/js/jquery.min.js"></script>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background-color: #FAFBFC;
        }
        
        .payment-method {
            transition: all 0.3s ease;
        }
        
        .payment-method:hover {
            transform: translateY(-2px);
        }
        
        .payment-method.selected {
            border-color: #3B82F6;
            background-color: rgba(59, 130, 246, 0.05);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        /* 加载动画 */
        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
            }
            80%, 100% {
                opacity: 0;
            }
        }
        
        @keyframes pulse-dot {
            0% {
                transform: scale(0.8);
            }
            50% {
                transform: scale(1);
            }
            100% {
                transform: scale(0.8);
            }
        }
        
        .pulse-ring {
            position: relative;
        }
        
        .pulse-ring:before {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: rgba(59, 130, 246, 0.3);
            animation: pulse-ring 1.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
    </style>
</head>

<body class="text-gray-800 antialiased tracking-tight min-h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100">
    <!-- 引用页眉 -->
    {include file='../app/user/view/ticket/header.html'}

    <!-- 主内容区 -->
    <main class="flex-grow py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">
            <!-- 顶部标题 -->
            <div class="text-center mb-12 animate__animated animate__fadeIn">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">完成支付</h1>
                <p class="text-gray-500">请选择您喜欢的支付方式完成订单</p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 左侧订单信息卡片 -->
                <div class="lg:col-span-2 animate__animated animate__fadeInLeft">
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden border border-gray-100">
                        <!-- 订单头部 -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b border-gray-100">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h2 class="text-xl font-semibold text-gray-800">订单详情</h2>
                                    <div class="flex items-center mt-1 text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                        <span>订单号: {$order.order}</span>
                                    </div>
                                </div>
                                <div class="order-timer py-2 px-4 bg-white rounded-full shadow-sm text-sm font-medium text-blue-600"></div>
                            </div>
                        </div>
                        
                        <!-- 订单详情表格 -->
                        <div class="p-6">
                            <div class="space-y-6">
                                <!-- 商品信息 -->
                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <h3 class="text-sm font-medium text-gray-500">商品信息</h3>
                                        <p class="text-base font-semibold text-gray-800 mt-1">{$order.goodsName}</p>
                                    </div>
                                </div>
                                
                                <!-- 下单时间 -->
                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <h3 class="text-sm font-medium text-gray-500">下单时间</h3>
                                        <p class="text-base font-semibold text-gray-800 mt-1">{$order.time}</p>
                                    </div>
                                </div>
                                
                                <!-- 优惠金额 -->
                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
                                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <h3 class="text-sm font-medium text-gray-500">优惠金额</h3>
                                        <p class="text-base font-semibold text-green-600 mt-1">￥{$order.discount}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧支付面板 -->
                <div class="lg:col-span-1 animate__animated animate__fadeInRight">
                    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 sticky top-4">
                        <!-- 金额信息 -->
                        <div class="p-6 border-b border-gray-100">
                            <div class="text-center">
                                <h2 class="text-lg font-medium text-gray-700">应付金额</h2>
                                <div class="mt-2 text-3xl font-bold text-blue-600">￥{$order.money}</div>
                            </div>
                        </div>
                        
                        <!-- 支付方式选择 -->
                        <div class="p-6">
                            <!-- 提示信息 -->
                            <div class="bg-red-50 rounded-lg p-3 mb-6">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-red-600">虚拟物品，及时交付，若非帐号本身问题，暂不支持退款</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 支付方式列表 -->
                            <h3 class="text-sm font-medium text-gray-500 mb-3">选择支付方式</h3>
                            <div class="space-y-3 payment-methods">
                                {if isset($order.is_membership) && $order.is_membership}
                                    <!-- 会员订单只显示余额支付 -->
                                    <div class="payment-method flex items-center p-4 border border-gray-200 rounded-xl cursor-pointer hover:shadow-md transition-all" data-type="yepay">
                                        <div class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center mr-4">
                                            <img src="/assets/images/pays/balance.svg" alt="余额支付" class="w-6 h-6">
                                        </div>
                                        <div class="flex-1">
                                            <span class="font-medium text-gray-800">余额支付</span>
                                            <p class="text-xs text-gray-500 mt-1">使用账户余额支付</p>
                                        </div>
                                        <div class="payment-check ml-2 w-6 h-6 rounded-full border-2 border-gray-200"></div>
                                    </div>
                                {else}
                                    <!-- 非会员订单显示所有可用支付方式 -->
                                    {if $pay.pay_wxpay==1}
                                    <div class="payment-method flex items-center p-4 border border-gray-200 rounded-xl cursor-pointer hover:shadow-md transition-all" data-type="wxpay">
                                        <div class="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center mr-4">
                                            <img src="/assets/images/pays/weidoufu_wxpay.svg" alt="微信支付" class="w-6 h-6">
                                        </div>
                                        <div class="flex-1">
                                            <span class="font-medium text-gray-800">微信支付</span>
                                            <p class="text-xs text-gray-500 mt-1">使用微信扫码支付</p>
                                        </div>
                                        <div class="payment-check ml-2 w-6 h-6 rounded-full border-2 border-gray-200"></div>
                                    </div>
                                    {/if}
                                    
                                    {if $pay.pay_alipay==1}
                                    <div class="payment-method flex items-center p-4 border border-gray-200 rounded-xl cursor-pointer hover:shadow-md transition-all" data-type="alipay">
                                        <div class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center mr-4">
                                            <img src="/assets/images/pays/alipay_pc.svg" alt="支付宝" class="w-6 h-6">
                                        </div>
                                        <div class="flex-1">
                                            <span class="font-medium text-gray-800">支付宝</span>
                                            <p class="text-xs text-gray-500 mt-1">使用支付宝扫码支付</p>
                                        </div>
                                        <div class="payment-check ml-2 w-6 h-6 rounded-full border-2 border-gray-200"></div>
                                    </div>
                                    {/if}
                                    
                                    {if $pay.pay_qqpay==1}
                                    <div class="payment-method flex items-center p-4 border border-gray-200 rounded-xl cursor-pointer hover:shadow-md transition-all" data-type="qqpay">
                                        <div class="w-10 h-10 rounded-full bg-purple-50 flex items-center justify-center mr-4">
                                            <img src="/assets/images/pays/qqpay_pc.svg" alt="QQ支付" class="w-6 h-6">
                                        </div>
                                        <div class="flex-1">
                                            <span class="font-medium text-gray-800">QQ支付</span>
                                            <p class="text-xs text-gray-500 mt-1">使用QQ钱包支付</p>
                                        </div>
                                        <div class="payment-check ml-2 w-6 h-6 rounded-full border-2 border-gray-200"></div>
                                    </div>
                                    {/if}
                                    
                                    {if $pay.pay_balance==1}
                                    <div class="payment-method flex items-center p-4 border border-gray-200 rounded-xl cursor-pointer hover:shadow-md transition-all" data-type="yepay">
                                        <div class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center mr-4">
                                            <img src="/assets/images/pays/balance.svg" alt="余额支付" class="w-6 h-6">
                                        </div>
                                        <div class="flex-1">
                                            <span class="font-medium text-gray-800">余额支付</span>
                                            <p class="text-xs text-gray-500 mt-1">使用账户余额支付</p>
                                        </div>
                                        <div class="payment-check ml-2 w-6 h-6 rounded-full border-2 border-gray-200"></div>
                                    </div>
                                    {/if}
                                {/if}
                            </div>
                            
                            <!-- 提交按钮区域 -->
                            <div class="mt-8">
                                <button id="submitPayment" class="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                    请选择支付方式
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 引用页脚 -->
    {include file='../app/user/view/ticket/footer.html'}
    
    <!-- 模态框组件 -->
    <div id="alertModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity modal-overlay" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full animate__animated animate__fadeInUp glass-card">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="modal-icon h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="modal-title text-lg leading-6 font-medium text-gray-900" id="modal-title"></h3>
                            <div class="mt-2">
                                <p class="modal-message text-sm text-gray-500"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" class="modal-button w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">确定</button>
                </div>
            </div>
        </div>
    </div>

<script>
class PaymentSystem {
    constructor() {
        this.isProcessing = false;
        this.modal = document.getElementById('alertModal');
        this.orderTimeoutMinutes = 15;
        this.selectedPaymentMethod = null;
        this.submitButton = document.getElementById('submitPayment');
        this.init();
    }

    init() {
        this.initEventListeners();
        this.startOrderTimeout();
        this.initLoadingState();
    }

    initEventListeners() {
        // 支付方式选择
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', (e) => {
                if (!this.isProcessing) {
                    this.handlePaymentMethodSelect(method);
                }
            });
        });

        // 提交按钮
        this.submitButton.addEventListener('click', () => {
            if (this.selectedPaymentMethod && !this.isProcessing) {
                this.initiatePayment(this.selectedPaymentMethod, this.selectedPaymentMethod.dataset.type);
            }
        });

        // 弹窗控制
        this.modal.querySelector('.modal-button').addEventListener('click', () => this.hideModal());
        this.modal.querySelector('.modal-overlay').addEventListener('click', () => this.hideModal());
        
        // 添加键盘事件支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !this.modal.classList.contains('hidden')) {
                this.hideModal();
            }
        });
    }

    initLoadingState() {
        this.loadingElement = document.createElement('div');
        this.loadingElement.className = 'hidden absolute inset-0 bg-white bg-opacity-80 rounded-xl flex items-center justify-center z-10';
        this.loadingElement.innerHTML = `
            <div class="pulse-ring">
                <div class="w-8 h-8 rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
            </div>
        `;
        document.querySelector('.payment-methods').appendChild(this.loadingElement);
    }

    handlePaymentMethodSelect(method) {
        // 移除所有已选中状态
        document.querySelectorAll('.payment-method').forEach(m => {
            m.classList.remove('selected');
            m.querySelector('.payment-check').classList.remove('bg-blue-600');
            m.querySelector('.payment-check').classList.add('border-gray-200');
            m.querySelector('.payment-check').classList.remove('border-blue-600');
        });
        
        // 为当前选中添加样式
        method.classList.add('selected');
        method.querySelector('.payment-check').classList.add('bg-blue-600');
        method.querySelector('.payment-check').classList.remove('border-gray-200');
        method.querySelector('.payment-check').classList.add('border-blue-600');
        
        // 更新选中状态和按钮
        this.selectedPaymentMethod = method;
        this.submitButton.textContent = `确认支付`;
        this.submitButton.disabled = false;
        
        // 添加视觉反馈
        method.classList.add('scale-95');
        setTimeout(() => method.classList.remove('scale-95'), 200);
    }

    async initiatePayment(methodElement, payType) {
        this.setLoadingState(true);

        try {
            // 基础支付数据
            const paymentData = {
                ord_bbh: "{$order.order}",
                ord_name: "{$order.goodsName}",
                time: "{$order.time}",
                discount: "{$order.discount}",
                ord_money: "{$order.money}",
                ord_combo: "{$order.combo_id}",
                ord_type: payType
            };

            // 如果是线下订单，添加 GoodType
            {if isset($order.is_offline) && $order.is_offline}
            paymentData.GoodType = 0;
            {/if}

            // 如果不是线下订单，添加这些字段
            {if (!isset($order.is_offline) || !$order.is_offline) && (!isset($order.is_permanent) || !$order.is_permanent)}
            Object.assign(paymentData, {
                duration: "{$order.duration}",
                unit: "{$order.unit}"
            });
            {/if}

            // 如果 is_permanent 为 true，添加 is_permanent 参数
            {if isset($order.is_permanent) && $order.is_permanent}
            paymentData.is_permanent = 1;
            paymentData.GoodType = 3;
            {/if} 

            // 如果 is_membership 为 true，添加 is_membership 参数
            {if isset($order.is_membership) && $order.is_membership}
            paymentData.is_membership = true;
            {/if}

            const response = await this.processPayment(paymentData);
            
            if (response.code === 200) {
                await this.handlePaymentSuccess(response.data);
            } else {
                throw new Error(response.msg || '支付请求处理失败');
            }
        } catch (error) {
            this.showModal('支付失败', `支付发起失败: ${error.message}`, 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    setLoadingState(isLoading) {
        this.isProcessing = isLoading;
        this.loadingElement.classList.toggle('hidden', !isLoading);
        this.submitButton.disabled = isLoading;
        
        if (isLoading) {
            this.submitButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                处理中...
            `;
        } else if (this.selectedPaymentMethod) {
            this.submitButton.textContent = `确认支付`;
        } else {
            this.submitButton.textContent = `请选择支付方式`;
        }
    }

    showModal(title, message, type = 'info') {
        const modalTitle = this.modal.querySelector('.modal-title');
        const modalMessage = this.modal.querySelector('.modal-message');
        const modalIcon = this.modal.querySelector('.modal-icon');
        
        modalTitle.textContent = title;
        modalMessage.textContent = message;
        
        // 设置不同类型的样式
        if (type === 'error') {
            modalIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />`;
            modalIcon.classList.add('text-red-600');
            modalIcon.classList.remove('text-green-600', 'text-blue-600');
        } else if (type === 'success') {
            modalIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />`;
            modalIcon.classList.add('text-green-600');
            modalIcon.classList.remove('text-red-600', 'text-blue-600');
        } else {
            modalIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />`;
            modalIcon.classList.add('text-blue-600');
            modalIcon.classList.remove('text-red-600', 'text-green-600');
        }
        
        this.modal.classList.remove('hidden');
        
        // 自动关闭（仅针对成功提示）
        if (type === 'success') {
            setTimeout(() => this.hideModal(), 3000);
        }
    }

    startOrderTimeout() {
        const timeoutMs = this.orderTimeoutMinutes * 60 * 1000;
        const startTime = Date.now();
        
        // 使用已存在的 order-timer 元素
        const timerElement = document.querySelector('.order-timer');
        if (!timerElement) return; // 添加安全检查

        const updateTimer = () => {
            const remainingMs = timeoutMs - (Date.now() - startTime);
            if (remainingMs <= 0) {
                this.handleOrderTimeout();
                return;
            }

            const minutes = Math.floor(remainingMs / 60000);
            const seconds = Math.floor((remainingMs % 60000) / 1000);
            timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            requestAnimationFrame(updateTimer);
        };

        updateTimer();
    }

    handleOrderTimeout() {
        document.querySelectorAll('.payment-method').forEach(method => {
            method.classList.add('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
        });
        this.submitButton.disabled = true;
        this.submitButton.textContent = '订单已超时';
        this.showModal('订单已超时', '您的订单已超时，请重新下单', 'error');
    }

    async processPayment(data) {
        // 优先判断是否为会员订单
        const paymentUrl = {if isset($order.is_membership) && $order.is_membership}
            "/user/pay/membershipPay"
        {else}
            {if isset($order.is_offline) && $order.is_offline}"/user/pay/offlinePay"{else}"/user/pay"{/if}
        {/if};
        
        return $.ajax({
            url: paymentUrl,
            type: "post",
            dataType: "json",
            data: data
        });
    }

    async handlePaymentSuccess(data) {
        try {
            const response = await $.post("/admin/Epay", data);
            
            // 检查是否是重定向脚本
            if (typeof response === 'string' && response.includes('window.location.href')) {
                // 创建支付容器显示过渡动画
                const rightPanel = document.querySelector('.lg\\:col-span-1 > div');
                const payContainer = document.createElement('div');
                payContainer.id = 'payContent';
                payContainer.className = 'bg-white rounded-2xl shadow-sm border border-gray-100 p-6';
                
                // 添加过渡动画
                payContainer.innerHTML = `
                    <div class="text-center py-4">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
                        <p class="mt-4 text-gray-600">支付成功，正在跳转...</p>
                    </div>
                `;
                
                if (rightPanel) {
                    rightPanel.parentNode.replaceChild(payContainer, rightPanel);
                }

                // 显示成功提示
                this.showModal('支付成功', '正在跳转到订单页面...', 'success');
                
                // 解析重定向URL
                const redirectUrl = response.match(/href='([^']+)'/)[1];
                
                // 延迟执行重定向，让用户看到成功提示
                setTimeout(() => {
                    window.location.href = redirectUrl;
                }, 1500);
                
                return;
            }
            
            // 检查是否是支付表单
            if (typeof response === 'string' && response.includes('alipaysubmit')) {
                // 创建支付容器
                const rightPanel = document.querySelector('.lg\\:col-span-1 > div');
                const payContainer = document.createElement('div');
                payContainer.id = 'payContent';
                payContainer.className = 'bg-white rounded-2xl shadow-sm border border-gray-100 p-6';
                
                // 添加加载提示
                payContainer.innerHTML = `
                    <div class="text-center py-4">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                        <p class="mt-4 text-gray-600">正在跳转到支付页面...</p>
                    </div>
                `;
                
                if (rightPanel) {
                    rightPanel.parentNode.replaceChild(payContainer, rightPanel);
                }

                // 创建临时容器解析HTML
                const tempContainer = document.createElement('div');
                tempContainer.innerHTML = response;
                
                // 获取表单元素
                const form = tempContainer.querySelector('form');
                
                if (form) {
                    // 将表单添加到文档中（隐藏）
                    form.style.display = 'none';
                    document.body.appendChild(form);
                    
                    // 显示成功提示
                    this.showModal('支付处理成功', '正在跳转到支付页面，请稍候...', 'success');
                    
                    // 延迟提交表单
                    setTimeout(() => {
                        try {
                            form.submit();
                        } catch (error) {
                            console.error('Form submission error:', error);
                            this.showModal('支付跳转失败', '请刷新页面重试', 'error');
                        }
                    }, 1500);
                } else {
                    throw new Error('支付表单解析失败');
                }
                
                return;
            }

            // 处理其他类型的响应
            let jsonResponse;
            try {
                jsonResponse = typeof response === 'string' ? JSON.parse(response) : response;
                
                if (jsonResponse.status === 'error') {
                    this.showModal('支付处理失败', jsonResponse.message || '支付失败', 'error');
                    return;
                }
                
                const rightPanel = document.querySelector('.lg\\:col-span-1 > div');
                const payContainer = document.createElement('div');
                payContainer.id = 'payContent';
                payContainer.className = 'bg-white rounded-2xl shadow-sm border border-gray-100 p-6';
                payContainer.innerHTML = response;
                
                if (rightPanel) {
                    rightPanel.parentNode.replaceChild(payContainer, rightPanel);
                    this.showModal('支付处理成功', '支付请求已成功提交，请按照页面指引完成支付', 'success');
                }
            } catch (e) {
                throw new Error('响应格式错误');
            }
        } catch (error) {
            this.showModal('支付处理异常', '支付处理过程中发生错误，请稍后再试', 'error');
            console.error('Payment error:', error);
        }
    }

    hideModal() {
        // 添加淡出动画
        const modalContent = this.modal.querySelector('.glass-card');
        modalContent.classList.remove('animate__fadeInUp');
        modalContent.classList.add('animate__fadeOutDown');
        
        setTimeout(() => {
            this.modal.classList.add('hidden');
            modalContent.classList.remove('animate__fadeOutDown');
            modalContent.classList.add('animate__fadeInUp');
        }, 300);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化支付系统
    const paymentSystem = new PaymentSystem();
    
    // 添加支付方式选中状态的初始化
    document.querySelectorAll('.payment-method').forEach(method => {
        const checkDiv = method.querySelector('.payment-check');
        if (!checkDiv.innerHTML) {
            checkDiv.innerHTML = `
                <svg class="w-4 h-4 text-white hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            `;
        }
    });
    
    // 自动选择默认支付方式
    const paymentMethods = document.querySelectorAll('.payment-method');
    if (paymentMethods.length > 0) {
        // 如果只有一种支付方式，直接选择它
        if (paymentMethods.length === 1) {
            paymentSystem.handlePaymentMethodSelect(paymentMethods[0]);
        } else {
            // 查找各种支付方式
            const alipayMethod = document.querySelector('.payment-method[data-type="alipay"]');
            const balanceMethod = document.querySelector('.payment-method[data-type="yepay"]');
            const wxpayMethod = document.querySelector('.payment-method[data-type="wxpay"]');
            const qqpayMethod = document.querySelector('.payment-method[data-type="qqpay"]');
            
            // 按优先级选择：支付宝 > 微信支付 > QQ支付 > 余额支付
            if (alipayMethod) {
                paymentSystem.handlePaymentMethodSelect(alipayMethod);
            } else if (wxpayMethod) {
                paymentSystem.handlePaymentMethodSelect(wxpayMethod);
            } else if (qqpayMethod) {
                paymentSystem.handlePaymentMethodSelect(qqpayMethod);
            } else if (balanceMethod) {
                paymentSystem.handlePaymentMethodSelect(balanceMethod);
            }
        }
    }
    
    // 添加页面加载动画效果
    setTimeout(() => {
        document.querySelectorAll('.animate__animated').forEach(el => {
            el.style.opacity = '1';
            el.style.visibility = 'visible';
        });
    }, 100);
});
</script>
</body>
</html>
