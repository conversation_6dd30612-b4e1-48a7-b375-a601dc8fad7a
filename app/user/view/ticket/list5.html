<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>{$system.sy_title}</title>
		<link href="/static/css/tailwind.min.css" rel="stylesheet">
		<script src="/static/js/jquery.min.js"></script>
		<style>
			/* 动画效果 */
			@keyframes slideInUp {
				from { transform: translateY(20px); opacity: 0; }
				to { transform: translateY(0); opacity: 1; }
			}
			
			.animate-slide-in {
				animation: slideInUp 0.5s ease-out forwards;
			}

			/* 工单卡片样式 */
			.ticket-card {
				transition: all 0.3s ease;
				border-left: 4px solid transparent;
			}
			
			.ticket-card:hover {
				transform: translateX(4px);
				border-left-color: #4f46e5;
			}

			/* 状态标签 */
			.status-badge {
				padding: 0.25rem 0.75rem;
				border-radius: 9999px;
				font-size: 0.875rem;
				font-weight: 500;
			}

			.status-pending {
				background-color: #fef3c7;
				color: #d97706;
			}

			.status-processing {
				background-color: #e0e7ff;
				color: #4f46e5;
			}

			.status-completed {
				background-color: #dcfce7;
				color: #16a34a;
			}

			/* 表单样式优化 */
			.form-input:focus {
				box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
			}

			.form-radio:checked {
				background-color: #4f46e5;
				border-color: #4f46e5;
			}

			/* 文件上传按钮美化 */
			.file-upload-btn {
				position: relative;
				overflow: hidden;
				display: inline-block;
			}

			.file-upload-btn input[type=file] {
				position: absolute;
				font-size: 100px;
				opacity: 0;
				right: 0;
				top: 0;
				cursor: pointer;
			}
		</style>
	</head>

	<body class="bg-gray-50 text-gray-800">

		<!-- 引用头部 -->
		{include file='../app/user/view/ticket/header.html'}	

		<!-- Main Content -->
		<main class="container mx-auto px-4 py-8">
			<div class="flex flex-col lg:flex-row gap-6">
				<!-- 左侧面板 -->
				<div class="lg:w-1/4 space-y-6">
					<!-- 用户信息卡片 -->
					<div class="bg-white rounded-2xl shadow-lg overflow-hidden">
						<div class="h-32 bg-gradient-to-r from-indigo-500 to-purple-600 relative">
							<div class="absolute -bottom-16 inset-x-0 flex justify-center">
								<img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" 
									 class="w-32 h-32 rounded-full border-4 border-white shadow-lg object-cover"
									 alt="用户头像">
							</div>
						</div>
						<div class="pt-20 pb-6 px-6 text-center">
							<h3 class="text-xl font-bold text-gray-900">{$user.us_username}</h3>
							<p class="mt-1 text-sm text-gray-500">工单管理中心</p>
						</div>
					</div>

					<!-- 导航菜单 -->
					<div class="bg-white rounded-2xl shadow-lg overflow-hidden">
						{include file='../app/user/view/ticket/index.html'}
					</div>
				</div>

				<!-- 右侧内容区 -->
				<div class="lg:w-3/4 space-y-6">
					<!-- 工单列表 -->
					<div class="bg-white rounded-2xl shadow-lg overflow-hidden">
						<div class="p-6 border-b border-gray-100">
							<div class="flex justify-between items-center">
								<h2 class="text-xl font-bold text-gray-800">我的工单</h2>
								<span class="text-sm text-gray-500">共 {$workorder|count} 个工单</span>
							</div>
						</div>

						<div class="p-6">
							{if condition="count($workorder)==0"}
								<div class="text-center py-12">
									<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
											  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
									</svg>
									<h3 class="mt-2 text-sm font-medium text-gray-900">暂无工单</h3>
									<p class="mt-1 text-sm text-gray-500">创建新工单来获取帮助</p>
								</div>
							{else/}
								<div class="space-y-4">
									{foreach $workorder as $index=>$val}
									<div class="ticket-card p-4 bg-white rounded-lg border animate-slide-in"
										 style="animation-delay: {$index * 0.1}s">
										<div class="flex justify-between items-center">
											<div class="space-y-1">
												<div class="flex items-center space-x-2">
													<span class="text-sm text-gray-500">工单号：{$val.id}</span>
													<span class="status-badge 
														{if $val.work_state eq '待处理'}status-pending
														{elseif $val.work_state eq '处理中'}status-processing
														{else}status-completed{/if}">
														{$val.work_state|raw}
													</span>
												</div>
												<div class="text-base font-medium text-gray-900">{$val.work_goods}</div>
											</div>
											<button onclick="submit2({$val.id})" 
													class="inline-flex items-center px-4 py-2 border border-transparent 
														   rounded-lg text-sm font-medium text-indigo-600 bg-indigo-50 
														   hover:bg-indigo-100 focus:outline-none focus:ring-2 
														   focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
												查看详情
											</button>
										</div>
									</div>
									{/foreach}
								</div>
								<div class="mt-6">
									{$workorder|raw}
								</div>
							{/if}
						</div>
					</div>

					<!-- 提交工单表单 -->
					<div class="bg-white rounded-2xl shadow-lg overflow-hidden">
						<div class="p-6 border-b border-gray-100">
							<h2 class="text-xl font-bold text-gray-800">提交新工单</h2>
						</div>

						<div class="p-6">
							<form class="space-y-6">
								<!-- 工单类型 -->
								<div class="space-y-2">
									<label class="text-sm font-medium text-gray-700">工单类型</label>
									<div class="flex space-x-6">
										<label class="flex items-center">
											<input type="radio" name="work_type" value="0" 
												   class="form-radio h-4 w-4 text-indigo-600">
											<span class="ml-2 text-sm text-gray-700">售前工单</span>
										</label>
										<label class="flex items-center">
											<input type="radio" name="work_type" value="1" 
												   class="form-radio h-4 w-4 text-indigo-600">
											<span class="ml-2 text-sm text-gray-700">售后工单</span>
										</label>
									</div>
								</div>

								<!-- 选择商品 -->
								<div class="space-y-2">
									<label for="work_goods" class="text-sm font-medium text-gray-700">选择商品</label>
									<select id="work_goods" name="work_goods" 
											class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 
												   focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 
												   rounded-lg">
										<option value="">请选择订单</option>
										{foreach $order as $val}
											<option value="{$val.ord_name}--订单id:{$val.id}">
												{$val.ord_name}--订单id:{$val.id}
											</option>
										{/foreach}
									</select>
								</div>

								<!-- 问题描述 -->
								<div class="space-y-2">
									<label for="work_content" class="text-sm font-medium text-gray-700">问题描述</label>
									<textarea id="work_content" name="work_content" rows="4" 
											  class="block w-full rounded-lg border-gray-300 shadow-sm 
													 focus:ring-indigo-500 focus:border-indigo-500"
											  placeholder="请详细描述您遇到的问题..."></textarea>
								</div>

								<!-- 文件上传 -->
								<div class="space-y-2">
									<label class="text-sm font-medium text-gray-700">问题截图</label>
									<div class="file-upload-btn">
										<button type="button" 
												class="inline-flex items-center px-4 py-2 border border-gray-300 
													   rounded-lg shadow-sm text-sm font-medium text-gray-700 
													   bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 
													   focus:ring-offset-2 focus:ring-indigo-500">
											<svg class="-ml-1 mr-2 h-5 w-5 text-gray-400" fill="none" 
												 stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
													  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"/>
											</svg>
											选择图片
										</button>
										<input type="file" name="file" id="file" accept="image/*">
									</div>
									<input type="hidden" name="work_img" id="work_img">
								</div>

								<!-- 提交按钮 -->
								<div class="flex justify-end">
									<button type="button" onclick="button()" 
											class="inline-flex items-center px-6 py-3 border border-transparent 
												   text-base font-medium rounded-lg text-white bg-gradient-to-r 
												   from-indigo-600 to-purple-600 hover:from-indigo-700 
												   hover:to-purple-700 focus:outline-none focus:ring-2 
												   focus:ring-offset-2 focus:ring-indigo-500 transition-all 
												   transform hover:scale-105">
										提交工单
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</main>

		<!-- 工单详情模态框 -->
		<div id="workOrderModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
			<div class="min-h-screen px-4 text-center">
				<div class="fixed inset-0 transition-opacity" aria-hidden="true">
					<div class="absolute inset-0 bg-gray-500 opacity-75"></div>
				</div>
				<span class="inline-block h-screen align-middle" aria-hidden="true">&#8203;</span>
				<div class="inline-block w-full max-w-2xl p-6 my-8 text-left align-middle transition-all transform 
							bg-white rounded-2xl shadow-xl">
					<div class="flex justify-between items-center mb-4">
						<h3 class="text-lg font-medium leading-6 text-gray-900">工单详情</h3>
						<button type="button" class="close-modal text-gray-400 hover:text-gray-500">
							<span class="sr-only">关闭</span>
							<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
							</svg>
						</button>
					</div>
					<div id="modalContent" class="mt-4 space-y-4"></div>
				</div>
			</div>
		</div>

		<script src="/static/js/alert.js"></script>
		<script>
			// 提交图片
			$("#file").on("change", function() {
				var formdata = new FormData($("form")[0]);
				$.ajax({
					type: 'post',
					url: '/Uploads',
					data: formdata,
					cache: false,
					processData: false,
					contentType: false,
					success: function(e) {
						$("#work_img").val(e.data.src);
					}
				});
			});

			function button() {
				var data = {
					work_type: $('input[name="work_type"]:checked').val(),
					work_goods: $("#work_goods").val(),
					work_content: $("#work_content").val(),
					work_img: $("#work_img").val()
				};
				if (data.work_type == 0) {
					if (data.work_content.length <= 10) {
						createAlert("red", "描述低于10个字符", 0);
						return false;
					}
					$.post("/user/base/workorder", data, function(e) {
						if (e == 1) {
							createAlert("#16b777", "提交成功", 1);
							upUrl();
						}
					});
				} else {
					for (let i in data) {
						if (!data[i] && i != "work_img") {
							createAlert("red", "请检查表单是否完整", 0);
							return false;
						}
					}
					$.post("/user/base/workorder", data, function(e) {
						if (e == 1) {
							createAlert("#16b777", "提交成功", 1);
							upUrl();
						}
					});
				}
			}

			// 查看工单详情
			function submit2(id) {
				$.post("/user/base/workorder", { id: id }, function(response) {
					if (response) {
						$("#modalContent").html(`
							<p>订单ID: ${response.id}</p>
							<p>订单类型： ${response.work_type}</p>
							<p>对应商品： ${response.work_goods}</p>
							<p>工单内容： ${response.work_content}</p>
							<p>工单附件： <img src="${response.work_img}" width="100px"></p>
							<p>提交用户： ${response.work_user}</p>
							<p>工单状态： ${response.work_state}</p>
							<p>回复内容： ${response.work_reply}</p>
							<p>更新时间： ${response.time}</p>
						`);
						$("#workOrderModal").fadeIn(300);
					} else {
						createAlert("red", "无法获取工单详情", 0);
					}
				}).fail(function() {
					createAlert("red", "请求失败，请稍后重试", 0);
				});
			}

			// 关闭模态框
			$(".close-modal").on("click", function() {
				$("#workOrderModal").fadeOut(300);
			});
		</script>
	</body>
<!-- 引用页脚 -->
{include file='../app/user/view/ticket/footer.html'}
</html>