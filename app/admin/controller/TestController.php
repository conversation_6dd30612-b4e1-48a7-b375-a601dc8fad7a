<?php
namespace app\admin\controller;

use think\facade\Db;
use think\facade\Request;
use think\response\Json;

class TestController
{
    /**
     * 测试数据库字段
     */
    public function testBanFields(): Json
    {
        try {
            // 检查字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM cdk_cards");
            
            $banFields = [];
            foreach ($columns as $column) {
                if (in_array($column['Field'], ['is_banned', 'banned_at', 'banned_reason', 'banned_by'])) {
                    $banFields[] = $column;
                }
            }
            
            return json([
                'code' => 1,
                'msg' => '字段检查完成',
                'data' => [
                    'ban_fields_count' => count($banFields),
                    'ban_fields' => $banFields,
                    'all_columns' => $columns
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '检查失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 自动添加封禁字段
     */
    public function addBanFields(): Json
    {
        try {
            // 检查字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM cdk_cards LIKE 'is_banned'");
            if (empty($columns)) {
                // 字段不存在，添加字段
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `is_banned` TINYINT(1) DEFAULT 0 COMMENT '是否封禁：0=正常，1=已封禁'");
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_at` DATETIME NULL COMMENT '封禁时间'");
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_reason` VARCHAR(255) NULL COMMENT '封禁原因'");
                Db::execute("ALTER TABLE `cdk_cards` ADD COLUMN `banned_by` INT(11) NULL COMMENT '封禁操作者ID（管理员或代理）'");

                return json([
                    'code' => 1,
                    'msg' => '封禁字段添加成功'
                ]);
            } else {
                return json([
                    'code' => 1,
                    'msg' => '封禁字段已存在，无需添加'
                ]);
            }
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '添加字段失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建日志表
     */
    public function createLogTables(): Json
    {
        try {
            $results = [];

            // 创建管理员日志表
            $adminLogExists = Db::query("SHOW TABLES LIKE 'tk_admin_logs'");
            if (empty($adminLogExists)) {
                $adminLogSql = "
                CREATE TABLE `tk_admin_logs` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
                  `action` varchar(50) NOT NULL COMMENT '操作类型',
                  `content` text COMMENT '操作内容',
                  `ip` varchar(45) COMMENT 'IP地址',
                  `created_at` datetime NOT NULL COMMENT '创建时间',
                  PRIMARY KEY (`id`),
                  KEY `admin_id` (`admin_id`),
                  KEY `action` (`action`),
                  KEY `created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表'
                ";
                Db::execute($adminLogSql);
                $results[] = '管理员日志表创建成功';
            } else {
                $results[] = '管理员日志表已存在';
            }

            // 创建代理日志表
            $agentLogExists = Db::query("SHOW TABLES LIKE 'tk_agent_logs'");
            if (empty($agentLogExists)) {
                $agentLogSql = "
                CREATE TABLE `tk_agent_logs` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `agent_id` int(11) NOT NULL COMMENT '代理ID',
                  `action` varchar(50) NOT NULL COMMENT '操作类型',
                  `content` text COMMENT '操作内容',
                  `ip` varchar(45) COMMENT 'IP地址',
                  `created_at` datetime NOT NULL COMMENT '创建时间',
                  PRIMARY KEY (`id`),
                  KEY `agent_id` (`agent_id`),
                  KEY `action` (`action`),
                  KEY `created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理操作日志表'
                ";
                Db::execute($agentLogSql);
                $results[] = '代理日志表创建成功';
            } else {
                $results[] = '代理日志表已存在';
            }

            return json([
                'code' => 1,
                'msg' => '日志表检查完成',
                'data' => $results
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '创建日志表失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试封禁功能
     */
    public function testBanFunction(): Json
    {
        try {
            $cdkCode = Request::param('cdk_code', '');
            if (empty($cdkCode)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供卡密码参数'
                ]);
            }

            // 查询卡密信息
            $cdkCard = Db::table('cdk_cards')
                ->where('cdk_code', $cdkCode)
                ->find();

            if (!$cdkCard) {
                return json([
                    'code' => 0,
                    'msg' => '卡密不存在'
                ]);
            }

            $result = [
                'cdk_info' => $cdkCard,
                'user_info' => null,
                'can_revoke' => false,
                'revoke_type' => null
            ];

            // 如果卡密已使用，查询用户信息
            if ($cdkCard['status'] === 'used' && $cdkCard['assigned_user']) {
                $user = Db::name('user')->where('id', $cdkCard['assigned_user'])->find();
                $result['user_info'] = $user;
                $result['can_revoke'] = true;

                // 判断撤销类型
                if ($cdkCard['cdk_type'] === 'permanent') {
                    $result['revoke_type'] = '永久版账号';
                } elseif ($cdkCard['cdk_type'] === 'offline') {
                    $result['revoke_type'] = '离线账号';
                } elseif (strpos($cdkCard['cdk_type'], 'member_') === 0) {
                    $result['revoke_type'] = '会员时间';
                    $membershipType = str_replace('member_', '', $cdkCard['cdk_type']);
                    $membershipInfo = Db::name('membership_pricing')
                        ->where('membership_type', $membershipType)
                        ->find();
                    $result['membership_info'] = $membershipInfo;
                } else {
                    $result['revoke_type'] = '在线时间';
                }
            }

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试离线卡密封禁功能
     */
    public function testOfflineBan(): Json
    {
        try {
            $cdkCode = Request::param('cdk_code', '');
            if (empty($cdkCode)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供卡密码参数'
                ]);
            }

            // 查询卡密信息
            $cdkCard = Db::table('cdk_cards')
                ->where('cdk_code', $cdkCode)
                ->find();

            if (!$cdkCard) {
                return json([
                    'code' => 0,
                    'msg' => '卡密不存在'
                ]);
            }

            if ($cdkCard['cdk_type'] !== 'offline') {
                return json([
                    'code' => 0,
                    'msg' => '该卡密不是离线卡密'
                ]);
            }

            $result = [
                'cdk_info' => $cdkCard,
                'user_info' => null,
                'accounts_before' => [],
                'orders' => []
            ];

            // 如果卡密已使用，查询用户信息和相关账号
            if ($cdkCard['status'] === 'used' && $cdkCard['assigned_user']) {
                $userId = $cdkCard['assigned_user'];

                // 查询用户信息
                $user = Db::name('user')->where('id', $userId)->find();
                $result['user_info'] = $user;

                // 查询用户的离线账号（封禁前）
                $accounts = Db::name('account')
                    ->where([
                        'ac_uid' => $userId,
                        'ac_goods' => $cdkCard['shop'],
                        'goods_Type' => 0  // 离线账号
                    ])
                    ->select();
                $result['accounts_before'] = $accounts;

                // 查询相关订单
                $orders = Db::name('order')
                    ->where([
                        'ord_uid' => $userId,
                        'GoodType' => 0,  // 离线账号
                        'ord_ifpay' => 1
                    ])
                    ->select();
                $result['orders'] = $orders;

                // 查询用户页面会显示的订单（模拟离线页面查询逻辑）
                $offlinePageOrders = Db::name('order')
                    ->where([
                        'ord_uid' => $userId,
                        'GoodType' => 0,  // 离线账号
                        'ord_ifpay' => 1  // 已支付（离线页面不检查or_maturity）
                    ])
                    ->select();
                $result['offline_page_orders'] = $offlinePageOrders;

                // 查询未到期的订单（其他页面的查询逻辑）
                $activeOrders = Db::name('order')
                    ->where([
                        'ord_uid' => $userId,
                        'GoodType' => 0,
                        'ord_ifpay' => 1,
                        'or_maturity' => 1  // 只查询未到期的订单
                    ])
                    ->select();
                $result['active_orders'] = $activeOrders;

                // 如果有account_id字段，查询对应账号
                if (isset($cdkCard['account_id']) && $cdkCard['account_id']) {
                    $linkedAccount = Db::name('account')->where('id', $cdkCard['account_id'])->find();
                    $result['linked_account'] = $linkedAccount;
                }
            }

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 查看所有会员类型的时效设置
     */
    public function checkMembershipSettings(): Json
    {
        try {
            $membershipTypes = Db::name('membership_pricing')->select();

            $result = [];
            foreach ($membershipTypes as $type) {
                $result[] = [
                    'id' => $type['id'],
                    'membership_type' => $type['membership_type'],
                    'price' => $type['price'],
                    'validity_period_days' => $type['validity_period'],
                    'validity_period_months' => round($type['validity_period'] / 30, 1),
                    'description' => $type['membership_type'] . ' - ' . $type['validity_period'] . '天 (约' . round($type['validity_period'] / 30, 1) . '个月)'
                ];
            }

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试会员卡密时效期计算
     */
    public function testMembershipExpiry(): Json
    {
        try {
            $cdkCode = Request::param('cdk_code', '');
            if (empty($cdkCode)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供卡密码参数'
                ]);
            }

            // 查询卡密信息
            $cdkCard = Db::table('cdk_cards')
                ->where('cdk_code', $cdkCode)
                ->find();

            if (!$cdkCard) {
                return json([
                    'code' => 0,
                    'msg' => '卡密不存在'
                ]);
            }

            if (strpos($cdkCard['cdk_type'], 'member_') !== 0) {
                return json([
                    'code' => 0,
                    'msg' => '该卡密不是会员卡密'
                ]);
            }

            // 解析会员类型
            $membershipType = str_replace('member_', '', $cdkCard['cdk_type']);

            // 查询会员类型信息
            $membershipInfo = null;
            if ($cdkCard['shop'] && $cdkCard['shop'] > 0) {
                $membershipInfo = Db::name('membership_pricing')
                    ->where('id', $cdkCard['shop'])
                    ->find();
            } else {
                $membershipInfo = Db::name('membership_pricing')
                    ->where('membership_type', $membershipType)
                    ->find();
            }

            // 计算时效期
            $now = new \DateTime('now', new \DateTimeZone('Asia/Shanghai'));
            $validityPeriodDays = (int)$cdkCard['expiry_date2'];
            $expiryCalculation = clone $now;
            $expiryCalculation->modify("+{$validityPeriodDays} day");
            $calculatedExpiry = $expiryCalculation->format('Y-m-d H:i:s');

            return json([
                'code' => 1,
                'msg' => '计算成功',
                'data' => [
                    'cdk_info' => $cdkCard,
                    'membership_info' => $membershipInfo,
                    'expiry_calculation' => [
                        'current_time' => $now->format('Y-m-d H:i:s'),
                        'validity_period_days' => $validityPeriodDays,
                        'calculated_expiry' => $calculatedExpiry,
                        'calculation_method' => '当前时间 + ' . $validityPeriodDays . ' 天'
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '计算失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试在线卡密封禁功能
     */
    public function testOnlineBan(): Json
    {
        try {
            $cdkCode = Request::param('cdk_code', '');
            if (empty($cdkCode)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供卡密码参数'
                ]);
            }

            // 查询卡密信息
            $cdkCard = Db::table('cdk_cards')
                ->where('cdk_code', $cdkCode)
                ->find();

            if (!$cdkCard) {
                return json([
                    'code' => 0,
                    'msg' => '卡密不存在'
                ]);
            }

            if (!in_array($cdkCard['cdk_type'], ['day', 'hour', 'online'])) {
                return json([
                    'code' => 0,
                    'msg' => '该卡密不是在线卡密'
                ]);
            }

            $result = [
                'cdk_info' => $cdkCard,
                'user_info' => null,
                'accounts_before' => [],
                'online_orders' => [],
                'cdk_orders' => []
            ];

            // 如果卡密已使用，查询用户信息和相关账号
            if ($cdkCard['status'] === 'used' && $cdkCard['assigned_user']) {
                $userId = $cdkCard['assigned_user'];

                // 查询用户信息
                $user = Db::name('user')->where('id', $userId)->find();
                $result['user_info'] = $user;

                // 查询用户的在线账号（封禁前）
                $accounts = Db::name('account')
                    ->where([
                        'ac_uid' => $userId,
                        'ac_goods' => $cdkCard['shop'],
                        'ac_vip' => 1  // 在线VIP账号
                    ])
                    ->select();
                $result['accounts_before'] = $accounts;

                // 查询相关的在线订单
                $onlineOrders = Db::name('order')
                    ->where([
                        'ord_uid' => $userId,
                        'GoodType' => 1,  // 在线账号
                        'ord_ifpay' => 1,
                        'or_maturity' => 1  // 未到期
                    ])
                    ->select();
                $result['online_orders'] = $onlineOrders;

                // 查询CDK兑换订单
                $cdkOrders = Db::name('order')
                    ->where([
                        'ord_uid' => $userId,
                        'ord_type' => 'CDK兑换',
                        'ord_ifpay' => 1,
                        'or_maturity' => 1
                    ])
                    ->select();
                $result['cdk_orders'] = $cdkOrders;

                // 如果有account_id字段，查询对应账号
                if (isset($cdkCard['account_id']) && $cdkCard['account_id']) {
                    $linkedAccount = Db::name('account')->where('id', $cdkCard['account_id'])->find();
                    $result['linked_account'] = $linkedAccount;
                }
            }

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试永久版卡密封禁功能
     */
    public function testPermanentBan(): Json
    {
        try {
            $cdkCode = Request::param('cdk_code', '');
            if (empty($cdkCode)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供卡密码参数'
                ]);
            }

            // 查询卡密信息
            $cdkCard = Db::table('cdk_cards')
                ->where('cdk_code', $cdkCode)
                ->find();

            if (!$cdkCard) {
                return json([
                    'code' => 0,
                    'msg' => '卡密不存在'
                ]);
            }

            if ($cdkCard['cdk_type'] !== 'permanent') {
                return json([
                    'code' => 0,
                    'msg' => '该卡密不是永久版卡密'
                ]);
            }

            $result = [
                'cdk_info' => $cdkCard,
                'user_info' => null,
                'accounts_before' => [],
                'permanent_orders' => [],
                'cdk_orders' => []
            ];

            // 如果卡密已使用，查询用户信息和相关账号
            if ($cdkCard['status'] === 'used' && $cdkCard['assigned_user']) {
                $userId = $cdkCard['assigned_user'];

                // 查询用户信息
                $user = Db::name('user')->where('id', $userId)->find();
                $result['user_info'] = $user;

                // 查询用户的永久版账号（封禁前）
                $accounts = Db::name('account')
                    ->where([
                        'ac_uid' => $userId,
                        'ac_goods' => $cdkCard['shop'],
                        'ac_vip' => 2  // 永久版账号
                    ])
                    ->select();
                $result['accounts_before'] = $accounts;

                // 查询用户所有的永久版账号（用于验证删除效果）
                $allPermanentAccounts = Db::name('account')
                    ->where([
                        'ac_uid' => $userId,
                        'ac_vip' => 2  // 永久版账号
                    ])
                    ->select();
                $result['all_permanent_accounts'] = $allPermanentAccounts;

                // 查询相关的永久版订单
                $permanentOrders = Db::name('order')
                    ->where([
                        'ord_uid' => $userId,
                        'GoodType' => 2,  // 永久版账号
                        'ord_ifpay' => 1,
                        'or_maturity' => 1  // 未到期
                    ])
                    ->select();
                $result['permanent_orders'] = $permanentOrders;

                // 查询CDK兑换订单
                $cdkOrders = Db::name('order')
                    ->where([
                        'ord_uid' => $userId,
                        'ord_type' => ['CDK兑换', '永久版卡密兑换'],
                        'ord_ifpay' => 1,
                        'or_maturity' => 1
                    ])
                    ->select();
                $result['cdk_orders'] = $cdkOrders;

                // 如果有account_id字段，查询对应账号
                if (isset($cdkCard['account_id']) && $cdkCard['account_id']) {
                    $linkedAccount = Db::name('account')->where('id', $cdkCard['account_id'])->find();
                    $result['linked_account'] = $linkedAccount;
                }
            }

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 验证永久版账号删除效果
     */
    public function verifyPermanentDeletion(): Json
    {
        try {
            $userId = Request::param('user_id', '');
            if (empty($userId)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供用户ID参数'
                ]);
            }

            // 查询用户信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return json([
                    'code' => 0,
                    'msg' => '用户不存在'
                ]);
            }

            // 查询用户当前的永久版账号（模拟永久版页面的查询逻辑）
            $permanentAccounts = Db::name('account')
                ->where([
                    'ac_uid' => $userId,
                    'ac_vip' => 2  // 永久版账号
                ])
                ->select();

            // 查询用户的永久版订单
            $permanentOrders = Db::name('order')
                ->where([
                    'ord_uid' => $userId,
                    'GoodType' => 2,  // 永久版账号
                    'ord_ifpay' => 1
                ])
                ->select();

            // 查询用户的永久版游戏订单（另一种类型）
            $permanentGameOrders = Db::name('order')
                ->where([
                    'ord_uid' => $userId,
                    'is_permanent' => 1,
                    'GoodType' => 3,
                    'ord_ifpay' => 1
                ])
                ->select();

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'user_info' => $user,
                    'permanent_accounts_count' => count($permanentAccounts),
                    'permanent_accounts' => $permanentAccounts,
                    'permanent_orders_count' => count($permanentOrders),
                    'permanent_orders' => $permanentOrders,
                    'permanent_game_orders_count' => count($permanentGameOrders),
                    'permanent_game_orders' => $permanentGameOrders,
                    'page_will_show_games' => count($permanentAccounts) > 0 ? '是' : '否'
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试用户会员状态
     */
    public function testMembershipStatus(): Json
    {
        try {
            $userId = Request::param('user_id', '');
            if (empty($userId)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供用户ID参数'
                ]);
            }

            // 查询用户信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return json([
                    'code' => 0,
                    'msg' => '用户不存在'
                ]);
            }

            $currentTime = time();
            $exitTime = !empty($user['exit_time']) ? strtotime($user['exit_time']) : 0;

            // 判断会员状态
            $isMember = false;
            $memberStatus = '非会员';
            $remainingDays = 0;

            if ($exitTime > 0) {
                if ($exitTime > $currentTime) {
                    $isMember = true;
                    $memberStatus = '会员有效';
                    $remainingDays = ceil(($exitTime - $currentTime) / (24 * 60 * 60));
                } else {
                    $memberStatus = '会员已过期';
                }
            }

            // 查询会员相关订单
            $memberOrders = Db::name('order')
                ->where('ord_uid', $userId)
                ->where('ord_name', 'like', '%会员%')
                ->where('ord_ifpay', 1)
                ->order('id', 'desc')
                ->select();

            // 查询未到期的会员订单
            $activeMemberOrders = Db::name('order')
                ->where([
                    'ord_uid' => $userId,
                    'ord_ifpay' => 1,
                    'or_maturity' => 1
                ])
                ->where('ord_name', 'like', '%会员%')
                ->select();

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'user_info' => [
                        'id' => $user['id'],
                        'username' => $user['us_username'],
                        'exit_time' => $user['exit_time']
                    ],
                    'membership_status' => [
                        'is_member' => $isMember,
                        'status' => $memberStatus,
                        'current_time' => date('Y-m-d H:i:s', $currentTime),
                        'exit_time' => $user['exit_time'],
                        'remaining_days' => $remainingDays
                    ],
                    'can_purchase_membership' => !$isMember && count($activeMemberOrders) == 0,
                    'member_orders_count' => count($memberOrders),
                    'active_member_orders_count' => count($activeMemberOrders),
                    'member_orders' => $memberOrders,
                    'active_member_orders' => $activeMemberOrders
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 验证离线卡密封禁后的页面显示效果
     */
    public function verifyOfflineBanEffect(): Json
    {
        try {
            $userId = Request::param('user_id', '');
            if (empty($userId)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供用户ID参数'
                ]);
            }

            // 查询用户信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return json([
                    'code' => 0,
                    'msg' => '用户不存在'
                ]);
            }

            // 模拟离线账号页面的查询逻辑（第1054-1065行）
            $offlinePageQuery = Db::table('tk_order')
                ->alias('o')
                ->join('tk_account a', 'o.ord_aid = a.id')
                ->where([
                    'o.ord_uid' => $userId,
                    'o.ord_ifpay' => 1,
                    'o.GoodType' => 0
                ])
                ->field('a.*, o.ord_name, o.ord_type, o.id as order_id, o.or_maturity')
                ->order('a.id', 'desc')
                ->select();

            // 查询用户的离线账号
            $offlineAccounts = Db::name('account')
                ->where([
                    'ac_uid' => $userId,
                    'goods_Type' => 0  // 离线账号
                ])
                ->select();

            // 查询所有离线订单（包括已删除的）
            $allOfflineOrders = Db::name('order')
                ->where([
                    'ord_uid' => $userId,
                    'GoodType' => 0,
                    'ord_ifpay' => 1
                ])
                ->select();

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'user_info' => [
                        'id' => $user['id'],
                        'username' => $user['us_username']
                    ],
                    'page_simulation' => [
                        'description' => '模拟离线账号页面的查询结果',
                        'query_logic' => '查询条件：ord_uid=' . $userId . ' AND ord_ifpay=1 AND GoodType=0',
                        'will_show_count' => count($offlinePageQuery),
                        'will_show_games' => $offlinePageQuery
                    ],
                    'account_status' => [
                        'description' => '用户的离线账号状态',
                        'account_count' => count($offlineAccounts),
                        'accounts' => $offlineAccounts
                    ],
                    'order_status' => [
                        'description' => '用户的离线订单状态',
                        'total_orders' => count($allOfflineOrders),
                        'orders' => $allOfflineOrders
                    ],
                    'conclusion' => [
                        'page_will_show_games' => count($offlinePageQuery) > 0 ? '是' : '否',
                        'reason' => count($offlinePageQuery) > 0 ?
                            '离线页面仍会显示游戏，因为订单记录存在' :
                            '离线页面不会显示游戏，订单记录已删除'
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试logCdkDebugInfo方法
     */
    public function testLogCdkDebugInfo(): Json
    {
        try {
            // 模拟一些测试数据
            $testData = [
                'test_type' => 'method_test',
                'timestamp' => date('Y-m-d H:i:s'),
                'test_params' => [
                    'cdk_code' => 'TEST123456',
                    'user_id' => 999,
                    'action' => 'debug_test'
                ]
            ];

            // 测试不同类型的日志记录
            $this->logCdkDebugInfo('测试调试信息记录', $testData);
            $this->logCdkDebugInfo('卡密封禁', ['cdk_code' => 'TEST123456', 'reason' => '测试封禁']);
            $this->logCdkDebugInfo('普通调试信息', ['message' => '这是一条普通的调试信息']);

            return json([
                'code' => 1,
                'msg' => 'logCdkDebugInfo方法测试完成',
                'data' => [
                    'test_completed' => true,
                    'test_data' => $testData,
                    'log_types_tested' => [
                        'normal_debug' => '普通调试信息（仅记录到文件）',
                        'important_action' => '重要操作（记录到文件和数据库）'
                    ],
                    'check_logs' => [
                        'file_log' => '请检查 runtime/log/ 目录下的日志文件',
                        'database_log' => '请检查 tk_admin_logs 表中的记录'
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败：' . $e->getMessage(),
                'error_details' => [
                    'error_message' => $e->getMessage(),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine()
                ]
            ]);
        }
    }

    /**
     * 记录卡密调试信息（测试控制器版本）
     * 用于调试和排查卡密相关问题
     */
    private function logCdkDebugInfo($message, $data = [])
    {
        $debugInfo = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'data' => $data,
            'request_id' => uniqid('test_cdk_', true),
            'admin_id' => session('admin_id') ?? 'test_admin',
            'ip' => request()->ip() ?? 'unknown',
            'source' => 'TestController'
        ];

        // 记录到日志文件
        \think\facade\Log::info('测试CDK调试信息: ' . $message, $debugInfo);

        // 如果是重要操作，也记录到数据库（如果需要的话）
        if (in_array($message, ['卡密封禁', '卡密解封', '卡密生成', '卡密删除'])) {
            try {
                // 检查日志表是否存在
                $logTables = Db::query("SHOW TABLES LIKE 'tk_admin_logs'");
                if (!empty($logTables)) {
                    Db::name('admin_logs')->insert([
                        'admin_id' => session('admin_id') ?? 0,
                        'action' => 'test_cdk_debug',
                        'content' => json_encode($debugInfo, JSON_UNESCAPED_UNICODE),
                        'ip' => request()->ip(),
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
            } catch (\Exception $e) {
                // 如果数据库记录失败，只记录到文件日志
                \think\facade\Log::warning('测试CDK调试信息数据库记录失败: ' . $e->getMessage());
            }
        }
    }

    /**
     * 测试会员离线账号显示名称
     */
    public function testMemberOfflineAccountNames(): Json
    {
        try {
            $userId = Request::param('user_id', '');
            if (empty($userId)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供用户ID参数'
                ]);
            }

            // 查询用户信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return json([
                    'code' => 0,
                    'msg' => '用户不存在'
                ]);
            }

            // 查询会员提取的离线账号订单（模拟离线页面查询逻辑）
            $memberOfflineOrders = Db::table('tk_order')
                ->alias('o')
                ->join('tk_account a', 'o.ord_aid = a.id')
                ->join('tk_goods g', 'a.ac_goods = g.id', 'left')
                ->where([
                    'o.ord_uid' => $userId,
                    'o.ord_ifpay' => 1,
                    'o.GoodType' => 0,  // 离线账号
                    'o.is_permanent' => 2,  // 会员提取账号订单
                    'o.ord_type' => 'vip_extract'  // 会员提取类型
                ])
                ->field([
                    'o.id as order_id',
                    'o.ord_bbh',
                    'o.ord_type',
                    'o.ord_name',
                    'o.GoodType',
                    'o.is_permanent',
                    'o.or_maturity',
                    'o.expiry_date',
                    'a.id as account_id',
                    'a.ac_name',
                    'a.ac_password',
                    'a.ac_goods',
                    'a.goods_Type',
                    'g.goods_name as game_name'
                ])
                ->order('o.id', 'desc')
                ->select();

            // 分析订单名称问题
            $nameAnalysis = [];
            foreach ($memberOfflineOrders as $order) {
                $nameAnalysis[] = [
                    'order_id' => $order['order_id'],
                    'ord_name' => $order['ord_name'],
                    'game_name' => $order['game_name'],
                    'ac_goods' => $order['ac_goods'],
                    'name_correct' => $order['ord_name'] === $order['game_name'],
                    'issue' => $order['ord_name'] === '离线VIP账号提取' ? '显示通用名称而非游戏名' : '正常'
                ];
            }

            // 查询所有离线账号订单（包括非会员提取的）
            $allOfflineOrders = Db::table('tk_order')
                ->alias('o')
                ->join('tk_account a', 'o.ord_aid = a.id')
                ->join('tk_goods g', 'a.ac_goods = g.id', 'left')
                ->where([
                    'o.ord_uid' => $userId,
                    'o.ord_ifpay' => 1,
                    'o.GoodType' => 0
                ])
                ->field([
                    'o.ord_name',
                    'o.ord_type',
                    'o.is_permanent',
                    'g.goods_name as game_name'
                ])
                ->select();

            return json([
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'user_info' => [
                        'id' => $user['id'],
                        'username' => $user['us_username']
                    ],
                    'member_offline_orders' => [
                        'count' => count($memberOfflineOrders),
                        'orders' => $memberOfflineOrders
                    ],
                    'name_analysis' => $nameAnalysis,
                    'all_offline_orders' => [
                        'count' => count($allOfflineOrders),
                        'orders' => $allOfflineOrders
                    ],
                    'fix_status' => [
                        'description' => '修复后，ord_name应该显示具体的游戏名称而不是"离线VIP账号提取"',
                        'expected_behavior' => '会员提取的离线账号应该在离线账号页面显示正确的游戏名称'
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试离线账号状态修复
     */
    public function testOfflineAccountStatus(): Json
    {
        try {
            // 查询所有离线账号的状态分布
            $offlineAccountStats = [
                'total' => Db::name('account')->where('goods_Type', 0)->count(),
                'available' => Db::name('account')->where(['goods_Type' => 0, 'ac_states' => 1])->count(),
                'unavailable' => Db::name('account')->where(['goods_Type' => 0, 'ac_states' => 0])->count(),
                'assigned' => Db::name('account')->where(['goods_Type' => 0, 'ac_uid' => ['<>', null]])->count(),
                'unassigned' => Db::name('account')->where(['goods_Type' => 0, 'ac_uid' => null])->count()
            ];

            // 查询有问题的离线账号（已分配但状态为不可用的）
            $problematicAccounts = Db::name('account')
                ->where([
                    'goods_Type' => 0,  // 离线账号
                    'ac_states' => 0,   // 不可用状态
                    'ac_uid' => ['<>', null]  // 已分配给用户
                ])
                ->field('id, ac_name, ac_uid, ac_states, ac_sell, ac_goods, time')
                ->limit(20)
                ->select();

            // 查询正常的离线账号示例
            $normalAccounts = Db::name('account')
                ->where([
                    'goods_Type' => 0,  // 离线账号
                    'ac_states' => 1,   // 可用状态
                    'ac_uid' => ['<>', null]  // 已分配给用户
                ])
                ->field('id, ac_name, ac_uid, ac_states, ac_sell, ac_goods, time')
                ->limit(10)
                ->select();

            // 分析问题
            $issues = [];
            if ($offlineAccountStats['unavailable'] > 0) {
                $issues[] = "发现 {$offlineAccountStats['unavailable']} 个离线账号状态为不可用";
            }

            $problemCount = count($problematicAccounts);
            if ($problemCount > 0) {
                $issues[] = "发现 {$problemCount} 个已分配但状态为不可用的离线账号";
            }

            return json([
                'code' => 1,
                'msg' => '离线账号状态检查完成',
                'data' => [
                    'statistics' => $offlineAccountStats,
                    'issues' => $issues,
                    'problematic_accounts' => $problematicAccounts,
                    'normal_accounts' => $normalAccounts,
                    'fix_info' => [
                        'description' => '离线账号应该永远保持可用状态（ac_states = 1）',
                        'expected_behavior' => '即使分配给用户，离线账号也应该保持可用状态',
                        'fixed_locations' => [
                            'CdkExchange.php:342 - 卡密兑换时保持离线账号可用',
                            'CdkExchange.php:610 - 在线卡密兑换时不影响离线账号',
                            'Index.php:526 - 永久版账号提取时根据类型设置状态'
                        ]
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '检查失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 修复离线账号状态
     */
    public function fixOfflineAccountStatus(): Json
    {
        try {
            $fixedCount = 0;

            // 查询所有状态为不可用的离线账号
            $problematicAccounts = Db::name('account')
                ->where([
                    'goods_Type' => 0,  // 离线账号
                    'ac_states' => 0    // 不可用状态
                ])
                ->field('id, ac_name, ac_uid, ac_states')
                ->select();

            foreach ($problematicAccounts as $account) {
                // 将离线账号状态修复为可用
                $result = Db::name('account')
                    ->where('id', $account['id'])
                    ->update(['ac_states' => 1]);

                if ($result) {
                    $fixedCount++;
                    \think\facade\Log::info("修复离线账号状态", [
                        'account_id' => $account['id'],
                        'account_name' => $account['ac_name'],
                        'user_id' => $account['ac_uid']
                    ]);
                }
            }

            return json([
                'code' => 1,
                'msg' => '离线账号状态修复完成',
                'data' => [
                    'total_problematic' => count($problematicAccounts),
                    'fixed_count' => $fixedCount,
                    'fixed_accounts' => array_slice($problematicAccounts, 0, 10), // 只显示前10个
                    'note' => '所有离线账号状态已修复为可用状态'
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '修复失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试封禁原因可选功能
     */
    public function testBanReasonOptional(): Json
    {
        try {
            // 模拟测试封禁请求（不实际执行封禁）
            $testResults = [];

            // 测试1：管理员后台 - 有原因的封禁
            $testResults['admin_with_reason'] = [
                'scenario' => '管理员后台 - 填写封禁原因',
                'cdk_code' => 'TEST123456',
                'reason' => '测试封禁原因',
                'expected' => '应该成功封禁',
                'validation_removed' => true
            ];

            // 测试2：管理员后台 - 无原因的封禁
            $testResults['admin_without_reason'] = [
                'scenario' => '管理员后台 - 不填写封禁原因',
                'cdk_code' => 'TEST123456',
                'reason' => '',
                'expected' => '应该成功封禁（原因为空）',
                'validation_removed' => true
            ];

            // 测试3：代理后台 - 有原因的封禁
            $testResults['agent_with_reason'] = [
                'scenario' => '代理后台 - 填写封禁原因',
                'cdk_code' => 'TEST789012',
                'reason' => '代理测试封禁',
                'expected' => '应该成功封禁',
                'validation_removed' => true
            ];

            // 测试4：代理后台 - 无原因的封禁
            $testResults['agent_without_reason'] = [
                'scenario' => '代理后台 - 不填写封禁原因',
                'cdk_code' => 'TEST789012',
                'reason' => '',
                'expected' => '应该成功封禁（原因为空）',
                'validation_removed' => true
            ];

            // 检查修改状态
            $modifications = [
                'admin_backend' => [
                    'file' => 'app/admin/controller/CdkController.php',
                    'line' => '786-788',
                    'change' => '移除了封禁原因必填验证',
                    'status' => 'completed'
                ],
                'admin_frontend' => [
                    'file' => 'app/admin/view/cdk/index.html',
                    'line' => '320-335',
                    'change' => '移除了前端封禁原因验证，改为选填提示',
                    'status' => 'completed'
                ],
                'agent_backend' => [
                    'file' => 'app/agent/controller/Index.php',
                    'line' => '953-955',
                    'change' => '移除了封禁原因必填验证',
                    'status' => 'completed'
                ],
                'agent_frontend_prompt' => [
                    'file' => 'app/agent/view/index/index.html',
                    'line' => '1232-1240',
                    'change' => '移除了ElMessageBox.prompt的输入验证',
                    'status' => 'completed'
                ],
                'agent_frontend_confirm' => [
                    'file' => 'app/agent/view/index/index.html',
                    'line' => '1281-1284',
                    'change' => '移除了confirmBanCard函数的原因验证',
                    'status' => 'completed'
                ]
            ];

            return json([
                'code' => 1,
                'msg' => '封禁原因可选功能测试完成',
                'data' => [
                    'test_scenarios' => $testResults,
                    'modifications' => $modifications,
                    'summary' => [
                        'total_changes' => count($modifications),
                        'backend_changes' => 2,
                        'frontend_changes' => 3,
                        'validation_removed' => '所有封禁原因必填验证已移除',
                        'user_experience' => '用户现在可以选择是否填写封禁原因'
                    ],
                    'testing_guide' => [
                        'admin_test' => [
                            'url' => '/admin/cdk/index',
                            'steps' => [
                                '1. 点击卡密列表中的"封禁"按钮',
                                '2. 在弹出的对话框中不填写封禁原因',
                                '3. 直接点击"确认封禁"',
                                '4. 验证是否能成功封禁'
                            ]
                        ],
                        'agent_test' => [
                            'url' => '代理后台卡密管理页面',
                            'steps' => [
                                '1. 点击卡密列表中的"封禁"按钮',
                                '2. 在弹出的输入框中不填写任何内容',
                                '3. 直接点击"确认封禁"',
                                '4. 验证是否能成功封禁'
                            ]
                        ]
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试离线账号购买修复
     */
    public function testOfflineAccountPurchase(): Json
    {
        try {
            // 查询离线账号状态
            $offlineAccountStats = [
                'total' => Db::name('account')->where('goods_Type', 0)->count(),
                'available' => Db::name('account')->where(['goods_Type' => 0, 'ac_states' => 1, 'ac_sell' => 1])->count(),
                'unavailable' => Db::name('account')->where(['goods_Type' => 0, 'ac_states' => 0])->count(),
                'not_for_sale' => Db::name('account')->where(['goods_Type' => 0, 'ac_sell' => 0])->count()
            ];

            // 查询有离线账号的商品
            $goodsWithOfflineAccounts = Db::table('tk_account')
                ->alias('a')
                ->join('tk_goods g', 'a.ac_goods = g.id', 'left')
                ->where('a.goods_Type', 0)
                ->field([
                    'a.ac_goods',
                    'g.goods_name',
                    'COUNT(*) as total_accounts',
                    'SUM(CASE WHEN a.ac_states = 1 AND a.ac_sell = 1 THEN 1 ELSE 0 END) as available_accounts'
                ])
                ->group('a.ac_goods')
                ->select();

            // 查询最近的离线账号购买订单
            $recentOfflineOrders = Db::table('tk_order')
                ->alias('o')
                ->join('tk_user u', 'o.ord_uid = u.id', 'left')
                ->where([
                    'o.GoodType' => 0,
                    'o.ord_ifpay' => 1
                ])
                ->field([
                    'o.id',
                    'o.ord_bbh',
                    'o.ord_name',
                    'o.ord_money',
                    'o.ord_aid',
                    'o.or_maturity',
                    'o.ord_remarks',
                    'o.time',
                    'u.us_username'
                ])
                ->order('o.id', 'desc')
                ->limit(10)
                ->select();

            // 分析问题
            $issues = [];
            if ($offlineAccountStats['available'] == 0) {
                $issues[] = "没有可用的离线账号（ac_states=1 且 ac_sell=1）";
            }

            // 检查修复状态
            $fixStatus = [
                'description' => '修复了离线账号购买时的错误逻辑',
                'problem' => '原逻辑要求离线账号必须有对应的在线账号且在线账号不能被占用',
                'solution' => '离线账号独立存在，只需要检查自身的状态（ac_states=1, ac_sell=1）',
                'fixed_file' => 'app/admin/controller/Epay.php',
                'fixed_method' => 'handleTypeZeroPurchase',
                'fixed_lines' => '515-544'
            ];

            return json([
                'code' => 1,
                'msg' => '离线账号购买测试完成',
                'data' => [
                    'offline_account_stats' => $offlineAccountStats,
                    'goods_with_offline_accounts' => $goodsWithOfflineAccounts,
                    'recent_offline_orders' => $recentOfflineOrders,
                    'issues' => $issues,
                    'fix_status' => $fixStatus,
                    'test_guide' => [
                        'description' => '测试离线账号购买功能',
                        'steps' => [
                            '1. 确保有可用的离线账号（ac_states=1, ac_sell=1）',
                            '2. 用户选择离线游戏商品',
                            '3. 使用余额支付',
                            '4. 验证是否能成功购买',
                            '5. 检查订单状态和账号分配'
                        ],
                        'expected_result' => '应该能成功购买，不会出现"暂无可用离线账号，相应的在线账号正在被使用中或不存在"错误'
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败：' . $e->getMessage()
            ]);
        }
    }
}
