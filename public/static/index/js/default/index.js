!(function (e) {
  var t = {}
  function n(o) {
    if (t[o]) return t[o].exports
    var r = (t[o] = { i: o, l: !1, exports: {} })
    return e[o].call(r.exports, r, r.exports, n), (r.l = !0), r.exports
  }
  ;(n.m = e),
    (n.c = t),
    (n.d = function (e, t, o) {
      n.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: o })
    }),
    (n.r = function (e) {
      'undefined' != typeof Symbol &&
        Symbol.toStringTag &&
        Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
        Object.defineProperty(e, '__esModule', { value: !0 })
    }),
    (n.t = function (e, t) {
      if ((1 & t && (e = n(e)), 8 & t)) return e
      if (4 & t && 'object' == typeof e && e && e.__esModule) return e
      var o = Object.create(null)
      if (
        (n.r(o),
        Object.defineProperty(o, 'default', { enumerable: !0, value: e }),
        2 & t && 'string' != typeof e)
      )
        for (var r in e)
          n.d(
            o,
            r,
            function (t) {
              return e[t]
            }.bind(null, r)
          )
      return o
    }),
    (n.n = function (e) {
      var t =
        e && e.__esModule
          ? function () {
              return e.default
            }
          : function () {
              return e
            }
      return n.d(t, 'a', t), t
    }),
    (n.o = function (e, t) {
      return Object.prototype.hasOwnProperty.call(e, t)
    }),
    (n.p = '//zuhaowan.zuhaowan.com/shanghu/www3.0/'),
    n((n.s = 'woDU'))
})({
  '/ub8': function (e, t, n) {
    'use strict'
    function o(e, t) {
      if (
        ((this.getdomain = function (e) {
          var t = e.indexOf('://')
          if (t > 0) var n = e.substr(t + 3)
          else n = e
          return /^www\./.test(n) && (n = n.substr(4)), n
        }),
        e == t)
      )
        return 1
      e = this.getdomain(e)
      var n = this.getdomain(t)
      if (e == n) return 1
      e = e.replace('.', '\\.')
      var o = new RegExp('\\.' + e + '$')
      return n.match(o) ? 2 : 0
    }
    var r = function (e) {
      try {
        if (null != document.getElementById('bdmark')) return
        var t = !1
        if (arguments[1]) {
          var n = window.location.host,
            r = window.location.href
          1 == o(arguments[1], n)
            ? ((e = e + '/#m/' + r), (t = !0))
            : 2 == o(arguments[1], n)
            ? ((e = e + '/#m/' + r), (t = !0))
            : ((e = r), (t = !1))
        } else t = !0
        t &&
          (window.location.hash.match('fromapp') ||
            (navigator.userAgent.match(
              /(IPhone|IPod|Android|Ios|WebOS|Symbian|Windows Phone|Windows CE|Phone|BlackBerry|Mobile)/i
            ) &&
              location.replace(e)))
      } catch (e) {}
    }
    if (void 0 !== zhw.getCookie('visitpc'));
    else {
      // 注释掉或移除跳转逻辑
      // var i = '/wap/new#/'
      // 2 == $('input[name="tb_domain_tmall"]').val() &&
      //   ((function (e) {
      //     throw new TypeError('"' + e + '" is read-only')
      //   })('cur_baseurl'),
      //   (i = '/wap/tmall/')),
      //   r(i)
    }
  },
  AyAv: function (e, t) {
    var n = zhw.getUrlValue()
    // 注释掉或移除设备检测和跳转
    // navigator.userAgent.match(
    //   /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    // ) &&
    //   'pc' != n.vtype &&
    //   (location.href = '/wap/')
  },
  woDU: function (e, t, n) {
    'use strict'
    n.r(t)
    n('zHor'), n('yMlT'), n('/ub8'), n('AyAv')
    function o(e, t) {
      var n = Object.keys(e)
      if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e)
        t &&
          (o = o.filter(function (t) {
            return Object.getOwnPropertyDescriptor(e, t).enumerable
          })),
          n.push.apply(n, o)
      }
      return n
    }
    function r(e) {
      for (var t = 1; t < arguments.length; t++) {
        var n = null != arguments[t] ? arguments[t] : {}
        t % 2
          ? o(Object(n), !0).forEach(function (t) {
              i(e, t, n[t])
            })
          : Object.getOwnPropertyDescriptors
          ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
          : o(Object(n)).forEach(function (t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
            })
      }
      return e
    }
    function i(e, t, n) {
      return (
        t in e
          ? Object.defineProperty(e, t, {
              value: n,
              enumerable: !0,
              configurable: !0,
              writable: !0
            })
          : (e[t] = n),
        e
      )
    }
    $(function () {
      var e = {
          lazyLoading: !0,
          lazyLoadingInPrevNext: !0,
          lazyLoadingInPrevNextAmount: 1,
          lazyLoadingOnTransitionStart: !0
        },
        t =
          (new Swiper(
            '.swiper_banner',
            r(
              {
                autoplay: 5e3,
                autoplayDisableOnInteraction: !1,
                loop: !0,
                pagination: '.swiper-pagination-banner',
                paginationClickable: !0,
                prevButton: '.banner-prev',
                nextButton: '.banner-next'
              },
              e
            )
          ),
          new Swiper('.hot_swiper', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: !0,
            pagination: '.hot-pagination',
            paginationClickable: !0,
            prevButton: '.swiper-button2',
            nextButton: '.swiper-button1'
          }),
          new Swiper(
            '.special_swiper',
            r(
              {
                effect: 'coverflow',
                grabCursor: !0,
                centeredSlides: !0,
                slidesPerView: 'auto',
                coverflow: {
                  rotate: 0,
                  stretch: 0,
                  depth: 200,
                  modifier: 1,
                  slideShadows: !0
                },
                loop: !0,
                pagination: '.special-pagination',
                paginationClickable: !0,
                prevButton: '.swiper-button_s2',
                nextButton: '.swiper-button_s1'
              },
              e
            )
          ),
          $('.centre_min_gtit > span')),
        n = $('.centre_min_gtext')
      t.on('click', function () {
        var e = $(this).index()
        t.removeClass('bottom_line').eq(e).addClass('bottom_line'),
          n.hide().eq(e).show()
      }),
        $('.centre_main_t_li').on({
          mouseenter: function () {
            var e = $(this),
              t = e.index()
            e.find('.centre_main_img_l').attr(
              'src',
              ''.concat(GLOBAL.path, '/image/incos').concat(t + 1, '.png')
            )
          },
          mouseleave: function () {
            var e = $(this),
              t = e.index()
            e.find('.centre_main_img_l').attr(
              'src',
              ''.concat(GLOBAL.path, '/image/icon').concat(t + 1, '.png')
            )
          }
        }),
        $('.centre_min_go').on('click', function (e) {
          e.preventDefault()
          var t = $('.centre_min_gtit .bottom_line').data('helf')
          window.open(t)
        })
    })
  },
  yMlT: function (e, t) {
    $(function () {
      var e = $('.load_top'),
        t = $('.load_bottom'),
        n = $('.notice-hide')
      1 != localStorage.getItem('home_open_page') &&
        (e.fadeIn(),
        t.fadeTo(300, 0.5),
        $('.load_bottom, .load_close').on('click', function () {
          $('.load_bottom,.load_top').fadeOut()
        })),
        n.on('click', function () {
          localStorage.setItem('home_open_page', 1),
            $('.load_bottom,.load_top').fadeOut()
        })
    })
  },
  zHor: function (e, t) {
    var n = zhw.getUrlValue().tg
    n && zhw.setCookie('tglm_id', n)
  }
})
