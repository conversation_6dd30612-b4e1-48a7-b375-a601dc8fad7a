<!DOCTYPE html>
<html>
<head>
    <title>支付功能测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input, select { padding: 5px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; width: 200px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 120px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>支付功能测试工具</h1>
        
        <div class="test-section">
            <h3>1. 会员购买测试</h3>
            <div class="form-group">
                <label>用户ID：</label>
                <input type="number" id="userId" placeholder="输入用户ID" value="43">
            </div>
            <div class="form-group">
                <label>用户名：</label>
                <input type="text" id="username" placeholder="输入用户名" value="13023092233">
            </div>
            <div class="form-group">
                <label>会员类型：</label>
                <select id="memberType">
                    <option value="月费会员">月费会员</option>
                    <option value="季费会员">季费会员</option>
                    <option value="年费会员">年费会员</option>
                </select>
            </div>
            <div class="form-group">
                <label>订单金额：</label>
                <input type="number" id="orderMoney" placeholder="输入金额" value="10" step="0.01">
            </div>
            <button onclick="testMembershipPurchase()">测试会员购买</button>
            <div id="purchaseResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 用户会员状态检查</h3>
            <div class="form-group">
                <label>用户ID：</label>
                <input type="number" id="checkUserId" placeholder="输入用户ID" value="43">
            </div>
            <button onclick="checkMembershipStatus()">检查会员状态</button>
            <div id="statusResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 修复说明</h3>
            <div class="result">
                <h4>修复的问题：</h4>
                <ul>
                    <li>❌ 错误：Class 'app\user\model\Us' not found</li>
                    <li>✅ 修复：使用正确的模型类 Us (已在文件顶部引入)</li>
                </ul>
                
                <h4>测试场景：</h4>
                <ul>
                    <li><strong>场景1：</strong>会员未过期 → 应该提示"已是会员，无需重复购买"</li>
                    <li><strong>场景2：</strong>会员已过期 → 应该允许购买新会员</li>
                    <li><strong>场景3：</strong>从未购买过会员 → 应该允许购买</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testMembershipPurchase() {
            const userId = document.getElementById('userId').value;
            const username = document.getElementById('username').value;
            const memberType = document.getElementById('memberType').value;
            const orderMoney = document.getElementById('orderMoney').value;
            
            if (!userId || !username || !memberType || !orderMoney) {
                alert('请填写完整信息');
                return;
            }
            
            const resultDiv = document.getElementById('purchaseResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试会员购买...';
            
            // 生成订单号
            const orderNo = 'TEST' + Date.now();
            
            // 构建请求数据
            const formData = new FormData();
            formData.append('ord_name', memberType);
            formData.append('ord_money', orderMoney);
            formData.append('ord_bbh', orderNo);
            formData.append('ord_type', '会员充值');
            
            // 设置Cookie（模拟登录状态）
            document.cookie = `id=${userId}; path=/`;
            document.cookie = `username=${username}; path=/`;
            
            fetch('/user/Pay/membershipPay', {
                method: 'POST',
                body: formData,
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                let statusClass = data.code === 200 ? 'success' : 
                                 data.code === 409 ? 'warning' : 'error';
                
                resultDiv.className = `result ${statusClass}`;
                resultDiv.innerHTML = `
                    <h4>购买测试结果：</h4>
                    <p><strong>状态码：</strong>${data.code}</p>
                    <p><strong>消息：</strong>${data.msg}</p>
                    <p><strong>订单号：</strong>${orderNo}</p>
                    ${data.data ? `<p><strong>详细信息：</strong><pre>${JSON.stringify(data.data, null, 2)}</pre></p>` : ''}
                `;
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<p>请求失败：${error.message}</p>`;
            });
        }
        
        function checkMembershipStatus() {
            const userId = document.getElementById('checkUserId').value;
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在查询会员状态...';
            
            fetch(`/admin/TestController/testMembershipStatus?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        const membershipData = data.data.membership_status;
                        const canPurchase = data.data.can_purchase_membership;
                        
                        let statusClass = membershipData.is_member ? 'success' : 'warning';
                        
                        resultDiv.className = `result ${statusClass}`;
                        resultDiv.innerHTML = `
                            <h4>用户会员状态：</h4>
                            <p><strong>用户ID：</strong>${data.data.user_info.id}</p>
                            <p><strong>用户名：</strong>${data.data.user_info.username}</p>
                            <p><strong>会员状态：</strong>${membershipData.status}</p>
                            <p><strong>当前时间：</strong>${membershipData.current_time}</p>
                            <p><strong>到期时间：</strong>${membershipData.exit_time || '无'}</p>
                            <p><strong>剩余天数：</strong>${membershipData.remaining_days}天</p>
                            <p><strong>可以购买会员：</strong><span style="color: ${canPurchase ? 'green' : 'red'}; font-weight: bold;">${canPurchase ? '是' : '否'}</span></p>
                            <p><strong>会员订单数量：</strong>${data.data.member_orders_count}</p>
                            <p><strong>未到期订单数量：</strong>${data.data.active_member_orders_count}</p>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `<p>查询失败：${data.msg}</p>`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<p>请求失败：${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
