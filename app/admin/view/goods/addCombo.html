<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>{if $type == 'offline'}添加离线商品{else}添加套餐{/if}</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
    <input type="hidden" name="product_type" value="{$type}">
    
    <!-- 离线商品添加表单 -->
    <div class="layui-form-item">
      <label class="layui-form-label required">选择商品</label>
      <div class="layui-input-block">
        <select name="product_id" lay-verify="required" lay-filter="productSelect" lay-search>
          <option value="">请选择商品</option>
          {foreach $goodsAll as $goods}
            <option value="{$goods.id}">{$goods.goods_name}</option>
          {/foreach}
        </select>
      </div>
    </div>
    
    <div class="layui-form-item">
      <label class="layui-form-label required">按天价格</label>
      <div class="layui-input-block">
        <input type="number" name="day_price" lay-verify="required|number" lay-reqtext="按天价格不能为空" placeholder="请输入按天价格" class="layui-input">
        <tip>请输入正确的金额数值</tip>
      </div>
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label required">按小时价格</label>
      <div class="layui-input-block">
        <input type="number" name="hour_price" lay-verify="required|number" lay-reqtext="按小时价格不能为空" placeholder="请输入按小时价格" class="layui-input">
        <tip>请输入正确的金额数值</tip>
      </div>
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label required">离线价格</label>
      <div class="layui-input-block">
        <input type="number" name="product_amount" lay-verify="required|number" lay-reqtext="离线价格不能为空" placeholder="请输入离线价格" class="layui-input">
        <tip>请输入正确的金额数值</tip>
      </div>
    </div>
    
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认添加">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-edit" id="layuiadmin-app-form-edit" value="确认编辑">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form','upload'], function(){
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload;
    
    //监听提交
    form.on('submit(layuiadmin-app-form-submit)', function(data){
      var field = data.field; //获取提交的字段
      var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
      
      // 统一使用 addOfflineProduct 接口
      var url = '/admin/base/addOfflineProduct';
      
      $.ajax({
        url: url,
        method: 'post',
        data: field,
        success: function(res){
          if(res.code === 0 || res.code === undefined) {
            layer.msg('保存成功');
            //提交 Ajax 成功后，关闭当前弹层并重载表格
            parent.layui.table.reload('LAY-app-content-list2');
            parent.layer.close(index); //再执行关闭
          } else {
            layer.msg('保存失败：' + (res.msg || '未知错误'));
          }
        }
      });
      
      return false; // 阻止默认 form 跳转
    });
  });
  </script>
</body>
</html>