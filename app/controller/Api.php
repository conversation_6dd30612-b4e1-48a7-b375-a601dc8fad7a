<?php
/**
 * Api.php
 *
 * @package    app\controller
 * @subpackage Api
 * @version    1.0
 * <AUTHOR>
 * @license    MIT
 * @company    Kongzu
 * @date       2024-12-27
 *
 * @description
 * 该文件包含API控制器类，提供订单管理、优惠券处理和账户监控的各种API接口。
 *
 * @copyright
 * (c) 2024 Kongzu. 版权所有。未经授权，严禁通过任何媒介复制此文件。专有且保密。
 */

namespace app\controller;

use app\BaseController;
use think\facade\Request;
use app\admin\model\Account;
use app\admin\model\Order;
use app\admin\model\Config;
use think\facade\Db;
use app\admin\model\Coupons;

/**
 * 类 Api
 *
 * @brief API控制器类
 *
 * 该类处理与订单过期检查、账户监控及其他相关功能的API请求。
 */
class Api extends BaseController
{
    /**
     * 构造函数
     *
     * 通过将默认时区设置为北京时间来初始化API控制器。
     */
    public function __construct()
    {
        date_default_timezone_set('Asia/Shanghai'); // 设置默认时区为北京时间
    }

    /**
     * 检查订单过期
     *
     * @brief 检查单个订单的过期状态。
     *
     * @return mixed 剩余时间或订单过期状态。
     */
    public function outDate()
    {
        // 接收订单 ID
        $order = Order::find(Request::param("id"));

        // 获取关联账户信息
        $account = Account::where('id', $order['ord_aid'])->find();

        if (!$account || !$account['exit_time']) {
            return '账户不存在或未设置到期时间';
        }

        // 如果当前时间超过了退出时间
        if (time() > $account['exit_time']) {
            // 更新订单状态为过期
            Order::where("id", $order["id"])->update(["or_maturity" => 0]);

            // 更新账户状态
            Account::where("id", $order["ord_aid"])->update([
                "ac_states" => 1,
                "ac_uid" => null
            ]);

            return 0; // 订单已过期
        }

        // 计算剩余时间
        return $this->timer(date("Y-m-d H:i:s", $account['exit_time']));
    }

    /**
     * 计算剩余时间
     *
     * @brief 计算并格式化到指定日期的剩余时间。
     *
     * @param string $date 目标日期。
     * @return string 格式化的剩余时间。
     */
    function timer($date)
    {
        $t = strtotime($date) - time(); // 计算时间差（单位：秒）
        $arr = [];

        // 天数
        $day = intval($t / 86400);
        $arr['day'] = $day;

        // 小时
        $hour = intval((($t / 86400) - $day) * 24);
        $arr['hour'] = $hour;

        // 分钟
        $minute = intval(((($t / 86400) - $day) * 24 - $hour) * 60);
        $arr['minute'] = $minute;

        // 秒
        $second = intval(((((($t / 86400) - $day) * 24 - $hour) * 60 - $minute) * 60));
        $arr['second'] = $second;

        // 格式化输出
        $dates = $arr["day"] . "天" . $arr["hour"] . "时" . $arr["minute"] . "分" . $arr["second"] . "秒";

        return $dates;
    }

    /**
     * 监控账户
     *
     * @brief 监控普通用户购买的在线账户和至尊会员提取的账户是否过期。
     *
     * @return string JSON格式的监控结果。
     */
    public function monitorAccounts()
    {
        // 验证参数 key
        $key = Request::param('key');
        if ($key !== 'Kongzu') {
            return json_encode([
                'code' => 403,
                'msg' => '无效的密钥',
            ], JSON_UNESCAPED_UNICODE);
        }

        // 查询配置
        $config = Config::find(1);
        $allowExpireNoOffline = $config ? $config['allow_expire_no_offline'] : 0;

        $accounts = Account::where('goods_Type', 1)->select();

        $totalCheckedAccounts = 0;
        $unrentedAccountCount = 0;
        $unexpiredAccountCount = 0;
        $recycledAccountList = [];
        $exceptionList = [];
        $recycledAccountOrders = [];

        foreach ($accounts as $account) {
            $totalCheckedAccounts++;
            try {
                if (empty($account['exit_time'])) {
                    $unrentedAccountCount++;
                } else {
                    if (time() > $account['exit_time']) {
                        // 判断是否允许过期账号不下线
                        if ($allowExpireNoOffline == 1) {
                            // ✅ 开启模式：仅标记过期，不设置改密标记，等待重新分配时处理
                            if (($account['expire_status'] ?? 0) == 0) {
                                Account::where('id', $account['id'])->update([
                                    'expire_status' => 1  // 仅标记过期，不改密
                                ]);

                                // 记录过期标记的日志
                                Db::table('monitor_logs')->insert([
                                    'account_name' => $account['ac_name'],
                                    'action' => '标记过期',
                                    'monitor_type' => '到期未下线监控',
                                    'timestamp' => date('Y-m-d H:i:s'),
                                    'status' => '成功',
                                    'error_message' => '检测到过期，标记等待重新分配'
                                ]);
                            }
                            // expire_status = 1 的账号保持等待状态，不进行任何处理
                        } else {
                            // 获取账号名称
                            $acName = Account::where('id', $account['id'])->value('ac_name');

                            // 先回收账号（更新状态）
                            Account::where(['ac_name' => $acName, 'goods_Type' => 1])->update([
                                'ac_vip' => 0,
                                'exit_time' => null,
                                'token_limit' => 0,
                                'ac_uid' => null,
                                'ac_states' => 1,
                                'ac_sell' => 1
                            ]);
                            $recycledAccountList[] = $account['ac_name'];

                            // 更新相关订单状态为到期
                            $orders = Order::where('ord_aid', $account['id'])->select();
                            foreach ($orders as $order) {
                                Order::where('id', $order['id'])->update(['or_maturity' => 0]);
                                $recycledAccountOrders[] = [
                                    'account' => $account['ac_name'],
                                    'order_id' => $order['ord_bbh']
                                ];
                            }

                            // 记录回收日志
                            Db::table('monitor_logs')->insert([
                                'account_name' => $account['ac_name'],
                                'action' => '回收',
                                'monitor_type' => '普通用户监控',
                                'timestamp' => date('Y-m-d H:i:s'),
                                'status' => '成功',
                                'error_message' => null
                            ]);

                            // 在回收账号之后进行密码修改
                            try {
                                $this->changePassword($account);

                                // 记录成功改密日志
                                Db::table('monitor_logs')->insert([
                                    'account_name' => $account['ac_name'],
                                    'action' => '回收后改密',
                                    'monitor_type' => '普通用户监控',
                                    'timestamp' => date('Y-m-d H:i:s'),
                                    'status' => '成功',
                                    'error_message' => null
                                ]);
                            } catch (\Exception $passwordError) {
                                // 记录改密失败日志但不影响回收流程
                                Db::table('monitor_logs')->insert([
                                    'account_name' => $account['ac_name'],
                                    'action' => '回收后改密',
                                    'monitor_type' => '普通用户监控',
                                    'timestamp' => date('Y-m-d H:i:s'),
                                    'status' => '失败',
                                    'error_message' => $passwordError->getMessage()
                                ]);
                            }
                        }
                    } else {
                        $unexpiredAccountCount++;
                    }
                }
            } catch (\Exception $e) {
                $exceptionList[] = [
                    'account' => $account['ac_name'],
                    'error' => $e->getMessage() ?: '未知错误'
                ];

                // 记录异常日志
                Db::table('monitor_logs')->insert([
                    'account_name' => $account['ac_name'],
                    'action' => '回收',
                    'monitor_type' => '普通用户监控',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'status' => '失败',
                    'error_message' => $e->getMessage()
                ]);
            }
        }

        return json([
            'code' => 200,
            'msg' => '账号监控执行完成',
            'data' => [
                '总检测账号数' => $totalCheckedAccounts,
                '未租用账号数' => $unrentedAccountCount,
                '未过期账号数' => $unexpiredAccountCount,
                '回收账号列表' => $recycledAccountList,
                '回收账号订单' => $recycledAccountOrders,
                '异常信息' => $exceptionList
            ],
            'meta' => [
                '版权' => 'Kongzu',
                '当前时间' => date('Y-m-d H:i:s'),
                '运行来源' => 'API监控服务',
                '请求来源' => Request::server('HTTP_REFERER', '未知')
            ]
        ], JSON_UNESCAPED_UNICODE);
    }

    /**
     * 修改密码
     *
     * @brief 修改账户密码，确保不会同时多次修改。
     *
     * @param array $account 账户详情。
     * @throws \Exception 如果无法获取锁或修改密码。
     */
    private function changePassword($account)
    {
        // 检查最近的改密记录
        $lastLog = Db::table('monitor_logs')
            ->where('account_name', $account['ac_name'])
            ->where('action', '改密')
            ->where('status', '成功')
            ->order('id', 'desc')
            ->find();

        if ($lastLog) {
            $lastTimestamp = strtotime($lastLog['timestamp']);
            if (time() - $lastTimestamp < 30) {
                throw new \Exception('30秒内不能重复改密或回收');
            }
        }

        // 先检查账号是否已被锁定
        $accountInfo = Account::where('id', $account['id'])->find();
        if ($accountInfo['is_locked'] == 1) {
            // 检查锁定时间，如果超过2分钟，则强制解锁（缩短锁定时间）
            $lockTime = Db::table('monitor_logs')
                ->where('account_name', $account['ac_name'])
                ->where('action', '锁定')
                ->order('id', 'desc')
                ->value('timestamp');

            if ($lockTime && (time() - strtotime($lockTime) > 120)) {
                // 锁定超过2分钟，强制解锁
                Account::where('id', $account['id'])->update(['is_locked' => 0]);

                // 记录强制解锁日志
                Db::table('monitor_logs')->insert([
                    'account_name' => $account['ac_name'],
                    'action' => '强制解锁',
                    'monitor_type' => '密码修改',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'status' => '成功',
                    'error_message' => '超过2分钟的锁定被强制释放'
                ]);
            } else {
                throw new \Exception('无法获取锁，可能正在进行改密');
            }
        }

        // 使用新的字段 is_locked 来避免重复改密
        $lockAcquired = Account::where('id', $account['id'])
            ->where('is_locked', 0) // 确保未被锁定
            ->update(['is_locked' => 1]); // 锁定账号

        if (!$lockAcquired) {
            // 记录获取锁失败日志
            Db::table('monitor_logs')->insert([
                'account_name' => $account['ac_name'],
                'action' => '获取锁',
                'monitor_type' => '密码修改',
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => '失败',
                'error_message' => '无法获取锁，可能正在进行改密'
            ]);

            throw new \Exception('无法获取锁，可能正在进行改密');
        }

        // 记录锁定日志
        Db::table('monitor_logs')->insert([
            'account_name' => $account['ac_name'],
            'action' => '锁定',
            'monitor_type' => '密码修改',
            'timestamp' => date('Y-m-d H:i:s'),
            'status' => '成功',
            'error_message' => null
        ]);

        try {
            $curl = curl_init();

            // 从数据库中获取当前密码
            $currentPassword = $account['ac_password'];

            // 生成随机11位中英文字符的新密码
            $newPassword = $this->generateRandomPassword(11);

            // 查询 tk_steamaccountdata 表获取所需数据
            $steamAccountData = Db::table('tk_steamaccountdata')
                ->where('account_name', $account['ac_name'])
                ->find();

            if (!$steamAccountData) {
                // 记录找不到Steam账号数据的错误日志
                Db::table('monitor_logs')->insert([
                    'account_name' => $account['ac_name'],
                    'action' => '改密',
                    'monitor_type' => '密码修改',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'status' => '失败',
                    'error_message' => '无法找到对应的 Steam 账号数据'
                ]);

                throw new \Exception('无法找到对应的 Steam 账号数据');
            }

            // 从 Config 表中获取 token_api
            $config = Config::find(1);
            $tokenApi = $config ? $config['token_api'] : 'http://154.37.213.41:5001/';

            // 构建完整的 URL
             $url = $tokenApi . '/api/v1/change_password';

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode([
                    "account_name" => $account['ac_name'],
                    "now_password" => $currentPassword,
                    "new_password" => $newPassword,
                    "shared_secret" => $steamAccountData['shared_secret'],
                    "identity_secret" => $steamAccountData['identity_secret'],
                    "device_id" => $steamAccountData['device_id'],
                    "steamid" => $steamAccountData['SteamID'],
                    "refresh_token" => $steamAccountData['RefreshToken']
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            $curlError = curl_error($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($response === false) {
                // 记录cURL请求失败的错误日志
                Db::table('monitor_logs')->insert([
                    'account_name' => $account['ac_name'],
                    'action' => '改密',
                    'monitor_type' => '密码修改',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'status' => '失败',
                    'error_message' => '密码修改失败: cURL请求失败 - ' . $curlError
                ]);

                throw new \Exception('密码修改失败: cURL请求失败 - ' . $curlError);
            }

            // 安全解析JSON响应
            $responseData = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                // 记录JSON解析失败的错误日志
                Db::table('monitor_logs')->insert([
                    'account_name' => $account['ac_name'],
                    'action' => '改密',
                    'monitor_type' => '密码修改',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'status' => '失败',
                    'error_message' => '密码修改失败: 无效的JSON响应 - ' . json_last_error_msg() . ' - 原始响应: ' . substr($response, 0, 100)
                ]);

                throw new \Exception('密码修改失败: 无效的JSON响应 - ' . json_last_error_msg() . ' - 原始响应: ' . substr($response, 0, 100));
            }

            // 检查响应数据结构
            if (!is_array($responseData)) {
                // 记录响应格式错误的日志
                Db::table('monitor_logs')->insert([
                    'account_name' => $account['ac_name'],
                    'action' => '改密',
                    'monitor_type' => '密码修改',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'status' => '失败',
                    'error_message' => '密码修改失败: 响应不是有效的数组格式 - 原始响应: ' . substr($response, 0, 100)
                ]);

                throw new \Exception('密码修改失败: 响应不是有效的数组格式 - 原始响应: ' . substr($response, 0, 100));
            }

            // 检查msg_code字段
            if (!isset($responseData['msg_code'])) {
                // 记录缺少msg_code字段的错误日志
                Db::table('monitor_logs')->insert([
                    'account_name' => $account['ac_name'],
                    'action' => '改密',
                    'monitor_type' => '密码修改',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'status' => '失败',
                    'error_message' => '密码修改失败: 响应中缺少msg_code字段 - 原始响应: ' . json_encode($responseData)
                ]);

                throw new \Exception('密码修改失败: 响应中缺少msg_code字段 - 原始响应: ' . json_encode($responseData));
            }

            // 检查响应状态
            if ($responseData['msg_code'] !== 0) {
                $errorMessage = isset($responseData['message']) ? $responseData['message'] : '未知错误';

                // 记录API返回错误的日志
                Db::table('monitor_logs')->insert([
                    'account_name' => $account['ac_name'],
                    'action' => '改密',
                    'monitor_type' => '密码修改',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'status' => '失败',
                    'error_message' => '密码修改失败: ' . $errorMessage . ' (HTTP状态码: ' . $httpCode . ')'
                ]);

                throw new \Exception('密码修改失败: ' . $errorMessage);
            }

            // 更新数据库中所有 ac_name 为 $account['ac_name'] 的新密码
            Account::where('ac_name', $account['ac_name'])->update(['ac_password' => $newPassword]);

            // 记录改密日志
            Db::table('monitor_logs')->insert([
                'account_name' => $account['ac_name'],
                'action' => '改密',
                'monitor_type' => '密码修改',
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => '成功',
                'old_password' => $currentPassword,
                'new_password' => $newPassword,
                'error_message' => null
            ]);
        } catch (\Exception $e) {
            // 处理异常并释放锁
            Account::where('id', $account['id'])->update(['is_locked' => 0]);

            // 记录解锁日志
            Db::table('monitor_logs')->insert([
                'account_name' => $account['ac_name'],
                'action' => '解锁',
                'monitor_type' => '密码修改',
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => '成功',
                'error_message' => '因异常自动解锁'
            ]);

            throw $e;
        }

        // 释放锁
        Account::where('id', $account['id'])->update(['is_locked' => 0]);

        // 记录解锁日志
        Db::table('monitor_logs')->insert([
            'account_name' => $account['ac_name'],
            'action' => '解锁',
            'monitor_type' => '密码修改',
            'timestamp' => date('Y-m-d H:i:s'),
            'status' => '成功',
            'error_message' => null
        ]);
    }

    /**
     * 生成随机密码
     *
     * @brief 生成高强度、难以爆破的随机密码，包含大小写字母和数字，但不含特殊字符。
     *
     * @param int $length 要生成的密码长度。
     * @return string 生成的随机密码。
     */
    private function generateRandomPassword($length = 11)
    {
        // 定义不同类型的字符集
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '**********';

        // 确保密码至少包含每种类型的字符
        $password = '';
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];

        // 合并所有字符集
        $allChars = $lowercase . $uppercase . $numbers;
        $allCharsLength = strlen($allChars);

        // 填充剩余长度的随机字符
        for ($i = 3; $i < $length; $i++) {
            $password .= $allChars[random_int(0, $allCharsLength - 1)];
        }

        // 打乱密码字符顺序，避免固定模式
        $passwordArray = str_split($password);
        shuffle($passwordArray);

        return implode('', $passwordArray);
    }

    /**
     * 清理过期的锁定状态
     *
     * @brief 清理超过2分钟的锁定状态，防止锁定状态卡住
     */
    private function clearExpiredLocks()
    {
        try {
            // 查找所有被锁定的账号
            $lockedAccounts = Account::where('is_locked', 1)->select();

            foreach ($lockedAccounts as $account) {
                // 检查锁定时间
                $lockTime = Db::table('monitor_logs')
                    ->where('account_name', $account['ac_name'])
                    ->where('action', '锁定')
                    ->order('id', 'desc')
                    ->value('timestamp');

                if ($lockTime && (time() - strtotime($lockTime) > 120)) {
                    // 锁定超过2分钟，强制解锁
                    Account::where('id', $account['id'])->update(['is_locked' => 0]);

                    // 记录强制解锁日志
                    Db::table('monitor_logs')->insert([
                        'account_name' => $account['ac_name'],
                        'action' => '批量强制解锁',
                        'monitor_type' => '锁定清理',
                        'timestamp' => date('Y-m-d H:i:s'),
                        'status' => '成功',
                        'error_message' => '清理超过2分钟的过期锁定'
                    ]);
                }
            }
        } catch (\Exception $e) {
            // 记录清理锁定失败的日志
            Db::table('monitor_logs')->insert([
                'account_name' => '系统',
                'action' => '清理锁定',
                'monitor_type' => '锁定清理',
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => '失败',
                'error_message' => '清理过期锁定失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 监控过期账户
     *
     * @brief 监控已过期但未下线的账户。
     *
     * @return string JSON格式的监控结果。
     */
    public function monitorExpiredAccounts()
    {
        // 验证参数 key
        $key = Request::param('key');
        if ($key !== 'Kongzu') {
            return json_encode([
                'code' => 403,
                'msg' => '无效的密钥',
            ], JSON_UNESCAPED_UNICODE);
        }

        // 查询配置
        $config = Config::find(1);
        $allowExpireNoOffline = $config ? $config['allow_expire_no_offline'] : 0;

        if ($allowExpireNoOffline == 1) {
            // ✅ 开启到期不下线模式：不进行自动改密，仅返回等待状态的账号信息
            $accounts = Account::where([
                'expire_status' => 1  // 查找等待重新分配的过期账号
            ])->select();

            $waitingAccounts = [];
            foreach ($accounts as $account) {
                if ($account['ac_states'] == 0 && time() > $account['exit_time']) {
                    $waitingAccounts[] = $account['ac_name'];
                }
            }

            return json([
                'code' => 200,
                'msg' => '到期不下线模式：账号等待重新分配',
                'data' => [
                    '等待重新分配的过期账号' => $waitingAccounts,
                    '说明' => '这些账号将在新用户租用相同游戏时自动重新分配'
                ],
                'meta' => [
                    '版权' => 'Kongzu',
                    '当前时间' => date('Y-m-d H:i:s'),
                    '运行来源' => 'API监控服务 - 到期不下线模式'
                ]
            ], JSON_UNESCAPED_UNICODE);
        } else {
            // ✅ 关闭模式：此接口不执行任何操作
            return json([
                'code' => 200,
                'msg' => '到期不下线功能已关闭，无需处理过期账号',
                'data' => [],
                'meta' => [
                    '版权' => 'Kongzu',
                    '当前时间' => date('Y-m-d H:i:s'),
                    '运行来源' => 'API监控服务'
                ]
            ], JSON_UNESCAPED_UNICODE);
        }

        // 以下代码已被禁用，改密操作移至分配时执行
        /*
        if ($allowExpireNoOffline == 1) {
            // 首先清理超时的锁定状态
            $this->clearExpiredLocks();

            // ✅ 只查找待处理状态的账号
            $accounts = Account::where([
                'modify' => 1,
                'expire_status' => 1  // 只处理待处理状态的账号
            ])->select();
            $modifiedAccounts = [];

            foreach ($accounts as $account) {
                if ($account['ac_states'] == 0) {
                    try {
                        // 执行改密操作
                        $this->changePassword($account);

                        // ✅ 改密成功后设置为已处理完成状态
                        Account::where('id', $account['id'])->update([
                            'modify' => 0,
                            'expire_status' => 2  // 设置为已处理完成
                        ]);
                        $modifiedAccounts[] = $account['ac_name'];

                        // 记录改密日志
                        Db::table('monitor_logs')->insert([
                            'account_name' => $account['ac_name'],
                            'action' => '改密',
                            'monitor_type' => '到期未下线监控',
                            'timestamp' => date('Y-m-d H:i:s'),
                            'status' => '成功',
                            'error_message' => '改密完成，设置为已处理状态'
                        ]);
                    } catch (\Exception $e) {
                        $errorMessage = $e->getMessage() ?: '未知错误';

                        // 如果是锁定相关错误，保持待处理状态让下次重试
                        if (strpos($errorMessage, '无法获取锁') === false &&
                            strpos($errorMessage, '30秒内不能重复改密') === false) {
                            // ✅ 非锁定错误，设置为已处理完成避免无限重试
                            Account::where('id', $account['id'])->update([
                                'modify' => 0,
                                'expire_status' => 2
                            ]);
                        }

                        $exceptionList[] = [
                            'account' => $account['ac_name'],
                            'error' => $errorMessage
                        ];

                        // 记录异常日志
                        Db::table('monitor_logs')->insert([
                            'account_name' => $account['ac_name'],
                            'action' => '改密',
                            'monitor_type' => '到期未下线监控',
                            'timestamp' => date('Y-m-d H:i:s'),
                            'status' => '失败',
                            'error_message' => $errorMessage
                        ]);
                    }
                }
            }

            return json_encode([
                'code' => 200,
                'msg' => '到期未下线账号监控完成',
                'data' => [
                    '修改密码的账号' => $modifiedAccounts,
                    '异常信息' => $exceptionList ?? []
                ]
            ], JSON_UNESCAPED_UNICODE);
        }

        return json_encode([
            'code' => 200,
            'msg' => '未启用到期账号不下线功能',
        ], JSON_UNESCAPED_UNICODE);
        */
    }

    /**
     * 更新账户访问记录
     *
     * @brief 重置在线账户池中所有账户的访问令牌限制。
     *
     * 每次调用API方法时，重置所有账户的token_limit为0。
     */
    public function updateAccountAccess()
    {
        try {
            // 查询 tk_account 表中所有账户并将它们的 token_limit 重置为 0
            Db::table('tk_account')->where('id', '>', 0)->update(['token_limit' => 0]);

            // 返回 JSON 响应
            return json([
                'code' => 200,
                'msg' => '所有账号令牌刷新完成'
            ], JSON_UNESCAPED_UNICODE);
        } catch (\Exception $e) {
            // 捕获异常并返回错误信息
            return json([
                'code' => 500,
                'msg' => '更新账号令牌时发生错误: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 更新账户密码
     *
     * @brief 根据传入的 key、账号和密码更新账户密码，并记录远程 API 同步日志。
     *
     * @return string JSON格式的更新结果。
     */
    public function updateAccountPassword()
    {
        // 获取请求参数
        $key = Request::param('key');
        $accountName = Request::param('account');
        $newPassword = Request::param('password');

        // 检查参数是否为空
        if (empty($key) || empty($accountName) || empty($newPassword)) {
            return json_encode([
                'code' => 400,
                'msg' => '参数不能为空',
            ], JSON_UNESCAPED_UNICODE);
        }

        // 验证密钥
        if ($key !== 'Kongzu') {
            return json_encode([
                'code' => 403,
                'msg' => '无效的密钥',
            ], JSON_UNESCAPED_UNICODE);
        }

        try {
            // 检查账号是否存在
            $accountExists = Account::where('ac_name', $accountName)->count() > 0;
            if (!$accountExists) {
                return json_encode([
                    'code' => 404,
                    'msg' => '账号不存在',
                ], JSON_UNESCAPED_UNICODE);
            }

            // 更新所有 ac_name 为传入账号的密码
            Account::where('ac_name', $accountName)->update(['ac_password' => $newPassword]);

            // 记录远程 API 同步日志
            Db::table('monitor_logs')->insert([
                'account_name' => $accountName,
                'action' => '更新密码',
                'monitor_type' => 'API密码回传更新',
                'timestamp' => date('Y-m-d H:i:s'),
                'new_password' => $newPassword,
                'status' => '成功',
                'error_message' => null
            ]);

            // 返回成功响应
            return json_encode([
                'code' => 200,
                'msg' => '密码更新成功',
            ], JSON_UNESCAPED_UNICODE);
        } catch (\Exception $e) {
            // 捕获异常并记录错误日志
            Db::table('monitor_logs')->insert([
                'account_name' => $accountName,
                'action' => '更新密码',
                'monitor_type' => 'API密码回传更新',
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => '失败',
                'error_message' => $e->getMessage()
            ]);

            // 返回错误响应
            return json_encode([
                'code' => 500,
                'msg' => '更新密码时发生错误: ' . $e->getMessage(),
            ], JSON_UNESCAPED_UNICODE);
        }
    }

     /**
     * 获取邮箱用户列表并同步到数据库
     *
     * 通过调用外部邮箱API获取所有邮箱用户信息，并将新用户同步到本地数据库。
     * 仅插入尚未存在于数据库中的用户记录。
     *
     * 支持的请求方法：GET、POST
     *
     * @return \think\Response 返回同步结果的JSON响应
     */
    public function getEmailUserList()
    {
        // 获取当前请求方法
        $method = Request::method();

        // 支持 GET 和 POST 请求
        if (in_array($method, ['GET', 'POST'])) {

            // 获取配置参数
            $config = Config::find(1);
            $url = "http://" . $config['email_api'] . "/plugin?action=a&name=mail_sys&s=get_all_user";

            // 初始化 cURL 会话
            $ch = curl_init();

            // 设置 cURL 选项
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true); // 设置请求方法为 POST
            curl_setopt($ch, CURLOPT_POSTFIELDS, ''); // 设置请求体为空
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回响应而不是输出
            curl_setopt($ch, CURLOPT_ENCODING, "gzip, deflate"); // 启用压缩响应

            // 设置请求头
            $headers = [
                "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0",
                "Accept: */*",
                "Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
                "Accept-Encoding: gzip, deflate",
                "Referer: http://" . $config['email_api'],
                "x-http-token: " . $config['email_tk'],
                "X-Requested-With: XMLHttpRequest",
                "Origin: http://" . $config['email_api'],
                "Connection: keep-alive",
                "Cookie: " . $config['email_ck'],
                "Priority: u=0",
                "Content-Length: 0",
                "Pragma: no-cache",
                "Cache-Control: no-cache"
            ];
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            // 执行 cURL 请求
            $response = curl_exec($ch);

            // 检查cURL是否有错误
            if ($response === false) {
                $error = curl_error($ch);
                curl_close($ch);
                return json(['error' => 'cURL Error: ' . $error], 500);
            }

            // 关闭 cURL 会话
            curl_close($ch);

            // 尝试解析 JSON 响应
            $responseData = json_decode($response, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                return json(['error' => 'JSON Decode Error: ' . json_last_error_msg(),'data'=>$response], 500);
            }

            // 检查新的响应结构
            if (!isset($responseData['data']) || !is_array($responseData['data'])) {
                return json(['error' => 'Invalid data format received from API.'], 400);
            }

            // 提取data数组
            $userData = $responseData['data'];

            // 开始数据库操作
            try {
                // 开启事务
                Db::startTrans();

                // 获取所有现有的用户名
                $existingUsernames = Db::name('userlist')->column('username');

                // 转换为键值对数组，便于快速查找
                $existingUsernames = array_flip($existingUsernames);

                $newUsers = [];

                foreach ($userData as $user) {
                    // 验证必要字段是否存在
                    if (
                        !isset(
                            $user['full_name'],
                            $user['username'],
                            $user['quota'],
                            $user['created'],
                            $user['modified'],
                            $user['active'],
                            $user['is_admin']
                        )
                    ) {
                        // 可以选择记录日志或跳过该记录
                        continue;
                    }

                    $username = $user['username'];

                    // 检查用户是否已存在
                    if (isset($existingUsernames[$username])) {
                        // 用户已存在，忽略
                        continue;
                    }

                    // 准备新用户数据
                    $newUsers[] = [
                        'full_name' => $user['full_name'],
                        'username' => $username,
                        'quota' => $user['quota'],
                        'created' => $user['created'],
                        'modified' => $user['modified'],
                        'active' => $user['active'],
                        'is_admin' => $user['is_admin'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                }

                // 如果有新用户，执行批量插入
                if (!empty($newUsers)) {
                    Db::name('userlist')->insertAll($newUsers);
                }

                // 提交事务
                Db::commit();

                // 返回原始响应数据
                return json($responseData);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json(['error' => 'Database Error: ' . $e->getMessage()], 500);
            }
        }
    }

    /**
     * 分配时改密方法
     *
     * @brief 专门用于重新分配过期账号时的改密操作
     *
     * @param array $account 账号信息
     * @return bool 改密是否成功
     */
    public function changePasswordForReassignment($account)
    {
        try {
            // 记录开始改密日志
            Db::table('monitor_logs')->insert([
                'account_name' => $account['ac_name'],
                'action' => '重新分配改密开始',
                'monitor_type' => '账号重新分配',
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => '开始',
                'error_message' => '开始为重新分配执行改密'
            ]);

            // 执行改密操作
            $this->changePassword($account);

            // 记录改密成功日志
            Db::table('monitor_logs')->insert([
                'account_name' => $account['ac_name'],
                'action' => '重新分配改密完成',
                'monitor_type' => '账号重新分配',
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => '成功',
                'error_message' => '重新分配改密成功'
            ]);

            return true;
        } catch (\Exception $e) {
            // 记录改密失败日志
            Db::table('monitor_logs')->insert([
                'account_name' => $account['ac_name'],
                'action' => '重新分配改密失败',
                'monitor_type' => '账号重新分配',
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => '失败',
                'error_message' => '重新分配改密失败: ' . $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 统一账号分配方法
     *
     * @brief 统一处理账号分配，智能处理expire_status状态
     *
     * @param string $acName 账号名称
     * @param int $userId 用户ID
     * @param int $exitTime 到期时间戳
     * @param array $extraFields 额外字段
     * @return bool 分配是否成功
     */
    public static function assignAccount($acName, $userId, $exitTime, $extraFields = [])
    {
        // 获取配置信息
        $config = Config::find(1);
        $allowExpireNoOffline = $config ? $config['allow_expire_no_offline'] : 0;

        // 获取当前账号信息以判断是否为过期账号重新分配
        $currentAccount = Account::where(['ac_name' => $acName, 'goods_Type' => 1])->find();
        $isExpiredReassignment = $currentAccount && ($currentAccount['expire_status'] ?? 0) == 1;

        // 基础分配字段
        $updateData = [
            'ac_states' => 0,
            'ac_uid' => $userId,
            'exit_time' => $exitTime,
            'token_limit' => 0
        ];

        // ✅ 智能处理expire_status和modify字段
        if ($allowExpireNoOffline == 0 || $isExpiredReassignment) {
            // 关闭模式或重新分配过期账号时才重置状态
            $updateData['expire_status'] = 0;
            $updateData['modify'] = 0;
        }
        // 开启模式下的正常分配不重置expire_status，保持原状态

        // 合并额外字段
        $updateData = array_merge($updateData, $extraFields);

        // 更新所有相同账号名称的在线账号
        $result = Account::where(['ac_name' => $acName, 'goods_Type' => 1])->update($updateData);

        // 记录分配日志
        $logMessage = $isExpiredReassignment ? "重新分配过期账号给用户: {$userId}" : "分配给用户: {$userId}";
        Db::table('monitor_logs')->insert([
            'account_name' => $acName,
            'action' => $isExpiredReassignment ? '过期账号重新分配' : '账号分配',
            'monitor_type' => '账号管理',
            'timestamp' => date('Y-m-d H:i:s'),
            'status' => $result ? '成功' : '失败',
            'error_message' => $result ? $logMessage : '分配失败'
        ]);

        return $result !== false;
    }

    /**
     * 统一账号回收方法
     *
     * @brief 统一处理账号回收，确保正确重置所有状态
     *
     * @param string $acName 账号名称
     * @param array $extraFields 额外字段
     * @return bool 回收是否成功
     */
    public static function recycleAccount($acName, $extraFields = [])
    {
        // 基础回收字段
        $updateData = [
            'ac_uid' => null,
            'exit_time' => null,
            'ac_sell' => 1,
            'ac_states' => 1,
            'ac_vip' => 0,
            'expire_status' => 0,  // ✅ 重置过期状态
            'modify' => 0,         // ✅ 重置改密标记
            'token_limit' => 0,
            'is_locked' => 0
        ];

        // 合并额外字段
        $updateData = array_merge($updateData, $extraFields);

        // 更新所有相同账号名称的账号
        $result = Account::where('ac_name', $acName)->update($updateData);

        // 记录回收日志
        Db::table('monitor_logs')->insert([
            'account_name' => $acName,
            'action' => '账号回收',
            'monitor_type' => '账号管理',
            'timestamp' => date('Y-m-d H:i:s'),
            'status' => $result ? '成功' : '失败',
            'error_message' => $result ? '账号已回收' : '回收失败'
        ]);

        return $result !== false;
    }
}
