<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>标签管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>
  <div class="layui-card layadmin-header">
    <div class="layui-breadcrumb" lay-filter="breadcrumb">
      <a lay-href="">主页</a>
      <a><cite>标签管理</cite></a>
    </div>
  </div>
  
  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-header">
        <button class="layui-btn layui-btn-sm" onclick="openAdd()">添加标签</button>
      </div>
      <div class="layui-card-body">
        <table id="tagList" lay-filter="tagList"></table>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'table'], function(){
    var table = layui.table
      ,$ = layui.jquery;  // 添加 jQuery 引用
    
    table.render({
      elem: '#tagList'
      ,url: '{:url("/admin/tags/list")}'
      ,cols: [[
        {field:'id', title: 'ID', width:80}
        ,{field:'name', title: '标签名称'}
        ,{title: '操作', width:150, templet: function(d){
          return '<a class="layui-btn layui-btn-xs" onclick="openEdit('+d.id+')">编辑</a>'+
                 '<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('+d.id+')">删除</a>';
        }}
      ]]
      ,page: true
    });
  });

  function openAdd(){
    layer.open({
      type: 2,
      title: '添加标签',
      area: ['500px', '300px'],
      content: '{:url("tags/add")}'
    });
  }

  function openEdit(id){
    layer.open({
      type: 2,
      title: '编辑标签',
      area: ['500px', '300px'],
      content: '{:url("tags/edit")}?id='+id
    });
  }

  function del(id){
    layer.confirm('确定删除吗？', function(index){
      layui.jquery.post('{:url("tags/delete")}', {id: id}, function(res){  // 使用 layui.jquery
        if(res.code == 0){
          layer.msg('删除成功');
          layui.table.reload('tagList');
          layer.close(index);  // 关闭确认框
        }else{
          layer.msg(res.msg);
        }
      });
    });
  }
  </script>
</body>
</html> 