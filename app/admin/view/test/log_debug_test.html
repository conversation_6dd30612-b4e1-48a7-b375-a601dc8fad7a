<!DOCTYPE html>
<html>
<head>
    <title>logCdkDebugInfo方法测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 3px; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>logCdkDebugInfo方法测试</h1>
        
        <div class="test-section">
            <h3>1. 方法功能测试</h3>
            <button onclick="testLogMethod()">测试logCdkDebugInfo方法</button>
            <div id="testResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 方法说明</h3>
            <div class="result info">
                <h4>logCdkDebugInfo方法的功能：</h4>
                <ul>
                    <li><strong>记录调试信息：</strong>用于调试和排查卡密相关问题</li>
                    <li><strong>文件日志：</strong>所有调试信息都会记录到日志文件</li>
                    <li><strong>数据库日志：</strong>重要操作会额外记录到数据库</li>
                    <li><strong>详细信息：</strong>包含时间戳、请求ID、管理员ID、IP地址等</li>
                </ul>
                
                <h4>方法签名：</h4>
                <div class="code-block">private function logCdkDebugInfo($message, $data = [])</div>
                
                <h4>参数说明：</h4>
                <ul>
                    <li><strong>$message：</strong>调试信息的描述</li>
                    <li><strong>$data：</strong>相关的数据数组（可选）</li>
                </ul>
                
                <h4>重要操作类型（会记录到数据库）：</h4>
                <ul>
                    <li>卡密封禁</li>
                    <li>卡密解封</li>
                    <li>卡密生成</li>
                    <li>卡密删除</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. 使用示例</h3>
            <div class="result">
                <h4>在代码中的使用示例：</h4>
                
                <h5>1. 普通调试信息：</h5>
                <div class="code-block">$this->logCdkDebugInfo('开始处理卡密', [
    'cdk_code' => $cdkCode,
    'user_id' => $userId,
    'action' => 'process_cdk'
]);</div>
                
                <h5>2. 重要操作记录：</h5>
                <div class="code-block">$this->logCdkDebugInfo('卡密封禁', [
    'cdk_code' => $cdkCode,
    'reason' => $reason,
    'admin_id' => $adminId
]);</div>
                
                <h5>3. 错误信息记录：</h5>
                <div class="code-block">$this->logCdkDebugInfo('卡密处理失败', [
    'cdk_code' => $cdkCode,
    'error' => $e->getMessage(),
    'trace' => $e->getTraceAsString()
]);</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. 日志查看</h3>
            <div class="result warning">
                <h4>如何查看日志：</h4>
                <ul>
                    <li><strong>文件日志：</strong>查看 <code>runtime/log/</code> 目录下的日志文件</li>
                    <li><strong>数据库日志：</strong>查看 <code>tk_admin_logs</code> 表中的记录</li>
                    <li><strong>日志格式：</strong>包含完整的调试信息和上下文数据</li>
                </ul>
                
                <h4>日志信息包含：</h4>
                <ul>
                    <li>时间戳</li>
                    <li>调试消息</li>
                    <li>相关数据</li>
                    <li>唯一请求ID</li>
                    <li>管理员ID</li>
                    <li>IP地址</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testLogMethod() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试logCdkDebugInfo方法...';
            
            fetch('/admin/TestController/testLogCdkDebugInfo', {
                method: 'GET',
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ 测试成功！</h4>
                        <p><strong>消息：</strong>${data.msg}</p>
                        <p><strong>测试完成：</strong>${data.data.test_completed ? '是' : '否'}</p>
                        
                        <h5>测试的日志类型：</h5>
                        <ul>
                            <li><strong>普通调试：</strong>${data.data.log_types_tested.normal_debug}</li>
                            <li><strong>重要操作：</strong>${data.data.log_types_tested.important_action}</li>
                        </ul>
                        
                        <h5>日志查看位置：</h5>
                        <ul>
                            <li><strong>文件日志：</strong>${data.data.check_logs.file_log}</li>
                            <li><strong>数据库日志：</strong>${data.data.check_logs.database_log}</li>
                        </ul>
                        
                        <h5>测试数据：</h5>
                        <div class="code-block">${JSON.stringify(data.data.test_data, null, 2)}</div>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ 测试失败！</h4>
                        <p><strong>错误消息：</strong>${data.msg}</p>
                        ${data.error_details ? `
                            <h5>错误详情：</h5>
                            <ul>
                                <li><strong>错误信息：</strong>${data.error_details.error_message}</li>
                                <li><strong>错误文件：</strong>${data.error_details.error_file}</li>
                                <li><strong>错误行号：</strong>${data.error_details.error_line}</li>
                            </ul>
                        ` : ''}
                    `;
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ 请求失败！</h4>
                    <p><strong>错误：</strong>${error.message}</p>
                `;
            });
        }
    </script>
</body>
</html>
