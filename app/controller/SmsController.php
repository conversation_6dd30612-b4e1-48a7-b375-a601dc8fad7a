<?php
namespace app\controller;

use app\BaseController;
use think\Request;
use AlibabaCloud\SDK\Dysmsapi\*********\Dysmsapi;
use AlibabaCloud\SDK\Dysmsapi\*********\Models\SendSmsRequest;
use Darabonba\OpenApi\Models\Config as OpenApiConfig;
use app\admin\model\Config as AdminConfig;

class SmsController
{
    /**
     * 发送登录验证码接口
     * @param string $phone
     * @param string $code
     * @return array
     */
    public function sendLoginCode($phone, $code)
    {
        $cn = AdminConfig::find(1);

        // 参数校验
        if (empty($phone)) {
            return ['status' => 'error', 'message' => '手机号不能为空'];
        }

        // 配置阿里云 AccessKey 信息
        $accessKeyId = $cn['ali_id'];
        $accessKeySecret = $cn['ali_ck'];

        // 配置短信签名与模板
        $signName = $cn['sign_Name'];
        $templateCode = $cn['template_Code'];

        // 初始化阿里云短信服务客户端
        $config = new OpenApiConfig([
            "accessKeyId"     => $accessKeyId,
            "accessKeySecret" => $accessKeySecret,
        ]);
        $config->endpoint = "dysmsapi.aliyuncs.com";
        $client = new Dysmsapi($config);

        // 构造发送请求对象
        $requestObj = new SendSmsRequest([
            'phoneNumbers'  => $phone,
            'signName'      => $signName,
            'templateCode'  => $templateCode,
            'templateParam' => json_encode(["code" => $code], JSON_UNESCAPED_UNICODE),
        ]);

        try {
            $response = $client->sendSms($requestObj);
            $result = $response->body;

            if (isset($result->code)) {
                if ($result->code === "OK") {
                    // 短信发送成功
                    return [
                        'status' => 'success',
                        'message' => '短信发送成功',
                    ];
                } else {
                    // 短信发送失败，返回错误信息
                    return [
                        'status' => 'error',
                        'message' => '短信发送失败: ' . ($result->Message ?? '未知错误')
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'message' => '短信接口响应异常，缺少必要字段',
                    'data' => $result
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => '请求异常',
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
}
