/**
 * 骨架屏加载动画样式
 * 用途：为图片懒加载提供优雅的占位效果
 * 创建时间：2025-06-23
 */

/* ================================
   基础骨架屏动画
   ================================ */

/* 闪烁动画关键帧 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 脉冲动画关键帧 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 波浪动画关键帧 */
@keyframes wave {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* ================================
   游戏卡片骨架屏
   ================================ */

.skeleton-placeholder {
  position: relative;
  overflow: hidden;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: wave 2s infinite;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.skeleton-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: shimmer 2s infinite;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  z-index: 1;
}

.skeleton-game-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.skeleton-text {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-line {
  height: 0.75rem;
  background: rgba(156, 163, 175, 0.3);
  border-radius: 0.375rem;
  animation: pulse 2s infinite;
}

.skeleton-line-1 {
  width: 100%;
  animation-delay: 0.1s;
}

.skeleton-line-2 {
  width: 70%;
  animation-delay: 0.2s;
}

/* ================================
   轮播图骨架屏
   ================================ */

.banner-skeleton {
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 50%, #f0f0f0 100%);
  background-size: 400% 400%;
  animation: wave 3s ease-in-out infinite;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.banner-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2.5s infinite;
}

.banner-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.banner-icon {
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.banner-text {
  color: #9ca3af;
  font-size: 1rem;
  font-weight: 500;
  animation: pulse 2s infinite;
  animation-delay: 0.3s;
}

/* ================================
   专栏图片骨架屏
   ================================ */

.column-skeleton {
  background: linear-gradient(90deg, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%);
  background-size: 200% 100%;
  animation: wave 2.2s infinite;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.column-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.5),
    transparent
  );
  animation: shimmer 2.2s infinite;
}

.column-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
  text-align: center;
  padding: 1rem;
}

.column-icon {
  margin-bottom: 0.75rem;
  animation: pulse 2s infinite;
}

.column-title {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  animation: pulse 2s infinite;
  animation-delay: 0.2s;
}

/* ================================
   图片加载完成后的效果
   ================================ */

.game-image.loaded,
.banner-image.loaded,
.column-image.loaded {
  opacity: 1 !important;
}

.skeleton-placeholder.hidden,
.banner-skeleton.hidden,
.column-skeleton.hidden {
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* ================================
   响应式适配
   ================================ */

@media (max-width: 768px) {
  .skeleton-game-icon {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }
  
  .skeleton-line {
    height: 0.625rem;
  }
  
  .banner-icon {
    font-size: 2.5rem;
  }
  
  .banner-text {
    font-size: 0.875rem;
  }
  
  .column-icon {
    font-size: 2rem;
  }
  
  .column-title {
    font-size: 0.75rem;
  }
}

/* ================================
   深色模式支持
   ================================ */

@media (prefers-color-scheme: dark) {
  .skeleton-placeholder {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  }
  
  .banner-skeleton {
    background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #374151 100%);
  }
  
  .column-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  }
  
  .skeleton-line {
    background: rgba(107, 114, 128, 0.4);
  }
  
  .banner-text,
  .column-title {
    color: #9ca3af;
  }
}

/* ================================
   高性能优化
   ================================ */

.skeleton-placeholder,
.banner-skeleton,
.column-skeleton {
  will-change: background-position;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.skeleton-shimmer,
.banner-shimmer,
.column-shimmer {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}
