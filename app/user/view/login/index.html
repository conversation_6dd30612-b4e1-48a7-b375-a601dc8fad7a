<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>{$system.sy_title}</title>
    <meta name="description" content="{$system.sy_des}" />
    <meta name="keywords" content="{$system.sy_key}" />
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/gt.js"></script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .animate-fadeIn {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .form-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .brand-gradient {
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
        }
        
        .transition-all {
            transition: all 0.3s ease;
        }

        .dark body {
            background-color: #111827;
            color: #e5e7eb;
        }
        .dark .bg-white {
            background-color: #1f2937;
        }
        .dark .text-gray-800 {
            color: #f9fafb;
        }
        .dark .text-gray-700 {
            color: #e5e7eb;
        }
        .dark .text-gray-500 {
            color: #9ca3af;
        }
        .dark .border-gray-200 {
            border-color: #374151;
        }
        .dark .border-gray-300 {
            border-color: #4b5563;
        }
        .dark input, .dark select, .dark textarea {
            background-color: #374151;
            border-color: #4b5563;
            color: #e5e7eb;
        }
        .dark .bg-gray-50 {
            background-color: #374151;
        }
        .dark .form-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 640px) {
            .geetest_holder.geetest_wind {
                width: 100% !important;
            }
        }

        .btn-gradient {
            background-image: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            background-image: linear-gradient(135deg, #4338CA 0%, #6D28D9 100%);
            transform: translateY(-1px);
        }

        input:focus {
            animation: pulse 1s;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4); }
            70% { box-shadow: 0 0 0 4px rgba(99, 102, 241, 0); }
            100% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0); }
        }
        
        /* 页眉样式 */
        .header-shadow {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-link {
            transition: all 0.3s ease;
        }
        
        .header-link:hover {
            color: #4F46E5;
            transform: translateY(-2px);
        }
    </style>
    <script>
    // 在<head>标签内添加
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('dark');
    }
    </script>
</head>
<body class="bg-gray-50 min-h-screen flex flex-col">
    <!-- 新增：页眉 -->
    <header class="w-full bg-white dark:bg-gray-900 header-shadow">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <!-- <div class="brand-gradient p-2 rounded-full mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                </div> -->
                <span class="text-xl font-bold text-gray-800 dark:text-white">租号猫</span>
            </div>
            <div class="flex items-center space-x-6">
                <!-- <a href="#" class="text-gray-600 dark:text-gray-300 header-link">品牌文化</a> -->
                <a href="/" class="text-gray-600 dark:text-gray-300 header-link">返回首页</a>
            </div>
        </div>
    </header>

    <div id="pageLoader" class="fixed inset-0 flex items-center justify-center bg-white dark:bg-gray-900 z-50">
        <div class="brand-gradient p-3 rounded-full animate-pulse">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
        </div>
    </div>

    <div class="flex-grow flex items-center justify-center p-4">
        <div class="w-full max-w-md animate-fadeIn px-4 sm:px-0">
            <!-- 品牌标识 -->
            <div class="text-center mb-8">
                <div class="brand-gradient inline-block p-3 rounded-full mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">租号猫 - {$system.sy_name}</h1>
                <p class="text-gray-500">安全登录访问您的账户</p>
            </div>
            
            <!-- 主卡片 -->
            <div class="bg-white rounded-xl form-shadow p-6">
                <!-- 选项卡 -->
                <div class="flex justify-center border-b border-gray-200 mb-6">
                    <button class="type-switch px-4 py-2 text-sm font-medium transition-all relative active" data-type="login">
                        登录
                        <span class="absolute bottom-0 left-0 w-full h-0.5 bg-indigo-600 transition-all"></span>
                    </button>
                    <button class="type-switch px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 transition-all relative" data-type="register">
                        注册
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all"></span>
                    </button>
                    <button class="type-switch px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 transition-all relative" data-type="reset">
                        找回密码
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 transition-all"></span>
                    </button>
                </div>

                <!-- 登录表单 -->
                <form id="loginForm" class="form-container active space-y-4">
                    <div>
                        <label for="login-username" class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input id="login-username" type="text" name="username" class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all hover:border-indigo-300" placeholder="请输入手机号" autocomplete="off" />
                        </div>
                    </div>
                    
                    <div>
                        <label for="login-password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input id="login-password" type="password" name="password" class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all hover:border-indigo-300" placeholder="请输入密码" autocomplete="off" />
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mt-1">
                        <div class="flex items-center">
                            <input id="remember-username" name="remember-username" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="remember-username" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                记住账号
                            </label>
                        </div>
                        <div class="text-sm">
                            <span class="cursor-pointer text-indigo-600 hover:text-indigo-500 type-switch-link" data-type="reset">忘记密码?</span>
                        </div>
                    </div>
                    
                    <button type="button" id="loginSubmit" class="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all shadow-md hover:shadow-lg">
                        登录
                    </button>
                    
                    <div class="relative mt-3">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-200"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">快速登录</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3 mt-3">
                        <button type="button" id="wechatQuickLogin" class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                            <svg class="w-5 h-5 mr-2 text-green-500" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8.5,13.5A1.5,1.5 0 0,0 7,15C7,16.66 8.34,18 10,18C11.66,18 13,16.66 13,15V11H18V9H13V5H11V13.5C11,14.33 10.33,15 9.5,15C8.67,15 8,14.33 8,13.5V9H6V13.5A3.5,3.5 0 0,0 9.5,17A3.5,3.5 0 0,0 13,13.5V11H18V9H13V5H11V9H8V13.5Z" />
                            </svg>
                            微信登录
                        </button>
                        <button type="button" id="qqQuickLogin" class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                            <svg class="w-5 h-5 mr-2 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,2C6.486,2,2,6.486,2,12c0,5.514,4.486,10,10,10c5.514,0,10-4.486,10-10C22,6.486,17.514,2,12,2z M15.411,14.918 c-0.184,0.312-0.535,0.469-0.881,0.389l-2.13-0.493c-0.184,0.182-0.378,0.355-0.584,0.514l0.536,2.113 c0.083,0.328-0.026,0.676-0.283,0.887c-0.26,0.213-0.621,0.25-0.921,0.094l-1.932-1.008c-0.205,0.021-0.413,0.033-0.624,0.033 c-0.211,0-0.419-0.012-0.624-0.033l-1.932,1.008c-0.302,0.157-0.662,0.119-0.921-0.094c-0.257-0.211-0.365-0.559-0.283-0.887 l0.536-2.113c-0.206-0.159-0.4-0.332-0.584-0.514l-2.13,0.493c-0.343,0.08-0.697-0.077-0.881-0.389 c-0.183-0.312-0.167-0.701,0.042-0.998l1.369-1.95c-0.054-0.249-0.094-0.501-0.115-0.76l-2.021-0.907 c-0.325-0.146-0.532-0.47-0.532-0.826c0-0.356,0.207-0.681,0.532-0.826l2.021-0.907c0.021-0.259,0.061-0.511,0.115-0.76 L4.522,5.933c-0.209-0.297-0.225-0.686-0.042-0.998c0.184-0.312,0.538-0.47,0.881-0.389l2.13,0.493 c0.184-0.182,0.378-0.355,0.584-0.514L7.539,2.412C7.456,2.084,7.565,1.736,7.822,1.525c0.26-0.213,0.621-0.25,0.921-0.094 l1.932,1.008c0.418-0.044,0.833-0.044,1.248,0l1.932-1.008c0.302-0.156,0.662-0.119,0.921,0.094 c0.257,0.211,0.366,0.559,0.283,0.887l-0.536,2.113c0.206,0.159,0.4,0.332,0.584,0.514l2.13-0.493 c0.343-0.081,0.697,0.077,0.881,0.389c0.183,0.312,0.167,0.701-0.042,0.998l-1.369,1.95c0.054,0.249,0.094,0.501,0.115,0.76 l2.021,0.907c0.325,0.146,0.532,0.47,0.532,0.826c0,0.356-0.207,0.681-0.532,0.826l-2.021,0.907 c-0.021,0.259-0.061,0.511-0.115,0.76l1.369,1.95C15.578,14.217,15.594,14.606,15.411,14.918z" />
                            </svg>
                            QQ登录
                        </button>
                    </div>
                </form>

                <!-- 注册表单 -->
                <form id="registerForm" class="form-container space-y-4" style="display: none;">
                    <div>
                        <label for="register-username" class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                </svg>
                            </div>
                            <input id="register-username" type="text" name="username" class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all hover:border-indigo-300" placeholder="请输入手机号" autocomplete="off" />
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">支持手机号注册</p>
                    </div>
                    
                    <div>
                        <label for="register-password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input id="register-password" type="password" name="password" class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all hover:border-indigo-300" placeholder="设置密码(不少于6位)" autocomplete="off" />
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer toggle-password">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">密码长度至少6位，建议使用字母、数字和符号组合</p>
                    </div>
                    
                    <div id="captcha" class="rounded-lg overflow-hidden"></div>
                    
                    <div class="verify-code" style="display:none;">
                        <label for="register-verify-code" class="block text-sm font-medium text-gray-700 mb-1">验证码</label>
                        <div class="relative flex">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input id="register-verify-code" type="text" name="verifyCode" class="pl-10 block w-full rounded-l-lg border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="请输入验证码" autocomplete="off" />
                            <button type="button" id="getVerifyCode" class="whitespace-nowrap min-w-[120px] px-4 py-2 border border-l-0 border-gray-300 rounded-r-lg bg-gray-50 text-sm font-medium text-indigo-600 hover:bg-gray-100 hover:text-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                                获取验证码
                            </button>
                        </div>
                    </div>
                    
                    <button type="button" id="registerSubmit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                        注册
                    </button>
                </form>

                <!-- 找回密码表单 -->
                <form id="resetForm" class="form-container space-y-4" style="display: none;">
                    <div>
                        <label for="reset-username" class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                </svg>
                            </div>
                            <input id="reset-username" type="text" name="username" class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all hover:border-indigo-300" placeholder="请输入手机号" autocomplete="off" />
                        </div>
                    </div>
                    
                    <div>
                        <label for="reset-password" class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input id="reset-password" type="password" name="new_password" class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all hover:border-indigo-300" placeholder="设置新密码(不少于6位)" autocomplete="off" />
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer toggle-password">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">密码长度至少6位，建议使用字母、数字和符号组合</p>
                    </div>
                    
                    <div id="resetCaptcha" class="rounded-lg overflow-hidden"></div>
                    
                    <div class="verify-code" style="display:none;">
                        <label for="reset-verify-code" class="block text-sm font-medium text-gray-700 mb-1">验证码</label>
                        <div class="relative flex">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input id="reset-verify-code" type="text" name="verifyCode" class="pl-10 block w-full rounded-l-lg border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="请输入验证码" autocomplete="off" />
                            <button type="button" id="resetVerifyCode" class="whitespace-nowrap min-w-[120px] px-4 py-2 border border-l-0 border-gray-300 rounded-r-lg bg-gray-50 text-sm font-medium text-indigo-600 hover:bg-gray-100 hover:text-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                                获取验证码
                            </button>
                        </div>
                    </div>
                    
                    <button type="button" id="resetSubmit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                        重置密码
                    </button>
                </form>
            </div>
            
            <!-- 页脚 -->
            <div class="mt-6 text-center text-xs text-gray-500">
                <p>© {$system.sy_name} 租号猫 版权所有</p>
            </div>
        </div>
    </div>

<script src="/static/js/alert.js"></script>
<script type="text/javascript" charset="utf-8">
// 微信快捷登录跳转
$("#wechatQuickLogin").click(function () {
    window.location.href = "/user/login/thirdPartyLogin?type=wx";
});

// QQ快捷登录跳转
$("#qqQuickLogin").click(function () {
    window.location.href = "/user/login/thirdPartyLogin?type=qq";
});

// 初始化极验验证
let geetestObj;

function initGeetestCaptcha() {
    let retryCount = 0;
    const maxRetries = 3;

    function tryInit() {
        $.ajax({
            url: "{$config.gee_api}register",
            type: "get",
            dataType: "json",
            success: function (data) {
                if (data.gt && data.challenge) {
                    if ($("#captcha").length === 0) {
                        console.error("Captcha container not found");
                        return;
                    }

                    initGeetest({
                        gt: data.gt,
                        challenge: data.challenge,
                        offline: !data.success,
                        new_captcha: true,
                        product: "popup",
                        width: "100%"
                    }, function (captchaObj) {
                        geetestObj = captchaObj;
                        
                        $("#captcha").empty();
                        captchaObj.appendTo("#captcha");

                        captchaObj.onReady(function() {
                            console.log("Geetest is ready");
                        });

                        captchaObj.onSuccess(function () {
                            const username = $('#registerForm input[name="username"]').val().trim();
                            if (!validateUsername(username)) {
                                createAlert("red", "请输入正确的手机号", 0);
                                geetestObj.reset();
                                return;
                            }

                            $.ajax({
                                url: "/user/base/verify",
                                type: "post",
                                data: {
                                    username: username,
                                    geetest_challenge: geetestObj.getValidate().geetest_challenge,
                                    geetest_validate: geetestObj.getValidate().geetest_validate,
                                    geetest_seccode: geetestObj.getValidate().geetest_seccode,
                                    actionType: "register"
                                },
                                success: function(res) {
                                    if(res.msg === 1) {
                                        if(res.sms_required) {
                                            createAlert("#16b777", "验证码已发送", 1);
                                            $('.verify-code').show();
                                            startCountdown();
                                        } else {
                                            // 如果不需要短信验证，直接点击注册按钮
                                            createAlert("#16b777", "验证成功，请提交注册信息", 1);
                                        }
                                    } else {
                                        createAlert("red", res.content || "发送失败", 0);
                                        geetestObj.reset();
                                    }
                                },
                                error: function() {
                                    createAlert("red", "请求失败，请重试", 0);
                                    geetestObj.reset();
                                }
                            });
                        });
                    });
                } else {
                    if (retryCount < maxRetries) {
                        retryCount++;
                        console.log(`Retrying geetest init... Attempt ${retryCount}`);
                        setTimeout(tryInit, 1000);
                    } else {
                        createAlert("red", "极验初始化失败，请刷新页面重试", 0);
                    }
                }
            },
            error: function () {
                if (retryCount < maxRetries) {
                    retryCount++;
                    console.log(`Retrying geetest init... Attempt ${retryCount}`);
                    setTimeout(tryInit, 1000);
                } else {
                    createAlert("red", "极验请求失败，请检查网络连接", 0);
                }
            }
        });
    }

    tryInit();
}

// 找回密码相关代码
let resetGeetestObj;

$(document).ready(function () {
    // 修改切换逻辑
    $('.type-switch').click(function() {
        $('.type-switch').removeClass('active text-indigo-600').addClass('text-gray-500');
        $('.type-switch span').css('width', '0');
        $(this).removeClass('text-gray-500').addClass('active text-indigo-600');
        $(this).find('span').css('width', '100%');
        
        $('.form-container').hide();
        if($(this).data('type') === 'login') {
            $('#loginForm').fadeIn(200);
        } else if($(this).data('type') === 'register') {
            $('#registerForm').fadeIn(200);
            if (geetestObj) {
                geetestObj.destroy();
            }
            initGeetestCaptcha();
        } else if($(this).data('type') === 'reset') {
            $('#resetForm').fadeIn(200);
            if (resetGeetestObj) {
                resetGeetestObj.destroy();
            }
            initResetGeetestCaptcha();
        }
    });

    // 登录提交
    $('#loginSubmit').click(function() {
        const btn = $(this);
        btn.html('<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>登录中...');
        btn.prop('disabled', true);
        
        const data = {
            username: $('#loginForm input[name="username"]').val().trim(),
            password: $('#loginForm input[name="password"]').val()
        };

        if(!validateForm(data)) {
            btn.html('登录');
            btn.prop('disabled', false);
            return;
        }

        $.ajax({
            url: "/user/base/login",
            type: "post",
            data: data,
            success: function(res) {
                if(res.msg === 1) {
                    createAlert("#16b777", "登录成功", 1);
                    if ($('#remember-username').is(':checked')) {
                        localStorage.setItem('rememberedUsername', data.username);
                    } else {
                        localStorage.removeItem('rememberedUsername');
                    }
                    setTimeout(() => window.location.href = "/user/index/onshop", 1000);
                } else {
                    createAlert("red", res.content || "登录失败", 0);
                    btn.html('登录');
                    btn.prop('disabled', false);
                }
            },
            error: function() {
                createAlert("red", "请求失败，请重试", 0);
                btn.html('登录');
                btn.prop('disabled', false);
            }
        });
    });

    // 注册提交
    $('#registerSubmit').click(function() {
        const btn = $(this);
        btn.html('<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>注册中...');
        btn.prop('disabled', true);
        
        const data = {
            username: $('#registerForm input[name="username"]').val().trim(),
            password: $('#registerForm input[name="password"]').val(),
            verify_code: $('#registerForm input[name="verifyCode"]').val()
        };

        if(!validateForm(data)) {
            btn.html('注册');
            btn.prop('disabled', false);
            return;
        }

        $.ajax({
            url: "/user/base/register",
            type: "post",
            data: data,
            success: function(res) {
                if(res.msg === 1) {
                    createAlert("#16b777", "注册成功", 1);
                    setTimeout(() => $('.type-switch[data-type="login"]').click(), 1000);
                } else {
                    createAlert("red", res.content || "注册失败", 0);
                    btn.html('注册');
                    btn.prop('disabled', false);
                }
            },
            error: function() {
                createAlert("red", "请求失败，请重试", 0);
                btn.html('注册');
                btn.prop('disabled', false);
            }
        });
    });

    // 找回密码提交
    $('#resetSubmit').click(function() {
        const btn = $(this);
        btn.html('<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>重置中...');
        btn.prop('disabled', true);
        
        const data = {
            username: $('#resetForm input[name="username"]').val().trim(),
            verify_code: $('#resetForm input[name="verifyCode"]').val(),
            new_password: $('#resetForm input[name="new_password"]').val()
        };

        if(!validateForm(data)) {
            btn.html('重置密码');
            btn.prop('disabled', false);
            return;
        }

        $.ajax({
            url: "/user/base/resetPassword",
            type: "post",
            data: data,
            success: function(res) {
                if(res.msg === 1) {
                    createAlert("#16b777", "密码重置成功", 1);
                    setTimeout(() => $('.type-switch[data-type="login"]').click(), 1000);
                } else {
                    createAlert("red", res.content || "重置失败", 0);
                    btn.html('重置密码');
                    btn.prop('disabled', false);
                }
            },
            error: function() {
                createAlert("red", "请求失败，请重试", 0);
                btn.html('重置密码');
                btn.prop('disabled', false);
            }
        });
    });

    // 从localStorage读取用户名
    const savedUsername = localStorage.getItem('rememberedUsername');
    if (savedUsername) {
        $('#login-username').val(savedUsername);
    }

    // 添加快捷切换链接处理
    $('.type-switch-link').click(function() {
        $('.type-switch[data-type="' + $(this).data('type') + '"]').click();
    });
});

// 表单验证
function validateForm(data) {
    let isValid = true;
    
    if(!validateUsername(data.username)) {
        const field = $('input[name="username"]:visible');
        showFieldError(field, "请输入正确的手机号");
        isValid = false;
    } else {
        showFieldError($('input[name="username"]:visible'), "");
    }
    
    if(!(data.password || data.new_password)) {
        const field = data.password ? $('input[name="password"]:visible') : $('input[name="new_password"]:visible');
        showFieldError(field, "请输入密码");
        isValid = false;
    } else {
        const field = data.password ? $('input[name="password"]:visible') : $('input[name="new_password"]:visible');
        showFieldError(field, "");
    }
    
    if(!isValid) {
        createAlert("red", "请完善表单信息", 0);
    }
    
    return isValid;
}

// 用户名验证
function validateUsername(username) {
    const regEmail = /^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]+$/;
    const regPhone = /^1[3-9]\d{9}$/;
    return regEmail.test(username) || regPhone.test(username);
}

// 倒计时功能
function startCountdown() {
    let seconds = 60;
    const btn = $('#getVerifyCode');
    btn.prop('disabled', true);
    btn.addClass('bg-gray-200 text-gray-500 hover:bg-gray-200 hover:text-gray-500');
    btn.removeClass('bg-gray-50 text-indigo-600 hover:bg-gray-100 hover:text-indigo-700');
    
    const timer = setInterval(() => {
        if(seconds <= 0) {
            clearInterval(timer);
            btn.prop('disabled', false);
            btn.text('获取验证码');
            btn.removeClass('bg-gray-200 text-gray-500 hover:bg-gray-200 hover:text-gray-500');
            btn.addClass('bg-gray-50 text-indigo-600 hover:bg-gray-100 hover:text-indigo-700');
            return;
        }
        btn.text(`${seconds}秒后重试`);
        seconds--;
    }, 1000);
}

// 初始化找回密码的极验验证
function initResetGeetestCaptcha() {
    let retryCount = 0;
    const maxRetries = 3;

    function tryInit() {
        $.ajax({
            url: "{$config.gee_api}register",
            type: "get",
            dataType: "json",
            success: function (data) {
                if (data.gt && data.challenge) {
                    if ($("#resetCaptcha").length === 0) {
                        console.error("Reset captcha container not found");
                        return;
                    }

                    initGeetest({
                        gt: data.gt,
                        challenge: data.challenge,
                        offline: !data.success,
                        new_captcha: true,
                        product: "popup",
                        width: "100%"
                    }, function (captchaObj) {
                        resetGeetestObj = captchaObj;
                        
                        $("#resetCaptcha").empty();
                        captchaObj.appendTo("#resetCaptcha");

                        captchaObj.onReady(function() {
                            console.log("Reset Geetest is ready");
                        });

                        captchaObj.onSuccess(function () {
                            const username = $('#resetForm input[name="username"]').val().trim();
                            if (!validateUsername(username)) {
                                createAlert("red", "请输入正确的手机号", 0);
                                resetGeetestObj.reset();
                                return;
                            }

                            $.ajax({
                                url: "/user/base/verify",
                                type: "post",
                                data: {
                                    username: username,
                                    geetest_challenge: resetGeetestObj.getValidate().geetest_challenge,
                                    geetest_validate: resetGeetestObj.getValidate().geetest_validate,
                                    geetest_seccode: resetGeetestObj.getValidate().geetest_seccode,
                                    actionType: "reset"
                                },
                                success: function(res) {
                                    if(res.msg === 1) {
                                        createAlert("#16b777", "验证码已发送", 1);
                                        $('#resetForm .verify-code').show();
                                        startResetCountdown();
                                    } else {
                                        createAlert("red", res.content || "发送失败", 0);
                                        resetGeetestObj.reset();
                                    }
                                },
                                error: function() {
                                    createAlert("red", "请求失败，请重试", 0);
                                    resetGeetestObj.reset();
                                }
                            });
                        });
                    });
                } else {
                    if (retryCount < maxRetries) {
                        retryCount++;
                        setTimeout(tryInit, 1000);
                    } else {
                        createAlert("red", "极验初始化失败，请刷新页面重试", 0);
                    }
                }
            },
            error: function () {
                if (retryCount < maxRetries) {
                    retryCount++;
                    setTimeout(tryInit, 1000);
                } else {
                    createAlert("red", "极验请求失败，请检查网络连接", 0);
                }
            }
        });
    }

    tryInit();
}

// 找回密码的倒计时功能
function startResetCountdown() {
    let seconds = 60;
    const btn = $('#resetVerifyCode');
    btn.prop('disabled', true);
    btn.addClass('bg-gray-200 text-gray-500 hover:bg-gray-200 hover:text-gray-500');
    btn.removeClass('bg-gray-50 text-indigo-600 hover:bg-gray-100 hover:text-indigo-700');
    
    const timer = setInterval(() => {
        if(seconds <= 0) {
            clearInterval(timer);
            btn.prop('disabled', false);
            btn.text('获取验证码');
            btn.removeClass('bg-gray-200 text-gray-500 hover:bg-gray-200 hover:text-gray-500');
            btn.addClass('bg-gray-50 text-indigo-600 hover:bg-gray-100 hover:text-indigo-700');
            return;
        }
        btn.text(`${seconds}秒后重试`);
        seconds--;
    }, 1000);
}

// 密码显示/隐藏功能
$(document).ready(function() {
    $('.toggle-password').click(function() {
        const passwordField = $(this).closest('.relative').find('input');
        const passwordFieldType = passwordField.attr('type');
        const newType = passwordFieldType === 'password' ? 'text' : 'password';
        
        passwordField.attr('type', newType);
        
        // 切换图标
        if (newType === 'text') {
            $(this).find('svg').html('<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />');
        } else {
            $(this).find('svg').html('<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />');
        }
    });
});

$(window).on('load', function() {
    setTimeout(function() {
        $('#pageLoader').fadeOut(300);
    }, 500);
});

function showFieldError(field, message) {
    const fieldContainer = field.closest('div.relative').parent();
    
    // 移除已有的错误消息
    fieldContainer.find('.error-message').remove();
    fieldContainer.find('input').removeClass('border-red-500 focus:ring-red-500');
    
    if (message) {
        // 添加错误样式
        field.addClass('border-red-500 focus:ring-red-500');
        
        // 添加错误消息
        fieldContainer.append(`<p class="error-message mt-1 text-xs text-red-500">${message}</p>`);
    }
}
</script>
</body>
</html>