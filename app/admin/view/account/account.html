

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>账号管理系统 - 游戏账号列表</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
  <meta http-equiv="Expires" content="0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Cache-control" content="no-cache">
  <meta http-equiv="Cache" content="no-cache">
  <style>
    /* 简化样式，确保与Layui兼容 */
    .layui-card-header {
      background: #009688;
      color: white;
    }
    .layui-card-header h2 {
      color: white;
      margin: 0;
      font-size: 16px;
    }
    .search-section {
      background: #f8f9fa;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 2px;
    }
    .search-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
      border-left: 3px solid #009688;
      padding-left: 8px;
    }
    .status-tag {
      padding: 2px 6px;
      border-radius: 2px;
      font-size: 12px;
      display: inline-block;
    }
    .status-available {
      background: #f6ffed;
      color: #52c41a;
    }
    .status-unavailable {
      background: #fff2f0;
      color: #ff4d4f;
    }
    .status-online {
      background: #e6f7ff;
      color: #1890ff;
    }
    .status-offline {
      background: #f9f0ff;
      color: #722ed1;
    }
    .account-info {
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .account-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #009688;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 11px;
    }
    .search-result-info {
      background: #e6f7ff;
      border: 1px solid #91d5ff;
      padding: 8px 12px;
      margin-bottom: 10px;
      display: none;
      border-radius: 2px;
    }
    .search-result-info.show {
      display: block;
    }

    /* 商品下拉列表样式 */
    .goods-dropdown {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white !important;
      border: 1px solid #e6e6e6;
      border-top: none;
      max-height: 200px;
      overflow-y: auto;
      z-index: 9999;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 0 0 4px 4px;
    }

    .goods-option {
      padding: 10px 12px !important;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;
      transition: all 0.2s;
      color: #333333 !important; /* 强制设置文字颜色 */
      font-size: 14px !important;
      line-height: 1.4 !important;
      background-color: white !important;
      display: block;
      text-align: left;
    }

    .goods-option:hover {
      background-color: #f8f9fa !important;
      color: #333333 !important; /* 悬停时保持文字颜色 */
    }

    .goods-option:last-child {
      border-bottom: none;
    }

    .goods-option.selected {
      background-color: #009688 !important;
      color: white !important; /* 选中时白色文字 */
    }

    .goods-option.highlighted {
      background-color: #e8f5e8 !important;
      color: #333333 !important; /* 高亮时深色文字 */
    }

    /* 确保下拉列表在最上层 */
    .layui-input-inline {
      position: relative;
      z-index: 1;
    }

    .layui-input-inline .goods-dropdown {
      z-index: 9999 !important;
    }

    /* 防止其他样式干扰 */
    .goods-dropdown * {
      box-sizing: border-box;
    }

    /* 强制覆盖可能的主题样式 */
    .goods-dropdown .goods-option,
    .goods-dropdown .goods-option:before,
    .goods-dropdown .goods-option:after {
      color: #333333 !important;
      background-color: white !important;
    }

    .goods-dropdown .goods-option:hover,
    .goods-dropdown .goods-option:hover:before,
    .goods-dropdown .goods-option:hover:after {
      color: #333333 !important;
      background-color: #f8f9fa !important;
    }

    .goods-dropdown .goods-option.selected,
    .goods-dropdown .goods-option.selected:before,
    .goods-dropdown .goods-option.selected:after {
      color: white !important;
      background-color: #009688 !important;
    }

    .goods-dropdown .goods-option.highlighted,
    .goods-dropdown .goods-option.highlighted:before,
    .goods-dropdown .goods-option.highlighted:after {
      color: #333333 !important;
      background-color: #e8f5e8 !important;
    }
    .search-result-text {
      color: #1890ff;
      font-size: 13px;
    }
    .clear-search {
      background: none;
      border: none;
      color: #1890ff;
      cursor: pointer;
      text-decoration: underline;
      font-size: 12px;
      margin-left: 8px;
    }
    .stats-bar {
      background: #f8f9fa;
      padding: 12px 15px;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 2px;
    }
    .stats-info {
      display: flex;
      gap: 15px;
    }
    .stat-item {
      font-size: 13px;
      color: #666;
    }
    .stat-number {
      font-weight: bold;
      color: #009688;
    }

    /* 修复下拉框选项字体颜色问题 */
    .layui-form-select dl {
      background: #fff;
    }
    .layui-form-select dl dd {
      color: #333 !important;
      background: #fff;
    }
    .layui-form-select dl dd:hover {
      background: #f2f2f2 !important;
      color: #333 !important;
    }
    .layui-form-select dl dd.layui-this {
      background: #009688 !important;
      color: #fff !important;
    }

    /* 确保下拉框输入框文字清晰 */
    .layui-form-select .layui-select-title input {
      color: #333 !important;
    }
    .layui-form-select .layui-select-title {
      color: #333 !important;
    }

    /* 修复搜索表单区域的样式 */
    .layui-form .layui-form-label {
      color: #333;
      font-weight: 500;
    }
    .layui-input {
      color: #333 !important;
    }

    /* 强制修复下拉框选项文字颜色 */
    .layui-form-selected dl dd,
    .layui-form-select dl dd {
      color: #333 !important;
      font-size: 14px;
      line-height: 36px;
      padding: 0 15px;
    }

    /* 下拉框选项悬停效果 */
    .layui-form-selected dl dd:hover,
    .layui-form-select dl dd:hover {
      background-color: #f8f9fa !important;
      color: #333 !important;
    }

    /* 选中状态 */
    .layui-form-selected dl dd.layui-this,
    .layui-form-select dl dd.layui-this {
      background-color: #009688 !important;
      color: #fff !important;
    }

    /* 下拉框容器 */
    .layui-form-select dl {
      border: 1px solid #e6e6e6;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      max-height: 200px;
      overflow-y: auto;
    }
  </style>
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-header">
        <h2>🎮 游戏账号管理</h2>
      </div>

      <!-- 搜索区域 -->
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
        <div class="layui-form-item">
          <div class="layui-inline">
            <label class="layui-form-label">账号名称</label>
            <div class="layui-input-inline">
              <input type="text" name="ac_name" placeholder="请输入账号名称" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">账号状态</label>
            <div class="layui-input-inline">
              <select name="ac_states">
                <option value="">全部状态</option>
                <option value="1">可用</option>
                <option value="0">不可用</option>
              </select>
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">所属商品</label>
            <div class="layui-input-inline" style="position: relative;">
              <input type="text" name="ac_goods" placeholder="请选择或输入商品名称" autocomplete="off" class="layui-input" id="goodsInput">
              <div class="goods-dropdown" id="goodsDropdown">
                <div class="goods-option" data-value="">全部商品</div>
                {foreach $goodsAll as $val}
                <div class="goods-option" data-value="{$val.id}" data-name="{$val.goods_name}">{$val.goods_name}</div>
                {/foreach}
              </div>
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">账号类型</label>
            <div class="layui-input-inline">
              <select name="goods_Type">
                <option value="">全部类型</option>
                <option value="1">在线账号</option>
                <option value="0">离线账号</option>
              </select>
            </div>
          </div>
          <div class="layui-inline">
            <button class="layui-btn" lay-submit lay-filter="LAY-app-contcomm-search">
              <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
            </button>
            <button type="reset" class="layui-btn layui-btn-primary" id="resetSearch">重置</button>
            <button type="button" class="layui-btn layui-btn-normal" id="showAllAccounts">显示全部</button>
            <button type="button" class="layui-btn layui-btn-warm" id="refreshPage" style="display:none;">刷新页面</button>
          </div>
        </div>
      </div>

      <div class="layui-card-body">

        <!-- 搜索结果信息 -->
        <div class="search-result-info" id="searchResultInfo">
          <div class="search-result-text">
            <span id="searchResultText"></span>
            <button class="clear-search" onclick="clearSearchResult()">清除搜索</button>
          </div>
        </div>

        <!-- 统计和操作区域 -->
        <div class="stats-bar">
          <div class="stats-info">
            <div class="stat-item">
              总账号数：<span class="stat-number" id="totalAccounts">0</span>
            </div>
            <div class="stat-item">
              可用账号：<span class="stat-number" id="availableAccounts">0</span>
            </div>
            <div class="stat-item">
              在线账号：<span class="stat-number" id="onlineAccounts">0</span>
            </div>
            <div class="stat-item">
              离线账号：<span class="stat-number" id="offlineAccounts">0</span>
            </div>
          </div>
          <div class="layui-btn-group">
            <button class="layui-btn layui-btn-sm" data-type="add">
              <i class="layui-icon layui-icon-add-1"></i> 添加账号
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-danger" data-type="batchdel">
              <i class="layui-icon layui-icon-delete"></i> 批量删除
            </button>
          </div>
        </div>

        <!-- 表格区域 -->
        <table id="LAY-app-content-list2" lay-filter="LAY-app-content-list2"></table>
      </div>
    </div>
  </div>

  <!-- 表格操作模板 -->
  <script type="text/html" id="table-content-com">
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
  </script>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'accountlist', 'table'], function(){
    var $ = layui.$
    ,form = layui.form
    ,table = layui.table;

    // 商品输入框下拉功能
    var goodsInput = $('#goodsInput');
    var goodsDropdown = $('#goodsDropdown');
    var goodsOptions = $('.goods-option');
    var selectedGoodsValue = '';

    // 点击输入框显示下拉列表
    goodsInput.on('focus click', function() {
      console.log('显示下拉列表');
      goodsDropdown.css('display', 'block');
      filterGoodsOptions($(this).val());
    });

    // 输入时过滤选项
    goodsInput.on('input', function() {
      var inputValue = $(this).val();
      filterGoodsOptions(inputValue);
      goodsDropdown.css('display', 'block');
    });

    // 过滤商品选项
    function filterGoodsOptions(searchText) {
      goodsOptions.each(function() {
        var optionText = $(this).text();
        if (optionText.toLowerCase().indexOf(searchText.toLowerCase()) !== -1) {
          $(this).show();
        } else {
          $(this).hide();
        }
      });
    }

    // 点击选项
    goodsOptions.on('click', function() {
      var value = $(this).data('value');
      var name = $(this).data('name') || $(this).text();

      goodsInput.val(name);
      selectedGoodsValue = value;
      goodsDropdown.css('display', 'none');

      // 移除其他选中状态
      goodsOptions.removeClass('selected');
      $(this).addClass('selected');

      console.log('选择商品:', name, '值:', value);
    });

    // 点击其他地方隐藏下拉列表
    $(document).on('click', function(e) {
      if (!$(e.target).closest('.layui-input-inline').length) {
        goodsDropdown.css('display', 'none');
      }
    });

    // 键盘导航支持
    var currentHighlight = -1;
    goodsInput.on('keydown', function(e) {
      var visibleOptions = goodsOptions.filter(':visible');

      if (e.keyCode === 40) { // 下箭头
        e.preventDefault();
        currentHighlight = Math.min(currentHighlight + 1, visibleOptions.length - 1);
        updateHighlight(visibleOptions);
      } else if (e.keyCode === 38) { // 上箭头
        e.preventDefault();
        currentHighlight = Math.max(currentHighlight - 1, 0);
        updateHighlight(visibleOptions);
      } else if (e.keyCode === 13) { // 回车
        e.preventDefault();
        if (currentHighlight >= 0 && visibleOptions.eq(currentHighlight).length) {
          visibleOptions.eq(currentHighlight).click();
        }
      } else if (e.keyCode === 27) { // ESC
        goodsDropdown.css('display', 'none');
        currentHighlight = -1;
      }
    });

    function updateHighlight(visibleOptions) {
      goodsOptions.removeClass('highlighted');
      if (currentHighlight >= 0 && visibleOptions.eq(currentHighlight).length) {
        visibleOptions.eq(currentHighlight).addClass('highlighted');
      }
    }

    // 获取表格实例
    var tableIns = table.render({
      elem: '#LAY-app-content-list2',
      url: '/admin/base/accountList',
      cols: [[
        {type: 'checkbox', fixed: 'left'},
        {field: 'id', width: 80, title: 'ID', sort: true},
        {field: 'ac_goods', title: '所属商品', minWidth: 120},
        {field: 'ac_name', minWidth: 150, title: '账号信息', templet: function(d) {
          var avatar = d.ac_name.charAt(0).toUpperCase();
          return '<div class="account-info"><div class="account-avatar">' + avatar + '</div><div>' + d.ac_name + '</div></div>';
        }},
        {field: 'ac_password', title: '密码', minWidth: 120},
        {field: 'ac_states', title: '状态', width: 100, align: 'center', templet: function(d) {
          if(d.ac_states == 1) {
            return '<span class="status-tag status-available">可用</span>';
          } else {
            return '<span class="status-tag status-unavailable">不可用</span>';
          }
        }},
        {field: 'ac_uid', title: '租赁用户', minWidth: 120, align: 'center'},
        {field: 'goods_Type', title: '类型', width: 100, align: 'center', templet: function(d) {
          if(d.goods_Type == 0) {
            return '<span class="status-tag status-offline">离线</span>';
          } else {
            return '<span class="status-tag status-online">在线</span>';
          }
        }},
        {field: 'time', title: '添加时间', width: 160, align: 'center'},
        {field: 'exit_time', title: '过期时间', width: 160, align: 'center', templet: function(d) {
          if (d.exit_time) {
            var date = new Date(d.exit_time * 1000);
            var formattedDate = date.getFullYear() + '-' +
              ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
              ('0' + date.getDate()).slice(-2) + ' ' +
              ('0' + date.getHours()).slice(-2) + ':' +
              ('0' + date.getMinutes()).slice(-2);
            return '<span style="color: #fa8c16;">' + formattedDate + '</span>';
          } else {
            return '<span style="color: #52c41a;">永久有效</span>';
          }
        }},
        {title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-content-com'}
      ]],
      page: true,
      limit: 15,
      limits: [10,20,30,40,50,60,70,80,100],
      text: {
        none: '暂无账号数据'
      },
      done: function(res, curr, count) {
        console.log('表格数据加载完成:', res);
        console.log('当前页:', curr, '总数:', count, '数据条数:', res.data.length);
        console.log('是否有搜索信息:', res.search_info ? '是' : '否');

        // 更新统计信息
        updateStats(res.data);

        // 显示搜索结果信息
        if (res.search_info && res.search_info.has_search) {
          console.log('显示搜索结果信息');
          showSearchResultInfo(res.search_info.total_found, res.search_info);
        } else {
          console.log('隐藏搜索结果信息，显示所有数据');
          hideSearchResultInfo();
          // 如果没有搜索条件，显示总数信息
          if (count > 0) {
            console.log('成功显示所有账号，总数:', count);
          }
        }
      }
    });

    // 更新统计信息
    function updateStats(data) {
      var total = data.length;
      var available = data.filter(item => item.ac_states == 1).length;
      var online = data.filter(item => item.goods_Type == 1).length;
      var offline = data.filter(item => item.goods_Type == 0).length;

      $('#totalAccounts').text(total);
      $('#availableAccounts').text(available);
      $('#onlineAccounts').text(online);
      $('#offlineAccounts').text(offline);
    }

    // 显示搜索结果信息
    function showSearchResultInfo(count, searchInfo) {
      var searchText = '搜索结果：找到 ' + count + ' 条匹配的账号记录';

      // 构建搜索条件描述
      var conditions = [];
      if (searchInfo.search_params) {
        if (searchInfo.search_params.ac_name) {
          conditions.push('账号名称："' + searchInfo.search_params.ac_name + '"');
        }
        if (searchInfo.search_params.ac_states !== undefined) {
          conditions.push('状态：' + (searchInfo.search_params.ac_states == '1' ? '可用' : '不可用'));
        }
        if (searchInfo.search_params.ac_goods) {
          conditions.push('商品ID：' + searchInfo.search_params.ac_goods);
        }
        if (searchInfo.search_params.ac_goods_name) {
          conditions.push('商品名称："' + searchInfo.search_params.ac_goods_name + '"');
        }
        if (searchInfo.search_params.goods_Type !== undefined) {
          conditions.push('类型：' + (searchInfo.search_params.goods_Type == '1' ? '在线' : '离线'));
        }
      }

      if (conditions.length > 0) {
        searchText += '，搜索条件：' + conditions.join('，');
      }

      $('#searchResultText').text(searchText);
      $('#searchResultInfo').addClass('show');
    }

    // 隐藏搜索结果信息
    function hideSearchResultInfo() {
      $('#searchResultInfo').removeClass('show');
    }

    // 显示所有账号数据（统一的重置函数）
    function showAllAccounts() {
      console.log('显示所有账号数据...');

      // 清空所有搜索字段
      $('input[name="ac_name"]').val('');
      goodsInput.val(''); // 清空商品输入框
      selectedGoodsValue = ''; // 重置选中的商品值
      $('select[name="ac_states"]').val('');
      $('select[name="goods_Type"]').val('');

      // 重新渲染表单
      form.render();

      // 隐藏下拉列表
      goodsDropdown.css('display', 'none');
      goodsOptions.removeClass('selected highlighted');

      // 最简单有效的解决方案：直接刷新页面
      // 这样可以完全清除所有搜索状态和缓存
      console.log('使用页面刷新方案彻底清除搜索状态');

      // 清除URL中的搜索参数
      if (window.history && window.history.replaceState) {
        window.history.replaceState({}, document.title, window.location.pathname);
      }

      // 显示提示信息
      layer.msg('正在显示所有账号...', {icon: 1, time: 1000});

      // 延迟刷新，让用户看到提示
      setTimeout(function() {
        window.location.reload();
      }, 500);
    }

    // 备用方案：完全刷新页面来清除所有搜索状态
    function refreshPageToShowAll() {
      console.log('使用页面刷新方案清除搜索状态');
      // 清除URL中的搜索参数
      if (window.history && window.history.replaceState) {
        window.history.replaceState({}, document.title, window.location.pathname);
      }
      // 刷新页面
      window.location.reload();
    }

    // 清除搜索结果（调用统一函数）
    window.clearSearchResult = function() {
      showAllAccounts();
    };

    //监听搜索
    form.on('submit(LAY-app-contcomm-search)', function(data){
      var field = data.field;
      console.log('搜索表单提交:', field);

      // 清理空值，只传递有效的搜索参数
      var searchParams = {};
      var hasValidSearch = false;

      if (field.ac_name && field.ac_name.trim() !== '') {
        searchParams.ac_name = field.ac_name.trim();
        hasValidSearch = true;
      }
      if (field.ac_states !== '') {
        searchParams.ac_states = field.ac_states;
        hasValidSearch = true;
      }
      // 处理商品搜索 - 支持ID和名称搜索
      if (field.ac_goods !== '') {
        // 如果选择了下拉选项，使用选中的值
        if (selectedGoodsValue !== '') {
          searchParams.ac_goods = selectedGoodsValue;
        } else {
          // 如果是手动输入，直接使用输入的文本进行模糊搜索
          searchParams.ac_goods_name = field.ac_goods;
        }
        hasValidSearch = true;
      }
      if (field.goods_Type !== '') {
        searchParams.goods_Type = field.goods_Type;
        hasValidSearch = true;
      }

      console.log('搜索参数:', searchParams, '有效搜索:', hasValidSearch);

      // 如果没有有效的搜索条件，显示所有数据
      if (!hasValidSearch) {
        console.log('没有搜索条件，显示所有数据');
        showAllAccounts();
        return false;
      }

      // 执行搜索重载
      table.reload('LAY-app-content-list2', {
        url: '/admin/base/accountList',
        where: searchParams,
        page: {
          curr: 1 // 重新从第一页开始
        }
      });

      return false;
    });

    // 重置搜索按钮
    $('#resetSearch').on('click', function(e) {
      e.preventDefault(); // 阻止默认行为
      console.log('重置按钮被点击');
      showAllAccounts();
      return false;
    });

    // 清除搜索按钮（搜索结果信息中的链接）
    $(document).on('click', '.clear-search', function(e) {
      e.preventDefault();
      console.log('清除搜索按钮被点击');
      showAllAccounts();
      return false;
    });

    // 显示全部账号按钮
    $('#showAllAccounts').on('click', function(e) {
      e.preventDefault();
      console.log('显示全部按钮被点击');
      showAllAccounts();
      return false;
    });

    // 刷新页面按钮（备用方案）
    $('#refreshPage').on('click', function(e) {
      e.preventDefault();
      console.log('刷新页面按钮被点击');
      refreshPageToShowAll();
      return false;
    });

    //点击事件
    var active = {
      batchdel: function(){
        var checkStatus = table.checkStatus('LAY-app-content-list2')
        ,checkData = checkStatus.data; //得到选中的数据

        if(checkData.length === 0){
          return layer.msg('请选择数据');
        }

        layer.confirm('确定删除吗？', function(index) {
            var arr=[]
            checkData.forEach(function(val,index){
                arr.push(val.id)
            })
		    layui.$.ajax({
                url: '/admin/base/delAccount',
                method: 'post',
                data: {id:arr},
                success: function(res){
                layer.msg('删除成功');
                }
            });
          table.reload('LAY-app-content-list2');
          layer.msg('已删除');
        });
      },
      add: function(){
            layer.open({
              type: 2
              ,title: '添加账号'
              ,content: '/admin/index/addAccount'
              ,maxmin: true
              ,area: ['550px', '550px']
              ,btn: ['确定', '取消']
              ,yes: function(index, layero){
                //点击确认触发 iframe 内容中的按钮提交
                var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
                submit.click();
              }
            });
          }
    }

    // 绑定操作按钮事件
    $('[data-type]').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

    // 修改表格工具条事件监听
    table.on('tool(LAY-app-content-list2)', function(obj){
      var data = obj.data;
      if(obj.event === 'del'){
        layer.confirm('确定删除吗？', function(index){
          layui.$.ajax({
            url: '/admin/base/delAccount',
            method: 'post',
            data: {id:[data.id]},
            success: function(res){
              layer.msg('删除成功');
              obj.del();
              layer.close(index);
            }
          });
        });
      } else if(obj.event === 'edit'){
        layer.open({
          type: 2
          ,title: '编辑账号'
          ,content: '/admin/index/editAccount?id=' + data.id
          ,maxmin: true
          ,area: ['550px', '600px']
          //,btn: ['确定', '取消']
          ,yes: function(index, layero){
            var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
            submit.click();
          }
          ,success: function(layero, index) {
            // 加载完成后的回调
          }
        });
      }
    });
  });
  </script>
</body>
</html>

