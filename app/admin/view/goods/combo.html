<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>商品套餐管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
  <meta http-equiv="Expires" content="0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Cache-control" content="no-cache">
  <meta http-equiv="Cache" content="no-cache">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-header">
        <h2>🎮 游戏套餐管理</h2>
      </div>
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
        <div class="layui-form-item">
          <div class="layui-inline">
            <label class="layui-form-label">商品名称</label>
            <div class="layui-input-inline">
              <input type="text" name="goods_name" placeholder="请输入商品名称进行搜索" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-inline">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="LAY-app-contcomm-search">
              <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
            </button>
            <button type="button" class="layui-btn layui-btn-primary" id="resetSearch">重置</button>
          </div>
        </div>
      </div>

      <!-- 搜索结果信息 -->
      <div class="layui-card-body" style="padding-top: 0;">
        <div id="searchResultInfo" style="background: #e6f7ff; border: 1px solid #91d5ff; padding: 10px 15px; margin-bottom: 15px; border-radius: 4px; display: none;">
          <span id="searchResultText" style="color: #1890ff; font-weight: 500;"></span>
          <button onclick="clearSearchResult()" style="background: none; border: none; color: #1890ff; cursor: pointer; text-decoration: underline; font-size: 12px; margin-left: 10px;">清除搜索</button>
        </div>
      </div>

      <div class="layui-card-body">
        <div style="padding-bottom: 10px;">
          <button class="layui-btn layuiadmin-btn-comm" data-type="add">添加</button>
          <button class="layui-btn layuiadmin-btn-comm" data-type="batchdel">删除</button>
        </div>

        <table class="layui-hide" id="LAY-app-content-list2" lay-filter="LAY-app-content-list2"></table>

        <script type="text/html" id="table-content-com">
          <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
        </script>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form', 'table'], function(){
    var $ = layui.$
    ,form = layui.form
    ,table = layui.table;

    // 初始化表格
    var tableIns = table.render({
      elem: '#LAY-app-content-list2',
      url: '/admin/base/comboList', // 统一使用一个接口
      cols: [[
        {type: "checkbox", width: 50},
        {field: 'id', title: 'ID', sort: true, width: 80},
        {field: 'goods_name', title: '商品名称', minWidth: 150},
        {field: 'day_price', title: '按天价格', width: 120, templet: function(d){
          return '<span style="color: #52c41a; font-weight: 500;">￥' + d.day_price + '</span>';
        }},
        {field: 'hour_price', title: '按小时价格', width: 120, templet: function(d){
          return '<span style="color: #1890ff; font-weight: 500;">￥' + d.hour_price + '</span>';
        }},
        {field: 'product_amount', title: '离线价格', width: 120, templet: function(d){
          if (d.product_amount) {
            return '<span style="color: #fa8c16; font-weight: 500;">￥' + d.product_amount + '</span>';
          } else {
            return '<span style="color: #999;">未设置</span>';
          }
        }},
        {field: 'time', title: '更新时间', width: 160},
        {title: '操作', minWidth: 150, toolbar: '#table-content-com', align: "center"}
      ]],
      page: true,
      limit: 15,
      limits: [10, 15, 20, 25, 50, 100],
      text: {
        none: '暂无相关数据'
      },
      done: function(res, curr, count) {
        // 显示搜索结果信息
        if (res.search_info && res.search_info.has_search) {
          showSearchResultInfo(res.search_info);
        } else {
          hideSearchResultInfo();
        }
      }
    });

    // 显示搜索结果信息
    function showSearchResultInfo(searchInfo) {
      var searchText = '🔍 搜索结果：找到 ' + searchInfo.result_count + ' 条包含 "' + searchInfo.keyword + '" 的套餐记录';
      $('#searchResultText').text(searchText);
      $('#searchResultInfo').show();
    }

    // 隐藏搜索结果信息
    function hideSearchResultInfo() {
      $('#searchResultInfo').hide();
    }

    // 清除搜索结果
    window.clearSearchResult = function() {
      $('input[name="goods_name"]').val('');
      form.render();
      tableIns.reload({
        where: {}
      });
      hideSearchResultInfo();
    };

    //监听搜索
    form.on('submit(LAY-app-contcomm-search)', function(data){
      var field = data.field;

      //执行重载
      tableIns.reload({
        where: field
      });

      return false; // 阻止表单默认提交
    });

    // 重置搜索
    $('#resetSearch').on('click', function() {
      clearSearchResult();
    });

    //点击事件
    var active = {
      batchdel: function(){
        var checkStatus = table.checkStatus('LAY-app-content-list2')
        ,checkData = checkStatus.data;

        if(checkData.length === 0){
          return layer.msg('请选择数据');
        }

        layer.confirm('确定删除吗？', function(index) {
          var ids = checkData.map(function(item) {
            return item.id;
          });

          //执行 Ajax 后台删除
          $.ajax({
            url: '/admin/base/delCombo',
            method: 'post',
            data: {id: ids},
            success: function(res){
              layer.msg('已删除');
              tableIns.reload();
            }
          });
        });
      },
      add: function(){
        layer.open({
          type: 2
          ,title: '添加商品价格'
          ,content: '/admin/index/addCombo'
          ,maxmin: true
          ,area: ['550px', '550px']
          ,btn: ['确定', '取消']
          ,yes: function(index, layero){
            var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
            submit.click();
          }
        });
      }
    };

    $('.layui-btn.layuiadmin-btn-comm').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

    //监听工具条
    table.on('tool(LAY-app-content-list2)', function(obj){
      var data = obj.data;

      if(obj.event === 'del'){
        layer.confirm('确定删除此商品价格？', function(index){
          $.ajax({
            url: '/admin/base/delCombo',
            method: 'post',
            data: {id: data.id},
            success: function(res){
              if(res.code === 0) {
                obj.del();
                layer.msg('删除成功');
              } else {
                layer.msg('删除失败：' + (res.msg || '未知错误'));
              }
            }
          });

          layer.close(index);
        });
      } else if(obj.event === 'edit'){
        layer.open({
          type: 2
          ,title: '编辑商品价格'
          ,content: '/admin/index/upCombo?id=' + data.id
          ,maxmin: true
          ,area: ['550px', '550px']
          ,btn: ['确定', '取消']
          ,yes: function(index, layero){
            var iframeWindow = window['layui-layer-iframe'+ index]
            ,submitID = 'layuiadmin-app-form-edit'
            ,submit = layero.find('iframe').contents().find('#'+ submitID);

            //监听提交
            iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
              var field = data.field;

              $.ajax({
                url: '/admin/base/updateOfflineProductPrice',
                method: 'post',
                data: field,
                success: function(res){
                  if(res.code === 0 || res.code === undefined) {
                    tableIns.reload();
                    layer.close(index);
                    layer.msg('编辑成功');
                  } else {
                    layer.msg('编辑失败：' + (res.msg || '未知错误'));
                  }
                }
              });
            });

            submit.trigger('click');
          }
        });
      }
    });
  });
  </script>
</body>
</html>

