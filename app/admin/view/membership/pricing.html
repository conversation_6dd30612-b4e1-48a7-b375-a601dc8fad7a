<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>会员价格管理</title>
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">会员价格管理</div>
        <div class="layui-card-body">
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layuiadmin-btn-comm" data-type="add">添加会员价格</button>
            </div>
            <table id="LAY-pricing-list" lay-filter="LAY-pricing-list"></table>
        </div>
    </div>
</div>

<!-- 工具栏 -->
<script type="text/html" id="table-pricing-list">
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
</script>

<!-- 添加/编辑表单 -->
<script type="text/html" id="pricing-form">
    <form class="layui-form" lay-filter="pricing-form" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <div class="layui-form-item">
            <label class="layui-form-label">会员类型</label>
            <div class="layui-input-block">
                <select name="membership_type" lay-verify="required">
                    <option value="">请选择会员类型</option>
                    <option value="月费会员">月费会员</option>
                    <option value="季费会员">季费会员</option>
                    <option value="年费会员">年费会员</option>
                    <option value="至尊会员">至尊会员</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">价格</label>
            <div class="layui-input-block">
                <input type="number" name="price" lay-verify="required|number" placeholder="请输入价格" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">有效期(天)</label>
            <div class="layui-input-block">
                <select name="validity_period" lay-verify="required">
                    <option value="">请选择有效期</option>
                    <option value="30">30天（月费）</option>
                    <option value="90">90天（季费）</option>
                    <option value="365">365天（年费）</option>
                    <option value="999999">永久</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-hide">
            <button class="layui-btn" lay-submit lay-filter="pricing-form-submit" id="pricing-form-submit">提交</button>
        </div>
    </form>
</script>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
layui.config({
    base: '/layuiadmin/'
}).extend({
    index: 'lib/index'
}).use(['index', 'table', 'form'], function(){
    var $ = layui.$
    ,form = layui.form
    ,table = layui.table;
    
    // 表格渲染
    table.render({
        elem: '#LAY-pricing-list'
        ,url: '/admin/index/getMembershipPricing'
        ,cols: [[
            {type: 'checkbox', fixed: 'left'}
            ,{field: 'id', width: 80, title: 'ID', sort: true}
            ,{field: 'membership_type', title: '会员类型'}
            ,{field: 'price', title: '价格'}
            ,{field: 'validity_period', title: '有效期(天)'}
            ,{title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-pricing-list'}
        ]]
        ,page: true
    });
    
    // 监听工具条
    table.on('tool(LAY-pricing-list)', function(obj){
        var data = obj.data;
        if(obj.event === 'del'){
            layer.confirm('确定删除此会员价格？', function(index){
                $.ajax({
                    url: '/admin/index/deleteMembershipPricing'
                    ,method: 'post'
                    ,data: {id: data.id}
                    ,success: function(res){
                        if(res.code === 0){
                            obj.del();
                            layer.msg('删除成功');
                        } else {
                            layer.msg('删除失败');
                        }
                    }
                });
                layer.close(index);
            });
        } else if(obj.event === 'edit'){
            showForm('编辑会员价格', data);
        }
    });
    
    // 事件
    var active = {
        add: function(){
            showForm('添加会员价格');
        }
    };
    
    $('.layui-btn.layuiadmin-btn-comm').on('click', function(){
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });
    
    // 显示表单
    function showForm(title, data){
        data = data || {};
        layer.open({
            type: 1
            ,title: title
            ,content: $('#pricing-form').html()
            ,maxmin: true
            ,area: ['500px', '450px']
            ,btn: ['确定', '取消']
            ,success: function(layero, index){
                // 先渲染表单
                form.render(null, 'pricing-form');
                
                // 再填充数据
                setTimeout(function(){
                    form.val('pricing-form', data);
                }, 100);
            }
            ,yes: function(index, layero){
                layero.find('#pricing-form-submit').trigger('click');
            }
        });
    }
    
    // 监听提交
    form.on('submit(pricing-form-submit)', function(data){
        var field = data.field;
        var url = field.id ? '/admin/index/updateMembershipPricing' : '/admin/index/addMembershipPricing';
        
        $.ajax({
            url: url
            ,method: 'post'
            ,data: field
            ,success: function(res){
                if(res.code === 0){
                    layer.msg(field.id ? '更新成功' : '添加成功');
                    table.reload('LAY-pricing-list');
                    layer.closeAll('page');
                } else {
                    layer.msg(res.msg || (field.id ? '更新失败' : '添加失败'));
                }
            }
        });
        
        return false;
    });
});
</script>
</body>
</html>
