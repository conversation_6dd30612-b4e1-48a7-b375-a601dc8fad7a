<?php

class OauthService
{
    private $config;

    public function __construct()
    {
        $this->config = config('oauth');
    }

    public function getLoginUrl($type)
    {
        $state = md5(uniqid(rand(), true));
        session('Oauth_state', $state);

        $keysArr = [
            "act" => "login",
            "appid" => $this->config['appid'],
            "appkey" => $this->config['appkey'],
            "type" => $type,
            "redirect_uri" => $this->config['callback'],
            "state" => $state,
        ];
        $loginUrl = $this->config['apiurl'] . 'connect.php?' . http_build_query($keysArr);

        return $this->curlRequest($loginUrl);
    }
    
    public function getBindUrl($type)
    {
        $state = md5(uniqid(rand(), true));
        session('Oauth_state', $state);

        $keysArr = [
            "act" => "login",
            "appid" => $this->config['appid'],
            "appkey" => $this->config['appkey'],
            "type" => $type,
            "redirect_uri" => $this->config['callback2'],
            "state" => $state,
        ];
        $loginUrl = $this->config['apiurl'] . 'connect.php?' . http_build_query($keysArr);

        return $this->curlRequest($loginUrl);
    }
    

    public function handleCallback($code)
    {
        $keysArr = [
            "act" => "callback",
            "appid" => $this->config['appid'],
            "appkey" => $this->config['appkey'],
            "code" => $code,
        ];
        $tokenUrl = $this->config['apiurl'] . 'connect.php?' . http_build_query($keysArr);

        return $this->curlRequest($tokenUrl);
    }

    private function curlRequest($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }
}