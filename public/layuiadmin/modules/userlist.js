/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */ ;
layui.define(["table", "form"], function(t) {
	var e = layui.$,
        $ = layui.$,
		i = layui.table,
		n = layui.form;
	i.render({
			elem: "#LAY-app-content-list2",
			url: "/admin/user/userList",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 100,
						title: "ID",
				// 		sort: !0
					},
					{
						field: "us_username",
						title: "账号",
						minWidth: 100
					},
				// 	{
				// 		field: "us_password",
				// 		title: "密码",
				// 		sort: !0
				// 	},
					{
						field: "us_email",
						title: "邮箱",
						sort: !0
					},
					{
						field: "us_name",
						title: "昵称",
						templet: "#buttonTpl",
						minWidth: 80,
						align: "center"
					},
					{
						field: "us_logo",
						title: "用户头像",
						templet: "#buttonTpl",
						minWidth: 80,
						align: "center"
					},
					{
						field: "us_money",
						title: "余额",
						templet: "#buttonTpl",
						minWidth: 80,
						align: "center"
					},
					{
						field: "superior",
						title: "上级id",
						templet: "#buttonTpl",
						minWidth: 80,
						align: "center"
					},
					{
						field: "time",
						title: "注册时间",
						templet: "#buttonTpl",
						minWidth: 80,
						align: "center"
					},
					{
						title: "操作",
						minWidth: 150,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-list"
					}
				]
			],
			page: !0,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list2)",
			function(t) {
				t.data;
				"del" === t.event ? layer.confirm("确定删除此商品？",
					function(e) {
					    console.log(t.data)
						t.del(), layer.close(e)
						
						layui.$.ajax({
                                    url: '/admin/user/delUser',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "更改用户",
					content: "/admin/index/upUser?id="+t.data.id,
					area: ["454px", "500px"],
					btn: ["确定", "取消"],
					yes: function(t, e) {
						var n = window["layui-layer-iframe" + t],
							l = "layuiadmin-app-form-submit",
							a = e.find("iframe")
							.contents()
							.find("#" + l);
						n.layui.form.on("submit(" + l + ")",
							function(e) {
							    console.log(e.field)
								    $.ajax({
                                        url: '/admin/user/upUser',
                                        method: 'post',
                                        data: e.field,
                                        success: function(res){
                                        }
                                    });
								e.field;
								layer.msg('保存成功');
								i.reload("LAY-app-content-list2"),
									layer.close(t)
							}), a.trigger("click")
					},
					success: function(t, e) {
                            
					}
				})

			}),
	i.render({
			elem: "#LAY-app-content-list3",
			url: "/admin/base/userlogo",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 100,
						title: "ID",
					},
					{
						field: "uid",
						title: "触发用户",
						minWidth: 100
					},
					{
						field: "content",
						title: "描述",
						sort: !0
					},
					{
						field: "ip",
						title: "操作ip",
						templet: "#buttonTpl",
						minWidth: 80,
						align: "center"
					},
					{
						field: "time",
						title: "时间",
						templet: "#buttonTpl",
						minWidth: 80,
						align: "center"
					},
					{
						title: "操作",
						minWidth: 150,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-list"
					}
				]
			],
			page: !0,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list3)",
			function(t) {
				t.data;
				"del" === t.event ? layer.confirm("确定删除此记录？",
					function(e) {
					    console.log(t.data)
						t.del(), layer.close(e)
						
						layui.$.ajax({
                                    url: '/admin/base/delUserlogo',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "更改用户",
					content: "/admin/base/delUserlogo?id="+t.data.id,
					area: ["454px", "500px"],
					btn: ["确定", "取消"],
					yes: function(t, e) {
						var n = window["layui-layer-iframe" + t],
							l = "layuiadmin-app-form-submit",
							a = e.find("iframe")
							.contents()
							.find("#" + l);
						n.layui.form.on("submit(" + l + ")",
							function(e) {
							    console.log(e.field)
								    $.ajax({
                                        url: '/admin/user/upUser',
                                        method: 'post',
                                        data: e.field,
                                        success: function(res){
                                        }
                                    });
								e.field;
								layer.msg('保存成功');
								i.reload("LAY-app-content-list3"),
									layer.close(t)
							}), a.trigger("click")
					},
					success: function(t, e) {
                            
					}
				})

			}),
		t("userlist", {})
});