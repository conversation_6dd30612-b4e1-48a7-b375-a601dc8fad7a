blockquote,
body,
button,
dd,
div,
dl,
dt,
form,
h1,
h2,
h3,
h4,
h5,
h6,
input,
li,
ol,
p,
pre,
td,
textarea,
th,
ul {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
a:active,
a:hover {
  outline: 0;
}
img {
  display: inline-block;
  border: none;
  vertical-align: middle;
}
li {
  list-style: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
}
button,
h4,
h5,
h6,
input,
select,
textarea {
  font-size: 100%;
}
button,
input,
optgroup,
option,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  outline: 0;
}
pre {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
body {
  line-height: 24px;
  font: 14px Helvetica Neue, Helvetica, PingFang SC, \5FAE\8F6F\96C5\9ED1,
    <PERSON>homa, Aria<PERSON>, sans-serif;
}
hr {
  height: 1px;
  margin: 10px 0;
  border: 0;
  clear: both;
}
a {
  color: #333;
  text-decoration: none;
}
a:hover {
  color: #777;
}
a cite {
  font-style: normal;
  *cursor: pointer;
}
.layui-border-box,
.layui-border-box * {
  box-sizing: border-box;
}
.layui-box,
.layui-box * {
  box-sizing: content-box;
}
.layui-clear {
  clear: both;
  *zoom: 1;
}
.layui-clear:after {
  content: '\20';
  clear: both;
  *zoom: 1;
  display: block;
  height: 0;
}
.layui-inline {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
.layui-edge,
.layui-inline {
  position: relative;
  vertical-align: middle;
}
.layui-edge {
  display: inline-block;
  width: 0;
  height: 0;
  border: 6px dashed transparent;
  overflow: hidden;
}
.layui-edge-top {
  top: -4px;
  border-bottom-color: #999;
  border-bottom-style: solid;
}
.layui-edge-right {
  border-left-color: #999;
  border-left-style: solid;
}
.layui-edge-bottom {
  top: 2px;
  border-top-color: #999;
  border-top-style: solid;
}
.layui-edge-left {
  border-right-color: #999;
  border-right-style: solid;
}
.layui-elip {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.layui-disabled,
.layui-icon,
.layui-unselect {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
.layui-disabled,
.layui-disabled:hover {
  color: #d2d2d2 !important;
  cursor: not-allowed !important;
}
.layui-circle {
  border-radius: 100%;
}
.layui-show {
  display: block !important;
}
.layui-hide {
  display: none !important;
}
@font-face {
  font-family: layui-icon;
  src: url(https://zuhaowan.zuhaowan.com/shanghu/www3.0/common/fonts/iconfont.3327b7c.eot);
  src: url(https://zuhaowan.zuhaowan.com/shanghu/www3.0/common/fonts/iconfont.3327b7c.eot#iefix)
      format('embedded-opentype'),
    url(https://zuhaowan.zuhaowan.com/shanghu/www3.0/common/img/iconfont.471366a.svg#iconfont)
      format('svg'),
    url(https://zuhaowan.zuhaowan.com/shanghu/www3.0/common/fonts/iconfont.8dc4646.woff)
      format('woff'),
    url(https://zuhaowan.zuhaowan.com/shanghu/www3.0/common/fonts/iconfont.a965966.ttf)
      format('truetype');
}
.layui-icon {
  font-family: layui-icon !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.layui-icon-reply-fill:before {
  content: '\e611';
}
.layui-icon-set-fill:before {
  content: '\e614';
}
.layui-icon-menu-fill:before {
  content: '\e60f';
}
.layui-icon-search:before {
  content: '\e615';
}
.layui-icon-share:before {
  content: '\e641';
}
.layui-icon-set-sm:before {
  content: '\e620';
}
.layui-icon-engine:before {
  content: '\e628';
}
.layui-icon-close:before {
  content: '\1006';
}
.layui-icon-close-fill:before {
  content: '\1007';
}
.layui-icon-chart-screen:before {
  content: '\e629';
}
.layui-icon-star:before {
  content: '\e600';
}
.layui-icon-circle-dot:before {
  content: '\e617';
}
.layui-icon-chat:before {
  content: '\e606';
}
.layui-icon-release:before {
  content: '\e609';
}
.layui-icon-list:before {
  content: '\e60a';
}
.layui-icon-chart:before {
  content: '\e62c';
}
.layui-icon-ok-circle:before {
  content: '\1005';
}
.layui-icon-layim-theme:before {
  content: '\e61b';
}
.layui-icon-table:before {
  content: '\e62d';
}
.layui-icon-right:before {
  content: '\e602';
}
.layui-icon-left:before {
  content: '\e603';
}
.layui-icon-cart-simple:before {
  content: '\e698';
}
.layui-icon-face-cry:before {
  content: '\e69c';
}
.layui-icon-face-smile:before {
  content: '\e6af';
}
.layui-icon-survey:before {
  content: '\e6b2';
}
.layui-icon-tree:before {
  content: '\e62e';
}
.layui-icon-upload-circle:before {
  content: '\e62f';
}
.layui-icon-add-circle:before {
  content: '\e61f';
}
.layui-icon-download-circle:before {
  content: '\e601';
}
.layui-icon-templeate-1:before {
  content: '\e630';
}
.layui-icon-util:before {
  content: '\e631';
}
.layui-icon-face-surprised:before {
  content: '\e664';
}
.layui-icon-edit:before {
  content: '\e642';
}
.layui-icon-speaker:before {
  content: '\e645';
}
.layui-icon-down:before {
  content: '\e61a';
}
.layui-icon-file:before {
  content: '\e621';
}
.layui-icon-layouts:before {
  content: '\e632';
}
.layui-icon-rate-half:before {
  content: '\e6c9';
}
.layui-icon-add-circle-fine:before {
  content: '\e608';
}
.layui-icon-prev-circle:before {
  content: '\e633';
}
.layui-icon-read:before {
  content: '\e705';
}
.layui-icon-404:before {
  content: '\e61c';
}
.layui-icon-carousel:before {
  content: '\e634';
}
.layui-icon-help:before {
  content: '\e607';
}
.layui-icon-code-circle:before {
  content: '\e635';
}
.layui-icon-water:before {
  content: '\e636';
}
.layui-icon-username:before {
  content: '\e66f';
}
.layui-icon-find-fill:before {
  content: '\e670';
}
.layui-icon-about:before {
  content: '\e60b';
}
.layui-icon-location:before {
  content: '\e715';
}
.layui-icon-up:before {
  content: '\e619';
}
.layui-icon-pause:before {
  content: '\e651';
}
.layui-icon-date:before {
  content: '\e637';
}
.layui-icon-layim-uploadfile:before {
  content: '\e61d';
}
.layui-icon-delete:before {
  content: '\e640';
}
.layui-icon-play:before {
  content: '\e652';
}
.layui-icon-top:before {
  content: '\e604';
}
.layui-icon-friends:before {
  content: '\e612';
}
.layui-icon-refresh-3:before {
  content: '\e9aa';
}
.layui-icon-ok:before {
  content: '\e605';
}
.layui-icon-layer:before {
  content: '\e638';
}
.layui-icon-face-smile-fine:before {
  content: '\e60c';
}
.layui-icon-dollar:before {
  content: '\e659';
}
.layui-icon-group:before {
  content: '\e613';
}
.layui-icon-layim-download:before {
  content: '\e61e';
}
.layui-icon-picture-fine:before {
  content: '\e60d';
}
.layui-icon-link:before {
  content: '\e64c';
}
.layui-icon-diamond:before {
  content: '\e735';
}
.layui-icon-log:before {
  content: '\e60e';
}
.layui-icon-rate-solid:before {
  content: '\e67a';
}
.layui-icon-fonts-del:before {
  content: '\e64f';
}
.layui-icon-unlink:before {
  content: '\e64d';
}
.layui-icon-fonts-clear:before {
  content: '\e639';
}
.layui-icon-triangle-r:before {
  content: '\e623';
}
.layui-icon-circle:before {
  content: '\e63f';
}
.layui-icon-radio:before {
  content: '\e643';
}
.layui-icon-align-center:before {
  content: '\e647';
}
.layui-icon-align-right:before {
  content: '\e648';
}
.layui-icon-align-left:before {
  content: '\e649';
}
.layui-icon-loading-1:before {
  content: '\e63e';
}
.layui-icon-return:before {
  content: '\e65c';
}
.layui-icon-fonts-strong:before {
  content: '\e62b';
}
.layui-icon-upload:before {
  content: '\e67c';
}
.layui-icon-dialogue:before {
  content: '\e63a';
}
.layui-icon-video:before {
  content: '\e6ed';
}
.layui-icon-headset:before {
  content: '\e6fc';
}
.layui-icon-cellphone-fine:before {
  content: '\e63b';
}
.layui-icon-add-1:before {
  content: '\e654';
}
.layui-icon-face-smile-b:before {
  content: '\e650';
}
.layui-icon-fonts-html:before {
  content: '\e64b';
}
.layui-icon-form:before {
  content: '\e63c';
}
.layui-icon-cart:before {
  content: '\e657';
}
.layui-icon-camera-fill:before {
  content: '\e65d';
}
.layui-icon-tabs:before {
  content: '\e62a';
}
.layui-icon-fonts-code:before {
  content: '\e64e';
}
.layui-icon-fire:before {
  content: '\e756';
}
.layui-icon-set:before {
  content: '\e716';
}
.layui-icon-fonts-u:before {
  content: '\e646';
}
.layui-icon-triangle-d:before {
  content: '\e625';
}
.layui-icon-tips:before {
  content: '\e702';
}
.layui-icon-picture:before {
  content: '\e64a';
}
.layui-icon-more-vertical:before {
  content: '\e671';
}
.layui-icon-flag:before {
  content: '\e66c';
}
.layui-icon-loading:before {
  content: '\e63d';
}
.layui-icon-fonts-i:before {
  content: '\e644';
}
.layui-icon-refresh-1:before {
  content: '\e666';
}
.layui-icon-rmb:before {
  content: '\e65e';
}
.layui-icon-home:before {
  content: '\e68e';
}
.layui-icon-user:before {
  content: '\e770';
}
.layui-icon-notice:before {
  content: '\e667';
}
.layui-icon-login-weibo:before {
  content: '\e675';
}
.layui-icon-voice:before {
  content: '\e688';
}
.layui-icon-upload-drag:before {
  content: '\e681';
}
.layui-icon-login-qq:before {
  content: '\e676';
}
.layui-icon-snowflake:before {
  content: '\e6b1';
}
.layui-icon-file-b:before {
  content: '\e655';
}
.layui-icon-template:before {
  content: '\e663';
}
.layui-icon-auz:before {
  content: '\e672';
}
.layui-icon-console:before {
  content: '\e665';
}
.layui-icon-app:before {
  content: '\e653';
}
.layui-icon-prev:before {
  content: '\e65a';
}
.layui-icon-website:before {
  content: '\e7ae';
}
.layui-icon-next:before {
  content: '\e65b';
}
.layui-icon-component:before {
  content: '\e857';
}
.layui-icon-more:before {
  content: '\e65f';
}
.layui-icon-login-wechat:before {
  content: '\e677';
}
.layui-icon-shrink-right:before {
  content: '\e668';
}
.layui-icon-spread-left:before {
  content: '\e66b';
}
.layui-icon-camera:before {
  content: '\e660';
}
.layui-icon-note:before {
  content: '\e66e';
}
.layui-icon-refresh:before {
  content: '\e669';
}
.layui-icon-female:before {
  content: '\e661';
}
.layui-icon-male:before {
  content: '\e662';
}
.layui-icon-password:before {
  content: '\e673';
}
.layui-icon-senior:before {
  content: '\e674';
}
.layui-icon-theme:before {
  content: '\e66a';
}
.layui-icon-tread:before {
  content: '\e6c5';
}
.layui-icon-praise:before {
  content: '\e6c6';
}
.layui-icon-star-fill:before {
  content: '\e658';
}
.layui-icon-rate:before {
  content: '\e67b';
}
.layui-icon-template-1:before {
  content: '\e656';
}
.layui-icon-vercode:before {
  content: '\e679';
}
.layui-icon-cellphone:before {
  content: '\e678';
}
.layui-icon-screen-full:before {
  content: '\e622';
}
.layui-icon-screen-restore:before {
  content: '\e758';
}
.layui-main {
  position: relative;
  width: 1140px;
  margin: 0 auto;
}
.layui-header {
  position: relative;
  z-index: 1000;
  height: 60px;
}
.layui-header a:hover {
  transition: all 0.5s;
  -webkit-transition: all 0.5s;
}
.layui-side {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  width: 200px;
  overflow-x: hidden;
}
.layui-side-scroll {
  position: relative;
  width: 220px;
  height: 100%;
  overflow-x: hidden;
}
.layui-body {
  position: absolute;
  left: 200px;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 998;
  width: auto;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
}
.layui-layout-body {
  overflow: hidden;
}
.layui-layout-admin .layui-header {
  background-color: #23262e;
}
.layui-layout-admin .layui-side {
  top: 60px;
  width: 200px;
  overflow-x: hidden;
}
.layui-layout-admin .layui-body {
  top: 60px;
  bottom: 44px;
}
.layui-layout-admin .layui-main {
  width: auto;
  margin: 0 15px;
}
.layui-layout-admin .layui-footer {
  position: fixed;
  left: 200px;
  right: 0;
  bottom: 0;
  height: 44px;
  line-height: 44px;
  padding: 0 15px;
  background-color: #eee;
}
.layui-layout-admin .layui-logo {
  position: absolute;
  left: 0;
  top: 0;
  width: 200px;
  height: 100%;
  line-height: 60px;
  text-align: center;
  color: #009688;
  font-size: 16px;
}
.layui-layout-admin .layui-header .layui-nav {
  background: none;
}
.layui-layout-left {
  position: absolute !important;
  left: 200px;
  top: 0;
}
.layui-layout-right {
  position: absolute !important;
  right: 0;
  top: 0;
}
.layui-container {
  box-sizing: border-box;
}
.layui-container,
.layui-fluid {
  position: relative;
  margin: 0 auto;
  padding: 0 15px;
}
.layui-row:after,
.layui-row:before {
  content: '';
  display: block;
  clear: both;
}
.layui-col-lg1,
.layui-col-lg2,
.layui-col-lg3,
.layui-col-lg4,
.layui-col-lg5,
.layui-col-lg6,
.layui-col-lg7,
.layui-col-lg8,
.layui-col-lg9,
.layui-col-lg10,
.layui-col-lg11,
.layui-col-lg12,
.layui-col-md1,
.layui-col-md2,
.layui-col-md3,
.layui-col-md4,
.layui-col-md5,
.layui-col-md6,
.layui-col-md7,
.layui-col-md8,
.layui-col-md9,
.layui-col-md10,
.layui-col-md11,
.layui-col-md12,
.layui-col-sm1,
.layui-col-sm2,
.layui-col-sm3,
.layui-col-sm4,
.layui-col-sm5,
.layui-col-sm6,
.layui-col-sm7,
.layui-col-sm8,
.layui-col-sm9,
.layui-col-sm10,
.layui-col-sm11,
.layui-col-sm12,
.layui-col-xs1,
.layui-col-xs2,
.layui-col-xs3,
.layui-col-xs4,
.layui-col-xs5,
.layui-col-xs6,
.layui-col-xs7,
.layui-col-xs8,
.layui-col-xs9,
.layui-col-xs10,
.layui-col-xs11,
.layui-col-xs12 {
  position: relative;
  display: block;
  box-sizing: border-box;
}
.layui-col-xs1,
.layui-col-xs2,
.layui-col-xs3,
.layui-col-xs4,
.layui-col-xs5,
.layui-col-xs6,
.layui-col-xs7,
.layui-col-xs8,
.layui-col-xs9,
.layui-col-xs10,
.layui-col-xs11,
.layui-col-xs12 {
  float: left;
}
.layui-col-xs1 {
  width: 8.33333333%;
}
.layui-col-xs2 {
  width: 16.66666667%;
}
.layui-col-xs3 {
  width: 25%;
}
.layui-col-xs4 {
  width: 33.33333333%;
}
.layui-col-xs5 {
  width: 41.66666667%;
}
.layui-col-xs6 {
  width: 50%;
}
.layui-col-xs7 {
  width: 58.33333333%;
}
.layui-col-xs8 {
  width: 66.66666667%;
}
.layui-col-xs9 {
  width: 75%;
}
.layui-col-xs10 {
  width: 83.33333333%;
}
.layui-col-xs11 {
  width: 91.66666667%;
}
.layui-col-xs12 {
  width: 100%;
}
.layui-col-xs-offset1 {
  margin-left: 8.33333333%;
}
.layui-col-xs-offset2 {
  margin-left: 16.66666667%;
}
.layui-col-xs-offset3 {
  margin-left: 25%;
}
.layui-col-xs-offset4 {
  margin-left: 33.33333333%;
}
.layui-col-xs-offset5 {
  margin-left: 41.66666667%;
}
.layui-col-xs-offset6 {
  margin-left: 50%;
}
.layui-col-xs-offset7 {
  margin-left: 58.33333333%;
}
.layui-col-xs-offset8 {
  margin-left: 66.66666667%;
}
.layui-col-xs-offset9 {
  margin-left: 75%;
}
.layui-col-xs-offset10 {
  margin-left: 83.33333333%;
}
.layui-col-xs-offset11 {
  margin-left: 91.66666667%;
}
.layui-col-xs-offset12 {
  margin-left: 100%;
}
@media screen and (max-width: 768px) {
  .layui-hide-xs {
    display: none !important;
  }
  .layui-show-xs-block {
    display: block !important;
  }
  .layui-show-xs-inline {
    display: inline !important;
  }
  .layui-show-xs-inline-block {
    display: inline-block !important;
  }
}
@media screen and (min-width: 768px) {
  .layui-container {
    width: 750px;
  }
  .layui-hide-sm {
    display: none !important;
  }
  .layui-show-sm-block {
    display: block !important;
  }
  .layui-show-sm-inline {
    display: inline !important;
  }
  .layui-show-sm-inline-block {
    display: inline-block !important;
  }
  .layui-col-sm1,
  .layui-col-sm2,
  .layui-col-sm3,
  .layui-col-sm4,
  .layui-col-sm5,
  .layui-col-sm6,
  .layui-col-sm7,
  .layui-col-sm8,
  .layui-col-sm9,
  .layui-col-sm10,
  .layui-col-sm11,
  .layui-col-sm12 {
    float: left;
  }
  .layui-col-sm1 {
    width: 8.33333333%;
  }
  .layui-col-sm2 {
    width: 16.66666667%;
  }
  .layui-col-sm3 {
    width: 25%;
  }
  .layui-col-sm4 {
    width: 33.33333333%;
  }
  .layui-col-sm5 {
    width: 41.66666667%;
  }
  .layui-col-sm6 {
    width: 50%;
  }
  .layui-col-sm7 {
    width: 58.33333333%;
  }
  .layui-col-sm8 {
    width: 66.66666667%;
  }
  .layui-col-sm9 {
    width: 75%;
  }
  .layui-col-sm10 {
    width: 83.33333333%;
  }
  .layui-col-sm11 {
    width: 91.66666667%;
  }
  .layui-col-sm12 {
    width: 100%;
  }
  .layui-col-sm-offset1 {
    margin-left: 8.33333333%;
  }
  .layui-col-sm-offset2 {
    margin-left: 16.66666667%;
  }
  .layui-col-sm-offset3 {
    margin-left: 25%;
  }
  .layui-col-sm-offset4 {
    margin-left: 33.33333333%;
  }
  .layui-col-sm-offset5 {
    margin-left: 41.66666667%;
  }
  .layui-col-sm-offset6 {
    margin-left: 50%;
  }
  .layui-col-sm-offset7 {
    margin-left: 58.33333333%;
  }
  .layui-col-sm-offset8 {
    margin-left: 66.66666667%;
  }
  .layui-col-sm-offset9 {
    margin-left: 75%;
  }
  .layui-col-sm-offset10 {
    margin-left: 83.33333333%;
  }
  .layui-col-sm-offset11 {
    margin-left: 91.66666667%;
  }
  .layui-col-sm-offset12 {
    margin-left: 100%;
  }
}
@media screen and (min-width: 992px) {
  .layui-container {
    width: 970px;
  }
  .layui-hide-md {
    display: none !important;
  }
  .layui-show-md-block {
    display: block !important;
  }
  .layui-show-md-inline {
    display: inline !important;
  }
  .layui-show-md-inline-block {
    display: inline-block !important;
  }
  .layui-col-md1,
  .layui-col-md2,
  .layui-col-md3,
  .layui-col-md4,
  .layui-col-md5,
  .layui-col-md6,
  .layui-col-md7,
  .layui-col-md8,
  .layui-col-md9,
  .layui-col-md10,
  .layui-col-md11,
  .layui-col-md12 {
    float: left;
  }
  .layui-col-md1 {
    width: 8.33333333%;
  }
  .layui-col-md2 {
    width: 16.66666667%;
  }
  .layui-col-md3 {
    width: 25%;
  }
  .layui-col-md4 {
    width: 33.33333333%;
  }
  .layui-col-md5 {
    width: 41.66666667%;
  }
  .layui-col-md6 {
    width: 50%;
  }
  .layui-col-md7 {
    width: 58.33333333%;
  }
  .layui-col-md8 {
    width: 66.66666667%;
  }
  .layui-col-md9 {
    width: 75%;
  }
  .layui-col-md10 {
    width: 83.33333333%;
  }
  .layui-col-md11 {
    width: 91.66666667%;
  }
  .layui-col-md12 {
    width: 100%;
  }
  .layui-col-md-offset1 {
    margin-left: 8.33333333%;
  }
  .layui-col-md-offset2 {
    margin-left: 16.66666667%;
  }
  .layui-col-md-offset3 {
    margin-left: 25%;
  }
  .layui-col-md-offset4 {
    margin-left: 33.33333333%;
  }
  .layui-col-md-offset5 {
    margin-left: 41.66666667%;
  }
  .layui-col-md-offset6 {
    margin-left: 50%;
  }
  .layui-col-md-offset7 {
    margin-left: 58.33333333%;
  }
  .layui-col-md-offset8 {
    margin-left: 66.66666667%;
  }
  .layui-col-md-offset9 {
    margin-left: 75%;
  }
  .layui-col-md-offset10 {
    margin-left: 83.33333333%;
  }
  .layui-col-md-offset11 {
    margin-left: 91.66666667%;
  }
  .layui-col-md-offset12 {
    margin-left: 100%;
  }
}
@media screen and (min-width: 1200px) {
  .layui-container {
    width: 1170px;
  }
  .layui-hide-lg {
    display: none !important;
  }
  .layui-show-lg-block {
    display: block !important;
  }
  .layui-show-lg-inline {
    display: inline !important;
  }
  .layui-show-lg-inline-block {
    display: inline-block !important;
  }
  .layui-col-lg1,
  .layui-col-lg2,
  .layui-col-lg3,
  .layui-col-lg4,
  .layui-col-lg5,
  .layui-col-lg6,
  .layui-col-lg7,
  .layui-col-lg8,
  .layui-col-lg9,
  .layui-col-lg10,
  .layui-col-lg11,
  .layui-col-lg12 {
    float: left;
  }
  .layui-col-lg1 {
    width: 8.33333333%;
  }
  .layui-col-lg2 {
    width: 16.66666667%;
  }
  .layui-col-lg3 {
    width: 25%;
  }
  .layui-col-lg4 {
    width: 33.33333333%;
  }
  .layui-col-lg5 {
    width: 41.66666667%;
  }
  .layui-col-lg6 {
    width: 50%;
  }
  .layui-col-lg7 {
    width: 58.33333333%;
  }
  .layui-col-lg8 {
    width: 66.66666667%;
  }
  .layui-col-lg9 {
    width: 75%;
  }
  .layui-col-lg10 {
    width: 83.33333333%;
  }
  .layui-col-lg11 {
    width: 91.66666667%;
  }
  .layui-col-lg12 {
    width: 100%;
  }
  .layui-col-lg-offset1 {
    margin-left: 8.33333333%;
  }
  .layui-col-lg-offset2 {
    margin-left: 16.66666667%;
  }
  .layui-col-lg-offset3 {
    margin-left: 25%;
  }
  .layui-col-lg-offset4 {
    margin-left: 33.33333333%;
  }
  .layui-col-lg-offset5 {
    margin-left: 41.66666667%;
  }
  .layui-col-lg-offset6 {
    margin-left: 50%;
  }
  .layui-col-lg-offset7 {
    margin-left: 58.33333333%;
  }
  .layui-col-lg-offset8 {
    margin-left: 66.66666667%;
  }
  .layui-col-lg-offset9 {
    margin-left: 75%;
  }
  .layui-col-lg-offset10 {
    margin-left: 83.33333333%;
  }
  .layui-col-lg-offset11 {
    margin-left: 91.66666667%;
  }
  .layui-col-lg-offset12 {
    margin-left: 100%;
  }
}
.layui-col-space1 {
  margin: -0.5px;
}
.layui-col-space1 > * {
  padding: 0.5px;
}
.layui-col-space3 {
  margin: -1.5px;
}
.layui-col-space3 > * {
  padding: 1.5px;
}
.layui-col-space5 {
  margin: -2.5px;
}
.layui-col-space5 > * {
  padding: 2.5px;
}
.layui-col-space8 {
  margin: -3.5px;
}
.layui-col-space8 > * {
  padding: 3.5px;
}
.layui-col-space10 {
  margin: -5px;
}
.layui-col-space10 > * {
  padding: 5px;
}
.layui-col-space12 {
  margin: -6px;
}
.layui-col-space12 > * {
  padding: 6px;
}
.layui-col-space15 {
  margin: -7.5px;
}
.layui-col-space15 > * {
  padding: 7.5px;
}
.layui-col-space18 {
  margin: -9px;
}
.layui-col-space18 > * {
  padding: 9px;
}
.layui-col-space20 {
  margin: -10px;
}
.layui-col-space20 > * {
  padding: 10px;
}
.layui-col-space22 {
  margin: -11px;
}
.layui-col-space22 > * {
  padding: 11px;
}
.layui-col-space25 {
  margin: -12.5px;
}
.layui-col-space25 > * {
  padding: 12.5px;
}
.layui-col-space30 {
  margin: -15px;
}
.layui-col-space30 > * {
  padding: 15px;
}
.layui-btn,
.layui-input,
.layui-select,
.layui-textarea,
.layui-upload-button {
  outline: none;
  -webkit-appearance: none;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  box-sizing: border-box;
}
.layui-elem-quote {
  margin-bottom: 10px;
  padding: 15px;
  line-height: 22px;
  border-left: 5px solid #009688;
  border-radius: 0 2px 2px 0;
  background-color: #f2f2f2;
}
.layui-quote-nm {
  border-style: solid;
  border-width: 1px 1px 1px 5px;
  background: none;
}
.layui-elem-field {
  margin-bottom: 10px;
  padding: 0;
  border-width: 1px;
  border-style: solid;
}
.layui-elem-field legend {
  margin-left: 20px;
  padding: 0 10px;
  font-size: 20px;
  font-weight: 300;
}
.layui-field-title {
  margin: 10px 0 20px;
  border-width: 1px 0 0;
}
.layui-field-box {
  padding: 10px 15px;
}
.layui-field-title .layui-field-box {
  padding: 10px 0;
}
.layui-progress {
  position: relative;
  height: 6px;
  border-radius: 20px;
  background-color: #e2e2e2;
}
.layui-progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  max-width: 100%;
  height: 6px;
  border-radius: 20px;
  text-align: right;
  background-color: #5fb878;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.layui-progress-big,
.layui-progress-big .layui-progress-bar {
  height: 18px;
  line-height: 18px;
}
.layui-progress-text {
  position: relative;
  top: -20px;
  line-height: 18px;
  font-size: 12px;
  color: #666;
}
.layui-progress-big .layui-progress-text {
  position: static;
  padding: 0 10px;
  color: #fff;
}
.layui-collapse {
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;
}
.layui-colla-content,
.layui-colla-item {
  border-top-width: 1px;
  border-top-style: solid;
}
.layui-colla-item:first-child {
  border-top: none;
}
.layui-colla-title {
  position: relative;
  height: 42px;
  line-height: 42px;
  padding: 0 15px 0 35px;
  color: #333;
  background-color: #f2f2f2;
  cursor: pointer;
  font-size: 14px;
  overflow: hidden;
}
.layui-colla-content {
  display: none;
  padding: 10px 15px;
  line-height: 22px;
  color: #666;
}
.layui-colla-icon {
  position: absolute;
  left: 15px;
  top: 0;
  font-size: 14px;
}
.layui-card {
  margin-bottom: 15px;
  border-radius: 2px;
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.layui-card:last-child {
  margin-bottom: 0;
}
.layui-card-header {
  position: relative;
  height: 42px;
  line-height: 42px;
  padding: 0 15px;
  border-bottom: 1px solid #f6f6f6;
  color: #333;
  border-radius: 2px 2px 0 0;
  font-size: 14px;
}
.layui-card-body {
  position: relative;
  padding: 10px 15px;
  line-height: 24px;
}
.layui-card-body[pad15] {
  padding: 15px;
}
.layui-card-body[pad20] {
  padding: 20px;
}
.layui-card-body .layui-table {
  margin: 5px 0;
}
.layui-card .layui-tab {
  margin: 0;
}
.layui-panel-window {
  position: relative;
  padding: 15px;
  border-radius: 0;
  border-top: 5px solid #e6e6e6;
  background-color: #fff;
}
.layui-bg-red {
  background-color: #ff5722 !important;
  color: #fff !important;
}
.layui-bg-orange {
  background-color: #ffb800 !important;
  color: #fff !important;
}
.layui-bg-green {
  background-color: #009688 !important;
  color: #fff !important;
}
.layui-bg-cyan {
  background-color: #2f4056 !important;
  color: #fff !important;
}
.layui-bg-blue {
  background-color: #1e9fff !important;
  color: #fff !important;
}
.layui-bg-black {
  background-color: #393d49 !important;
  color: #fff !important;
}
.layui-bg-gray {
  background-color: #eee !important;
  color: #666 !important;
}
.layui-badge-rim,
.layui-colla-content,
.layui-colla-item,
.layui-collapse,
.layui-elem-field,
.layui-form-pane .layui-form-item[pane],
.layui-form-pane .layui-form-label,
.layui-input,
.layui-layedit,
.layui-layedit-tool,
.layui-quote-nm,
.layui-select,
.layui-tab-bar,
.layui-tab-card,
.layui-tab-title,
.layui-tab-title .layui-this:after,
.layui-textarea {
  border-color: #e6e6e6;
}
.layui-timeline-item:before,
hr {
  background-color: #e6e6e6;
}
.layui-text {
  line-height: 22px;
  font-size: 14px;
  color: #666;
}
.layui-text h1,
.layui-text h2,
.layui-text h3 {
  font-weight: 500;
  color: #333;
}
.layui-text h1 {
  font-size: 30px;
}
.layui-text h2 {
  font-size: 24px;
}
.layui-text h3 {
  font-size: 18px;
}
.layui-text a:not(.layui-btn) {
  color: #01aaed;
}
.layui-text a:not(.layui-btn):hover {
  text-decoration: underline;
}
.layui-text ul {
  padding: 5px 0 5px 15px;
}
.layui-text ul li {
  margin-top: 5px;
  list-style-type: disc;
}
.layui-text em,
.layui-word-aux {
  color: #999 !important;
  padding: 0 5px !important;
}
.layui-btn {
  display: inline-block;
  vertical-align: middle;
  height: 38px;
  line-height: 38px;
  padding: 0 18px;
  background-color: #009688;
  color: #fff;
  white-space: nowrap;
  text-align: center;
  font-size: 14px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
.layui-btn:hover {
  opacity: 0.8;
  filter: alpha(opacity=80);
  color: #fff;
}
.layui-btn:active {
  opacity: 1;
  filter: alpha(opacity=100);
}
.layui-btn + .layui-btn {
  margin-left: 10px;
}
.layui-btn-container {
  font-size: 0;
}
.layui-btn-container .layui-btn {
  margin-right: 10px;
  margin-bottom: 10px;
}
.layui-btn-container .layui-btn + .layui-btn {
  margin-left: 0;
}
.layui-table .layui-btn-container .layui-btn {
  margin-bottom: 9px;
}
.layui-btn-radius {
  border-radius: 100px;
}
.layui-btn .layui-icon {
  margin-right: 3px;
  font-size: 18px;
  vertical-align: bottom;
  vertical-align: middle\0;
}
.layui-btn-primary {
  border: 1px solid #c9c9c9;
  background-color: #fff;
  color: #555;
}
.layui-btn-primary:hover {
  border-color: #009688;
  color: #333;
}
.layui-btn-normal {
  background-color: #1e9fff;
}
.layui-btn-warm {
  background-color: #ffb800;
}
.layui-btn-danger {
  background-color: #ff5722;
}
.layui-btn-disabled,
.layui-btn-disabled:active,
.layui-btn-disabled:hover {
  border: 1px solid #e6e6e6;
  background-color: #fbfbfb;
  color: #c9c9c9;
  cursor: not-allowed;
  opacity: 1;
}
.layui-btn-lg {
  height: 44px;
  line-height: 44px;
  padding: 0 25px;
  font-size: 16px;
}
.layui-btn-sm {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  font-size: 12px;
}
.layui-btn-sm i {
  font-size: 16px !important;
}
.layui-btn-xs {
  height: 22px;
  line-height: 22px;
  padding: 0 5px;
  font-size: 12px;
}
.layui-btn-xs i {
  font-size: 14px !important;
}
.layui-btn-group {
  display: inline-block;
  vertical-align: middle;
  font-size: 0;
}
.layui-btn-group .layui-btn {
  margin-left: 0 !important;
  margin-right: 0 !important;
  border-left: 1px solid hsla(0, 0%, 100%, 0.5);
  border-radius: 0;
}
.layui-btn-group .layui-btn-primary {
  border-left: none;
}
.layui-btn-group .layui-btn-primary:hover {
  border-color: #c9c9c9;
  color: #009688;
}
.layui-btn-group .layui-btn:first-child {
  border-left: none;
  border-radius: 2px 0 0 2px;
}
.layui-btn-group .layui-btn-primary:first-child {
  border-left: 1px solid #c9c9c9;
}
.layui-btn-group .layui-btn:last-child {
  border-radius: 0 2px 2px 0;
}
.layui-btn-group .layui-btn + .layui-btn {
  margin-left: 0;
}
.layui-btn-group + .layui-btn-group {
  margin-left: 10px;
}
.layui-btn-fluid {
  width: 100%;
}
.layui-input,
.layui-laypage-btn {
  color: #88b5e3;
}
.layui-input,
.layui-select,
.layui-textarea {
  height: 38px;
  line-height: 1.3;
  line-height: 38px\9;
  border-width: 1px;
  border-style: solid;
  background-color: #fff;
  border-radius: 2px;
}
.layui-input::-webkit-input-placeholder,
.layui-select::-webkit-input-placeholder,
.layui-textarea::-webkit-input-placeholder {
  line-height: 1.3;
}
.layui-input,
.layui-textarea {
  display: block;
  width: 100%;
  padding-left: 10px;
}
.layui-input:hover,
.layui-textarea:hover {
  border-color: #d2d2d2 !important;
}
.layui-input:focus,
.layui-textarea:focus {
  border-color: #c9c9c9 !important;
}
.layui-textarea {
  position: relative;
  min-height: 100px;
  height: auto;
  line-height: 20px;
  padding: 6px 10px;
  resize: vertical;
}
.layui-select {
  padding: 0 10px;
}
.layui-form input[type='checkbox'],
.layui-form input[type='radio'],
.layui-form select {
  display: none;
}
.layui-form [lay-ignore] {
  display: initial;
}
.layui-form-item {
  margin-bottom: 15px;
  clear: both;
  *zoom: 1;
}
.layui-form-item:after {
  content: '\20';
  clear: both;
  *zoom: 1;
  display: block;
  height: 0;
}
.layui-form-label {
  position: relative;
  float: left;
  display: block;
  padding: 9px 15px;
  width: 80px;
  font-weight: 400;
  line-height: 20px;
  text-align: right;
}
.layui-form-label-col {
  display: block;
  float: none;
  padding: 9px 0;
  line-height: 20px;
  text-align: left;
}
.layui-form-item .layui-inline {
  margin-bottom: 5px;
  margin-right: 10px;
}
.layui-input-block,
.layui-input-inline {
  position: relative;
}
.layui-input-block {
  margin-left: 110px;
  min-height: 36px;
}
.layui-input-inline {
  display: inline-block;
  vertical-align: middle;
}
.layui-form-item .layui-input-inline {
  float: left;
  width: 190px;
  margin-right: 10px;
}
.layui-form-text .layui-input-inline {
  width: auto;
}
.layui-form-mid {
  position: relative;
  float: left;
  display: block;
  padding: 9px 0 !important;
  line-height: 20px;
  margin-right: 10px;
}
.layui-form-danger + .layui-form-select .layui-input,
.layui-form-danger:focus {
  border-color: #ff5722 !important;
}
.layui-form-select {
  position: relative;
}
.layui-form-select .layui-input {
  padding-right: 30px;
  cursor: pointer;
}
.layui-form-select .layui-edge {
  position: absolute;
  right: 10px;
  top: 50%;
  margin-top: -3px;
  cursor: pointer;
  border-top: solid #c2c2c2;
  border-width: 6px;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.layui-form-select dl {
  display: none;
  position: absolute;
  left: 0;
  top: 42px;
  padding: 5px 0;
  z-index: 999;
  min-width: 100%;
  border: 1px solid #d2d2d2;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  box-sizing: border-box;
}
.layui-form-select dl dd,
.layui-form-select dl dt {
  padding: 0 10px;
  line-height: 36px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.layui-form-select dl dt {
  font-size: 12px;
  color: #999;
}
.layui-form-select dl dd {
  cursor: pointer;
}
.layui-form-select dl dd:hover {
  background-color: #f2f2f2;
}
.layui-form-select .layui-select-group dd {
  padding-left: 20px;
}
.layui-form-select dl dd.layui-select-tips {
  padding-left: 10px !important;
  color: #999;
}
.layui-form-select dl dd.layui-this {
  background-color: #5fb878;
  color: #fff;
}
.layui-form-select dl dd.layui-disabled {
  background-color: #fff;
}
.layui-form-selected dl {
  display: block;
}
.layui-form-selected .layui-edge {
  margin-top: -9px;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
  margin-top: -3px\0;
}
:root .layui-form-selected .layui-edge {
  margin-top: -9px\0 / IE9;
}
.layui-form-selectup dl {
  top: auto;
  bottom: 42px;
}
.layui-select-none {
  margin: 5px 0;
  text-align: center;
  color: #999;
}
.layui-select-disabled .layui-disabled {
  border-color: #eee !important;
}
.layui-select-disabled .layui-edge {
  border-top-color: #d2d2d2;
}
.layui-form-checkbox {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  height: 30px;
  line-height: 30px;
  margin-right: 10px;
  padding-right: 30px;
  background-color: #fff;
  cursor: pointer;
  font-size: 0;
  -webkit-transition: 0.1s linear;
  transition: 0.1s linear;
  box-sizing: border-box;
}
.layui-form-checkbox * {
  display: inline-block;
  vertical-align: middle;
}
.layui-form-checkbox span {
  padding: 0 10px;
  height: 100%;
  font-size: 14px;
  border-radius: 2px 0 0 2px;
  background-color: #d2d2d2;
  color: #fff;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.layui-form-checkbox:hover span {
  background-color: #c2c2c2;
}
.layui-form-checkbox i {
  position: absolute;
  right: 0;
  top: 0;
  width: 30px;
  height: 28px;
  border: 1px solid #d2d2d2;
  border-left: none;
  border-radius: 0 2px 2px 0;
  color: #fff;
  font-size: 20px;
  text-align: center;
}
.layui-form-checkbox:hover i {
  border-color: #c2c2c2;
  color: #c2c2c2;
}
.layui-form-checked,
.layui-form-checked:hover {
  border-color: #5fb878;
}
.layui-form-checked:hover span,
.layui-form-checked span {
  background-color: #5fb878;
}
.layui-form-checked:hover i,
.layui-form-checked i {
  color: #5fb878;
}
.layui-form-item .layui-form-checkbox {
  margin-top: 4px;
}
.layui-form-checkbox[lay-skin='primary'] {
  height: auto !important;
  line-height: normal !important;
  border: none !important;
  margin-right: 0;
  padding-right: 0;
  background: none;
}
.layui-form-checkbox[lay-skin='primary'] span {
  float: right;
  padding-right: 15px;
  line-height: 18px;
  background: none;
  color: #666;
}
.layui-form-checkbox[lay-skin='primary'] i {
  position: relative;
  top: 0;
  width: 16px;
  height: 16px;
  line-height: 16px;
  border: 1px solid #d2d2d2;
  font-size: 12px;
  border-radius: 2px;
  background-color: #fff;
  -webkit-transition: 0.1s linear;
  transition: 0.1s linear;
}
.layui-form-checkbox[lay-skin='primary']:hover i {
  border-color: #5fb878;
  color: #fff;
}
.layui-form-checked[lay-skin='primary'] i {
  border-color: #5fb878;
  background-color: #5fb878;
  color: #fff;
}
.layui-checkbox-disbaled[lay-skin='primary'] span {
  background: none !important;
  color: #c2c2c2;
}
.layui-checkbox-disbaled[lay-skin='primary']:hover i {
  border-color: #d2d2d2;
}
.layui-form-item .layui-form-checkbox[lay-skin='primary'] {
  margin-top: 10px;
}
.layui-form-switch {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  height: 22px;
  line-height: 22px;
  min-width: 35px;
  padding: 0 5px;
  margin-top: 8px;
  border: 1px solid #d2d2d2;
  border-radius: 20px;
  cursor: pointer;
  background-color: #fff;
}
.layui-form-switch,
.layui-form-switch i {
  -webkit-transition: 0.1s linear;
  transition: 0.1s linear;
}
.layui-form-switch i {
  position: absolute;
  left: 5px;
  top: 3px;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  background-color: #d2d2d2;
}
.layui-form-switch em {
  position: relative;
  top: 0;
  width: 25px;
  margin-left: 21px;
  padding: 0 !important;
  text-align: center !important;
  color: #999 !important;
  font-style: normal !important;
  font-size: 12px;
}
.layui-form-onswitch {
  border-color: #5fb878;
  background-color: #5fb878;
}
.layui-form-onswitch i {
  left: 100%;
  margin-left: -21px;
  background-color: #fff;
}
.layui-form-onswitch em {
  margin-left: 5px;
  margin-right: 21px;
  color: #fff !important;
}
.layui-checkbox-disbaled {
  border-color: #e2e2e2 !important;
}
.layui-checkbox-disbaled span {
  background-color: #e2e2e2 !important;
}
.layui-checkbox-disbaled i {
  border-color: #e2e2e2 !important;
}
.layui-checkbox-disbaled:hover i {
  color: #fff !important;
}
[lay-radio] {
  display: none;
}
.layui-form-radio {
  line-height: 28px;
  margin: 6px 10px 0 0;
  padding-right: 10px;
  cursor: pointer;
  font-size: 0;
}
.layui-form-radio,
.layui-form-radio * {
  display: inline-block;
  vertical-align: middle;
}
.layui-form-radio * {
  font-size: 14px;
}
.layui-form-radio > i {
  margin-right: 8px;
  font-size: 22px;
  color: #c2c2c2;
}
.layui-form-radio > i:hover,
.layui-form-radioed > i {
  color: #5fb878;
}
.layui-radio-disbaled > i {
  color: #e2e2e2 !important;
}
.layui-form-pane .layui-form-label {
  width: 110px;
  padding: 8px 15px;
  height: 38px;
  line-height: 20px;
  border-width: 1px;
  border-style: solid;
  border-radius: 2px 0 0 2px;
  text-align: center;
  background-color: #fbfbfb;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-sizing: border-box;
}
.layui-form-pane .layui-input-inline {
  margin-left: -1px;
}
.layui-form-pane .layui-input-block {
  margin-left: 110px;
  left: -1px;
}
.layui-form-pane .layui-input {
  border-radius: 0 2px 2px 0;
}
.layui-form-pane .layui-form-text .layui-form-label {
  float: none;
  width: 100%;
  border-radius: 2px;
  box-sizing: border-box;
  text-align: left;
}
.layui-form-pane .layui-form-text .layui-input-inline {
  display: block;
  margin: 0;
  top: -1px;
  clear: both;
}
.layui-form-pane .layui-form-text .layui-input-block {
  margin: 0;
  left: 0;
  top: -1px;
}
.layui-form-pane .layui-form-text .layui-textarea {
  min-height: 100px;
  border-radius: 0 0 2px 2px;
}
.layui-form-pane .layui-form-checkbox {
  margin: 4px 0 4px 10px;
}
.layui-form-pane .layui-form-radio,
.layui-form-pane .layui-form-switch {
  margin-top: 6px;
  margin-left: 10px;
}
.layui-form-pane .layui-form-item[pane] {
  position: relative;
  border-width: 1px;
  border-style: solid;
}
.layui-form-pane .layui-form-item[pane] .layui-form-label {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  border-width: 0 1px 0 0;
}
.layui-form-pane .layui-form-item[pane] .layui-input-inline {
  margin-left: 110px;
}
@media screen and (max-width: 450px) {
  .layui-form-item .layui-form-label {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .layui-form-item .layui-inline {
    display: block;
    margin-right: 0;
    margin-bottom: 20px;
    clear: both;
  }
  .layui-form-item .layui-inline:after {
    content: '\20';
    clear: both;
    display: block;
    height: 0;
  }
  .layui-form-item .layui-input-inline {
    display: block;
    float: none;
    left: -3px;
    width: auto;
    margin: 0 0 10px 112px;
  }
  .layui-form-item .layui-input-inline + .layui-form-mid {
    margin-left: 110px;
    top: -5px;
    padding: 0;
  }
  .layui-form-item .layui-form-checkbox {
    margin-right: 5px;
    margin-bottom: 5px;
  }
}
.layui-layedit {
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;
}
.layui-layedit-tool {
  padding: 3px 5px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  font-size: 0;
}
.layedit-tool-fixed {
  position: fixed;
  top: 0;
  border-top: 1px solid #e2e2e2;
}
.layui-layedit-tool .layedit-tool-mid,
.layui-layedit-tool .layui-icon {
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  font-size: 14px;
}
.layui-layedit-tool .layui-icon {
  position: relative;
  width: 32px;
  height: 30px;
  line-height: 30px;
  margin: 3px 5px;
  color: #777;
  cursor: pointer;
  border-radius: 2px;
}
.layui-layedit-tool .layui-icon:hover {
  color: #393d49;
}
.layui-layedit-tool .layui-icon:active {
  color: #000;
}
.layui-layedit-tool .layedit-tool-active {
  background-color: #e2e2e2;
  color: #000;
}
.layui-layedit-tool .layui-disabled,
.layui-layedit-tool .layui-disabled:hover {
  color: #d2d2d2;
  cursor: not-allowed;
}
.layui-layedit-tool .layedit-tool-mid {
  width: 1px;
  height: 18px;
  margin: 0 10px;
  background-color: #d2d2d2;
}
.layedit-tool-html {
  width: 50px !important;
  font-size: 30px !important;
}
.layedit-tool-b,
.layedit-tool-code,
.layedit-tool-help {
  font-size: 16px !important;
}
.layedit-tool-d,
.layedit-tool-face,
.layedit-tool-image,
.layedit-tool-unlink {
  font-size: 18px !important;
}
.layedit-tool-image input {
  position: absolute;
  font-size: 0;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.01;
  filter: Alpha(opacity=1);
  cursor: pointer;
}
.layui-layedit-iframe iframe {
  display: block;
  width: 100%;
}
#LAY_layedit_code {
  overflow: hidden;
}
.layui-laypage {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: middle;
  margin: 42px 0 50px;
  font-size: 0;
}
.layui-laypage > a:first-child,
.layui-laypage > a:first-child em {
  border-radius: 2px 0 0 2px;
}
.layui-laypage > a:last-child,
.layui-laypage > a:last-child em {
  border-radius: 0 2px 2px 0;
}
.layui-laypage > :first-child {
  margin-left: 0 !important;
}
.layui-laypage > :last-child {
  margin-right: 0 !important;
}
.layui-laypage a,
.layui-laypage span {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: middle;
  padding: 0 15px;
  height: 40px;
  width: 40px;
  box-sizing: border-box;
  line-height: 40px;
  margin: 0 10px 0 0;
  background-color: #142334;
  color: #88b5e3;
  font-size: 14px;
}
.layui-laypage a:hover {
  color: #009688;
}
.layui-laypage em {
  font-style: normal;
}
.layui-laypage .layui-laypage-spr {
  color: #999;
  font-weight: 700;
}
.layui-laypage a {
  text-decoration: none;
}
.layui-laypage .layui-laypage-curr {
  position: relative;
}
.layui-laypage .layui-laypage-curr em {
  position: relative;
  color: #fff;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  position: absolute;
  left: 0;
  top: 0;
  margin-right: 10px;
  padding: 1px;
  width: 40px;
  height: 40px;
  box-sizing: border-box;
  background-color: #009688;
}
.layui-laypage-em {
  border-radius: 2px;
}
.layui-laypage-next em,
.layui-laypage-prev em {
  font-family: Sim sun;
  font-size: 16px;
}
.layui-laypage .layui-laypage-count,
.layui-laypage .layui-laypage-limits,
.layui-laypage .layui-laypage-refresh,
.layui-laypage .layui-laypage-skip {
  margin-left: 12px;
  margin-right: 20px;
  padding: 0;
  border: none;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: middle;
  height: 40px;
  width: 198px;
  box-sizing: border-box;
  line-height: 40px;
  background-color: transparent;
  color: #88b5e3;
  font-size: 16px;
}
.layui-laypage .layui-laypage-limits,
.layui-laypage .layui-laypage-refresh {
  vertical-align: top;
}
.layui-laypage .layui-laypage-refresh i {
  font-size: 18px;
  cursor: pointer;
}
.layui-laypage select {
  height: 22px;
  padding: 3px;
  border-radius: 2px;
  cursor: pointer;
}
.layui-laypage .layui-laypage-skip {
  height: 40px;
  line-height: 40px;
  color: #88b5e3;
}
.layui-laypage button,
.layui-laypage input {
  height: 40px;
  line-height: 40px;
  border-radius: 2px;
  border: 0;
  vertical-align: top;
  background-color: #142334;
  box-sizing: border-box;
}
.layui-laypage input {
  display: inline-block;
  width: 40px;
  padding: 0 3px;
  margin: 0 12px 0 22px;
  border: 0;
  text-align: center;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #009688 !important;
}
.layui-laypage button {
  margin-left: 22px;
  padding: 0 10px;
  cursor: pointer;
}
.layui-flow-more {
  margin: 10px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
}
.layui-flow-more a {
  height: 32px;
  line-height: 32px;
}
.layui-flow-more a * {
  display: inline-block;
  vertical-align: top;
}
.layui-flow-more a cite {
  padding: 0 20px;
  border-radius: 3px;
  background-color: #eee;
  color: #333;
  font-style: normal;
}
.layui-flow-more a cite:hover {
  opacity: 0.8;
}
.layui-flow-more a i {
  font-size: 30px;
  color: #737383;
}
.layui-table {
  width: 100%;
  margin: 10px 0;
  background-color: #fff;
  color: #666;
}
.layui-table tr {
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.layui-table th {
  text-align: left;
  font-weight: 400;
}
.layui-table-click,
.layui-table-header,
.layui-table-hover,
.layui-table-mend,
.layui-table-patch,
.layui-table-tool,
.layui-table[lay-even] tr:nth-child(2n),
.layui-table tbody tr:hover,
.layui-table thead tr {
  background-color: #f2f2f2;
}
.layui-table-fixed-r,
.layui-table-header,
.layui-table-page,
.layui-table-tips-main,
.layui-table-tool,
.layui-table-view,
.layui-table[lay-skin='line'],
.layui-table[lay-skin='row'],
.layui-table td,
.layui-table th {
  border: 1px solid #e6e6e6;
}
.layui-table td,
.layui-table th {
  position: relative;
  padding: 9px 15px;
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
}
.layui-table[lay-skin='line'] td,
.layui-table[lay-skin='line'] th {
  border-width: 0 0 1px;
}
.layui-table[lay-skin='row'] td,
.layui-table[lay-skin='row'] th {
  border-width: 0 1px 0 0;
}
.layui-table[lay-skin='nob'] td,
.layui-table[lay-skin='nob'] th {
  border: none;
}
.layui-table img {
  max-width: 100px;
}
.layui-table[lay-size='lg'] td,
.layui-table[lay-size='lg'] th {
  padding: 15px 30px;
}
.layui-table-view .layui-table[lay-size='lg'] .layui-table-cell {
  height: 40px;
  line-height: 40px;
}
.layui-table[lay-size='sm'] td,
.layui-table[lay-size='sm'] th {
  padding: 5px 10px;
  font-size: 12px;
}
.layui-table-view .layui-table[lay-size='sm'] .layui-table-cell {
  height: 20px;
  line-height: 20px;
}
.layui-table[lay-data] {
  display: none;
}
.layui-table-box,
.layui-table-view {
  position: relative;
  overflow: hidden;
}
.layui-table-view {
  margin: 10px 0;
}
.layui-table-view .layui-table {
  position: relative;
  width: auto;
  margin: 0;
}
.layui-table-view .layui-table[lay-skin='line'] {
  border-width: 0 1px 0 0;
}
.layui-table-view .layui-table[lay-skin='row'] {
  border-width: 0 0 1px;
}
.layui-table-view .layui-table td,
.layui-table-view .layui-table th {
  padding: 5px 0;
  border-top: none;
  border-left: none;
}
.layui-table-view .layui-table td {
  cursor: default;
}
.layui-table-view .layui-form-checkbox[lay-skin='primary'] i {
  width: 18px;
  height: 18px;
}
.layui-table-header {
  border-width: 0 0 1px;
  overflow: hidden;
}
.layui-table-header .layui-table {
  margin-bottom: -1px;
}
.layui-table-sort {
  width: 10px;
  height: 20px;
  margin-left: 5px;
  cursor: pointer !important;
}
.layui-table-sort .layui-edge {
  position: absolute;
  left: 5px;
  border-width: 5px;
}
.layui-table-sort .layui-table-sort-asc {
  top: 4px;
  border-top: none;
  border-bottom-style: solid;
  border-bottom-color: #b2b2b2;
}
.layui-table-sort .layui-table-sort-asc:hover {
  border-bottom-color: #666;
}
.layui-table-sort .layui-table-sort-desc {
  bottom: 4px;
  border-bottom: none;
  border-top-style: solid;
  border-top-color: #b2b2b2;
}
.layui-table-sort .layui-table-sort-desc:hover {
  border-top-color: #666;
}
.layui-table-sort[lay-sort='asc'] .layui-table-sort-asc {
  border-bottom-color: #000;
}
.layui-table-sort[lay-sort='desc'] .layui-table-sort-desc {
  border-top-color: #000;
}
.layui-table-cell {
  height: 28px;
  line-height: 28px;
  padding: 0 15px;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
}
.layui-table-cell .layui-form-checkbox[lay-skin='primary'],
.layui-table-cell .layui-form-radio[lay-skin='primary'] {
  top: -1px;
  vertical-align: middle;
}
.layui-table-cell .layui-form-radio {
  padding-right: 0;
}
.layui-table-cell .layui-form-radio > i {
  margin-right: 0;
}
.layui-table-cell .layui-table-link {
  color: #01aaed;
}
.laytable-cell-checkbox,
.laytable-cell-numbers,
.laytable-cell-radio,
.laytable-cell-space {
  padding: 0;
  text-align: center;
}
.layui-table-body {
  position: relative;
  overflow: auto;
  margin-right: -1px;
  margin-bottom: -1px;
}
.layui-table-body .layui-none {
  line-height: 40px;
  text-align: center;
  color: #999;
}
.layui-table-fixed {
  position: absolute;
  left: 0;
  top: 0;
}
.layui-table-fixed .layui-table-body {
  overflow: hidden;
}
.layui-table-fixed-l {
  box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.08);
}
.layui-table-fixed-r {
  left: auto;
  right: -1px;
  border-width: 0 0 0 1px;
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.08);
}
.layui-table-fixed-r .layui-table-header {
  position: relative;
  overflow: visible;
}
.layui-table-mend {
  position: absolute;
  right: -49px;
  top: 0;
  height: 100%;
  width: 50px;
}
.layui-table-tool {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 30px;
  padding: 10px 15px;
  border-width: 0 0 1px;
}
.layui-table-page {
  position: relative;
  width: 100%;
  padding: 7px 7px 0;
  border-width: 1px 0 0;
  height: 41px;
  margin-bottom: -1px;
  font-size: 12px;
}
.layui-table-page > div {
  height: 26px;
}
.layui-table-page .layui-laypage {
  margin: 0;
}
.layui-table-page .layui-laypage a,
.layui-table-page .layui-laypage span {
  height: 26px;
  line-height: 26px;
  margin-bottom: 10px;
  border: none;
  background: none;
}
.layui-table-page .layui-laypage a,
.layui-table-page .layui-laypage span.layui-laypage-curr {
  padding: 0 12px;
}
.layui-table-page .layui-laypage span {
  margin-left: 0;
  padding: 0;
}
.layui-table-page .layui-laypage .layui-laypage-prev {
  margin-left: -7px !important;
}
.layui-table-page .layui-laypage .layui-laypage-curr .layui-laypage-em {
  left: 0;
  top: 0;
  padding: 0;
}
.layui-table-page .layui-laypage button,
.layui-table-page .layui-laypage input {
  height: 26px;
  line-height: 26px;
}
.layui-table-page .layui-laypage input {
  width: 40px;
}
.layui-table-page .layui-laypage button {
  padding: 0 10px;
}
.layui-table-page select {
  height: 18px;
}
.layui-table-view select[lay-ignore] {
  display: inline-block;
}
.layui-table-patch .layui-table-cell {
  padding: 0;
  width: 30px;
}
.layui-table-edit {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  padding: 0 14px 1px;
  border-radius: 0;
  box-shadow: 1px 1px 20px rgba(0, 0, 0, 0.15);
}
.layui-table-edit:focus {
  border-color: #5fb878 !important;
}
select.layui-table-edit {
  padding: 0 0 0 10px;
  border-color: #c9c9c9;
}
.layui-table-view .layui-form-checkbox,
.layui-table-view .layui-form-radio,
.layui-table-view .layui-form-switch {
  top: 0;
  margin: 0;
  box-sizing: content-box;
}
.layui-table-view .layui-form-checkbox {
  top: -1px;
  height: 26px;
  line-height: 26px;
}
body .layui-table-tips .layui-layer-content {
  background: none;
  padding: 0;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}
.layui-table-tips-main {
  margin: -44px 0 0 -1px;
  max-height: 150px;
  padding: 8px 15px;
  font-size: 14px;
  overflow-y: scroll;
  background-color: #fff;
  color: #333;
}
.layui-table-tips-c {
  position: absolute;
  right: -3px;
  top: -12px;
  width: 18px;
  height: 18px;
  padding: 3px;
  text-align: center;
  font-weight: 700;
  border-radius: 100%;
  font-size: 14px;
  cursor: pointer;
  background-color: #666;
}
.layui-table-tips-c:hover {
  background-color: #999;
}
.layui-upload-file {
  display: none !important;
  opacity: 0.01;
  filter: Alpha(opacity=1);
}
.layui-upload-list {
  margin: 10px 0;
}
.layui-upload-choose {
  padding: 0 10px;
  color: #999;
}
.layui-upload-drag {
  position: relative;
  display: inline-block;
  padding: 30px;
  border: 1px dashed #e2e2e2;
  background-color: #fff;
  text-align: center;
  cursor: pointer;
  color: #999;
}
.layui-upload-drag .layui-icon {
  font-size: 50px;
  color: #009688;
}
.layui-upload-drag[lay-over] {
  border-color: #009688;
}
.layui-upload-form {
  display: inline-block;
}
.layui-upload-iframe {
  position: absolute;
  width: 0;
  height: 0;
  border: 0;
  visibility: hidden;
}
.layui-upload-wrap {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.layui-upload-wrap .layui-upload-file {
  display: block !important;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10;
  font-size: 100px;
  width: 100%;
  height: 100%;
  opacity: 0.01;
  filter: Alpha(opacity=1);
  cursor: pointer;
}
.layui-rate,
.layui-rate * {
  display: inline-block;
  vertical-align: middle;
}
.layui-rate {
  list-style: none;
  padding: 10px 5px 10px 0;
  font-size: 0;
}
.layui-rate li i.layui-icon {
  font-size: 20px;
  color: #ffb800;
  margin-right: 5px;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.layui-rate li i:hover {
  cursor: pointer;
  transform: scale(1.12);
  -webkit-transform: scale(1.12);
}
.layui-rate[readonly] li i:hover {
  cursor: default;
  transform: scale(1);
}
.layui-code {
  position: relative;
  margin: 10px 0;
  padding: 15px;
  line-height: 20px;
  border: 1px solid #ddd;
  border-left-width: 6px;
  background-color: #f2f2f2;
  color: #333;
  font-family: Courier New;
  font-size: 12px;
}
.layui-tree {
  line-height: 26px;
}
.layui-tree li {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.layui-tree li .layui-tree-spread,
.layui-tree li a {
  display: inline-block;
  vertical-align: top;
  height: 26px;
  *display: inline;
  *zoom: 1;
  cursor: pointer;
}
.layui-tree li a {
  font-size: 0;
}
.layui-tree li a i {
  font-size: 16px;
}
.layui-tree li a cite {
  padding: 0 6px;
  font-size: 14px;
  font-style: normal;
}
.layui-tree li i {
  padding-left: 6px;
  color: #333;
  -moz-user-select: none;
}
.layui-tree li .layui-tree-check {
  font-size: 13px;
}
.layui-tree li .layui-tree-check:hover {
  color: #009e94;
}
.layui-tree li ul {
  display: none;
  margin-left: 20px;
}
.layui-tree li .layui-tree-enter {
  line-height: 24px;
  border: 1px dotted #000;
}
.layui-tree-drag {
  display: none;
  position: absolute;
  left: -666px;
  top: -666px;
  background-color: #f2f2f2;
  padding: 5px 10px;
  border: 1px dotted #000;
  white-space: nowrap;
}
.layui-tree-drag i {
  padding-right: 5px;
}
.layui-nav {
  position: relative;
  padding: 0 20px;
  background-color: #393d49;
  color: #fff;
  border-radius: 2px;
  font-size: 0;
  box-sizing: border-box;
}
.layui-nav * {
  font-size: 14px;
}
.layui-nav .layui-nav-item {
  position: relative;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: middle;
  line-height: 60px;
}
.layui-nav .layui-nav-item a {
  display: block;
  padding: 0 20px;
  color: #fff;
  color: hsla(0, 0%, 100%, 0.7);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.layui-nav-bar,
.layui-nav-tree .layui-nav-itemed:after,
.layui-nav .layui-this:after {
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 5px;
  background-color: #5fb878;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}
.layui-nav-bar {
  z-index: 1000;
}
.layui-nav .layui-nav-item a:hover,
.layui-nav .layui-this a {
  color: #fff;
}
.layui-nav .layui-this:after {
  content: '';
  top: auto;
  bottom: 0;
  width: 100%;
}
.layui-nav-img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
  border-radius: 50%;
}
.layui-nav .layui-nav-more {
  content: '';
  width: 0;
  height: 0;
  border-style: dashed;
  border-color: transparent;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
  position: absolute;
  top: 50%;
  right: 3px;
  margin-top: -3px;
  border-top: solid hsla(0, 0%, 100%, 0.7);
  border-width: 6px;
}
.layui-nav-itemed > a .layui-nav-more,
.layui-nav .layui-nav-mored {
  margin-top: -9px;
  border-style: dashed dashed solid;
  border-color: transparent transparent #fff;
}
.layui-nav-child {
  display: none;
  position: absolute;
  left: 0;
  top: 65px;
  min-width: 100%;
  line-height: 36px;
  padding: 5px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  border: 1px solid #d2d2d2;
  background-color: #fff;
  z-index: 100;
  border-radius: 2px;
  white-space: nowrap;
}
.layui-nav .layui-nav-child a {
  color: #333;
}
.layui-nav .layui-nav-child a:hover {
  background-color: #f2f2f2;
  color: #000;
}
.layui-nav-child dd {
  position: relative;
}
.layui-nav-child dd.layui-this,
.layui-nav .layui-nav-child dd.layui-this a {
  background-color: #5fb878;
  color: #fff;
}
.layui-nav-child dd.layui-this:after {
  display: none;
}
.layui-nav-tree {
  width: 200px;
  padding: 0;
}
.layui-nav-tree .layui-nav-item {
  display: block;
  width: 100%;
  line-height: 45px;
}
.layui-nav-tree .layui-nav-item a {
  position: relative;
  height: 45px;
  line-height: 45px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.layui-nav-tree .layui-nav-item a:hover {
  background-color: #4e5465;
}
.layui-nav-tree .layui-nav-bar {
  width: 5px;
  height: 0;
}
.layui-nav-tree .layui-nav-child dd.layui-this,
.layui-nav-tree .layui-nav-child dd.layui-this a,
.layui-nav-tree .layui-this,
.layui-nav-tree .layui-this > a,
.layui-nav-tree .layui-this > a:hover {
  background-color: #009688;
  color: #fff;
}
.layui-nav-tree .layui-this:after {
  display: none;
}
.layui-nav-itemed > a,
.layui-nav-tree .layui-nav-title a,
.layui-nav-tree .layui-nav-title a:hover {
  color: #fff !important;
}
.layui-nav-tree .layui-nav-bar {
  background-color: #009688;
}
.layui-nav-tree .layui-nav-child {
  position: relative;
  z-index: 0;
  top: 0;
  border: none;
  box-shadow: none;
}
.layui-nav-tree .layui-nav-child a {
  height: 40px;
  line-height: 40px;
  color: #fff;
  color: hsla(0, 0%, 100%, 0.7);
}
.layui-nav-tree .layui-nav-child,
.layui-nav-tree .layui-nav-child a:hover {
  background: none;
  color: #fff;
}
.layui-nav-tree .layui-nav-more {
  right: 10px;
}
.layui-nav-itemed > .layui-nav-child {
  display: block;
  padding: 0;
  background-color: rgba(0, 0, 0, 0.3) !important;
}
.layui-nav-itemed > .layui-nav-child > .layui-this > .layui-nav-child {
  display: block;
}
.layui-nav-side {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  overflow-x: hidden;
  z-index: 999;
}
.layui-bg-blue .layui-nav-bar,
.layui-bg-blue .layui-nav-itemed:after,
.layui-bg-blue .layui-this:after {
  background-color: #93d1ff;
}
.layui-bg-blue .layui-nav-child dd.layui-this {
  background-color: #1e9fff;
}
.layui-bg-blue .layui-nav-itemed > a,
.layui-nav-tree.layui-bg-blue .layui-nav-title a,
.layui-nav-tree.layui-bg-blue .layui-nav-title a:hover {
  background-color: #007ddb !important;
}
.layui-breadcrumb {
  visibility: hidden;
  font-size: 0;
}
.layui-breadcrumb > * {
  font-size: 14px;
}
.layui-breadcrumb a {
  color: #999 !important;
}
.layui-breadcrumb a:hover {
  color: #5fb878 !important;
}
.layui-breadcrumb a cite {
  color: #666;
  font-style: normal;
}
.layui-breadcrumb span[lay-separator] {
  margin: 0 10px;
  color: #999;
}
.layui-tab {
  margin: 10px 0;
  text-align: left !important;
}
.layui-tab[overflow] > .layui-tab-title {
  overflow: hidden;
}
.layui-tab-title {
  position: relative;
  left: 0;
  height: 40px;
  white-space: nowrap;
  font-size: 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.layui-tab-title,
.layui-tab-title li {
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}
.layui-tab-title li {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: middle;
  font-size: 14px;
  position: relative;
  line-height: 40px;
  min-width: 65px;
  padding: 0 15px;
  text-align: center;
  cursor: pointer;
}
.layui-tab-title li a {
  display: block;
}
.layui-tab-title .layui-this {
  color: #000;
}
.layui-tab-title .layui-this:after {
  left: 0;
  content: '';
  width: 100%;
  height: 41px;
  border-bottom: solid #fff;
  border-radius: 2px 2px 0 0;
  box-sizing: border-box;
  pointer-events: none;
}
.layui-tab-bar,
.layui-tab-title .layui-this:after {
  position: absolute;
  top: 0;
  border-style: solid;
  border-width: 1px;
}
.layui-tab-bar {
  right: 0;
  z-index: 10;
  width: 30px;
  height: 39px;
  line-height: 39px;
  border-radius: 2px;
  text-align: center;
  background-color: #fff;
  cursor: pointer;
}
.layui-tab-bar .layui-icon {
  position: relative;
  display: inline-block;
  top: 3px;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.layui-tab-item {
  display: none;
}
.layui-tab-more {
  padding-right: 30px;
  height: auto !important;
  white-space: normal !important;
}
.layui-tab-more li.layui-this:after {
  border-bottom-color: #e2e2e2;
  border-radius: 2px;
}
.layui-tab-more .layui-tab-bar .layui-icon {
  top: -2px;
  top: 3px\0;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
:root .layui-tab-more .layui-tab-bar .layui-icon {
  top: -2px\0 / IE9;
}
.layui-tab-content {
  padding: 10px;
}
.layui-tab-title li .layui-tab-close {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  line-height: 20px;
  margin-left: 8px;
  top: 1px;
  text-align: center;
  font-size: 14px;
  color: #c2c2c2;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}
.layui-tab-title li .layui-tab-close:hover {
  border-radius: 2px;
  background-color: #ff5722;
  color: #fff;
}
.layui-tab-brief > .layui-tab-title .layui-this {
  color: #009688;
}
.layui-tab-brief > .layui-tab-more li.layui-this:after,
.layui-tab-brief > .layui-tab-title .layui-this:after {
  border-radius: 0;
  border: none;
  border-bottom: 2px solid #5fb878;
}
.layui-tab-brief[overflow] > .layui-tab-title .layui-this:after {
  top: -1px;
}
.layui-tab-card {
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
}
.layui-tab-card > .layui-tab-title {
  background-color: #f2f2f2;
}
.layui-tab-card > .layui-tab-title li {
  margin-right: -1px;
  margin-left: -1px;
}
.layui-tab-card > .layui-tab-title .layui-this {
  background-color: #fff;
}
.layui-tab-card > .layui-tab-title .layui-this:after {
  border-width: medium 1px 1px;
  border-top: 1px;
  border-bottom-color: #fff;
}
.layui-tab-card > .layui-tab-title .layui-tab-bar {
  height: 40px;
  line-height: 40px;
  border-radius: 0;
  border-top: none;
  border-right: none;
}
.layui-tab-card > .layui-tab-more .layui-this {
  background: none;
  color: #5fb878;
}
.layui-tab-card > .layui-tab-more .layui-this:after {
  border: none;
}
.layui-timeline {
  padding-left: 5px;
}
.layui-timeline-item {
  position: relative;
  padding-bottom: 20px;
}
.layui-timeline-axis {
  position: absolute;
  left: -5px;
  top: 0;
  z-index: 10;
  width: 20px;
  height: 20px;
  line-height: 20px;
  background-color: #fff;
  color: #5fb878;
  border-radius: 50%;
  text-align: center;
  cursor: pointer;
}
.layui-timeline-axis:hover {
  color: #ff5722;
}
.layui-timeline-item:before {
  content: '';
  position: absolute;
  left: 5px;
  top: 0;
  z-index: 0;
  width: 1px;
  height: 100%;
}
.layui-timeline-item:last-child:before {
  display: none;
}
.layui-timeline-item:first-child:before {
  display: block;
}
.layui-timeline-content {
  padding-left: 25px;
}
.layui-timeline-title {
  position: relative;
  margin-bottom: 10px;
}
.layui-badge,
.layui-badge-dot,
.layui-badge-rim {
  position: relative;
  display: inline-block;
  padding: 0 6px;
  font-size: 12px;
  text-align: center;
  background-color: #ff5722;
  color: #fff;
  border-radius: 2px;
}
.layui-badge {
  height: 18px;
  line-height: 18px;
}
.layui-badge-dot {
  width: 8px;
  height: 8px;
  padding: 0;
  border-radius: 50%;
}
.layui-badge-rim {
  height: 18px;
  line-height: 18px;
  border-width: 1px;
  border-style: solid;
  background-color: #fff;
  color: #666;
}
.layui-btn .layui-badge,
.layui-btn .layui-badge-dot {
  margin-left: 5px;
}
.layui-nav .layui-badge,
.layui-nav .layui-badge-dot {
  position: absolute;
  top: 50%;
  margin: -8px 6px 0;
}
.layui-tab-title .layui-badge,
.layui-tab-title .layui-badge-dot {
  left: 5px;
  top: -2px;
}
.layui-carousel {
  position: relative;
  left: 0;
  top: 0;
  background-color: transparent;
}
.layui-carousel > [carousel-item] {
  position: relative;
  width: 100%;
  height: 100%;
}
.layui-carousel > [carousel-item]:before {
  position: absolute;
  content: '\e63d';
  left: 50%;
  top: 50%;
  width: 100px;
  line-height: 20px;
  margin: -10px 0 0 -50px;
  text-align: center;
  color: #c2c2c2;
  font-family: layui-icon !important;
  font-size: 30px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  opacity: 0;
}
.layui-carousel > [carousel-item] > * {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  transition-duration: 0.3s;
  -webkit-transition-duration: 0.3s;
}
.layui-carousel-updown > * {
  -webkit-transition: up 0.3s ease-in-out;
  transition: up 0.3s ease-in-out;
}
.layui-carousel-arrow {
  display: none\0;
  opacity: 0;
  position: absolute;
  left: 10px;
  top: 50%;
  margin-top: -18px;
  width: 37px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  font-size: 20px;
  border: 2px solid #3d3d3d;
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  cursor: pointer;
}
.layui-carousel-arrow[lay-type='add'] {
  left: auto !important;
  right: 10px;
}
.layui-carousel[lay-arrow='always'] .layui-carousel-arrow {
  opacity: 1;
  left: -67px;
}
.layui-carousel[lay-arrow='always'] .layui-carousel-arrow[lay-type='add'] {
  right: -66px;
}
.layui-carousel[lay-arrow='none'] .layui-carousel-arrow {
  display: none;
}
.layui-carousel-arrow:hover,
.layui-carousel:hover .layui-carousel-arrow {
  display: block\0;
  opacity: 1;
  left: -67px;
}
.layui-carousel:hover .layui-carousel-arrow[lay-type='add'] {
  right: -66px;
}
.layui-carousel-ind {
  position: relative;
  top: -35px;
  width: 100%;
  line-height: 0 !important;
  text-align: center;
  font-size: 0;
}
.layui-carousel[lay-indicator='outside'] {
  margin-bottom: 30px;
}
.layui-carousel[lay-indicator='outside'] .layui-carousel-ind {
  top: 10px;
}
.layui-carousel[lay-indicator='outside'] .layui-carousel-ind ul {
  background-color: rgba(0, 0, 0, 0.5);
}
.layui-carousel[lay-indicator='none'] .layui-carousel-ind {
  display: none;
}
.layui-carousel-ind ul {
  padding: 5px;
  border-radius: 10px;
  position: absolute;
  bottom: -12px;
  right: -8px;
}
.layui-carousel-ind li,
.layui-carousel-ind ul {
  display: inline-block;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
}
.layui-carousel-ind li {
  width: 10px;
  height: 10px;
  margin: 0 5px;
  font-size: 14px;
  background-color: #e2e2e2;
  background-color: hsla(0, 0%, 100%, 0.5);
  border-radius: 50%;
  cursor: pointer;
}
.layui-carousel-ind li.layui-this,
.layui-carousel-ind li:hover {
  background-color: #2ae2ff;
}
.layui-carousel > [carousel-item] > .layui-carousel-next,
.layui-carousel > [carousel-item] > .layui-carousel-prev,
.layui-carousel > [carousel-item] > .layui-this {
  display: block;
}
.layui-carousel > [carousel-item] > .layui-this {
  left: 0;
}
.layui-carousel > [carousel-item] > .layui-carousel-prev {
  left: -100%;
}
.layui-carousel > [carousel-item] > .layui-carousel-next {
  left: 100%;
}
.layui-carousel > [carousel-item] > .layui-carousel-next.layui-carousel-left,
.layui-carousel > [carousel-item] > .layui-carousel-prev.layui-carousel-right {
  left: 0;
}
.layui-carousel > [carousel-item] > .layui-this.layui-carousel-left {
  left: -100%;
}
.layui-carousel > [carousel-item] > .layui-this.layui-carousel-right {
  left: 100%;
}
.layui-carousel[lay-anim='updown'] .layui-carousel-arrow {
  left: 50% !important;
  top: 20px;
  margin: 0 0 0 -18px;
}
.layui-carousel[lay-anim='updown'] .layui-carousel-arrow[lay-type='add'] {
  top: auto !important;
  bottom: 20px;
}
.layui-carousel[lay-anim='updown'] .layui-carousel-ind {
  position: absolute;
  top: 50%;
  right: 20px;
  width: auto;
  height: auto;
}
.layui-carousel[lay-anim='updown'] .layui-carousel-ind ul {
  padding: 3px 5px;
}
.layui-carousel[lay-anim='updown'] .layui-carousel-ind li {
  display: block;
  margin: 6px 0;
}
.layui-carousel[lay-anim='updown'] > [carousel-item] > * {
  left: 0 !important;
}
.layui-carousel[lay-anim='updown'] > [carousel-item] > .layui-this {
  top: 0;
}
.layui-carousel[lay-anim='updown'] > [carousel-item] > .layui-carousel-prev {
  top: -100%;
}
.layui-carousel[lay-anim='updown'] > [carousel-item] > .layui-carousel-next {
  top: 100%;
}
.layui-carousel[lay-anim='updown']
  > [carousel-item]
  > .layui-carousel-next.layui-carousel-left,
.layui-carousel[lay-anim='updown']
  > [carousel-item]
  > .layui-carousel-prev.layui-carousel-right {
  top: 0;
}
.layui-carousel[lay-anim='updown']
  > [carousel-item]
  > .layui-this.layui-carousel-left {
  top: -100%;
}
.layui-carousel[lay-anim='updown']
  > [carousel-item]
  > .layui-this.layui-carousel-right {
  top: 100%;
}
.layui-carousel[lay-anim='fade'] > [carousel-item] > * {
  left: 0 !important;
}
.layui-carousel[lay-anim='fade'] > [carousel-item] > .layui-carousel-next,
.layui-carousel[lay-anim='fade'] > [carousel-item] > .layui-carousel-prev {
  opacity: 0;
}
.layui-carousel[lay-anim='fade']
  > [carousel-item]
  > .layui-carousel-next.layui-carousel-left,
.layui-carousel[lay-anim='fade']
  > [carousel-item]
  > .layui-carousel-prev.layui-carousel-right {
  opacity: 1;
}
.layui-carousel[lay-anim='fade']
  > [carousel-item]
  > .layui-this.layui-carousel-left,
.layui-carousel[lay-anim='fade']
  > [carousel-item]
  > .layui-this.layui-carousel-right {
  opacity: 0;
}
.layui-fixbar {
  position: fixed;
  right: 15px;
  bottom: 15px;
  z-index: 9999;
}
.layui-fixbar li {
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-bottom: 1px;
  text-align: center;
  cursor: pointer;
  font-size: 30px;
  background-color: #9f9f9f;
  color: #fff;
  border-radius: 2px;
  opacity: 0.95;
}
.layui-fixbar li:hover {
  opacity: 0.85;
}
.layui-fixbar li:active {
  opacity: 1;
}
.layui-fixbar .layui-fixbar-top {
  display: none;
  font-size: 40px;
}
body .layui-util-face {
  border: none;
  background: none;
}
body .layui-util-face .layui-layer-content {
  padding: 0;
  background-color: #fff;
  color: #666;
  box-shadow: none;
}
.layui-util-face .layui-layer-TipsG {
  display: none;
}
.layui-util-face ul {
  position: relative;
  width: 372px;
  padding: 10px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}
.layui-util-face ul li {
  cursor: pointer;
  float: left;
  border: 1px solid #e8e8e8;
  height: 22px;
  width: 26px;
  overflow: hidden;
  margin: -1px 0 0 -1px;
  padding: 4px 2px;
  text-align: center;
}
.layui-util-face ul li:hover {
  position: relative;
  z-index: 2;
  border: 1px solid #eb7350;
  background: #fff9ec;
}
.layui-anim {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.layui-anim.layui-icon {
  display: inline-block;
}
.layui-anim-loop {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.layui-trans,
.layui-trans a {
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
@-webkit-keyframes layui-rotate {
  0% {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(1turn);
  }
}
@keyframes layui-rotate {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
.layui-anim-rotate {
  -webkit-animation-name: layui-rotate;
  animation-name: layui-rotate;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}
@-webkit-keyframes layui-up {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    opacity: 0.3;
  }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
  }
}
@keyframes layui-up {
  0% {
    transform: translate3d(0, 100%, 0);
    opacity: 0.3;
  }
  to {
    transform: translateZ(0);
    opacity: 1;
  }
}
.layui-anim-up {
  -webkit-animation-name: layui-up;
  animation-name: layui-up;
}
@-webkit-keyframes layui-upbit {
  0% {
    -webkit-transform: translate3d(0, 30px, 0);
    opacity: 0.3;
  }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
  }
}
@keyframes layui-upbit {
  0% {
    transform: translate3d(0, 30px, 0);
    opacity: 0.3;
  }
  to {
    transform: translateZ(0);
    opacity: 1;
  }
}
.layui-anim-upbit {
  -webkit-animation-name: layui-upbit;
  animation-name: layui-upbit;
}
@-webkit-keyframes layui-scale {
  0% {
    opacity: 0.3;
    -webkit-transform: scale(0.5);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
  }
}
@keyframes layui-scale {
  0% {
    opacity: 0.3;
    -ms-transform: scale(0.5);
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    -ms-transform: scale(1);
    transform: scale(1);
  }
}
.layui-anim-scale {
  -webkit-animation-name: layui-scale;
  animation-name: layui-scale;
}
@-webkit-keyframes layui-scale-spring {
  0% {
    opacity: 0.5;
    -webkit-transform: scale(0.5);
  }
  80% {
    opacity: 0.8;
    -webkit-transform: scale(1.1);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
  }
}
@keyframes layui-scale-spring {
  0% {
    opacity: 0.5;
    transform: scale(0.5);
  }
  80% {
    opacity: 0.8;
    transform: scale(1.1);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.layui-anim-scaleSpring {
  -webkit-animation-name: layui-scale-spring;
  animation-name: layui-scale-spring;
}
@-webkit-keyframes layui-fadein {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes layui-fadein {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.layui-anim-fadein {
  -webkit-animation-name: layui-fadein;
  animation-name: layui-fadein;
}
@-webkit-keyframes layui-fadeout {
  0% {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes layui-fadeout {
  0% {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.layui-anim-fadeout {
  -webkit-animation-name: layui-fadeout;
  animation-name: layui-fadeout;
}
html {
  background-attachment: fixed;
}
html #layuicss-skinlayercss {
  display: none;
  position: absolute;
  width: 1989px;
}
.layui-layer,
.layui-layer-shade {
  position: fixed;
  _position: absolute;
  pointer-events: auto;
}
.layui-layer-shade {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  _height: expression(document.body.offsetHeight + 'px');
}
.layui-layer {
  -webkit-overflow-scrolling: touch;
  top: 150px;
  left: 0;
  margin: 0;
  padding: 0;
  background-color: #fff;
  -webkit-background-clip: content;
  box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);
}
.layui-layer-close {
  position: absolute;
}
.layui-layer-content {
  position: relative;
}
.layui-layer-border {
  border: 1px solid #b2b2b2;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
}
.layui-layer-load {
  background: url(data:image/gif;base64,R0lGODlhJQAlAJECAL3L2AYrTv///wAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFCgACACwAAAAAJQAlAAACi5SPqcvtDyGYIFpF690i8xUw3qJBwUlSadmcLqYmGQu6KDIeM13beGzYWWy3DlB4IYaMk+Dso2RWkFCfLPcRvFbZxFLUDTt21BW56TyjRep1e20+i+eYMR145W2eefj+6VFmgTQi+ECVY8iGxcg35phGo/iDFwlTyXWphwlm1imGRdcnuqhHeop6UAAAIfkEBQoAAgAsEAACAAQACwAAAgWMj6nLXAAh+QQFCgACACwVAAUACgALAAACFZQvgRi92dyJcVJlLobUdi8x4bIhBQAh+QQFCgACACwXABEADAADAAACBYyPqcsFACH5BAUKAAIALBUAFQAKAAsAAAITlGKZwWoMHYxqtmplxlNT7ixGAQAh+QQFCgACACwQABgABAALAAACBYyPqctcACH5BAUKAAIALAUAFQAKAAsAAAIVlC+BGL3Z3IlxUmUuhtR2LzHhsiEFACH5BAUKAAIALAEAEQAMAAMAAAIFjI+pywUAIfkEBQoAAgAsBQAFAAoACwAAAhOUYJnAagwdjGq2amXGU1PuLEYBACH5BAUKAAIALBAAAgAEAAsAAAIFhI+py1wAIfkEBQoAAgAsFQAFAAoACwAAAhWUL4AIvdnciXFSZS6G1HYvMeGyIQUAIfkEBQoAAgAsFwARAAwAAwAAAgWEj6nLBQAh+QQFCgACACwVABUACgALAAACE5RgmcBqDB2MarZqZcZTU+4sRgEAIfkEBQoAAgAsEAAYAAQACwAAAgWEj6nLXAAh+QQFCgACACwFABUACgALAAACFZQvgAi92dyJcVJlLobUdi8x4bIhBQAh+QQFCgACACwBABEADAADAAACBYSPqcsFADs=)
    #eee 50% no-repeat;
}
.layui-layer-ico {
  background: url(data:image/png;base64,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)
    no-repeat;
}
.layui-layer-btn a,
.layui-layer-dialog .layui-layer-ico,
.layui-layer-setwin a {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: top;
}
.layui-layer-move {
  display: none;
  position: fixed;
  *position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  cursor: move;
  opacity: 0;
  filter: alpha(opacity=0);
  background-color: #fff;
  z-index: 2147483647;
}
.layui-layer-resize {
  position: absolute;
  width: 15px;
  height: 15px;
  right: 0;
  bottom: 0;
  cursor: se-resize;
}
.layui-layer {
  border-radius: 2px;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
}
@-webkit-keyframes layer-bounceIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes layer-bounceIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    -ms-transform: scale(0.5);
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}
.layer-anim {
  -webkit-animation-name: layer-bounceIn;
  animation-name: layer-bounceIn;
}
@-webkit-keyframes layer-zoomInDown {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.1) translateY(-2000px);
    transform: scale(0.1) translateY(-2000px);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  60% {
    opacity: 1;
    -webkit-transform: scale(0.475) translateY(60px);
    transform: scale(0.475) translateY(60px);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
@keyframes layer-zoomInDown {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.1) translateY(-2000px);
    -ms-transform: scale(0.1) translateY(-2000px);
    transform: scale(0.1) translateY(-2000px);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  60% {
    opacity: 1;
    -webkit-transform: scale(0.475) translateY(60px);
    -ms-transform: scale(0.475) translateY(60px);
    transform: scale(0.475) translateY(60px);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
.layer-anim-01 {
  -webkit-animation-name: layer-zoomInDown;
  animation-name: layer-zoomInDown;
}
@-webkit-keyframes layer-fadeInUpBig {
  0% {
    opacity: 0;
    -webkit-transform: translateY(2000px);
    transform: translateY(2000px);
  }
  to {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes layer-fadeInUpBig {
  0% {
    opacity: 0;
    -webkit-transform: translateY(2000px);
    -ms-transform: translateY(2000px);
    transform: translateY(2000px);
  }
  to {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}
.layer-anim-02 {
  -webkit-animation-name: layer-fadeInUpBig;
  animation-name: layer-fadeInUpBig;
}
@-webkit-keyframes layer-zoomInLeft {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.1) translateX(-2000px);
    transform: scale(0.1) translateX(-2000px);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  60% {
    opacity: 1;
    -webkit-transform: scale(0.475) translateX(48px);
    transform: scale(0.475) translateX(48px);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
@keyframes layer-zoomInLeft {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.1) translateX(-2000px);
    -ms-transform: scale(0.1) translateX(-2000px);
    transform: scale(0.1) translateX(-2000px);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  60% {
    opacity: 1;
    -webkit-transform: scale(0.475) translateX(48px);
    -ms-transform: scale(0.475) translateX(48px);
    transform: scale(0.475) translateX(48px);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
.layer-anim-03 {
  -webkit-animation-name: layer-zoomInLeft;
  animation-name: layer-zoomInLeft;
}
@-webkit-keyframes layer-rollIn {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100%) rotate(-120deg);
    transform: translateX(-100%) rotate(-120deg);
  }
  to {
    opacity: 1;
    -webkit-transform: translateX(0) rotate(0deg);
    transform: translateX(0) rotate(0deg);
  }
}
@keyframes layer-rollIn {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100%) rotate(-120deg);
    -ms-transform: translateX(-100%) rotate(-120deg);
    transform: translateX(-100%) rotate(-120deg);
  }
  to {
    opacity: 1;
    -webkit-transform: translateX(0) rotate(0deg);
    -ms-transform: translateX(0) rotate(0deg);
    transform: translateX(0) rotate(0deg);
  }
}
.layer-anim-04 {
  -webkit-animation-name: layer-rollIn;
  animation-name: layer-rollIn;
}
@keyframes layer-fadeIn {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.layer-anim-05 {
  -webkit-animation-name: layer-fadeIn;
  animation-name: layer-fadeIn;
}
@-webkit-keyframes layer-shake {
  0%,
  to {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
}
@keyframes layer-shake {
  0%,
  to {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translateX(-10px);
    -ms-transform: translateX(-10px);
    transform: translateX(-10px);
  }
  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translateX(10px);
    -ms-transform: translateX(10px);
    transform: translateX(10px);
  }
}
.layer-anim-06 {
  -webkit-animation-name: layer-shake;
  animation-name: layer-shake;
}
@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.layui-layer-title {
  padding: 0 80px 0 20px;
  height: 42px;
  line-height: 42px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: #f8f8f8;
  border-radius: 2px 2px 0 0;
}
.layui-layer-setwin {
  position: absolute;
  right: 15px;
  *right: 0;
  top: 15px;
  font-size: 0;
  line-height: normal;
}
.layui-layer-setwin a {
  position: relative;
  width: 16px;
  height: 16px;
  margin-left: 10px;
  font-size: 12px;
  _overflow: hidden;
}
.layui-layer-setwin .layui-layer-min cite {
  position: absolute;
  width: 14px;
  height: 2px;
  left: 0;
  top: 50%;
  margin-top: -1px;
  background-color: #2e2d3c;
  cursor: pointer;
  _overflow: hidden;
}
.layui-layer-setwin .layui-layer-min:hover cite {
  background-color: #2d93ca;
}
.layui-layer-setwin .layui-layer-max {
  background-position: -32px -40px;
}
.layui-layer-setwin .layui-layer-max:hover {
  background-position: -16px -40px;
}
.layui-layer-setwin .layui-layer-maxmin {
  background-position: -65px -40px;
}
.layui-layer-setwin .layui-layer-maxmin:hover {
  background-position: -49px -40px;
}
.layui-layer-setwin .layui-layer-close1 {
  background-position: 1px -40px;
  cursor: pointer;
}
.layui-layer-setwin .layui-layer-close1:hover {
  opacity: 0.7;
}
.layui-layer-setwin .layui-layer-close2 {
  position: absolute;
  right: -28px;
  top: -28px;
  width: 30px;
  height: 30px;
  margin-left: 0;
  background-position: -149px -31px;
  *right: -18px;
  _display: none;
}
.layui-layer-setwin .layui-layer-close2:hover {
  background-position: -180px -31px;
}
.layui-layer-btn {
  text-align: right;
  padding: 0 10px 12px;
  pointer-events: auto;
  user-select: none;
  -webkit-user-select: none;
}
.layui-layer-btn a {
  height: 28px;
  line-height: 28px;
  margin: 6px 6px 0;
  padding: 0 15px;
  border: 1px solid #dedede;
  background-color: #f1f1f1;
  color: #333;
  border-radius: 2px;
  font-weight: 400;
  cursor: pointer;
  text-decoration: none;
}
.layui-layer-btn a:hover {
  opacity: 0.9;
  text-decoration: none;
}
.layui-layer-btn a:active {
  opacity: 0.8;
}
.layui-layer-btn .layui-layer-btn0 {
  border-color: #4898d5;
  background-color: #2e8ded;
  color: #fff;
}
.layui-layer-btn-l {
  text-align: left;
}
.layui-layer-btn-c {
  text-align: center;
}
.layui-layer-dialog {
  min-width: 260px;
}
.layui-layer-dialog .layui-layer-content {
  position: relative;
  padding: 20px;
  line-height: 24px;
  word-break: break-all;
  overflow: hidden;
  font-size: 14px;
  overflow-x: hidden;
  overflow-y: auto;
}
.layui-layer-dialog .layui-layer-content .layui-layer-ico {
  position: absolute;
  top: 16px;
  left: 15px;
  _left: -40px;
  width: 30px;
  height: 30px;
}
.layui-layer-ico1 {
  background-position: -30px 0;
}
.layui-layer-ico2 {
  background-position: -60px 0;
}
.layui-layer-ico3 {
  background-position: -90px 0;
}
.layui-layer-ico4 {
  background-position: -120px 0;
}
.layui-layer-ico5 {
  background-position: -150px 0;
}
.layui-layer-ico6 {
  background-position: -180px 0;
}
.layui-layer-rim {
  border: 6px solid #8d8d8d;
  border: 6px solid rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  box-shadow: none;
}
.layui-layer-msg {
  min-width: 180px;
  border: 1px solid #d3d4d3;
  box-shadow: none;
}
.layui-layer-hui {
  min-width: 100px;
  background-color: #000;
  filter: alpha(opacity=60);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border: none;
}
.layui-layer-hui .layui-layer-content {
  padding: 12px 25px;
  text-align: center;
}
.layui-layer-dialog .layui-layer-padding {
  padding: 20px 20px 20px 55px;
  text-align: left;
}
.layui-layer-page .layui-layer-content {
  position: relative;
  overflow: auto;
}
.layui-layer-iframe .layui-layer-btn,
.layui-layer-page .layui-layer-btn {
  padding-top: 10px;
}
.layui-layer-nobg {
  background: none;
}
.layui-layer-iframe iframe {
  display: block;
  width: 100%;
}
.layui-layer-loading {
  border-radius: 100%;
  background: none;
  box-shadow: none;
  border: none;
}
.layui-layer-loading .layui-layer-content {
  width: 60px;
  height: 24px;
  background: url(data:image/gif;base64,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)
    no-repeat;
}
.layui-layer-loading .layui-layer-loading1 {
  width: 37px;
  height: 37px;
  background: url(data:image/gif;base64,R0lGODlhJQAlAJECAL3L2AYrTv///wAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFCgACACwAAAAAJQAlAAACi5SPqcvtDyGYIFpF690i8xUw3qJBwUlSadmcLqYmGQu6KDIeM13beGzYWWy3DlB4IYaMk+Dso2RWkFCfLPcRvFbZxFLUDTt21BW56TyjRep1e20+i+eYMR145W2eefj+6VFmgTQi+ECVY8iGxcg35phGo/iDFwlTyXWphwlm1imGRdcnuqhHeop6UAAAIfkEBQoAAgAsEAACAAQACwAAAgWMj6nLXAAh+QQFCgACACwVAAUACgALAAACFZQvgRi92dyJcVJlLobUdi8x4bIhBQAh+QQFCgACACwXABEADAADAAACBYyPqcsFACH5BAUKAAIALBUAFQAKAAsAAAITlGKZwWoMHYxqtmplxlNT7ixGAQAh+QQFCgACACwQABgABAALAAACBYyPqctcACH5BAUKAAIALAUAFQAKAAsAAAIVlC+BGL3Z3IlxUmUuhtR2LzHhsiEFACH5BAUKAAIALAEAEQAMAAMAAAIFjI+pywUAIfkEBQoAAgAsBQAFAAoACwAAAhOUYJnAagwdjGq2amXGU1PuLEYBACH5BAUKAAIALBAAAgAEAAsAAAIFhI+py1wAIfkEBQoAAgAsFQAFAAoACwAAAhWUL4AIvdnciXFSZS6G1HYvMeGyIQUAIfkEBQoAAgAsFwARAAwAAwAAAgWEj6nLBQAh+QQFCgACACwVABUACgALAAACE5RgmcBqDB2MarZqZcZTU+4sRgEAIfkEBQoAAgAsEAAYAAQACwAAAgWEj6nLXAAh+QQFCgACACwFABUACgALAAACFZQvgAi92dyJcVJlLobUdi8x4bIhBQAh+QQFCgACACwBABEADAADAAACBYSPqcsFADs=)
    no-repeat;
}
.layui-layer-ico16,
.layui-layer-loading .layui-layer-loading2 {
  width: 32px;
  height: 32px;
  background: url(data:image/gif;base64,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)
    no-repeat;
}
.layui-layer-tips {
  background: none;
  box-shadow: none;
  border: none;
}
.layui-layer-tips .layui-layer-content {
  position: relative;
  line-height: 22px;
  min-width: 12px;
  padding: 5px 10px;
  font-size: 12px;
  _float: left;
  border-radius: 2px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  background-color: #000;
  color: #fff;
}
.layui-layer-tips .layui-layer-close {
  right: -2px;
  top: -1px;
}
.layui-layer-tips i.layui-layer-TipsG {
  position: absolute;
  width: 0;
  height: 0;
  border: 8px dashed transparent;
  *overflow: hidden;
}
.layui-layer-tips i.layui-layer-TipsB,
.layui-layer-tips i.layui-layer-TipsT {
  left: 5px;
  border-right-style: solid;
  border-right-color: #000;
}
.layui-layer-tips i.layui-layer-TipsT {
  bottom: -8px;
}
.layui-layer-tips i.layui-layer-TipsB {
  top: -8px;
}
.layui-layer-tips i.layui-layer-TipsL,
.layui-layer-tips i.layui-layer-TipsR {
  top: 1px;
  border-bottom-style: solid;
  border-bottom-color: #000;
}
.layui-layer-tips i.layui-layer-TipsR {
  left: -8px;
}
.layui-layer-tips i.layui-layer-TipsL {
  right: -8px;
}
.layui-layer-lan[type='dialog'] {
  min-width: 280px;
}
.layui-layer-lan .layui-layer-title {
  background: #4476a7;
  color: #fff;
  border: none;
}
.layui-layer-lan .layui-layer-btn {
  padding: 5px 10px 10px;
  text-align: right;
  border-top: 1px solid #e9e7e7;
}
.layui-layer-lan .layui-layer-btn a {
  background: #bbb5b5;
  border: none;
}
.layui-layer-lan .layui-layer-btn .layui-layer-btn1 {
  background: #c9c5c5;
}
.layui-layer-molv .layui-layer-title {
  background: #009f95;
  color: #fff;
  border: none;
}
.layui-layer-molv .layui-layer-btn a {
  background: #009f95;
}
.layui-layer-molv .layui-layer-btn .layui-layer-btn1 {
  background: #92b8b1;
}
.layui-layer-iconext {
  background: url(data:image/png;base64,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)
    no-repeat;
}
.layui-layer-prompt .layui-layer-input {
  display: block;
  width: 220px;
  height: 30px;
  margin: 0 auto;
  line-height: 30px;
  padding: 0 5px;
  border: 1px solid #ccc;
  box-shadow: inset 1px 1px 5px rgba(0, 0, 0, 0.1);
  color: #333;
}
.layui-layer-prompt textarea.layui-layer-input {
  width: 300px;
  height: 100px;
  line-height: 20px;
}
.layui-layer-prompt .layui-layer-content {
  padding: 20px;
}
.layui-layer-prompt .layui-layer-btn {
  padding-top: 0;
}
.layui-layer-tab {
  box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.4);
}
.layui-layer-tab .layui-layer-title {
  padding-left: 0;
  border-bottom: 1px solid #ccc;
  background-color: #eee;
  overflow: visible;
}
.layui-layer-tab .layui-layer-title span {
  position: relative;
  float: left;
  min-width: 80px;
  max-width: 260px;
  padding: 0 20px;
  text-align: center;
  cursor: default;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.layui-layer-tab .layui-layer-title span.layui-layer-tabnow {
  height: 43px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  background-color: #fff;
  z-index: 10;
}
.layui-layer-tab .layui-layer-title span:first-child {
  border-left: none;
}
.layui-layer-tabmain {
  line-height: 24px;
  clear: both;
}
.layui-layer-tabmain .layui-layer-tabli {
  display: none;
}
.layui-layer-tabmain .layui-layer-tabli.xubox_tab_layer {
  display: block;
}
.xubox_tabclose {
  position: absolute;
  right: 10px;
  top: 5px;
  cursor: pointer;
}
.layui-layer-photos {
  -webkit-animation-duration: 0.8s;
  animation-duration: 0.8s;
}
.layui-layer-photos .layui-layer-content {
  overflow: hidden;
  text-align: center;
}
.layui-layer-photos .layui-layer-phimg img {
  position: relative;
  width: 100%;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: top;
}
.layui-layer-imgbar,
.layui-layer-imguide {
  display: none;
}
.layui-layer-imgnext,
.layui-layer-imgprev {
  position: absolute;
  top: 50%;
  width: 27px;
  _width: 44px;
  height: 44px;
  margin-top: -22px;
  outline: none;
  blr: expression(this.onFocus=this.blur());
}
.layui-layer-imgprev {
  left: 10px;
  background-position: -5px -5px;
  _background-position: -70px -5px;
}
.layui-layer-imgprev:hover {
  background-position: -33px -5px;
  _background-position: -120px -5px;
}
.layui-layer-imgnext {
  right: 10px;
  _right: 8px;
  background-position: -5px -50px;
  _background-position: -70px -50px;
}
.layui-layer-imgnext:hover {
  background-position: -33px -50px;
  _background-position: -120px -50px;
}
.layui-layer-imgbar {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 32px;
  line-height: 32px;
  background-color: rgba(0, 0, 0, 0.8);
  background-color: #000\9;
  filter: Alpha(opacity=80);
  color: #fff;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 0;
}
.layui-layer-imgtit * {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  vertical-align: top;
  font-size: 12px;
}
.layui-layer-imgtit a {
  max-width: 65%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: #fff;
}
.layui-layer-imgtit a:hover {
  color: #fff;
  text-decoration: underline;
}
.layui-layer-imgtit em {
  padding-left: 10px;
  font-style: normal;
}
@-webkit-keyframes layer-bounceOut {
  to {
    opacity: 0;
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
  }
  30% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes layer-bounceOut {
  to {
    opacity: 0;
    -webkit-transform: scale(0.7);
    -ms-transform: scale(0.7);
    transform: scale(0.7);
  }
  30% {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05);
  }
  0% {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}
.layer-anim-close {
  -webkit-animation-name: layer-bounceOut;
  animation-name: layer-bounceOut;
  -webkit-animation-duration: 0.2s;
  animation-duration: 0.2s;
}
@media screen and (max-width: 1100px) {
  .layui-layer-iframe {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}
