/**
 * 优美的无图片占位样式
 * 用途：当图片资源不可用时显示优雅的提示
 * 创建时间：2025-06-23
 */

/* ================================
   基础无图片占位容器
   ================================ */

.no-image-placeholder {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.no-image-placeholder:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #94a3b8;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 背景装饰图案 */
.no-image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* ================================
   游戏卡片无图片样式
   ================================ */

.game-no-image {
  width: 100%;
  height: 160px; /* 对应 h-40 */
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  position: relative;
}

.game-no-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.game-no-image .content {
  position: relative;
  z-index: 1;
  color: white;
  text-align: center;
}

.game-no-image .icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
  opacity: 0.9;
  animation: float 3s ease-in-out infinite;
}

.game-no-image .title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  opacity: 0.95;
}

.game-no-image .subtitle {
  font-size: 0.875rem;
  opacity: 0.7;
}

/* ================================
   轮播图无图片样式
   ================================ */

.banner-no-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #1e40af 0%, #3b82f6 50%, #1e40af 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  position: relative;
}

.banner-no-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(ellipse at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
}

.banner-no-image .content {
  position: relative;
  z-index: 1;
  color: white;
  text-align: center;
}

.banner-no-image .icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  animation: pulse 2s ease-in-out infinite;
}

.banner-no-image .title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.banner-no-image .subtitle {
  font-size: 1.125rem;
  opacity: 0.8;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* ================================
   专栏无图片样式
   ================================ */

.column-no-image {
  width: 100%;
  height: 256px; /* 对应 h-64 */
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  position: relative;
}

.column-no-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shine 3s ease-in-out infinite;
}

.column-no-image .content {
  position: relative;
  z-index: 1;
  color: white;
  text-align: center;
  padding: 1.5rem;
}

.column-no-image .icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.9;
  animation: bounce 2s ease-in-out infinite;
}

.column-no-image .title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.column-no-image .subtitle {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* ================================
   通用无图片样式
   ================================ */

.generic-no-image {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  min-height: 200px;
}

.generic-no-image .icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.generic-no-image .title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.generic-no-image .subtitle {
  font-size: 0.875rem;
  opacity: 0.7;
}

/* ================================
   动画效果
   ================================ */

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ================================
   响应式适配
   ================================ */

@media (max-width: 768px) {
  .game-no-image .icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .game-no-image .title {
    font-size: 0.875rem;
  }
  
  .game-no-image .subtitle {
    font-size: 0.75rem;
  }
  
  .banner-no-image .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }
  
  .banner-no-image .title {
    font-size: 1.5rem;
  }
  
  .banner-no-image .subtitle {
    font-size: 1rem;
  }
  
  .column-no-image .icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
  }
  
  .column-no-image .title {
    font-size: 1.125rem;
  }
}

/* ================================
   深色模式支持
   ================================ */

@media (prefers-color-scheme: dark) {
  .no-image-placeholder {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #4b5563;
    color: #e5e7eb;
  }
  
  .no-image-placeholder:hover {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-color: #6b7280;
  }
}

/* ================================
   交互效果
   ================================ */

.no-image-placeholder.interactive {
  cursor: pointer;
  user-select: none;
}

.no-image-placeholder.interactive:active {
  transform: translateY(0px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* 加载状态 */
.no-image-placeholder.loading {
  position: relative;
  overflow: hidden;
}

.no-image-placeholder.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading-sweep 2s infinite;
}

@keyframes loading-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
