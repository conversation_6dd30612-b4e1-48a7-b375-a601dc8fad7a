/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */
;
layui.define(["table", "form", "element"],
function(e) {
	var t = layui.$,
	i = layui.table,
	r = (layui.form, layui.element);
	i.render({
		elem: "#LAY-app-system-order",
		url: "/admin/base/workorder",
		cols: [[{
			type: "numbers",
			fixed: "left"
		},
		{
			field: "id",
			width: 100,
			title: "工单号",
			sort: !0,
			align: "center",
		},
		{
			field: "work_type",
			width: 100,
			title: "业务性质",
			align: "center",
		},
		{
			field: "work_goods",
			minWidth: 100,
			title: "工单商品",
			align: "center",
		},
		{
			field: "progress",
			title: "进度",
			width: 200,
			align: "center",
			templet: "#progressTpl"
		},
		{
			field: "work_user",
			width: 100,
			title: "用户ID",
			align: "center",
		},
		{
			field: "work_img",
			width: 100,
			title: "工单补充图",
			align: "center",
		},
		{
			field: "work_reply",
			title: "回复内容",
			minWidth: 80,
			align: "center"
		},
		{
			field: "work_state",
			title: "工单状态",
			templet: "#buttonTpl",
			minWidth: 80,
			align: "center"
		},
		{
			title: "操作",
			align: "center",
			fixed: "right",
			toolbar: "#table-system-order"
		}]],
		page: !0,
		limit: 10,
		limits: [10,20,30,40,50,60,70,80,100],
		text: "对不起，加载出现异常！",
		done: function() {
			r.render("progress")
		}
	}),
	i.on("tool(LAY-app-system-order)",
	function(e) {
		e.data;
		if("del" === e.event){
		    t(e.tr);
            layer.confirm("确定删除此商品？",
			function() {
				layui.$.ajax({
                            url: '/admin/base/delWorkorder',
                            method: 'post',
                            data: {id:e.data.id},
                            success: function(res){
                            layer.msg('删除成功');
                            }
                        });
			})
		}
		if ("edit" === e.event) {
			t(e.tr);
			layer.open({
				type: 2,
				title: "编辑工单",
				content: "/admin/index/upWorkorder?id="+e.data.id,
				area: ["450px", "450px"],
				btn: ["确定", "取消"],
				yes: function(e, t) {
					var r = window["layui-layer-iframe" + e],
					l = "LAY-app-workorder-submit",
					o = t.find("iframe").contents().find("#" + l);
					r.layui.form.on("submit(" + l + ")",
					function(t) {
						t.field;
						i.reload("LAY-user-front-submit"),
						layer.close(e)
                            layui.$.ajax({
                                url: '/admin/base/upWorkorder',
                                method: 'post',
                                data: t.field,
                                success: function(res){
                                layer.msg('保存成功');
                                }
                            });
						
					}),
					o.trigger("click")
				},
				success: function(e, t) {}
			})
		}
		
	}),
	e("workorder", {})
});