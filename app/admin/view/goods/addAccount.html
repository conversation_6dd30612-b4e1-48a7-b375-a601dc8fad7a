<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>添加账号</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
      <div class="layui-form-item">
      <label class="layui-form-label">选择分类</label>
      <div class="layui-input-inline">
        <select name="goods_class" lay-verify="required" lay-search>
            <option value="">请选择标签</option>
            {foreach $goodsAll as $index=>$val}
              <option value="{$val.id}">{$val.goods_name}</option>
            {/foreach}
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">账号密码</label>
      <div class="layui-input-inline">
        <textarea name="accountData" lay-verify="required" style="width: 400px; height: 100px;" autocomplete="off" class="layui-textarea" placeholder="添加方式:账号|密码或账号--密码(如:admin--123456),添加多个账号请用回车或者逗号隔开(如:aa1--aa1,aa2--aa2)"></textarea>
      </div>
    </div>
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认添加">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-edit" id="layuiadmin-app-form-edit" value="确认编辑">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form','upload'], function(){
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload ;

    //监听提交
    form.on('submit(layuiadmin-app-form-submit)', function(data){
      var field = data.field; //获取提交的字段
      var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引  
    var arr=[]
    var spAll=field['accountData'].split(/,|\r|\n/g)
    spAll.forEach(function(value,index){
        arr.push(value.split(/--|\|/g))
    })
    arr=arr.filter(function(res){
        return res!=''
    })
    console.log(arr)
    $.ajax({
        url: '/admin/base/addAccount',
        method: 'post',
        data: {account:arr,goodsclass:field.goods_class},
        success: function(res){
        layer.msg("成功添加"+res+"个账号");
        }
    });
    return false; // 阻止默认 form 跳转


      //提交 Ajax 成功后，关闭当前弹层并重载表格
      //$.ajax({});
      parent.layui.table.reload('LAY-app-content-list'); //重载表格
      parent.layer.close(index); //再执行关闭 
    });
  })
  </script>
</body>
</html>