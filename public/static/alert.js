//   创建提示框
  function createAlert(back,content,i){
      $(".createAlert1").remove()
   /*失败*/ let i0=`<svg t="1697599214685" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11707" width="20" height="20"><path d="M511.999488 65.290005C265.29162 65.290005 65.290517 265.291109 65.290517 511.998977c0 246.708891 200.001103 446.709995 446.708971 446.709995S958.70846 758.707868 958.70846 511.998977C958.70846 265.291109 758.707356 65.290005 511.999488 65.290005L511.999488 65.290005zM716.82855 637.854383c6.803963 6.819313 6.803963 17.856693 0 24.676006l-54.298673 54.29765c-6.819313 6.804986-17.85567 6.820336-24.676006 0L511.999488 590.973656 386.144082 716.828039c-6.819313 6.804986-17.871019 6.804986-24.676006 0l-54.29765-54.29765c-6.804986-6.804986-6.804986-17.856693 0-24.676006l125.869732-125.855406L307.170426 386.144594c-6.804986-6.819313-6.804986-17.871019 0-24.676006l54.29765-54.298673c6.820336-6.803963 17.856693-6.803963 24.676006 0l125.855406 125.870756 125.854383-125.870756c6.820336-6.803963 17.856693-6.803963 24.676006 0l54.298673 54.298673c6.803963 6.804986 6.803963 17.856693 0 24.676006L590.973144 511.998977 716.82855 637.854383 716.82855 637.854383zM716.82855 637.854383" fill="#FF696A" p-id="11708"></path></svg>`
   /*成功*/ let i1=`<svg t="1697599283833" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3143" width="20" height="20"><path d="M511.998465 65.290005c-246.722194 0-446.708971 200.001103-446.708971 446.708971 0 246.708891 199.986777 446.709995 446.708971 446.709995 246.708891 0 446.711018-200.001103 446.711018-446.709995C958.709483 265.291109 758.707356 65.290005 511.998465 65.290005L511.998465 65.290005zM756.308727 405.884171 465.103412 697.081299c-6.804986 6.819313-17.856693 6.819313-24.662703 0L267.69025 524.33084c-6.804986-6.804986-6.804986-17.856693 0-24.668843l54.29765-54.290487c6.804986-6.812149 17.856693-6.812149 24.662703 0l106.122993 106.107643 224.574778-224.561475c6.804986-6.812149 17.857716-6.812149 24.663726 0l54.29765 54.290487C763.128039 388.020314 763.128039 399.072021 756.308727 405.884171L756.308727 405.884171 756.308727 405.884171zM756.308727 405.884171" fill="#89C27E" p-id="3144"></path></svg>`  
   i==0?i=i0:i=i1
   $("body").append(`<div class="createAlert1"><i class="createAlert">${i} ${content}</i></div>`)
      $(".createAlert1").css({
        position: 'fixed',
        width:'100%',
        top: '30px',
        zIndex:'9999',
      })
      $(".createAlert").css({
        margin:'auto',
        padding: '3px 10px',
        backgroundColor: back,
        color: '#fff',
        fontStyle: 'normal',
        fontSize: '12px',
        borderRadius: '5px',
        fontFamily:'system-ui',
        display: 'flex',
        alignItems: 'center',
        width:'fit-content',
      })
      $(".createAlert").stop().fadeIn(1000,function(){
      $(".createAlert").stop().fadeOut(2000,function(){
          $(".createAlert1").remove()
      })
    })
   }
//   显示提示框 showAlert(背景颜色,内容,2加载状态)
  function showAlert(back,content,i){
   /*失败*/ let i0=`<svg t="1697599214685" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11707" width="20" height="20"><path d="M511.999488 65.290005C265.29162 65.290005 65.290517 265.291109 65.290517 511.998977c0 246.708891 200.001103 446.709995 446.708971 446.709995S958.70846 758.707868 958.70846 511.998977C958.70846 265.291109 758.707356 65.290005 511.999488 65.290005L511.999488 65.290005zM716.82855 637.854383c6.803963 6.819313 6.803963 17.856693 0 24.676006l-54.298673 54.29765c-6.819313 6.804986-17.85567 6.820336-24.676006 0L511.999488 590.973656 386.144082 716.828039c-6.819313 6.804986-17.871019 6.804986-24.676006 0l-54.29765-54.29765c-6.804986-6.804986-6.804986-17.856693 0-24.676006l125.869732-125.855406L307.170426 386.144594c-6.804986-6.819313-6.804986-17.871019 0-24.676006l54.29765-54.298673c6.820336-6.803963 17.856693-6.803963 24.676006 0l125.855406 125.870756 125.854383-125.870756c6.820336-6.803963 17.856693-6.803963 24.676006 0l54.298673 54.298673c6.803963 6.804986 6.803963 17.856693 0 24.676006L590.973144 511.998977 716.82855 637.854383 716.82855 637.854383zM716.82855 637.854383" fill="#FF696A" p-id="11708"></path></svg>`
   /*成功*/ let i1=`<svg t="1697599283833" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3143" width="20" height="20"><path d="M511.998465 65.290005c-246.722194 0-446.708971 200.001103-446.708971 446.708971 0 246.708891 199.986777 446.709995 446.708971 446.709995 246.708891 0 446.711018-200.001103 446.711018-446.709995C958.709483 265.291109 758.707356 65.290005 511.998465 65.290005L511.998465 65.290005zM756.308727 405.884171 465.103412 697.081299c-6.804986 6.819313-17.856693 6.819313-24.662703 0L267.69025 524.33084c-6.804986-6.804986-6.804986-17.856693 0-24.668843l54.29765-54.290487c6.804986-6.812149 17.856693-6.812149 24.662703 0l106.122993 106.107643 224.574778-224.561475c6.804986-6.812149 17.857716-6.812149 24.663726 0l54.29765 54.290487C763.128039 388.020314 763.128039 399.072021 756.308727 405.884171L756.308727 405.884171 756.308727 405.884171zM756.308727 405.884171" fill="#89C27E" p-id="3144"></path></svg>`  
   /*成功*/
   let i2=`<svg t="1700981736690" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2455" width="50" height="50"><path d="M782.065385 901.683615c10.892554 18.38743 4.796721 42.970626-14.290231 53.363521-18.687226 10.892554-42.470967 4.496926-53.063726-14.290232-11.392212-18.38743-4.796721-42.470967 13.690641-53.363521 19.386747-10.792622 42.770762-4.197131 53.663316 14.290232z m-230.342539 83.243095c0 21.285449-17.587977 39.07329-39.67288 39.07329-21.285449 0-39.07329-17.388114-39.07329-39.07329v-27.381282c0-21.785108 17.887772-39.173221 39.07329-39.173222 21.785108 0 39.67288 17.388114 39.67288 39.173222v27.381282z m-241.135162-44.169806c-11.092417 19.286816-35.175954 25.382649-53.663316 14.490095-18.987021-10.692691-25.682444-34.576364-14.490095-53.663316l29.080121-50.865229c11.192349-18.687226 35.175954-25.182785 54.162975-14.490094 18.38743 10.892554 25.182785 35.175954 13.990436 53.663316l-29.080121 50.865228zM123.415634 782.764907c-18.987021 10.592759-43.270421 4.197131-53.663316-14.490095-11.092417-18.38743-4.796721-42.770762 13.990436-53.663316l78.146579-45.269054c18.687226-10.392895 42.970626-4.496926 53.663317 14.490094 10.592759 18.38743 4.097199 42.770762-14.490095 53.663316l-77.646921 45.269055zM40.27247 551.822777c-21.785108 0-39.07329-17.587977-39.07329-39.073289 0-21.785108 17.288182-39.373085 39.07329-39.373085h121.716795c21.785108 0 39.07329 17.587977 39.07329 39.07329 0 21.785108-17.288182 39.373085-39.07329 39.373084H40.27247z m43.570216-241.534888c-18.987021-10.592759-25.182785-34.576364-13.990437-53.363521 10.392895-18.987021 34.576364-25.382649 53.663317-14.490095L255.82512 319.281741c18.687226 10.692691 24.88299 34.576364 14.490094 53.063726-11.092417 18.987021-35.175954 25.082854-53.563384 14.490095l-132.909144-76.547673z m158.591587-187.072119l92.137016 159.990632c11.192349 18.687226 35.175954 25.382649 53.86318 14.290231 18.687226-10.592759 24.88299-34.876159 13.990436-53.663316L309.888162 84.142481C299.295404 65.655119 275.311799 58.959696 256.824436 69.852249c-18.987021 10.892554-25.082854 34.976091-14.390163 53.363521z m230.642335-82.643505c0-21.285449 17.887772-39.373085 39.07329-39.373085 21.785108 0 39.67288 17.687909 39.672879 39.373085V224.846296c0 21.785108-17.587977 39.373085-39.672879 39.67288-21.285449 0-39.07329-17.288182-39.07329-39.67288V40.572265z m241.135161 43.570216c11.192349-18.987021 34.876159-25.182785 53.663316-14.490095 18.987021 10.592759 25.682444 34.576364 14.490095 53.663316l-92.336879 159.990632c-10.392895 18.687226-34.976091 25.282717-53.663316 14.490094-18.687226-10.892554-24.88299-35.175954-14.290231-53.963111l92.137015-159.690836z m187.671709 158.291792L741.692983 334.771153c-18.687226 10.592759-25.182785 34.576364-14.490094 53.663316 11.092417 18.38743 35.175954 24.583195 53.663316 14.290231l160.290426-92.336879c18.38743-10.592759 25.182785-34.576364 13.990436-53.363521-10.792622-18.987021-34.876159-25.782375-53.263589-14.590027z m82.34371 230.94213c21.984971 0 39.373085 17.587977 39.07329 39.373085 0 21.485313-16.988387 39.07329-39.07329 39.073289H799.753294c-21.285449 0-39.173221-17.587977-39.173222-39.373084 0-21.485313 17.887772-39.07329 39.173222-39.07329h184.473894z" fill="#999999" p-id="2456"></path></svg>`
   if(i==0){
       i=i0
   }else if(i==1){
       i=i1
   }else if(i==2){
       i=i2
   }
   $("body").append(`<div class="createAlert1"><i class="createAlert"><spn id="loading">${i}</span> ${content}</i></div>`)
      $(".createAlert1").css({
        position: 'fixed',
        width:'100%',
        top: '30px',
        zIndex:'9999',
      })
      $(".createAlert").css({
        margin:'auto',
        padding: '3px 10px',
        backgroundColor: back,
        color: '#fff',
        fontStyle: 'normal',
        fontSize: '12px',
        borderRadius: '5px',
        fontFamily:'system-ui',
        display: 'flex',
        alignItems: 'center',
        width:'fit-content',
      })
      $(".createAlert").stop().fadeIn(1000);
      if(i==i2){
            defAnimate()
      }
       function defAnimate(){
           $(".createAlert1").css("top","30%")
            $("#loading").animate({rotate:"0deg"},0).animate({rotate:"360deg"},600,function(){
            defAnimate()
            })
         }
   }
//   隐藏弹窗
function hideAlert(){
     $(".createAlert").stop().fadeOut(1000,function(){
          $(".createAlert1").remove()
      })
}
//   刷新当前页面
  function upUrl(){
      setTimeout(function() {
          window.location.href=window.location.href
      }, 1000);
  }