<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>新增离线卡密</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <style>
        .offline-form-container {
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .offline-form-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 800px;
            animation: slideInUp 0.6s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .offline-form-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .offline-form-header h2 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .subtitle {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .offline-form-body {
            padding: 40px;
        }
        
        .form-step {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #ff6b6b;
        }
        
        .step-icon {
            width: 40px;
            height: 40px;
            background: #ff6b6b;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            font-size: 18px;
        }
        
        .step-content h3 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 18px;
        }
        
        .step-content p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .enhanced-form-item {
            margin-bottom: 25px;
        }
        
        .enhanced-form-item .layui-form-label {
            width: 120px;
            font-weight: 600;
            color: #333;
            font-size: 15px;
        }
        
        .enhanced-form-item .layui-input-block {
            margin-left: 140px;
        }
        
        .enhanced-form-item .layui-input,
        .enhanced-form-item .layui-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .enhanced-form-item .layui-input:focus,
        .enhanced-form-item .layui-select:focus {
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }
        
        .quantity-controls {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .quantity-btn {
            width: 40px;
            height: 40px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .quantity-btn:hover {
            background: #ee5a24;
            transform: scale(1.05);
        }
        
        .quantity-input {
            width: 100px !important;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }
        
        .quick-select {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .quick-btn {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            color: #666;
        }
        
        .quick-btn:hover,
        .quick-btn.active {
            background: #ff6b6b;
            border-color: #ff6b6b;
            color: white;
            transform: translateY(-2px);
        }
        
        .submit-section {
            margin-top: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border: none;
            border-radius: 25px;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 200px;
            height: 50px;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }
        
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
            display: none;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .preview-section {
            margin-top: 30px;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 12px;
            border-left: 4px solid #2196f3;
        }
        
        .preview-text {
            color: #1976d2;
            font-weight: 600;
            text-align: center;
        }
        
        .success-animation {
            animation: pulse 0.6s ease-in-out;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .price-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .price-info .price-label {
            color: #856404;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .price-info .price-value {
            color: #d63031;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>

<body>
<div class="offline-form-container">
    <div class="offline-form-card">
        <div class="offline-form-header">
            <h2>🎮 新增离线CDK卡密</h2>
            <div class="subtitle">为离线游戏生成永久激活卡密</div>
        </div>
        <div class="offline-form-body">
            <!-- 步骤指示 -->
            <div class="form-step">
                <div class="step-icon">1</div>
                <div class="step-content">
                    <h3>选择离线游戏商品</h3>
                    <p>选择要生成卡密的离线游戏商品，系统将自动使用该商品的离线价格</p>
                </div>
            </div>

            <form class="layui-form" id="addOfflineCdkForm">
                <!-- 商品类型选择框 -->
                <div class="enhanced-form-item">
                    <label class="layui-form-label">🎮 离线商品</label>
                    <div class="layui-input-block">
                        <select name="goods_id" id="goodsType" lay-filter="goodsType" lay-search required>
                            <option value="">请选择离线游戏商品</option>
                            {volist name="goodsList" id="goods"}
                            <option value="{$goods.id}" data-price="{$goods.product_amount}">{$goods.goods_name} (￥{$goods.product_amount})</option>
                            {/volist}
                        </select>
                    </div>
                </div>

                <!-- 价格显示 -->
                <div class="enhanced-form-item" id="priceDisplay" style="display: none;">
                    <label class="layui-form-label">💰 卡密价格</label>
                    <div class="layui-input-block">
                        <div class="price-info">
                            <div class="price-label">离线账号价格：</div>
                            <div class="price-value" id="priceValue">￥0.00</div>
                        </div>
                    </div>
                </div>

                <!-- 代理选择 -->
                <div class="enhanced-form-item">
                    <label class="layui-form-label">👤 指定代理</label>
                    <div class="layui-input-block">
                        <select name="agent_id" id="agentSelect">
                            <option value="">管理员生成（不指定代理）</option>
                            {volist name="agentsList" id="agent"}
                            <option value="{$agent.id}">{$agent.username}</option>
                            {/volist}
                        </select>
                    </div>
                </div>

                <!-- 步骤指示 -->
                <div class="form-step">
                    <div class="step-icon">2</div>
                    <div class="step-content">
                        <h3>设置生成数量</h3>
                        <p>选择要生成的离线卡密数量，支持批量生成最多100张</p>
                    </div>
                </div>

                <!-- 生成数量 -->
                <div class="enhanced-form-item">
                    <label class="layui-form-label">🔢 生成数量</label>
                    <div class="layui-input-block">
                        <div class="quantity-controls">
                            <div class="quantity-selector">
                                <div class="quantity-btn" onclick="decreaseQuantity()">-</div>
                                <input type="number" name="num" id="generateNum" class="layui-input quantity-input" value="1" min="1" max="100" required>
                                <div class="quantity-btn" onclick="increaseQuantity()">+</div>
                            </div>
                            <div class="quick-select">
                                <span style="color: #666; font-size: 12px; margin-right: 10px;">快速选择：</span>
                                <div class="quick-btn" onclick="setQuantity(1)">1张</div>
                                <div class="quick-btn" onclick="setQuantity(5)">5张</div>
                                <div class="quick-btn" onclick="setQuantity(10)">10张</div>
                                <div class="quick-btn" onclick="setQuantity(20)">20张</div>
                                <div class="quick-btn" onclick="setQuantity(50)">50张</div>
                                <div class="quick-btn" onclick="setQuantity(100)">100张</div>
                            </div>
                            <div class="layui-form-mid layui-word-aux" style="color: #999; font-size: 12px;">
                                💡 离线卡密激活后永久有效，建议根据实际需求选择合适的数量
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交区域 -->
                <div class="submit-section">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; color: #333;">🚀 准备生成离线卡密</h3>
                        <p style="margin: 5px 0 0 0; color: #666; font-size: 13px;">确认信息无误后点击生成按钮</p>
                    </div>
                    <div style="text-align: center;">
                        <button class="layui-btn submit-btn" lay-submit lay-filter="submitAddOfflineCdk">
                            <span class="loading-spinner" id="loadingSpinner"></span>
                            <span id="submitText">🎯 立即生成离线卡密</span>
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary" style="margin-left: 15px; border-radius: 20px; display: inline-flex; align-items: center; justify-content: center; line-height: 1; height: 40px; min-width: 100px;">
                            🔄 重置表单
                        </button>
                    </div>
                </div>

                <!-- 生成预览 -->
                <div class="preview-section" id="previewSection" style="display: none;">
                    <div class="preview-text" id="cdkPreview">等待生成结果...</div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script src="/static/js/jquery/1.10.2/jquery.min.js"></script>
<script>
    layui.config({
        base: '/layuiadmin/' // 静态资源所在路径
    }).extend({
        index: 'lib/index' // 主入口模块
    }).use(['index', 'form'], function () {
        var $ = layui.$, form = layui.form;

        // 监听商品选择
        form.on('select(goodsType)', function (data) {
            var selectedOption = $(data.elem).find('option:selected');
            var price = selectedOption.data('price');

            if (price) {
                $('#priceValue').text('￥' + price);
                $('#priceDisplay').show();
            } else {
                $('#priceDisplay').hide();
            }
        });

        // 数量控制函数
        window.increaseQuantity = function() {
            var input = $('#generateNum');
            var currentVal = parseInt(input.val()) || 1;
            if (currentVal < 100) {
                input.val(currentVal + 1);
                updateQuantityDisplay();
            }
        };

        window.decreaseQuantity = function() {
            var input = $('#generateNum');
            var currentVal = parseInt(input.val()) || 1;
            if (currentVal > 1) {
                input.val(currentVal - 1);
                updateQuantityDisplay();
            }
        };

        window.setQuantity = function(num) {
            $('#generateNum').val(num);
            updateQuantityDisplay();
            // 添加点击动画
            $('.quick-btn').removeClass('active');
            event.target.classList.add('active');
            setTimeout(() => event.target.classList.remove('active'), 200);
        };

        function updateQuantityDisplay() {
            var num = parseInt($('#generateNum').val()) || 1;
            var preview = $('#cdkPreview');
            if (num === 1) {
                preview.text('准备生成 1 张离线卡密');
            } else {
                preview.text('准备批量生成 ' + num + ' 张离线卡密');
            }
        }

        // 监听数量输入变化
        $('#generateNum').on('input', function() {
            var val = parseInt($(this).val());
            if (val > 100) $(this).val(100);
            if (val < 1) $(this).val(1);
            updateQuantityDisplay();
        });

        // 初始化预览
        updateQuantityDisplay();
        $('#previewSection').show();

        // 监听表单提交
        form.on('submit(submitAddOfflineCdk)', function (data) {
            // 验证生成数量
            var num = parseInt(data.field.num);
            if(isNaN(num) || num < 1 || num > 100) {
                layer.msg('生成数量必须在1-100之间', {icon: 2});
                return false;
            }

            // 显示加载状态
            showLoading();

            $.ajax({
                url: '/admin/CdkController/addOfflineCdk',
                type: 'POST',
                data: data.field,
                success: function (response) {
                    hideLoading();

                    if (response.msg === 1) {
                        var previewSection = $('#previewSection');
                        var cdkPreview = $('#cdkPreview');

                        if (response.cdk_codes && response.cdk_codes.length > 0) {
                            // 批量生成的情况
                            cdkPreview.html(`
                                <div style="text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 10px;">🎉</div>
                                    <div style="font-size: 18px; color: #52c41a; font-weight: bold;">批量生成成功！</div>
                                    <div style="margin-top: 8px; color: #666;">共生成 ${response.cdk_codes.length} 张离线卡密</div>
                                </div>
                            `);
                            // 立即显示批量卡密弹窗
                            showCdkCodesModal(response.cdk_codes, '离线卡密');
                        } else {
                            // 单个生成的情况
                            cdkPreview.html(`
                                <div style="text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 10px;">✅</div>
                                    <div style="font-size: 18px; color: #52c41a; font-weight: bold;">生成成功！</div>
                                    <div style="margin-top: 8px; color: #666;">离线卡密已生成</div>
                                </div>
                            `);
                            // 立即显示单个卡密弹窗
                            showSingleCdkModal(response.cdk_code, '离线卡密');
                        }

                        // 添加成功动画
                        previewSection.addClass('success-animation');
                        setTimeout(() => previewSection.removeClass('success-animation'), 600);

                        // 不自动关闭，等用户操作完弹窗后再提示
                        layer.msg('🎉 离线卡密生成成功！', { icon: 1, time: 2000 });
                    } else {
                        $('#cdkPreview').html(`
                            <div style="text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                                <div style="font-size: 16px; color: #ff4d4f; font-weight: bold;">生成失败</div>
                                <div style="margin-top: 8px; color: #666;">${response.content || '未知错误'}</div>
                            </div>
                        `);
                        layer.msg('生成失败：' + response.content, { icon: 2 });
                    }
                },
                error: function () {
                    hideLoading();
                    $('#cdkPreview').html(`
                        <div style="text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                            <div style="font-size: 16px; color: #ff4d4f; font-weight: bold;">网络错误</div>
                            <div style="margin-top: 8px; color: #666;">请检查网络连接后重试</div>
                        </div>
                    `);
                    layer.msg('服务器错误，请稍后再试', { icon: 2 });
                }
            });
            return false;
        });

        // 显示加载状态
        function showLoading() {
            $('#loadingSpinner').show();
            $('#submitText').text('🔄 正在生成中...');
            $('.submit-btn').prop('disabled', true);
            $('#cdkPreview').html(`
                <div style="text-align: center;">
                    <div class="loading-spinner" style="display: inline-block; margin-right: 10px;"></div>
                    <span>正在生成离线卡密，请稍候...</span>
                </div>
            `);
        }

        // 隐藏加载状态
        function hideLoading() {
            $('#loadingSpinner').hide();
            $('#submitText').text('🎯 立即生成离线卡密');
            $('.submit-btn').prop('disabled', false);
        }

        // 显示批量生成的卡密弹窗
        function showCdkCodesModal(codes, type) {
            var content = '<div style="max-height: 400px; overflow-y: auto;">';
            content += '<div style="margin-bottom: 15px; text-align: center;"><strong>🎯 生成的' + type + '列表</strong></div>';
            codes.forEach((code, index) => {
                content += `
                    <div style="display: flex; align-items: center; padding: 8px; border: 1px solid #e8e8e8; margin-bottom: 5px; border-radius: 6px; background: #f0f9ff;">
                        <span style="flex: 1; font-family: monospace; font-size: 13px; color: #333;">${index + 1}. ${code}</span>
                        <button onclick="copyToClipboard('${code}'); closeModalAfterCopy();" style="background: #ff6b6b; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">📋 复制</button>
                    </div>
                `;
            });
            content += '</div>';

            var modalIndex = layer.open({
                type: 1,
                title: '🎯 生成的' + type + '列表 (' + codes.length + '个)',
                area: ['600px', '500px'],
                content: content,
                btn: ['📋 全部复制', '关闭'],
                yes: function() {
                    var allCodes = codes.join('\n');
                    copyToClipboard(allCodes);
                    closeModalAfterCopy();
                },
                btn2: function() {
                    closeCurrentPage();
                }
            });
            window.currentModalIndex = modalIndex;
        }

        // 显示单个卡密弹窗
        function showSingleCdkModal(code, type) {
            var content = `
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 24px; margin-bottom: 15px;">🎯</div>
                    <div style="font-size: 18px; color: #ff6b6b; font-weight: bold; margin-bottom: 15px;">${type}生成成功！</div>
                    <div style="margin-bottom: 10px; color: #666;">您的卡密码：</div>
                    <div style="padding: 15px; background: #fff5f5; border: 2px dashed #ff6b6b; border-radius: 8px; font-family: monospace; word-break: break-all; font-size: 16px; color: #333; margin-bottom: 15px;">
                        ${code}
                    </div>
                    <button onclick="copyToClipboard('${code}'); closeModalAfterCopy();" style="background: #ff6b6b; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-size: 14px;">
                        📋 复制卡密
                    </button>
                </div>
            `;

            var modalIndex = layer.open({
                type: 1,
                title: '🎯 ' + type + '生成成功',
                area: ['450px', '350px'],
                content: content,
                btn: ['关闭'],
                yes: function() {
                    closeCurrentPage();
                }
            });
            window.currentModalIndex = modalIndex;
        }

        // 复制到剪贴板
        window.copyToClipboard = function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    layer.msg('📋 复制成功！', {icon: 1});
                });
            } else {
                // 兼容性方案
                var textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                layer.msg('📋 复制成功！', {icon: 1});
            }
        };

        // 复制后关闭弹窗
        window.closeModalAfterCopy = function() {
            setTimeout(function() {
                if (window.currentModalIndex) {
                    layer.close(window.currentModalIndex);
                }
                closeCurrentPage();
            }, 1000); // 1秒后关闭
        };

        // 关闭当前页面
        function closeCurrentPage() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
            parent.layui.table.reload('LAY-app-cdk-list');
        }
    });
</script>
</body>
</html>
