<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>后台管理系统</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body class="layui-layout-body">

<div id="LAY_app">
  <div class="layui-layout layui-layout-admin">
    <div class="layui-header">
      <!-- 头部区域 -->
      <ul class="layui-nav layui-layout-left">
        <li class="layui-nav-item layadmin-flexible" lay-unselect>
          <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
            <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
          </a>
        </li>
        <li class="layui-nav-item layui-hide-xs" lay-unselect>
          <a href="/" target="_blank" title="前台">
            <i class="layui-icon layui-icon-website"></i>
          </a>
        </li>
        <li class="layui-nav-item" lay-unselect>
          <a href="javascript:;" layadmin-event="refresh" title="刷新">
            <i class="layui-icon layui-icon-refresh-3"></i>
          </a>
        </li>
      </ul>
      <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">
        <li class="layui-nav-item layui-hide-xs" lay-unselect>
          <a href="javascript:;" layadmin-event="theme">
            <i class="layui-icon layui-icon-theme"></i>
          </a>
        </li>
        <li class="layui-nav-item layui-hide-xs" lay-unselect>
          <a href="javascript:;" layadmin-event="note">
            <i class="layui-icon layui-icon-note"></i>
          </a>
        </li>
        <li class="layui-nav-item layui-hide-xs" lay-unselect>
          <a href="javascript:;" layadmin-event="fullscreen">
            <i class="layui-icon layui-icon-screen-full"></i>
          </a>
        </li>
        <li class="layui-nav-item" lay-unselect>
          <a href="javascript:;">
            <cite>{$admin.username}</cite>
          </a>
          <dl class="layui-nav-child">
            <dd><a lay-href="/admin/base/update">检查更新</a></dd>
            <dd><a lay-href="/admin/index/password">修改密码</a></dd>
            <hr>
            <dd layadmin-event="" style="text-align: center;"><a href="/admin/base/exit">退出</a></dd>
          </dl>
        </li>

        <li class="layui-nav-item layui-hide-xs" lay-unselect>
          <a href="javascript:;" layadmin-event="theme"><i class="layui-icon layui-icon-more-vertical"></i></a>
        </li>
        <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm" lay-unselect>
          <a href="javascript:;" layadmin-event="theme"><i class="layui-icon layui-icon-more-vertical"></i></a>
        </li>
      </ul>
    </div>

    <!-- 侧边菜单 -->
    <div class="layui-side layui-side-menu">
      <div class="layui-side-scroll">
        <div class="layui-logo" lay-href="/admin/index/console">
          <span>Admin控制面板</span>
        </div>

        <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
          <li data-name="home" class="layui-nav-item">
            <a href="javascript:;" lay-href="/admin/index/console" lay-tips="控制台" lay-direction="2">
               <i class="layui-icon layui-icon-home"></i>
              <cite>控制台</cite>
            </a>
          </li>
          
          <!-- 系统管理 -->
          <li data-name="system" class="layui-nav-item">
            <a href="javascript:;" lay-tips="系统管理" lay-direction="2">
              <i class="layui-icon layui-icon-set"></i>
              <cite>系统管理</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="config">
                <a lay-href="/admin/index/config">网站设置</a>
              </dd>
              <dd data-name="systems">
                <a lay-href="/admin/index/systems">系统配置</a>
              </dd>
              <dd data-name="banner">
                <a lay-href="/admin/index/banner">轮播配置</a>
              </dd>
              <dd data-name="notice">
                <a lay-href="/admin/index/notice">公告管理</a>
              </dd>
              <dd data-name="logs">
                <a lay-href="/admin/index/systemLogs">系统日志</a>
              </dd>
            </dl>
          </li>
          
          <!-- 商品管理 -->
          <li data-name="goods" class="layui-nav-item">
            <a href="javascript:;" lay-tips="商品管理" lay-direction="2">
              <i class="layui-icon layui-icon-app"></i>
              <cite>商品管理</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="goodsclass">
                <a lay-href="/admin/index/goodsclass">商品分类</a>
              </dd>
              <dd data-name="goods">
                <a lay-href="/admin/index/goods">游戏列表</a>
              </dd>
            </dl>
          </li>
          
          <!-- 标签管理 -->
          <li data-name="tags" class="layui-nav-item">
            <a href="javascript:;" lay-tips="标签管理" lay-direction="2">
              <i class="layui-icon layui-icon-tabs"></i>
              <cite>标签管理</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="tagsList">
                <a lay-href="/admin/tags">标签列表</a>
              </dd>
            </dl>
          </li>
          
          <!-- 套餐管理 -->
          <li data-name="combo" class="layui-nav-item">
            <a href="javascript:;" lay-tips="套餐管理" lay-direction="2">
              <i class="layui-icon layui-icon-template"></i>
              <cite>套餐管理</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="combo">
                <a lay-href="/admin/index/combo">游戏套餐管理</a>
              </dd>
              <dd data-name="membershipPricing">
                <a lay-href="/admin/index/membershipPricing">会员价格管理</a>
              </dd>
            </dl>
          </li>
          
          <!-- 用户中心 -->
          <li data-name="user" class="layui-nav-item">
            <a href="javascript:;" lay-tips="用户中心" lay-direction="2">
              <i class="layui-icon layui-icon-user"></i> 
              <cite>用户中心</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="userlist">
                <a lay-href="/admin/index/user">用户列表</a>
              </dd>
              <dd data-name="thirdParty">
                <a lay-href="/admin/index/thirdPartyBindings">快捷登录绑定</a>
              </dd>
              <dd data-name="userlogin">
                <a lay-href="/admin/index/userlogin">用户登录日志</a>
              </dd>
            </dl>
          </li>
          
          <!-- 邮箱用户管理 -->
          <li data-name="emailManagement" class="layui-nav-item">
            <a href="javascript:;" lay-tips="邮箱用户管理" lay-direction="2">
              <i class="layui-icon layui-icon-email"></i>
              <cite>邮箱管理</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="emailUsers">
                <a lay-href="/admin/index/EmailUserList">邮箱列表</a>
              </dd>
            </dl>
          </li>
          
          <!-- 账号资源 -->
          <li data-name="account" class="layui-nav-item">
            <a href="javascript:;" lay-tips="账号资源" lay-direction="2">
              <i class="layui-icon layui-icon-date"></i>
              <cite>账号资源</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="accountList">
                <a lay-href="/admin/index/account">账号列表</a>
              </dd>
              <dd data-name="tokenPool">
                <a lay-href="/admin/index/tokenPool">账号令牌池</a>
              </dd>
            </dl>
          </li>
          
          <!-- 卡密管理 -->
          <li data-name="cdkmanage" class="layui-nav-item">
            <a href="javascript:;" lay-tips="卡密管理" lay-direction="2">
              <i class="layui-icon layui-icon-vercode"></i>
              <cite>卡密管理</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="cdk">
                <a lay-href="/admin/CdkController/index">CDK管理</a>
              </dd>
            </dl>
          </li>
          
          <!-- 交易管理 -->
          <li data-name="trade" class="layui-nav-item">
            <a href="javascript:;" lay-tips="交易管理" lay-direction="2">
              <i class="layui-icon layui-icon-senior"></i>
              <cite>交易管理</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="order">
                <a lay-href="/admin/index/order">订单管理</a>
              </dd>
            </dl>
          </li>
          
          <!-- 客户服务 -->
          <li data-name="service" class="layui-nav-item">
            <a href="javascript:;" lay-tips="客户服务" lay-direction="2">
              <i class="layui-icon layui-icon-form"></i>
              <cite>客户服务</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="workorder">
                <a lay-href="/admin/index/workorder">工单管理</a>
              </dd>
            </dl>
          </li>
          
          <!-- 代理系统 -->
          <li data-name="agent" class="layui-nav-item">
            <a href="javascript:;" lay-tips="代理系统" lay-direction="2">
              <i class="layui-icon layui-icon-share"></i>
              <cite>代理系统</cite>
            </a>
            <dl class="layui-nav-child">
              <dd data-name="agentList">
                <a lay-href="/admin/index/agents">代理列表</a>
              </dd>
            </dl>
          </li>
          
          <!-- 退出系统 -->
          <li data-name="get" class="layui-nav-item">
            <a href="/admin/base/exit" lay-tips="退出" lay-direction="2">
              <i class="layui-icon layui-icon-auz"></i>
              <cite>退出</cite>
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- 页面标签 -->
    <div class="layadmin-pagetabs" id="LAY_app_tabs">
      <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
      <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
      <div class="layui-icon layadmin-tabs-control layui-icon-down">
        <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;"></a>
            <dl class="layui-nav-child layui-anim-fadein">
              <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
              <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
              <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
            </dl>
          </li>
        </ul>
      </div>
      <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
        <ul class="layui-tab-title" id="LAY_app_tabsheader">
          <li lay-id="admin/index/console" lay-attr="admin/index/console" class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
        </ul>
      </div>
    </div>


    <!-- 主体内容 -->
    <div class="layui-body" id="LAY_app_body">
      <div class="layadmin-tabsbody-item layui-show">
        <iframe src="/admin/index/console" frameborder="0" class="layadmin-iframe"></iframe>
      </div>
    </div>

    <!-- 辅助元素，一般用于移动设备下遮罩 -->
    <div class="layadmin-body-shade" layadmin-event="shade"></div>
  </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
  layui.config({
    base: '../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use('index');
</script>
</body>
</html>


