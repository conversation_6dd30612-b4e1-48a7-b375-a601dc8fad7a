<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>页面不存在 - 管理系统</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <style>
    .error-container {
      text-align: center;
      padding: 50px 20px;
      min-height: 400px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .error-icon {
      font-size: 80px;
      color: #ff5722;
      margin-bottom: 20px;
    }
    .error-title {
      font-size: 24px;
      color: #333;
      margin-bottom: 10px;
    }
    .error-message {
      font-size: 16px;
      color: #666;
      margin-bottom: 30px;
      max-width: 600px;
      line-height: 1.5;
    }
    .error-details {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 30px;
      font-size: 14px;
      color: #999;
      max-width: 600px;
      word-break: break-all;
    }
    .error-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="error-icon">
      <i class="layui-icon layui-icon-face-cry"></i>
    </div>
    
    <div class="error-title">页面不存在</div>
    
    <div class="error-message">
      {$message|default='抱歉，您访问的页面不存在或已被删除'}
    </div>
    
    {if isset($error)}
    <div class="error-details">
      错误详情：{$error}
    </div>
    {/if}
    
    <div class="error-actions">
      <button class="layui-btn layui-btn-primary" onclick="history.back()">
        <i class="layui-icon layui-icon-return"></i> 返回上页
      </button>
      <button class="layui-btn" onclick="parent.location.reload()">
        <i class="layui-icon layui-icon-refresh"></i> 刷新页面
      </button>
      <button class="layui-btn layui-btn-normal" onclick="parent.location.href='/admin/index/workorder'">
        <i class="layui-icon layui-icon-list"></i> 工单列表
      </button>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
    layui.use(['layer'], function(){
      var layer = layui.layer;
      
      // 如果是在iframe中，可以关闭当前层
      if (window.parent !== window) {
        setTimeout(function() {
          // 可以选择自动关闭或显示提示
          // parent.layer.closeAll();
        }, 3000);
      }
    });
  </script>
</body>
</html>
