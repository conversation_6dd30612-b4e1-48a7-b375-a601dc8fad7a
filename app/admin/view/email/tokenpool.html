<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>令牌池列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">令牌池管理</div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-event="add"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-normal" id="importBtn"><i class="layui-icon">&#xe67c;</i>导入文件</button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-warm" id="importFolderBtn"><i class="layui-icon">&#xe7a0;</i>批量导入文件夹</button>
                    </div>
                    <div class="layui-inline">
                        <input type="text" id="searchInput" placeholder="搜索账户名称/备注" class="layui-input" style="width: 200px;">
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="searchBtn">搜索</button>
                    </div>
                </div>
            </div>

            <table class="layui-table" id="tokenTable" lay-filter="tokenTable"></table>
        </div>
    </div>
</div>

<!-- 隐藏的文件上传表单 -->
<form id="importForm" style="display: none;">
    <input type="file" id="fileInput" name="file" accept=".maFile">
</form>

<!-- 隐藏的文件夹上传表单 -->
<form id="importFolderForm" style="display: none;">
    <input type="file" id="folderInput" name="folder" webkitdirectory directory multiple>
</form>

<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
layui.use(['table', 'form', 'util', 'element'], function () {
    var $ = layui.jquery;
    var table = layui.table;
    var form = layui.form;
    var util = layui.util;
    var element = layui.element;

    // 保存原始数据
    var originalData = [];
    
    // 修改表格渲染配置，保存表格配置到变量
    var tableConfig = {
        elem: '#tokenTable',
        url: '/admin/index/getSteamAccountData',
        page: true,
        cols: [[
            {type: 'numbers', title: '序号', width: 50},
            {field: 'id', title: 'ID', width: 70},
            {field: 'account_name', title: '账户名称', minWidth: 120},
            {field: 'shared_secret', title: 'Shared Secret', minWidth: 180},
            {field: 'revocation_code', title: '撤销码', minWidth: 130},
            {field: 'token_gid', title: 'Token GID', minWidth: 130},
            {field: 'SteamID', title: 'SteamID', minWidth: 130},
            {field: 'bz', title: '账号备注', minWidth: 130, templet: function(d){
                return d.bz ? d.bz : '<span class="layui-text-gray">当前令牌暂无备注</span>';
            }},
            {field: 'status', title: '状态', width: 80, templet: function(d){
                return d.status == 1 ? '<span class="layui-badge layui-bg-green">有效</span>' : 
                       '<span class="layui-badge layui-bg-gray">无效</span>';
            }},
            {title: '操作', toolbar: '#tableBar', width: 120, fixed: 'right', align: 'center'}
        ]],
        skin: 'line',
        done: function(res) {
            // 保存加载到的数据
            originalData = res.data;
        }
    };
    
    // 初始渲染表格
    table.render(tableConfig);

    // 工具条点击事件
    table.on('tool(tokenTable)', function (obj) {
        var data = obj.data;
        var event = obj.event;

        if (event === 'edit') {
            // 行内编辑功能
            layer.prompt({
                formType: 2,
                value: data.bz,
                title: '编辑账号备注',
                area: ['300px', '150px'] // 自定义文本域宽高
            }, function(value, index){
                layer.close(index);
                // 发送更新请求到后端
                $.post('/admin/index/updateAccountRemark', {
                    id: data.id,
                    bz: value
                }, function (res) {
                    if (res.code == 0) {
                        layer.msg('更新成功', {icon: 1});
                        obj.update({
                            bz: value
                        });
                    } else {
                        layer.msg('更新失败', {icon: 2});
                    }
                }, 'json');
            });
        }

        if (event === 'del') {
            layer.confirm('确定要删除此令牌吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/index/token_delete', {
                    id: data.id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code == 0) {
                        layer.msg(res.msg, {icon: 1});
                        table.reload('tokenTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });

    // 添加按钮点击事件
    $('.toolbar [lay-event]').on('click', function () {
        var event = $(this).attr('lay-event');
        if (event === 'add') {
            layer.open({
                type: 2,
                title: '添加令牌',
                content: '/admin/index/token_add',
                area: ['550px', '450px'],
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index];
                    var submit = layero.find('iframe').contents().find('#tokenSubmit');
                    submit.click();
                }
            });
        }
    });

    // 修改搜索功能，从服务器获取搜索结果
    $('#searchBtn').on('click', function(){
        var searchValue = $('#searchInput').val();
        
        // 使用table.reload从服务器重新加载数据
        table.reload('tokenTable', {
            page: {
                curr: 1 // 从第一页开始
            },
            where: {
                searchValue: searchValue // 传递搜索参数到后端
            }
        });
    });

    // 添加回车搜索功能
    $('#searchInput').on('keyup', function(e){
        if(e.keyCode === 13){
            $('#searchBtn').click();
        }
    });

    // 导入文件按钮点击事件
    $('#importBtn').on('click', function(){
        $('#fileInput').click();
    });

    // 文件选择后自动上传
    $('#fileInput').on('change', function(){
        var file = this.files[0];
        if (!file) return;
        
        // 判断文件类型 - 修改为只接受.maFile
        if (!file.name.endsWith('.maFile')) {
            layer.msg('请选择.maFile格式的文件', {icon: 2});
            return;
        }
        
        // 读取文件内容
        var reader = new FileReader();
        reader.onload = function(e) {
            var fileContent = e.target.result;
            
            // 开始上传数据
            layer.load(2);
            $.ajax({
                url: '/admin/index/importTokenFile',
                type: 'POST',
                data: {
                    tokenData: fileContent
                },
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code == 0) {
                        layer.msg(res.msg, {icon: 1});
                        // 重新加载表格数据
                        table.reload('tokenTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('导入失败，请检查文件格式是否正确', {icon: 2});
                }
            });
        };
        
        reader.readAsText(file);
        
        // 清空文件输入框，使得可以重复选择同一个文件
        $('#importForm')[0].reset();
    });
    
    // 批量导入文件夹按钮点击事件
    $('#importFolderBtn').on('click', function(){
        $('#folderInput').click();
    });
    
    // 文件夹选择后处理所有文件
    $('#folderInput').on('change', function(){
        var files = this.files;
        if (!files || files.length === 0) return;
        
        // 过滤出仅maFile文件
        var validFiles = [];
        for (var i = 0; i < files.length; i++) {
            if (files[i].name.endsWith('.maFile')) {
                validFiles.push(files[i]);
            }
        }
        
        if (validFiles.length === 0) {
            layer.msg('未找到有效的.maFile文件', {icon: 2});
            return;
        }
        
        // 创建进度条弹窗
        var loadIndex = layer.open({
            type: 1,
            title: '批量导入进度',
            content: '<div class="layui-progress layui-progress-big" lay-showpercent="true" lay-filter="importProgress">' +
                    '<div class="layui-progress-bar" lay-percent="0%"></div>' +
                    '</div>' +
                    '<div id="importStatus" style="padding: 15px; text-align: center;">正在读取文件...</div>',
            area: ['500px', '150px'],
            closeBtn: 0
        });
        
        // 初始化进度条
        element.render('progress');
        
        // 读取所有文件内容
        var tokenDataArray = [];
        var fileContents = 0;
        
        function updateProgress() {
            var percent = Math.floor((fileContents / validFiles.length) * 100);
            element.progress('importProgress', percent + '%');
            $('#importStatus').text('正在读取文件... (' + fileContents + '/' + validFiles.length + ')');
        }
        
        function readFile(index) {
            if (index >= validFiles.length) {
                // 全部文件读取完成，发送到服务器
                $('#importStatus').text('正在上传到服务器...');
                
                $.ajax({
                    url: '/admin/index/batchImportTokens',
                    type: 'POST',
                    data: {
                        tokenDataArray: tokenDataArray
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code == 0) {
                            // 显示成功信息
                            var existingMsg = '';
                            if (res.data.existAccounts && res.data.existAccounts.length > 0) {
                                existingMsg = '<br>已存在的账号: ' + res.data.existAccounts.join(', ');
                            }
                            
                            $('#importStatus').html(res.msg + existingMsg);
                            
                            // 添加关闭按钮
                            layer.closeAll('loading');
                            layer.title('导入完成', loadIndex);
                            
                            // 重新加载表格
                            table.reload('tokenTable');
                            
                            // 在进度条弹窗上添加关闭按钮
                            var closeBtn = $('<div class="layui-btn layui-btn-primary" style="margin-top:10px;">关闭</div>');
                            $('#importStatus').after(closeBtn);
                            
                            closeBtn.on('click', function(){
                                layer.close(loadIndex);
                            });
                        } else {
                            $('#importStatus').text('导入失败: ' + res.msg);
                            
                            // 添加关闭按钮
                            var closeBtn = $('<div class="layui-btn layui-btn-primary" style="margin-top:10px;">关闭</div>');
                            $('#importStatus').after(closeBtn);
                            
                            closeBtn.on('click', function(){
                                layer.close(loadIndex);
                            });
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        $('#importStatus').text('网络错误，请重试');
                        
                        // 添加关闭按钮
                        var closeBtn = $('<div class="layui-btn layui-btn-primary" style="margin-top:10px;">关闭</div>');
                        $('#importStatus').after(closeBtn);
                        
                        closeBtn.on('click', function(){
                            layer.close(loadIndex);
                        });
                    }
                });
                
                return;
            }
            
            var file = validFiles[index];
            var reader = new FileReader();
            
            reader.onload = function(e) {
                // 将文件内容添加到数组
                tokenDataArray.push(e.target.result);
                fileContents++;
                
                // 更新进度条
                updateProgress();
                
                // 读取下一个文件
                readFile(index + 1);
            };
            
            reader.onerror = function() {
                fileContents++;
                updateProgress();
                readFile(index + 1);
            };
            
            reader.readAsText(file);
        }
        
        // 开始读取第一个文件
        readFile(0);
        
        // 清空文件输入框，使得可以重复选择同一个文件夹
        $('#importFolderForm')[0].reset();
    });
});
</script>
</body>
</html> 