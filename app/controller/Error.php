<?php
namespace app\controller;

use app\BaseController;

class Error extends BaseController
{
    public function index()
    {
        $code = input('code', 500);
        $message = input('message', '系统发生错误');
        
        $html = <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误提示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .error-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 90%;
        }
        .error-code {
            font-size: 3rem;
            color: #e74c3c;
            margin: 0;
        }
        .error-message {
            color: #2c3e50;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <h1 class="error-code">{$code}</h1>
        <p class="error-message">{$message}</p>
    </div>
</body>
</html>
HTML;

        return response($html);
    }
} 