

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layuiAdmin 工单管理 iframe 框</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-form-order" id="layuiadmin-form-order" style="padding: 20px 30px 0 0;">
    
     <div class="layui-form-item" style="display:none">
       <label class="layui-form-label">工单ID</label>
       <input type="text" name="id" value="{$workorder.id}" />
    </div>
     <div class="layui-form-item">
      <label class="layui-form-label">业务性质</label>
      <div class="layui-input-inline">
        <select name="work_type" class="select">
          <option value="0">售前</option>
          <option value="1">售后</option>
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">工单状态</label>
      <div class="layui-input-inline">
        <select name="work_state" class="select">
          <option value="0">处理中</option>
          <option value="1">已处理</option>
        </select>
      </div>
    </div>
 
    <div class="layui-form-item" style="display: flex;align-items: center;justif">
      <label class="layui-form-label">补充图</label>
      <div class="layui-input-inline">
        <a href="javascript:;" onclick="top.location.href='{$workorder.work_img}'">
            <img width="100px" src="{$workorder.work_img}" alt="" />
        </a>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">提交内容</label>
      <div class="layui-input-inline">
       <textarea name="work_reply" class="layui-textarea" disabled >{$workorder.work_content}</textarea>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">回复内容</label>
      <div class="layui-input-inline">
       <textarea name="work_reply" class="layui-textarea">{$workorder.work_reply}</textarea>
      </div>
    </div>
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="LAY-app-workorder-submit" id="LAY-app-workorder-submit" value="确认">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  let select=document.querySelectorAll(".select");
  select[0].value="{$workorder.work_type}";
  select[1].value="{$workorder.work_state}";
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form'], function(){
    var $ = layui.$
    ,form = layui.form;
    
  })
  </script>
</body>
</html>