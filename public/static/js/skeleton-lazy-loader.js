/**
 * 骨架屏懒加载管理器
 * 功能：为图片提供优雅的骨架屏加载效果
 * 用法：自动检测页面中的懒加载图片并应用骨架屏效果
 * 创建时间：2025-06-23
 */

class SkeletonLazyLoader {
  /**
   * 构造函数
   * @param {Object} options 配置选项
   */
  constructor(options = {}) {
    this.options = {
      // 提前加载距离（像素）
      rootMargin: options.rootMargin || '100px',
      // 可见性阈值
      threshold: options.threshold || 0.1,
      // 最大并发加载数
      maxConcurrent: options.maxConcurrent || 3,
      // 加载延迟（毫秒）
      loadDelay: options.loadDelay || 200,
      // 是否启用调试模式
      debug: options.debug || false,
      ...options
    };

    this.loadingQueue = [];
    this.loadingCount = 0;
    this.observer = null;
    this.init();
  }

  /**
   * 初始化懒加载器
   */
  init() {
    if (!('IntersectionObserver' in window)) {
      this.log('IntersectionObserver not supported, falling back to immediate loading');
      this.fallbackLoad();
      return;
    }

    this.createObserver();
    this.observeImages();
    this.log('Skeleton lazy loader initialized');
  }

  /**
   * 创建Intersection Observer
   */
  createObserver() {
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.addToQueue(entry.target);
          this.observer.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: this.options.rootMargin,
      threshold: this.options.threshold
    });
  }

  /**
   * 观察所有懒加载图片
   */
  observeImages() {
    const images = document.querySelectorAll('img[data-src]');
    images.forEach(img => {
      this.observer.observe(img);
    });
    this.log(`Observing ${images.length} images`);

    // 特殊处理：监听DOM变化，自动观察新添加的图片
    this.setupMutationObserver();
  }

  /**
   * 设置DOM变化监听器
   */
  setupMutationObserver() {
    if (!('MutationObserver' in window)) return;

    const mutationObserver = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 检查新添加的元素是否包含懒加载图片
            const lazyImages = node.querySelectorAll ? node.querySelectorAll('img[data-src]') : [];
            lazyImages.forEach(img => {
              this.observer.observe(img);
              this.log(`Auto-observing new image: ${img.dataset.src}`);
            });

            // 检查元素本身是否是懒加载图片
            if (node.tagName === 'IMG' && node.dataset.src) {
              this.observer.observe(node);
              this.log(`Auto-observing new image element: ${node.dataset.src}`);
            }
          }
        });
      });
    });

    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 添加图片到加载队列
   * @param {HTMLImageElement} img 图片元素
   */
  addToQueue(img) {
    this.loadingQueue.push(img);
    this.processQueue();
  }

  /**
   * 处理加载队列
   */
  processQueue() {
    if (this.loadingCount >= this.options.maxConcurrent || this.loadingQueue.length === 0) {
      return;
    }

    const img = this.loadingQueue.shift();
    this.loadingCount++;

    setTimeout(() => {
      this.loadImage(img);
    }, this.options.loadDelay);
  }

  /**
   * 加载单个图片
   * @param {HTMLImageElement} img 图片元素
   */
  loadImage(img) {
    const src = img.dataset.src;
    if (!src) {
      this.onLoadComplete(img, false);
      return;
    }

    this.log(`Loading image: ${src}`);

    // 创建新的图片对象进行预加载
    const newImg = new Image();

    newImg.onload = () => {
      this.onImageLoaded(img, src);
    };

    newImg.onerror = () => {
      this.onImageError(img, src);
    };

    // 开始加载
    newImg.src = src;
  }

  /**
   * 图片加载成功处理
   * @param {HTMLImageElement} img 原始图片元素
   * @param {string} src 图片源地址
   */
  onImageLoaded(img, src) {
    this.log(`Image loaded successfully: ${src}`);

    // 设置图片源
    img.src = src;
    img.removeAttribute('data-src');

    // 添加加载完成类
    img.classList.add('loaded');

    // 隐藏骨架屏
    this.hideSkeleton(img);

    // 显示图片
    setTimeout(() => {
      img.style.opacity = '1';
    }, 50);

    this.onLoadComplete(img, true);
  }

  /**
   * 图片加载失败处理
   * @param {HTMLImageElement} img 原始图片元素
   * @param {string} src 图片源地址
   */
  onImageError(img, src) {
    this.log(`Image failed to load: ${src}`);

    // 隐藏骨架屏
    this.hideSkeleton(img);

    // 使用无图片处理器创建优美的占位符
    if (window.noImageHandler) {
      window.noImageHandler.handleImage(img);
    } else {
      // 降级处理：设置默认图片
      this.setFallbackImage(img);
      setTimeout(() => {
        img.style.opacity = '1';
      }, 50);
    }

    this.onLoadComplete(img, false);
  }

  /**
   * 隐藏骨架屏
   * @param {HTMLImageElement} img 图片元素
   */
  hideSkeleton(img) {
    const container = img.closest('.h-40, .swiper-slide, .column-item');
    if (!container) return;

    const skeleton = container.querySelector('.skeleton-placeholder, .banner-skeleton, .column-skeleton');
    if (skeleton) {
      skeleton.classList.add('hidden');
      setTimeout(() => {
        skeleton.style.display = 'none';
      }, 300);
    }
  }

  /**
   * 设置备用图片
   * @param {HTMLImageElement} img 图片元素
   */
  setFallbackImage(img) {
    const alt = img.alt || '图片';

    // 根据图片类型设置不同的占位图
    if (img.classList.contains('game-image')) {
      // Steam游戏图片备用方案
      img.src = this.generateGamePlaceholder(alt);
    } else if (img.classList.contains('banner-image')) {
      img.src = this.generateBannerPlaceholder();
    } else if (img.classList.contains('column-image')) {
      img.src = this.generateColumnPlaceholder(alt);
    } else {
      img.src = `https://via.placeholder.com/400x300/e5e7eb/9ca3af?text=${encodeURIComponent(alt)}`;
    }

    img.removeAttribute('data-src');
  }

  /**
   * 生成游戏占位图
   * @param {string} gameName 游戏名称
   * @returns {string} 占位图URL
   */
  generateGamePlaceholder(gameName) {
    // 创建SVG占位图
    const svg = `
      <svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad1)"/>
        <circle cx="300" cy="150" r="40" fill="rgba(255,255,255,0.2)"/>
        <path d="M280 130 L320 130 L320 170 L280 170 Z M290 140 L310 140 L310 160 L290 160 Z" fill="white"/>
        <text x="300" y="250" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">${gameName}</text>
        <text x="300" y="280" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)" text-anchor="middle">Steam游戏</text>
      </svg>
    `;
    return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
  }

  /**
   * 生成轮播图占位图
   * @returns {string} 占位图URL
   */
  generateBannerPlaceholder() {
    const svg = `
      <svg width="1200" height="400" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="bannerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#bannerGrad)"/>
        <circle cx="600" cy="200" r="60" fill="rgba(255,255,255,0.1)"/>
        <rect x="570" y="170" width="60" height="60" rx="8" fill="rgba(255,255,255,0.2)"/>
        <text x="600" y="320" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle">Steam账号租借平台</text>
        <text x="600" y="350" font-family="Arial, sans-serif" font-size="16" fill="rgba(255,255,255,0.8)" text-anchor="middle">优质游戏体验，安全可靠</text>
      </svg>
    `;
    return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
  }

  /**
   * 生成专栏占位图
   * @param {string} title 专栏标题
   * @returns {string} 占位图URL
   */
  generateColumnPlaceholder(title) {
    const svg = `
      <svg width="460" height="215" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="columnGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#columnGrad)"/>
        <circle cx="230" cy="107" r="30" fill="rgba(255,255,255,0.2)"/>
        <rect x="210" y="87" width="40" height="40" rx="4" fill="rgba(255,255,255,0.3)"/>
        <text x="230" y="160" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle">${title}</text>
        <text x="230" y="180" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)" text-anchor="middle">精彩专栏</text>
      </svg>
    `;
    return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
  }

  /**
   * 加载完成处理
   * @param {HTMLImageElement} img 图片元素
   * @param {boolean} success 是否成功
   */
  onLoadComplete(img, success) {
    this.loadingCount--;

    // 触发自定义事件
    const event = new CustomEvent('skeletonImageLoaded', {
      detail: { img, success }
    });
    document.dispatchEvent(event);

    // 继续处理队列
    this.processQueue();
  }

  /**
   * 降级处理（不支持IntersectionObserver时）
   */
  fallbackLoad() {
    const images = document.querySelectorAll('img[data-src]');
    images.forEach(img => {
      this.loadImage(img);
    });
  }

  /**
   * 手动加载指定图片
   * @param {HTMLImageElement|string} target 图片元素或选择器
   */
  loadImageManually(target) {
    const img = typeof target === 'string' ? document.querySelector(target) : target;
    if (img && img.dataset.src) {
      this.loadImage(img);
    }
  }

  /**
   * 预加载指定图片
   * @param {Array} urls 图片URL数组
   */
  preloadImages(urls) {
    urls.forEach(url => {
      const img = new Image();
      img.src = url;
    });
  }

  /**
   * 销毁懒加载器
   */
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.loadingQueue = [];
    this.loadingCount = 0;
    this.log('Skeleton lazy loader destroyed');
  }

  /**
   * 调试日志
   * @param {string} message 日志信息
   */
  log(message) {
    if (this.options.debug) {
      console.log(`[SkeletonLazyLoader] ${message}`);
    }
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  // 创建全局懒加载器实例
  window.skeletonLoader = new SkeletonLazyLoader({
    debug: false, // 生产环境设为false
    maxConcurrent: 3,
    loadDelay: 100,
    rootMargin: '150px'
  });

  console.log('骨架屏懒加载器已启动');
});

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SkeletonLazyLoader;
}
