<?php
namespace app\user\controller;

use app\BaseController;
use think\Request;
use app\user\model\ThirdPartyBinding;
use think\facade\Cookie;
use think\facade\Session;
use think\facade\View;

class BindingController extends BaseController
{
    /**
     * 显示用户绑定管理界面
     */
    public function index()
    {
        // 获取当前登录用户的 ID
        $userId = Cookie::get("id");
        if (!$userId) {
            return redirect('/user/login')->with('error', '请先登录');
        }

        // 查询用户的绑定信息
        $bindings = ThirdPartyBinding::where('user_id', $userId)->select();

        // 渲染绑定管理界面
        return view('binding/index', ['bindings' => $bindings]);
    }

    /**
     * 绑定第三方账号入口
     * @param Request $request
     * @return \think\response\Json|\think\response\Redirect
     */
    public function bind(Request $request)
    {
        $type = $request->get('type', 'wx'); // 默认为微信
        $oauthService = new \OauthService();

        // 获取第三方登录 URL
        $loginData = $oauthService->getBindUrl($type);
        if ($loginData['code'] === 0) {
            return redirect($loginData['url']); // 跳转到第三方登录页面
        }

        // 返回错误信息
        return json(['error' => $loginData['msg']]);
    }

    /**
     * 第三方绑定回调处理
     * @param Request $request
     * @return \think\response\Json|\think\response\Redirect
     */
    public function callback(Request $request)
    {
        $code = $request->get('code');
        $state = $request->get('state');

        // 检查 CSRF 防护
        $storedState = session('Oauth_state');
        if ($state !== $storedState) {
            return json(['error' => 'CSRF validation failed']);
        }

        $oauthService = new \OauthService();
        $callbackData = $oauthService->handleCallback($code);
        // return json($callbackData);
        if ($callbackData['code'] === 0) {
            $socialUid = $callbackData['social_uid']; // 获取第三方用户唯一标识
            $accessToken = $callbackData['access_token'];
            $type = $callbackData['type'];

            // 获取当前用户 ID
            $userId = Cookie::get("id");
            if (!$userId) {
                return redirect('/user/login')->with('error', '请先登录');
            }

            // 检查是否已绑定该第三方账号
            $existingBinding = ThirdPartyBinding::where('social_uid', $socialUid)->find();
            if ($existingBinding) {
                return redirect('/user/index/setup')->with('error', '该账号已被绑定');
            }

            // 创建绑定记录
            ThirdPartyBinding::create([
                'faceimg' => $callbackData['faceimg'],
                'gender' => $callbackData['gender'],
                'ip' => $callbackData['ip'],
                'nickname' => $callbackData['nickname'],
                'user_id' => $userId,
                'third_party' => $type,
                'social_uid' => $socialUid,
                'access_token' => $accessToken,
            ]);

            return redirect('success')->with('success', '绑定成功');
        }

        return json(['error' => $callbackData['msg']]);
    }
    
    public function success()
    {
        return View::fetch('binding/success');
    }

    /**
     * 解除绑定
     * @param Request $request
     * @return \think\response\Json|\think\response\Redirect
     */
    public function unbind(Request $request)
    {
        $bindingId = $request->post('binding_id');
        $userId = Cookie::get("id");

        if (!$userId) {
            return redirect('/user/login')->with('error', '请先登录');
        }

        $binding = ThirdPartyBinding::where('user_id', $userId)->find();

        if (!$binding) {
            return json(['error' => '绑定记录不存在']);
        }

        $binding->delete();
        return redirect('index')->with('success', '解绑成功');
    }
} 