<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加令牌</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css">
</head>
<body>
<div class="layui-form" lay-filter="tokenForm" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">令牌数据</label>
        <div class="layui-input-block">
            <textarea name="tokenData" placeholder="请输入完整的令牌JSON数据" class="layui-textarea" style="height: 300px;"></textarea>
        </div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="tokenSubmit" id="tokenSubmit">提交</button>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 监听提交
    form.on('submit(tokenSubmit)', function(data){
        try {
            // 验证JSON格式
            var jsonData = JSON.parse(data.field.tokenData);
            
            // 发送到后端
            $.ajax({
                url: '/admin/index/addtoken',
                type: 'POST',
                data: {tokenData: data.field.tokenData},
                success: function(res){
                    if(res.code == 0){
                        layer.msg('添加成功', {icon: 1});
                        // 刷新父页面表格
                        parent.layui.table.reload('tokenTable');
                        // 关闭弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    } else {
                        layer.msg(res.msg || '添加失败', {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('服务器错误', {icon: 2});
                }
            });
        } catch(e) {
            layer.msg('请输入有效的JSON格式数据', {icon: 2});
            return false;
        }
        return false;
    });
});
</script>
</body>
</html> 