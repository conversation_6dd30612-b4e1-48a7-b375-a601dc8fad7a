<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>添加账号</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
    
    <div class="layui-form-item">
      <label class="layui-form-label">用户账号</label>
      <div class="layui-input-inline">
        <input type="text" name="us_username" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">用户密码</label>
      <div class="layui-input-inline">
        <input type="text" name="us_password" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">用户昵称</label>
      <div class="layui-input-inline">
        <input type="text" name="us_name" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">用户邮箱</label>
      <div class="layui-input-inline">
        <input type="text" name="us_email" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">用户余额</label>
      <div class="layui-input-inline">
        <input type="text" name="us_money" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">上级ID</label>
      <div class="layui-input-inline">
        <input type="text" name="superior" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input">
      </div>
    </div>
     <div class="layui-form-item">
      <label class="layui-form-label">用户头像</label>
      <div class="layui-input-inline">
        <input type="text" name="us_logo" lay-verify="required" placeholder="请上传图片" autocomplete="off" class="layui-input" value="">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="layuiadmin-upload-useradmin">上传图片</button> 
    </div>
    
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认添加">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-edit" id="layuiadmin-app-form-edit" value="确认编辑">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form','upload'], function(){
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload ;
      //图片上传
    upload.render({
      elem: '#layuiadmin-upload-useradmin'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });
    //监听提交
    form.on('submit(layuiadmin-app-form-submit)', function(data){
      var field = data.field; //获取提交的字段
      var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引  
    $.ajax({
        url: '/admin/user/addUser',
        method: 'post',
        data: data.field,
        success: function(res){
        layer.msg('保存成功');
        }
    });
    return false; // 阻止默认 form 跳转


      //提交 Ajax 成功后，关闭当前弹层并重载表格
      //$.ajax({});
      parent.layui.table.reload('LAY-app-content-list'); //重载表格
      parent.layer.close(index); //再执行关闭 
    });
  })
  </script>
</body>
</html>