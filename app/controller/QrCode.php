<?php
namespace app\controller;
use think\helper\Str;
use app\BaseController;

class QrCode extends BaseController
{
    public static function index($url="https://www.ymui.cn/"){
        require app()->getRootPath().'vendor/phpqrcode/phpqrcode.php';
        $size=4;    //图片大小
        $errorCorrectionLevel = "Q"; // 容错级别：L、M、Q、H
        $matrixPointSize = "8"; // 点的大小：1到10
        //实例化
        $qr = new \QRcode();
        //打开缓冲区
        ob_start();
        $res = $qr::png($url, false, $errorCorrectionLevel, $matrixPointSize);
        $qrcode = base64_encode(ob_get_contents());
        //会清除缓冲区的内容，并将缓冲区关闭，但不会输出内容。
        ob_end_clean();
        return "data:image/png;base64,{$qrcode}";
    }
}