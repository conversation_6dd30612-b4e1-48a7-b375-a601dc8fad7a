<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>修改文章</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item" style="display:none">
      <label class="layui-form-label">ID</label>
      <div class="layui-input-inline">
        <input type="text" name="id" lay-verify="required" placeholder="公告标题" autocomplete="off" class="layui-input"  value="{$notice.id}">
      </div>
    </div>
      <div class="layui-form-item">
      <label class="layui-form-label">选择分类</label>
      <div class="layui-input-inline">
        <select name="not_system" lay-verify="required" id="select" value="">
            <option value="0">公告文章</option>
            <option value="1">用户协议</option>
            <option value="2">系统公告</option>
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">公告标题</label>
      <div class="layui-input-inline">
        <input type="text" name="not_title" lay-verify="required" placeholder="公告标题" autocomplete="off" class="layui-input"  value="{$notice.not_title}">
      </div>
    </div>
     <div class="layui-form-item">
      <label class="layui-form-label">文章首图</label>
      <div class="layui-input-inline">
        <input type="text" name="not_img" lay-verify="required" placeholder="请上传图片" autocomplete="off" class="layui-input"  value="{$notice.not_img}">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="layuiadmin-upload-useradmin">上传图片</button> 
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">文章描述词</label>
      <div class="layui-input-inline">
        <input type="text" name="not_des" lay-verify="required" placeholder="文章描述词" autocomplete="off" class="layui-input"  value="{$notice.not_des}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">文章关键词</label>
      <div class="layui-input-inline">
        <input type="text" name="not_key" lay-verify="required" placeholder="文章关键词" autocomplete="off" class="layui-input"  value="{$notice.not_key}">
      </div>
    </div>
    <div class="layui-form-item" style="padding:10px 30px">
      <div class="layui-form-item">
          文章内容:
          <textarea class="layui-textarea" name="not_content" id="myText" style="display: none;"> </textarea>
      </div>

    </div>
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认添加">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-edit" id="layuiadmin-app-form-edit" value="确认编辑">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
   document.querySelector("#select").value="{$notice.not_system}";
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form','upload', 'layer', 'layedit'], function(){
      var $ = layui.$
      ,form = layui.form
      ,upload = layui.upload
      ,layer = layui.layer
      ,layedit = layui.layedit; // 确保变量声明正确
    
    //上传图片,必须放在"创建一个编辑器"前面
    //layedit.set一定要放在 layedit.build 前面，否则配置全局接口将无效。
    layedit.set({
        uploadImage: {
            url: '/uploads',//接口url
            type: 'post', //默认post
            data: {
                uid: this.uid
            }
        }
    });
    
    // 确保编辑器内容已经准备好
    var noticeContent = `{$notice.not_content|raw}`;
    
    //建立编辑器 
    var editIndex = layedit.build('myText',{
        height: 500,  //设置编辑器高度
    });
    
    // 使用setTimeout确保编辑器完全初始化后再设置内容
    setTimeout(function(){
        layedit.setContent(editIndex, noticeContent, false);
    }, 100);
    
    document.querySelector("body").onmousemove=function(){
        layedit.sync(editIndex);
    }
    
    //图片上传
    upload.render({
      elem: '#layuiadmin-upload-useradmin'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });
  })
  </script>
</body>
</html>