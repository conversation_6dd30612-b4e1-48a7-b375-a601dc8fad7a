<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
      <div class="layui-form-item">
      <label class="layui-form-label">优惠方式</label>
      <div class="layui-input-inline">
        <select name="coupon_type" lay-verify="required" value="1">
            <option value="1">固定金额</option>
            <option value="2">百分比</option>
        </select>
      </div>
    </div>
      <div class="layui-form-item">
      <label class="layui-form-label">优惠类型</label>
      <div class="layui-input-inline">
        <select name="coupons_typeuser" lay-verify="required" value="1">
            <option value="0">新用户专属</option>
            <option value="1">新老通用</option>
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">优惠金额</label>
      <div class="layui-input-inline">
        <input type="text" name="coupon_value" lay-verify="required" placeholder="根据优惠方式自动计算" autocomplete="off" class="layui-input">
      </div>
      <div class="layui-form-mid layui-word-aux" style="font-size:12px">如果选百分比此处金额代表比例(5)代表5%</div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">多少天可用</label>
      <div class="layui-input-inline">
        <input type="text" name="coupons_time" lay-verify="required" placeholder="填天数(0不限制)" autocomplete="off" class="layui-input">
      </div>
      <div class="layui-form-mid layui-word-aux" style="font-size:12px">对应固定天数的套餐才能使用</div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">有效天数</label>
      <div class="layui-input-inline">
        <input type="text" name="coupons_date" lay-verify="required" placeholder="有效天数" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">生成数量</label>
      <div class="layui-input-inline">
        <input type="text" name="coupons_arr" lay-verify="required" placeholder="生成多少张优惠卷" autocomplete="off" class="layui-input" value="">
      </div>
    </div>
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认添加">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-edit" id="layuiadmin-app-form-edit" value="确认编辑">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form','upload', 'layer', 'layedit'], function(){
      var returnContent;
      var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload ;
    //监听提交
    form.on('submit(layuiadmin-app-form-submit)', function(data){
    $.ajax({
        url: '/admin/base/addCoupons',
        method: 'post',
        data: data.field,
        success: function(res){
        layer.msg('保存成功');
              //提交 Ajax 成功后，关闭当前弹层并重载表格
      //$.ajax({});
      parent.layui.table.reload('LAY-app-content-list2'); //重载表格
      parent.layer.close(index); //再执行关闭 
        }
    });
    // return false; // 阻止默认 form 跳转


    });
  })
  </script>
</body>
</html>