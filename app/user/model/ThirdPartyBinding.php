<?php
namespace app\user\model;

use think\Model;

class ThirdPartyBinding extends Model
{
        // 完全指定表名（不包括数据库名）
        protected $table = 'third_party_bindings';
    
    // 禁用前缀
    protected $prefix = '';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置允许写入的字段
    protected $fillable = [
        'user_id', 
        'third_party', 
        'social_uid', 
        'access_token', 
        'nickname', 
        'faceimg', 
        'gender', 
        'ip',
        'create_time'
    ];
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = false;
    
    // 初始化方法中再次确保表名设置正确
    protected function initialize()
    {
        parent::initialize();
        // 明确设置表名，避免框架自动添加前缀
        $this->setTable('third_party_bindings');
    }
}
