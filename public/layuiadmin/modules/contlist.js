/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */ ;
layui.define(["table", "form"], function(t) {
	var e = layui.$,
        $ = layui.$,
		i = layui.table,
		n = layui.form;
	i.render({
			elem: "#LAY-app-content-list",
			url: "/admin/base/goodsclassList",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 100,
						title: "分类ID",
						sort: !0
					},
					{
						field: "cl_name",
						title: "分类名称",
						minWidth: 100
					},
					{
						field: "time",
						title: "添加时间",
						sort: !0
					},
					{
						field: "status",
						title: "发布状态",
						templet: "#buttonTpl",
						minWidth: 80,
						align: "center"
					},
					{
						title: "操作",
						minWidth: 150,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-list"
					}
				]
			],
			page: !0,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list)",
			function(t) {
			    
				var e = t.data;
				console.log(e)
				"del" === t.event ? layer.confirm("确定删除此分类？",
					function(e) {
						t.del(),
							layer.close(e)
							    layui.$.ajax({
                                    url: '/admin/base/delGoodsClass',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "编辑分类",
					content: `/admin/index/upgoodsClass?id=${e.id}&cl_name=${e.cl_name}&status=${e.status}`,
					maxmin: !0,
					area: ["400px", "400px"],
					btn: ["确定", "取消"],
					yes: function(e, i) {
						var l = window["layui-layer-iframe" + e],
							a = i.find("iframe").contents().find("#layuiadmin-app-form-edit");
						l.layui.form.on("submit(layuiadmin-app-form-edit)", function(i) {
							var l = i.field;
							    layui.$.ajax({
                                    url: '/admin/base/upGoodsClass',
                                    method: 'post',
                                    data: l,
                                    success: function(res){
                                    layer.msg('保存成功');
                                    }
                                });
							
							
							
							t.update({
								label: l.label,
								title: l.title,
								author: l.author,
								status: l.status
							}), n.render(), layer.close(e)
						}), a.trigger("click")
					}
				})}), 
	i.render({
			elem: "#LAY-app-content-tags",
			url: layui.setter.base + "json/content/tags.js",
			cols: [
				[{
						type: "numbers",
						fixed: "left"
					},
					{
						field: "id",
						width: 100,
						title: "ID",
						sort: !0
					},
					{
						field: "tags",
						title: "分类名",
						minWidth: 100
					},
					{
						title: "操作",
						width: 150,
						align: "center",
						fixed: "right",
						toolbar: "#layuiadmin-app-cont-tagsbar"
					}
				]
			],
			text: "对不起，加载出现异常！"
		}), 
		i.on("tool(LAY-app-content-tags)", 
		    function(t) {
			var i = t.data;
			if ("del" === t.event) layer.confirm("确定删除此分类？",
				function(e) {
					t.del(), layer.close(e)
				});
			else if ("edit" === t.event) {
				e(t.tr);
				layer.open({
					type: 2,
					title: "编辑分类",
					content: "../../../views/app/content/tagsform.html?id=" + i.id,
					area: ["400px", "400px"],
					btn: ["确定", "取消"],
					yes: function(e, i) {
						var n = i.find("iframe")
							.contents()
							.find("#layuiadmin-app-form-tags"),
							l = n.find('input[name="tags"]')
							.val();
						l.replace(/\s/g, "") && (t.update({
								tags: l
							}),
							layer.close(e))
					},
					success: function(t, e) {
						var n = t.find("iframe")
							.contents()
							.find("#layuiadmin-app-form-tags")
							.click();
						n.find('input[name="tags"]')
							.val(i.tags)
					}
				})
			}
		}),
	i.render({
			elem: "#LAY-app-content-list2",
			url: "/admin/base/goodsList",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 60,
						title: "ID",
						sort: !0
					},
					{
						field: "goods_class",
						width: 100,
						title: "分类名称",
					},
					{
						field: "goods_name",
						title: "商品名称",
						minWidth: 100
					},
					// {
					// 	field: "goods_img",
					// 	title: "商品图片",
					// 	minWidth: 100
					// },
					// {
					// 	field: "goods_money",
					// 	title: "商品价格",
					// 	minWidth: 80,
					// 	sort: !0
					// },
					{
						field: "goods_key",
						title: "标签词",
						minWidth: 80,
						align: "center"
					},
					// {
					// 	field: "goods_show",
					// 	title: "首页状态",
					// 	templet: "#buttonTpl",
					// 	minWidth: 80,
					// 	align: "center"
					// },
					{
						field: "goods_des",
						title: "商品描述",
						minWidth: 80,
						align: "center"
					},
					// {
					// 	field: "goods_tj",
					// 	title: "是否推荐",
					// 	templet: "#tj",
					// 	minWidth: 80,
					// 	align: "center"
					// },
					{
						field: "goods_xl",
						title: "销量",
						minWidth: 60,
						align: "center"
					},
					{
						field: "permanent_price2",
						title: "永久版价格",
						minWidth: 80,
						align: "center"
					},
					{
						field: "time",
						title: "添加时间",
						minWidth: 80,
						align: "center"
					},
					{
						title: "操作",
						width: 150,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-com"
					}
				]
			],
			page: !0,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list2)",
			function(t) {
				t.data;
				"del" === t.event ? layer.confirm("确定删除此商品？",
					function(e) {
					    console.log(t.data)
						t.del(), layer.close(e)
						
						layui.$.ajax({
                                    url: '/admin/base/delGoods',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "编辑商品",
					content: "/admin/index/upGoods?id="+t.data.id,
					area: ["500px", "500px"],
					btn: ["确定"],
					yes: function(index, layero) {
						var iframe = layero.find("iframe")[0];
						var iframeWindow = iframe.contentWindow;
						
						// 使用 iframe 中定义的方法获取数据
						var formData = iframeWindow.getFormData();
						
						$.ajax({
							url: '/admin/base/upGoods',
							method: 'post',
							data: formData,
							success: function(res){
								layer.msg('保存成功');
								i.reload("LAY-app-content-list2");
								layer.close(index);
							}
						});
					}
				})

			}),
		t("contlist", {})
});