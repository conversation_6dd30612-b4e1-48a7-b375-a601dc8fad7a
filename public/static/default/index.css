body,
div,
li,
span,
ul {
  font-family: Microsoft Yahei;
  font-size: 12px;
}
li {
  padding: 0;
  margin: 0;
}
.banner .move {
  -webkit-box-shadow: 0 0 12px 0 #e7e7e7;
  box-shadow: 0 0 12px 0 #e7e7e7;
}
.main {
  background-color: #f2f2f2;
  overflow: hidden;
}
.bg_wsize {
  width: 100%;
}
.popular_bgs {
  margin-top: 72px;
  background: url(https://zuhaowan.zuhaowan.com/shanghu/www3.0/pages/fenxiao/index/default/img/tu.2d169f6.png)
    no-repeat;
}
.bg_w {
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(-180%, #000),
    color-stop(30%, #f2f2f2)
  );
  background: -webkit-linear-gradient(top, #000 -180%, #f2f2f2 30%);
  background: -moz-linear-gradient(top, #000 -180%, #f2f2f2 30%);
  background: linear-gradient(180deg, #000 -180%, #f2f2f2 30%);
}
.bg_w,
.bg_w1 {
  position: absolute;
}
.bg_w1 {
  top: 792px;
  left: 706px;
}
.bg_w2 {
  position: absolute;
  top: 1356px;
  width: 100%;
}
.help_s {
  overflow: hidden;
}
.advertising {
  position: fixed;
  top: 50%;
  left: 4%;
}

.centre_main_t_li_mt {
  display: flex;
  align-items: center;
  height: 54px;
  margin-right: 16px;
  margin-left: 20px;
  padding-top: -2px;
  float: left;
  width: 20px;
}
.centre_main_t_li_mts2 {
  margin-left: 22px;
}
.swiper_banner a,
.swiper_banner a img {
  display: block;
  width: 720px;
}
.swiper_banner .banner-prev {
  left: 0;
}
.swiper_banner .banner-next {
  right: 0;
}
.swiper_banner .banner-next,
.swiper_banner .banner-prev {
  display: block;
  position: absolute;
  width: 37px;
  height: 38px;
  border-radius: 3px;
  cursor: pointer;
  font: 20px 宋体;
  line-height: 38px;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
}
.swiper_banner .swiper-pagination-banner .swiper-pagination-bullet {
  background-color: #888a8d;
}
.swiper_banner .swiper-pagination-banner .swiper-pagination-bullet-active {
  background-color: #fff;
}
.swiper_banner .swiper-pagination-banner {
  bottom: 10px;
}
.contents {
  overflow: hidden;
  background-color: #f2f2f2;
  min-width: 1200px;
}
.centre_main_t {
  height: 378px;
  margin-top: 16px;
}
.centre_main_f {
  height: 134px;
  margin-top: 12px;
}
.centre_main_f,
.centre_main_t_l {
  background-color: #fff;
  -webkit-box-shadow: 0 0 12px 0 #e7e7e7;
  box-shadow: 0 0 12px 0 #e7e7e7;
  position: relative;
}
.centre_main_t_l {
  width: 190px;
  height: 378px;
  display: inline-block;
}
.centre_main_t_li {
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  color: #727272;
  cursor: pointer;
}
.centre_main_t_li .nav_item {
  display: inline-block;
  width: 100%;
}
.centre_main_t_lil {
  display: flex;
  align-items: center;
  height: 54px;
  margin-left: 0;
  margin-right: 20px;
}

.centre_main_t_pt {
  padding-left: 54px;
}
.centre_main_t_li:hover {
  background-color: #333;
  color: #fff;
}
.centre_main_t_li:hover .nav_item {
  color: #fff;
}
.centre_main_t_c {
  width: 720px;
  display: inline-block;
  margin: 0 10px;
}
.centre_main_t_c,
.centre_main_t_r {
  height: 100%;
  -webkit-box-shadow: 0 0 12px 0 #e7e7e7;
  box-shadow: 0 0 12px 0 #e7e7e7;
}
.centre_main_t_r {
  width: 270px;
  background-color: #fff;
  position: relative;
}
.centre_main_t_rs {
  padding: 12px 16px;
  overflow: hidden;
}
.centre_main_t_rtx {
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.centre_main_t_rtext {
  margin-left: 14px;
}
.centre_min_btn {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-top: 14px;
}
.centre_min_btn_l {
  margin-right: 24px;
  -webkit-box-shadow: 0 2px 10px 0 #e2e2e1;
  box-shadow: 0 2px 10px 0 #e2e2e1;
}
.centre_min_btn_l,
.centre_min_btn_r {
  width: 96px;
  height: 34px;
  text-align: center;
  line-height: 34px;
  border-radius: 50px;
  display: inline-block;
  cursor: pointer;
}
.centre_min_btn_r {
  background-color: #e6292c;
  color: #fff;
  -webkit-box-shadow: 0 2px 10px 0 #f8c4c5;
  box-shadow: 0 2px 10px 0 #f8c4c5;
}
.centre_main_t_rportrait {
  width: 64px;
  height: 64px;
  margin-left: 4px;
  margin-bottom: 6px;
}
.centre_main_t_rportrait img {
  width: 64px;
  height: 64px;
  border-radius: 100px;
  -webkit-box-shadow: 0 2px 10px 0 #e2e2e1;
  box-shadow: 0 2px 10px 0 #e2e2e1;
}
.centre_min_g {
  margin-top: 28px;
}
.centre_min_title_l {
  margin-left: 14px;
}
.centre_color1 {
  color: #8a8a8a;
}
.centre_min_gtit span {
  display: inline-block;
}
.centre_min_gtits {
  position: relative;
  cursor: pointer;
}
.bottom_line {
  font-weight: 600;
}
.bottom_line:after {
  content: '';
  width: 26px;
  height: 2px;
  background: #252525;
  position: absolute;
  bottom: -8px;
  left: 0;
}
.centre_min_gtit span {
  cursor: pointer;
}
.centre_min_gtext {
  margin-top: 24px;
  min-height: 116px;
}
.centre_cour {
  height: 19px;
  color: #888;
  cursor: pointer;
  margin-bottom: 10px;
}
.centre_cour:hover {
  color: #e6292c;
}
.centre_ben {
  text-align: center;
  color: #7c7c7c;
  margin-top: 10px;
}
.centre_text_p {
  width: 148px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.centre_color2 {
  color: #4a4a4a;
  margin-top: 8px;
}
.centre_min_btnsh {
  width: 236px;
  height: 38px;
  text-align: center;
  line-height: 40px;
  color: #fff;
  background-color: #e6292c;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
}
.centre_min_btnsh:hover {
  -webkit-box-shadow: 0 2px 10px 0 #f8c4c5;
  box-shadow: 0 2px 10px 0 #f8c4c5;
}
.centre_min_btnsh a {
  color: #fff;
}
.centre_main_fp {
  padding: 28px 40px;
}
.centre_main_fs {
  height: auto;
  width: 100%;
  display: table;
}
.centre_main_img {
  width: 74px;
  height: 74px;
  margin-left: 20px;
}
.centre_main_img img {
  width: 88px;
  height: 88px;
}
.centre_main_f_tr {
  width: 114px;
  float: left;
  margin-left: 30px;
}
.font_1 {
  font-size: 16px;
  font-weight: 600;
}
.font_2 {
  margin-top: 14px;
  font-size: 14px;
  color: #535353;
}
.centre_main_f_li {
  display: table-cell;
}
.font_s {
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 12px;
}
.color_2 {
  color: #888;
}
.popular_game_t {
  height: 196px;
  position: relative;
}
.popular_game_textbg {
  background: url(https://zuhaowan.zuhaowan.com/shanghu/www3.0/pages/fenxiao/index/default/img/HOTGAMES.5270839.png)
    no-repeat 340px 56px;
}
.popular_game_text {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.t_l {
  text-align: center;
}
.popular_game_carousel {
  height: 390px;
}
.popular_game_carousel1 {
  height: 440px;
}
.popular_game_bz {
  height: 180px;
  margin-bottom: 98px;
}
.swiper-button2 {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAABS2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxNDIgNzkuMTYwOTI0LCAyMDE3LzA3LzEzLTAxOjA2OjM5ICAgICAgICAiPgogPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIi8+CiA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo8P3hwYWNrZXQgZW5kPSJyIj8+nhxg7wAAANRJREFUSInt1rGOgkAQgOEfoUJCcdaYQCPK+z8MW627aL1EvUICe8UlxpIlGQvD30++zFQTGWM8HygBKIpCFLHWshEV3lqhL4XGceSsddBMsgRSbcs0TUEzwRsppfDeUx+PcpBqW4bnk0NdE8dxEDTrdN57tNYMw7AImQ1ZY/h9PDg1DVEUBSMw83Q/ux0A18tlETIbyrKMsqpwztFZKwcBpGlKWVX0fU/XdXLQCytLeucwxshBAOl2S7Hfc7/dZCGAPM85NY08tKQVWqFXCfz/XdL9AdMYSKTXdPGRAAAAAElFTkSuQmCC);
  background-size: 100%;
}
.swiper-button1,
.swiper-container-rtl .swiper-button2 {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAABS2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxNDIgNzkuMTYwOTI0LCAyMDE3LzA3LzEzLTAxOjA2OjM5ICAgICAgICAiPgogPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIi8+CiA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo8P3hwYWNrZXQgZW5kPSJyIj8+nhxg7wAAAOBJREFUSInt1T0OgkAQBeDRcANtuQFeYbN7AzyD0QMYLf0N0hlKUWr1GKKVGo1YUNENB3kWNpYsyVoQXr2bb7PzkmkwM+gPsYiIbNs2iuR5Tk2jwk9qqGLQeDSk42FvHmq12hRFO4rjk77EzNDJNtxASYHb9Vr4DjNDGwKAIFhDSYH7/WYWAgB/5UFJgfc7MQsBwKDfg5KiEFS63s/Hg7IsI9ftmikDACTJC0oK+L5X6Hypr0vTVAspBV0uZygpsFzMtR6nPaPdNiTH6dBkOtOeaYOZUS++GqomZBF962c6Hx0d1IpXAvIbAAAAAElFTkSuQmCC);
  background-size: 100%;
}
.swiper-button2,
.swiper-container-rtl .swiper-button1 {
  left: 1140px;
  right: auto;
}
.swiper-pagination-bullet-active {
  opacity: 1;
  background: #515151;
}
.swiper-button1,
.swiper-button2 {
  top: 99%;
  width: 24px;
  height: 24px;
  right: 0;
}
.hot_swiper,
.special_swiper {
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  height: 100%;
  z-index: 1;
}
.swiper-slide2 {
  background-position: 50%;
  background-size: cover;
  width: 820px;
  height: 380px;
}
.swiper-slide2 img {
  height: 100%;
  width: 100%;
  -webkit-box-shadow: 2px 0 20px 0 #1d1009;
  box-shadow: 2px 0 20px 0 #1d1009;
}
.swiper-button_s2 {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAOCAYAAAASVl2WAAAAqklEQVQYlW2QoQ7CQBBEpw2GhGD4Cz4AFAkYBA6BRPMlFfwBGoHgJ1AIPAZVg9wGtw1qMNNkueslJ+7m7ezsFiTRd5oWFYABSGbXnAdz0pwsUoemxR7AWc9ZWrntKs05J4koroO47P47cRHETXSFOXcSvuZcpYFLAFMF+kyGuGXzqkUll7c5R38tQsijoFeE0jFPgp7mLDNA0EXQvRcQdBVUZ6sOK38AGP8AAPkXB1dvDlwAAAAASUVORK5CYII=)
    no-repeat;
  background-color: rgba(0, 0, 0, 0.5);
  background-position: 15px 15px;
  left: 190px;
}
.swiper-button_s1,
.swiper-button_s2 {
  width: 42px;
  height: 42px;
  opacity: 1;
  top: 180px;
}
.swiper-button_s1 {
  display: block;
  position: absolute;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAOCAYAAAASVl2WAAAAsElEQVQYlXXQoQoCURCF4X8XDBvFIBaDPoTgA9mMdo2+gOwTmIwiWqxGiyBsFDGNxTCCIscyQXbXgQmX88GdGcw1MtdCEnWNuU7mkrnyf6BjriLQvAIkYa6muW6BphUQqG2uR6BJBQTqmesVaCyJRBK/dX/SBfZAH5illKqVcQGu8RxW1jLXNr7Y1M2wjnBXt8UywkPdHfIIj+ZKypdcRXg2V6M8Uwp8gAIYtDLe5a2+MhAoF7XwOnYAAAAASUVORK5CYII=)
    no-repeat;
  background-position: 15px 15px;
  background-color: rgba(0, 0, 0, 0.5);
  font-size: 20px;
  font-family: 寰蒋闆呴粦;
  color: hsla(0, 0%, 100%, 0.5);
  cursor: pointer;
  z-index: 100;
  right: 190px;
  color: #fff;
}
.swiper-button_text {
  line-height: 40px;
  margin-left: 16px;
  font-size: 16px;
}
.popular_bz_font {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.popular_game_bzs {
  display: table;
  width: 100%;
}
.popular_bz_mt {
  margin-top: 24px;
  font-weight: 600;
}
.popular_game_bzli {
  display: table-cell;
  color: #666;
  cursor: pointer;
  position: relative;
}
.popular_game_bzli:hover {
  color: #313131;
}
.popular_game_bzli:hover .popular_bz_line {
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  transition: all 0.5s linear;
  background-color: #e6292c;
}
.popular_bz_line {
  height: 1px;
  width: 206px;
  display: inline-block;
  background-color: #d6d6d6;
}
.popular_bz_line.line {
  left: 0;
  width: 0;
  height: 1px;
  background-color: #e6292c;
  -webkit-transition: width 0.5s linear;
  -moz-transition: width 0.5s linear;
  transition: width 0.5s linear;
  position: absolute;
  top: 31px;
}
.popular_bz_fontp {
  width: 236px;
  margin-top: 20px;
  line-height: 20px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  overflow: hidden;
  height: 100px;
}
.popular_game_li {
  width: 284px;
  height: 336px;
  display: inline-block;
  margin-right: 17px;
  position: relative;
  cursor: pointer;
  -webkit-box-shadow: 0 0 12px 0 #e7e7e7;
  box-shadow: 0 0 12px 0 #e7e7e7;
  background-color: #fff;
}
.popular_game_li .popular_game_li_zg {
  -webkit-transition: opacity 0.2s linear;
  -moz-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
  opacity: 0;
}
.popular_game_li:hover .popular_game_li_zg {
  opacity: 1;
}
.popular_game_li:nth-child(4) {
  margin-right: 0;
}
.popular_game_li img {
  width: 100%;
  height: 216px;
}
.popular_game_lits {
  padding: 24px 20px;
  display: block;
}
.popular_game_col {
  font-size: 16px;
  margin-right: 14px;
  font-weight: 600;
  float: left;
}
.popular_game_col1 {
  height: 20px;
  line-height: 20px;
  text-align: center;
  display: inline-block;
  border: 1px solid red;
  border-radius: 4px;
  background-color: #fdeded;
  color: red;
  padding: 0 8px;
}
.popular_game_li_zg a:hover {
  color: #000;
}
.popular_game_col2 {
  margin-top: 20px;
  color: #929292;
}
.popular_game_li_zg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-image: -webkit-gradient(
    linear,
    left bottom,
    left top,
    color-stop(35%, #fff),
    to(hsla(0, 0%, 100%, 0.8))
  );
  background-image: -webkit-linear-gradient(
    bottom,
    #fff 35%,
    hsla(0, 0%, 100%, 0.8)
  );
  background-image: -moz-linear-gradient(
    bottom,
    #fff 35%,
    hsla(0, 0%, 100%, 0.8) 100%
  );
  background-image: linear-gradient(0deg, #fff 35%, hsla(0, 0%, 100%, 0.8));
}
.popular_game_li_zg1 {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  margin-top: 86px;
}
.popular_game_col2s {
  margin-top: 20px;
  color: #6c6b6b;
  padding: 0 20px;
}
.popular_game_li_btns {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.popular_game_li_btn {
  width: 182px;
  height: 46px;
  line-height: 46px;
  text-align: center;
  color: #fff;
  background-color: #e6292c;
  display: inline-block;
  border-radius: 50px;
  font-size: 16px;
  margin: 54px auto auto;
  cursor: pointer;
  -webkit-box-shadow: 0 2px 10px 0 #f8c4c5;
  box-shadow: 0 2px 10px 0 #f8c4c5;
}
.popular_game_li_btn a {
  color: #fff;
}
.popular_game_bzli:hover .line {
  width: 206px;
}
.gtext_hide {
  display: none;
}
.gtext_show {
  display: block;
}
.game_list {
  margin-top: 72px;
}
.game_list_content {
  display: flex;
  flex-wrap: wrap;
  width: 1200px;
  margin: 0 auto;
}
.game_list_content .game_list_item {
  box-sizing: border-box;
  width: 30%;
  padding: 5px;
  border-radius: 5px;
  margin: 10px 20px;
  background-color: #fff;
  border: 1px solid #ddd;
}
.game_list_content .game_list_item .title {
  cursor: pointer;
}
.game_list_content h4 {
  margin: 0 auto;
  margin-top: 20px;
  width: 90%;
  font-size: 18px;
  white-space: nowrap; /* 超出的空白区域不换行 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 文本超出显示省略号 */
}
.game_list_content img {
  width: 100%;
}
.game_list_content .icon {
  width: 25px;
  height: 25px;
  margin-top: 8px;
}
.game_list_content .game_list_item .jg_item {
  display: flex;
  justify-content: space-between;
  border: 0px solid #ccc;
  clear: left;
  height: 35px;
  line-height: 35px;
  padding: 0 10px;
  padding-right: 20px;
  margin-top: 10px;
  text-overflow: ellipsis;
}
.game_list_content .line_through {
  text-decoration: line-through !important;
}
.game_list_content .game_list_item .jg div {
  box-sizing: border-box;
  padding: 0 10px;
  cursor: pointer;
  border-radius: 5px;
}
.game_list_content .game_list_item .jg {
  padding-right: 10px;
}
.game_list_content .game_list_item .jg div:hover {
  background-color: #d4d4d4;
}
.game_list_content .game_list_item .jg span {
  color: goldenrod !important;
}
.game_list_item span {
  color: red;
  font-size: 14px !important;
}
.game_list_item i {
  display: inline-block;
  color: #fff;
  width: 8px;
  height: 8px;
  margin-bottom: 5px;
  margin-right: 5px;
  background-color: rgba(242, 13, 13, 0.6);
}
