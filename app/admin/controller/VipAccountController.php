<?php
namespace app\admin\controller;

use app\BaseController;
use app\model\VipAccount;
use think\facade\Request;
use think\facade\Log;
use think\facade\View;

class VipAccountController extends BaseController
{
    /**
     * 渲染VIP账号管理页面
     *
     * @return \think\Response
     */
    public function index()
    {
        return view('vip_account/index');
    }

    /**
     * 获取VIP账号列表
     *
     * @return \think\Response
     */
    public function getVipAccounts()
    {
        $page = Request::get('page', 1);
        $limit = Request::get('limit', 10);

        try {
            $total = VipAccount::count();
            $data = VipAccount::page($page, $limit)
                ->order('id', 'desc')
                ->select()
                ->toArray();

            return json([
                'code'  => 0,
                'msg'   => '',
                'count' => $total,
                'data'  => $data,
            ]);
        } catch (\Exception $e) {
            Log::error('获取VIP账号列表失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '获取VIP账号列表失败'], 500);
        }
    }

    /**
     * 添加新VIP账号
     *
     * @return \think\Response
     */
    public function addVipAccount()
    {
        if (Request::isPost()) {
            $data = Request::post();

            // 数据验证
            $validate = \think\facade\Validate::rule([
                'username' => 'require|max:100',
                'password' => 'require|max:255',
                'status'   => 'require|in:0,1',
                'user_id'  => 'integer|allowEmpty', // 修改此行
            ]);

            if (!$validate->check($data)) {
                return json(['code' => 1, 'msg' => $validate->getError()], 400);
            }

            try {
                $vipAccount = VipAccount::create([
                    'username'   => $data['username'],
                    'password'   => $data['password'],
                    'status'     => $data['status'],
                    'user_id'    => !empty($data['user_id']) ? intval($data['user_id']) : null, // 确保为空时为null
                ]);

                return json(['code' => 0, 'msg' => 'VIP账号添加成功']);
            } catch (\Exception $e) {
                Log::error('添加VIP账号失败: ' . $e->getMessage());
                return json(['code' => 1, 'msg' => '添加VIP账号失败'], 500);
            }
        }

        return view('vip_account/add');
    }

    /**
     * 编辑VIP账号
     *
     * @return \think\Response
     */
    public function editVipAccount()
    {
        if (Request::isPost()) {
            $data = Request::post();

            // 数据验证
            $validate = \think\facade\Validate::rule([
                'id'       => 'require|integer',
                'username' => 'require|max:100',
                'password' => 'max:255',
                'status'   => 'require|in:0,1',
                //'user_id'  => 'integer|allowEmpty', // 修改此行
            ]);

            if (!$validate->check($data)) {
                return json(['code' => 1, 'msg' => $validate->getError()], 400);
            }

            try {
                $vipAccount = VipAccount::find($data['id']);
                if (!$vipAccount) {
                    return json(['code' => 1, 'msg' => 'VIP账号不存在'], 404);
                }

                $updateData = [
                    'username' => $data['username'],
                    'status'   => $data['status'],
                    'user_id'  => !empty($data['user_id']) ? intval($data['user_id']) : null, // 确保为空时为null
                ];

                if (!empty($data['password'])) {
                    $updateData['password'] = $data['password'];
                }

                // 使用 save() 方法保存更新
                $vipAccount->save($updateData);

                return json(['code' => 0, 'msg' => 'VIP账号更新成功']);
            } catch (\Exception $e) {
                Log::error('编辑VIP账号失败: ' . $e->getMessage());
                return json(['code' => 1, 'msg' => '编辑VIP账号失败'], 500);
            }
        }

        // GET 请求，渲染编辑页面
        $id = Request::get('id');
        if (!$id) {
            return json(['code' => 1, 'msg' => '缺少VIP账号ID'], 400);
        }

        try {
            $vipAccount = VipAccount::find($id);
            if (!$vipAccount) {
                return json(['code' => 1, 'msg' => 'VIP账号不存在'], 404);
            }

            return view('vip_account/edit', ['vipAccount' => $vipAccount]);
        } catch (\Exception $e) {
            Log::error('获取VIP账号失败: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '获取VIP账号失败'], 500);
        }
    }

    /**
     * 删除VIP账号
     *
     * @return \think\Response
     */
    public function delVipAccount()
    {
        if (Request::isPost()) {
            $ids = Request::post('id/a'); // 获取数组格式的id

            if (empty($ids)) {
                return json(['code' => 1, 'msg' => '未选择要删除的VIP账号'], 400);
            }

            try {
                VipAccount::destroy($ids);
                return json(['code' => 0, 'msg' => 'VIP账号删除成功']);
            } catch (\Exception $e) {
                Log::error('删除VIP账号失败: ' . $e->getMessage());
                return json(['code' => 1, 'msg' => '删除VIP账号失败'], 500);
            }
        }

        return json(['code' => 1, 'msg' => '非法请求'], 405);
    }
}
