<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{$system.sy_title}</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="keywords" content="{$system.sy_title},租号,租号玩,游戏租号,租号平台,租号网,吃鸡租号,CF租号,DNF租号,LOL租号,逆战租号" />
<meta name="description" content="{$system.sy_title}是国内专业的游戏帐号出租、租用平台，玩家无需帐号密码即可畅玩游戏极品账号，同时租号软件还有防外挂等安全防护功能，能从根源上保证游戏帐号安全，为游戏玩家打造一个安全、方便、快捷的游戏账号租赁交易平台。" />
<link rel="icon" href="" type="image/x-icon" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
<meta name="renderer" content="webkit" />
<!-- 禁止百度转码  -->
<meta http-equiv="Cache-Control" content="no-transform" />

<script>
  window.GLOBAL = {
    path: "{$system.sy_url}/shangv1/default_v2",
  };
</script>
<style>
  .alert_box_box {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    top: 0;
    z-index: 999;
    backdrop-filter: blur(5px);
  }

  .alert_box {
    display: none;
    width: 480px;
    position: fixed;
    background: rgba(28, 28, 32, 0.95);
    backdrop-filter: blur(20px);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .alert_box_top {
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    font-size: 15px;
    font-weight: 500;
    color: #fff;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  }

  .alert_box_main {
    padding: 24px;
    display: flex;
    gap: 20px;
    background: rgba(255, 255, 255, 0.02);
  }

  .alert_box_img {
    width: 140px;
    height: 140px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s;
  }

  .alert_box_img:hover {
    transform: translateY(-2px);
  }

  .alert_box_title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    background: linear-gradient(90deg, #fff, #ffffffcc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .alert_box_money {
    font-size: 28px;
    color: #ff4757;
    font-weight: 700;
    margin: 12px 0;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .alert_box_money .zh {
    font-size: 16px;
    color: #2ecc71;
    font-weight: 500;
  }

  .alert_box_sc {
    padding: 20px;
    background: rgba(255,255,255,0.02);
    border-top: 1px solid rgba(255,255,255,0.05);
  }

  .alert_box_sc_duration {
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .alert_box_sc_duration label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }

  .alert_box_sc_duration input[type="number"] {
    width: 80px;
    padding: 8px 12px;
    border: 1px solid rgba(255,255,255,0.1);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    color: #fff;
    font-size: 14px;
  }

  .alert_box_yhj {
    padding: 16px 20px;
    display: flex;
    gap: 12px;
    background: rgba(255,255,255,0.02);
    border-top: 1px solid rgba(255,255,255,0.05);
  }

  .alert_box_yhj input[type="text"] {
    flex: 1;
    padding: 14px 20px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 15px;
    transition: all 0.2s;
  }

  .alert_box_yhj input[type="text"]:focus {
    border-color: rgba(26, 137, 250, 0.5);
    box-shadow: 0 0 0 3px rgba(26, 137, 250, 0.15);
    outline: none;
  }

  .alert_box_yhj input[type="button"] {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    background: #1a89fa;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .alert_box_yhj input[type="button"]:hover {
    background: #0d7fe7;
  }

  .alert_box_fk {
    padding: 20px;
    background: rgba(255,255,255,0.02);
  }

  .alert_box_fk input {
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 14px;
    background: linear-gradient(135deg, #1a89fa, #0d7fe7);
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 4px 12px rgba(26, 137, 250, 0.2);
  }

  .alert_box_fk input:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(26, 137, 250, 0.3);
    background: linear-gradient(135deg, #0d7fe7, #0066cc);
  }

  .alert_box_fk input:active {
    transform: translateY(1px);
  }

  .yhj_list {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background: linear-gradient(145deg, #1a1a1e, #24242a);
    border-radius: 16px;
    z-index: 1001;
    box-shadow: 0 10px 30px rgba(0,0,0,0.25);
  }

  .yhj_box {
    padding: 20px;
    max-height: calc(80vh - 40px);
    overflow-y: auto;
  }

  .yhj_box_ul li {
    margin-bottom: 16px;
    padding: 16px;
    background: rgba(255,255,255,0.05);
    border-radius: 12px;
  }

  @media (max-width: 480px) {
    .alert_box {
      width: 95%;
    }

    .alert_box_main {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .alert_box_sc_duration {
      justify-content: center;
    }

    .alert_box_yhj {
      flex-direction: column;
    }

    .alert_box_img {
      width: 120px;
      height: 120px;
    }
  }

  .coupons_check_butten {
    display: inline-block;
    padding: 10px 24px;
    background: linear-gradient(135deg, #1a89fa, #0d7fe7);
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    border: none;
    box-shadow: 0 4px 12px rgba(26,137,250,0.2);
  }

  .coupons_check_butten:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(26,137,250,0.3);
    background: linear-gradient(135deg, #0d7fe7, #0066cc);
  }

  .coupons_check_butten:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(26,137,250,0.2);
  }

  .duration-input-group {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 4px;
    display: flex;
    align-items: center;
    width: fit-content;
  }

  .duration-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.08);
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    border-radius: 10px;
    transition: all 0.2s;
  }

  .duration-btn:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-1px);
  }

  .duration-btn:active {
    transform: scale(0.95);
  }

  #durationInput {
    width: 80px;
    text-align: center;
    border: none;
    background: transparent;
    color: #fff;
    font-size: 18px;
    padding: 8px;
    margin: 0 12px;
  }

  .duration-unit-group {
    display: flex;
    gap: 16px;
    margin-left: 20px;
  }

  .duration-unit-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }

  .duration-unit-group label span {
    color: #fff;
    font-size: 14px;
  }

  .duration-unit-group input[type="radio"] {
    width: 16px;
    height: 16px;
    accent-color: #1a89fa;
  }

  .no-price-notice {
    color: #ff4757;
    font-size: 14px;
    margin-top: 10px;
    text-align: center;
    padding: 10px;
    background: rgba(255,71,87,0.1);
    border-radius: 8px;
  }

  .game_list_item {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 20px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .game_list_item:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.05);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }

  .game_list_item:active {
    transform: translateY(-2px);
  }

  .duration-toggle {
    display: flex;
    gap: 8px;
    margin-left: 20px;
  }

  .toggle-btn {
    padding: 8px 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .toggle-btn.active {
    background: #1a89fa;
    color: #fff;
    border-color: #1a89fa;
    box-shadow: 0 2px 8px rgba(26, 137, 250, 0.2);
  }

  .toggle-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .offline-btn {
    background: linear-gradient(135deg, #ff4757, #ff6b81) !important;
    color: white !important;
    border: none !important;
  }

  .offline-btn:hover {
    background: linear-gradient(135deg, #ff6b81, #ff4757) !important;
    transform: translateY(-1px);
  }

  .offline-btn:active {
    transform: translateY(1px);
  }

  /* 1. 更新顶部导航样式 */
  .header {
    background: rgba(28, 28, 32, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .header_r li {
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.2s;
  }

  .header_r li:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  /* 2. 搜索框样式优化
  .game_search input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 12px 20px;
    transition: all 0.2s;
  }

  .game_search input:focus {
    border-color: #1a89fa;
    box-shadow: 0 0 0 3px rgba(26, 137, 250, 0.15);
  } */

  /* 3. 游戏列表卡片样式 */
  .game_list_item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 20px;
    transition: all 0.3s;
  }

  .game_list_item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }

  /* 4. 按钮样式统一 */
  .btn-primary {
    background: linear-gradient(135deg, #1a89fa, #0d7fe7);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    color: #fff;
    font-weight: 600;
    transition: all 0.2s;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(26, 137, 250, 0.3);
  }

  /* 5. 底部样式优化 */
  .footer {
    margin-top: 60px;
    background: rgba(28, 28, 32, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 40px 0;
  }

  .button-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .btn-primary {
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 14px;
    background: linear-gradient(135deg, #1a89fa, #0d7fe7);
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 4px 12px rgba(26, 137, 250, 0.2);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(26, 137, 250, 0.3);
    background: linear-gradient(135deg, #0d7fe7, #0066cc);
  }

  .btn-primary:active {
    transform: translateY(1px);
  }

  .btn-offline {
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 14px;
    background: linear-gradient(135deg, #ff4757, #ff6b81);
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.2);
  }

  .btn-offline:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 71, 87, 0.3);
    background: linear-gradient(135deg, #ff6b81, #ff4757);
  }

  .btn-offline:active {
    transform: translateY(1px);
  }

  /* 修改标签筛选样式 */
  .filter-tags {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    margin: 20px auto;
    max-width: 1200px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  .filter-section {
    display: flex;
    align-items: center;
  }

  .filter-label {
    color: #333;
    font-size: 15px;
    font-weight: 500;
    margin-right: 20px;
  }

  .filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .filter-tag {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    background: #f5f7fa;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid #e4e7ed;
  }

  .filter-tag:hover {
    background: #ecf5ff;
    color: #409eff;
  }

  .filter-tag.active {
    background: #409eff;
    color: #fff;
    border-color: #409eff;
  }

  .filter-tag.not-limit {
    background: #fff;
    border: 1px solid #dcdfe6;
  }

  .filter-tag.not-limit.active {
    background: #409eff;
    color: #fff;
    border-color: #409eff;
  }

  /* 内容列表样式 */
  .content-sel-result {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  .content-sel-result-tr {
    transition: all 0.3s;
    border-bottom: 1px solid #f0f2f5;
  }

  .content-sel-result-tr:hover {
    background: #f5f7fa;
  }

  .content-sel-result-td {
    padding: 20px;
  }

  .content-sel-result-td img {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
  }

  .content-sel-result-td img:hover {
    transform: translateY(-2px);
  }

  .content-sel-result-td-second-h {
    color: #303133;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s;
  }

  .content-sel-result-td-second-h:hover {
    color: #409eff;
  }

  .content-sel-result-td-second-tags {
    margin-top: 12px;
    display: flex;
    gap: 8px;
  }

  .tag-item {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .tag-item:nth-child(1) {
    background: #fff0f5;
    color: #ff69b4;
  }

  .tag-item:nth-child(2) {
    background: #f0f8ff;
    color: #1e90ff;
  }

  .content-sel-result-td-fourth-order {
    padding: 10px 24px;
    border-radius: 8px;
    background: linear-gradient(135deg, #409eff, #1890ff);
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
  }

  .content-sel-result-td-fourth-order:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }

  @media (max-width: 768px) {
    .filter-tags {
      margin: 15px;
      padding: 15px;
    }

    .filter-section {
      flex-direction: column;
      align-items: flex-start;
    }

    .filter-label {
      margin-bottom: 10px;
    }

    .content-sel-result-tr {
      display: flex;
      flex-direction: column;
    }

    .content-sel-result-td {
      width: 100%;
      text-align: center;
    }

    .content-sel-result-td img {
      width: 100%;
      max-width: 280px;
      height: auto;
    }
  }

  /* 导航栏样式更新 */
  .navigation {
    background: rgba(28, 28, 32, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }

  /* 内容区域宽度统一 */
  .content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .content-sel-result {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    margin-top: 20px;
  }

  /* 响应式处理 */
  @media (max-width: 768px) {
    .content {
      padding: 0 15px;
    }
  }

  /* 修复 content-sel-result-tr 的布局 */
  .content-sel-result table {
    width: 100%;
  }

  .content-sel-result-tr {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .content-sel-result-td:first-child {
    width: 320px;
    flex-shrink: 0;
  }

  .content-sel-result-td-second {
    flex: 1;
    padding: 0 20px;
  }

  .content-sel-result-td-fourth {
    width: 120px;
    flex-shrink: 0;
  }

  @media (max-width: 768px) {
    .content-sel-result-tr {
      flex-direction: column;
      gap: 16px;
    }

    .content-sel-result-td:first-child,
    .content-sel-result-td-second,
    .content-sel-result-td-fourth {
      width: 100%;
      padding: 0;
      text-align: center;
    }
  }
</style>


<!-- <script src="http://zuhaowan.zuhaowan.com/static/www/da.js/da.js"></script>
<script src="http://zuhaowan.zuhaowan.com/static/www/da.js/da-web-fxIndexPvUv.js"></script>

    <link rel="stylesheet" href="http://zuhaowan.zuhaowan.com/shanghu/www3.0/common/css/core.css?t=20240328" />
    <link rel="stylesheet" href="http://zuhaowan.zuhaowan.com/shanghu/www3.0/common/css/common.css?t=20240328" />
    <link rel="stylesheet" href="http://cdn.shifen.host/zuhao/css/public.css" />
    <link rel="stylesheet" href="http://zuhaowan.zuhaowan.com/shanghu/www3.0/pages/fenxiao/search/default/css/index.css?t=20240328" /> -->

    <script>
        window.isFX = true;
    </script>
     <link rel="stylesheet" href="/static/core.css">
     <link rel="stylesheet" href="/static/common.css">
     <link rel="stylesheet" href="/static/public.css">
     <link rel="stylesheet" href="/static/default/core.css">
     <link rel="stylesheet" href="/static/default/index.css">
     <script src="/static/core.js"></script>
     <script src="/static/common.js"></script>
     <script src="/static/public.js" defer></script>
     <script src="/static/default/core.js" defer></script>
     <script src="/static/default/index.js" defer></script>
<body>
<!-- 模板 默认 -->
  <!-- 公用顶部 -->
  <div class="header">
    <div class="container lh">
      <div class="header_l fl">
        <span class="fl slogan">
          欢迎来到{$system.sy_title} </span>
        <input type="hidden" value="0">
        <span class="ml fl">
          <a href="/user/login">
            <span class="ml_1">
              登录
            </span>
          </a>
        </span>
      </div>
      <div class="header_r fr">
        <ul class="clearfix">
          <a href="/">
            <li class="fl header_r_bk">
              首页
            </li>
          </a>
          <!-- <a href="/Wap/index.html">
            <li class="fl header_r_bk">
              手机版
            </li> -->
          </a>
        </ul>
      </div>
    </div>
  </div>

<!-- logo、搜索 -->
<div class="search_com_box">
  <div class="win_top w1200">
      <a class="logo_box" href="/">
          <img src="{$system.sy_logo}" style="width: 110px; height:90px">
      </a>
      <div class="win_top w1200">
          <div class="game_search">
              <div class="game_choix">
                  <div class="choix">
                      <form name="search" action="/home/<USER>/search" method="post">
                          <input id="keyWords" type="text" name="keyWords" value="" autofocus="" placeholder="输入关键词为您查找"
                                 class="fl">
                          <a href="javascript:void(0);" id="search">搜索</a>
                      </form>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>

<!--导航-->
<div class="navigation">
  <div class="container navigation_m">
    <ul class="navigation_nav_s" id="nav">
      <li class="navigation_nav active" data-tag="index">
      </li>
      <li class="navigation_nav" data-tag="zuhao">
        <a href="./" style="text-align: center">首页</a>
      </li>
      <li class="navigation_nav" data-tag="cf">
        <a href="/user/index/feedback" style="text-align: center">工单</a>
      </li>
      <li class="navigation_nav" data-tag="lol">
        <a href="/user/CdkExchange/index" style="text-align: center">CDK兑换</a>
      </li>
      <li class="navigation_nav" data-tag="zs">
        <a href="/user/index/notice" style="text-align: center">帮助</a>
      </li>
      <li class="navigation_nav" data-tag="all">
        <a href="./" style="text-align: center">全部游戏</a>
      </li>
      <li class="navigation_nav" data-tag="help">
        <a href="/user/index/onshop" style="text-align: center">用户中心</a>
      </li>
    </ul>
  </div>
</div>
<!--导航END-->

<!-- 修改标签选择组件部分 -->
<div class="filter-tags">
  <div class="filter-section">
    <span class="filter-label">标签:</span>
    <div class="filter-options">
      <span class="filter-tag not-limit active">不限</span>
      {volist name="gameTags" id="tag"}
        <span class="filter-tag" data-id="{$tag.id}">{$tag.name}</span>
      {/volist}
    </div>
  </div>
</div>

<!--渐变蒙层-->
<div class="linear-gradient-mask"></div>

<div class="content">
    <div class="content-sel-result">
        <table>
            <tbody>
                {volist name="goods" id="item"}
                <tr class="content-sel-result-tr">
                    <td class="content-sel-result-td">
                        <img style="width: 320px;height: 140px" src="{$item.goods_img}" alt="{$item.goods_name}">
                    </td>
                    <td class="content-sel-result-td content-sel-result-td-second">
                        <a class="content-sel-result-td-second-h" href="/home/<USER>/goodsDetail?id={$item.id}" title="{$item.goods_name}">
                            {$item.goods_name}
                        </a>
                        <div class="content-sel-result-td-second-tags">
                            <span class="tag-item">热门</span>
                            <span class="tag-item">推荐</span>
                        </div>
                    </td>
                    <td class="content-sel-result-td content-sel-result-td-fourth">
                        <div class="content-sel-result-td-fourth-order" onclick="window.location.href='/home/<USER>/goodsDetail?id={$item.id}'">
                            立即租赁
                        </div>
                    </td>
                </tr>
                {/volist}
            </tbody>
        </table>
    </div>
</div>
<div>
    <!-- 系统选择 -->
    <div>
    </div>
    <div></div>
    </div>
    <!-- 公共底部 -->
    <div class="footer">
    <div class="footer_zx">
        <div class="container">
            <div class="footer_ts">
                <ul class="footer_center">
                    <li class="footer_center_li">
                        <img src="http://zuhaowan.zuhaowan.com/shangv1/default_v2/image/iocn2.png"/>
                        <p class="footer_center_mt"><span>快捷交易</span><span>省心上号</span></p>
                    </li>
                    <li class="footer_center_li footer_center_limt">
                        <img src="http://zuhaowan.zuhaowan.com/shangv1/default_v2/image/iocn3.png"/>
                        <p class="footer_center_mt"><span>7x24小时</span><span> 专属客服</span></p>
                    </li>
                    <li class="footer_center_li">
                        <img src="http://zuhaowan.zuhaowan.com/shangv1/default_v2/image/iocn1.png"/>
                        <p class="footer_center_mt"><span>权威认证</span><span>安全可靠</span></p>
                    </li>
                </ul>
            </div>
            <div class="footer_center_lis">
                <ul>
                    <li><a href="/Index/news/id/520.html">免责声明</a></li>
                    <span class="ma_lrs">|</span>
                    <li><a href="/Index/news/id/526.html">隐私保密</a></li>
                    <span class="ma_lrs">|</span>
                    <li><a href="/Index/news/id/528.html">租号安全</a></li>
                    <span class="ma_lrs">|</span>
                    <li><a href="/Index/news/id/524.html">投诉帮助</a></li>
                                        <span class="ma_lrs">|</span>
                    <li><a href="/about/">关于我们</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="foot">
        <a target="_blank" href="http://beian.miit.gov.cn"></a>
    </div>
    <div class="bz_s1 container">
        <ul>
                    </ul>
    </div>
</div>
</div>

<!-- 添加新的简化版JavaScript -->
<script>
$(document).ready(function() {
  // 标签点击处理
  $('.filter-tag').click(function() {
    const $this = $(this);

    if($this.hasClass('not-limit')) {
      $('.filter-tag').removeClass('active');
      $this.addClass('active');
    } else {
      $('.filter-tag.not-limit').removeClass('active');
      $this.toggleClass('active');
    }

    const selectedTags = $('.filter-tag.active:not(.not-limit)').map(function() {
      return $(this).data('id');
    }).get();

    filterByTags(selectedTags);
  });
});

function filterByTags(tagIds) {
  $.ajax({
    url: '/home/<USER>/filterByTags',
    type: 'POST',
    data: { tagIds: tagIds },
    success: function(response) {
      if(response.success) {
        updateGoodsList(response.data);
      }
    },
    error: function() {
      createAlert("red", "筛选失败，请重试", 0);
    }
  });
}

function updateGoodsList(goods) {
  const $tbody = $('.content-sel-result tbody');
  $tbody.empty();

  goods.forEach(item => {
    $tbody.append(`
      <tr class="content-sel-result-tr">
        <td class="content-sel-result-td">
          <img style="width: 320px;height: 140px" src="${item.goods_img}" alt="${item.goods_name}">
        </td>
        <td class="content-sel-result-td content-sel-result-td-second">
          <a class="content-sel-result-td-second-h" href="/home/<USER>/goodsDetail?id=${item.id}" title="${item.goods_name}">
            ${item.goods_name}
          </a>
          <div class="content-sel-result-td-second-tags">
            <span class="tag-item">热门</span>
            <span class="tag-item">推荐</span>
          </div>
        </td>
        <td class="content-sel-result-td content-sel-result-td-fourth">
          <div class="content-sel-result-td-fourth-order" onclick="window.location.href='/home/<USER>/goodsDetail?id=${item.id}'">
            立即租赁
          </div>
        </td>
      </tr>
    `);
  });
}
</script>
  </body>

  </html>