<!DOCTYPE html>
<html>
<head>
    <title>会员离线账号名称显示测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input { padding: 5px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; width: 200px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 120px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .issue-yes { color: #dc3545; font-weight: bold; }
        .issue-no { color: #28a745; font-weight: bold; }
        .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 3px; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>会员离线账号名称显示测试</h1>
        
        <div class="test-section">
            <h3>1. 问题描述</h3>
            <div class="result warning">
                <h4>🐛 发现的BUG：</h4>
                <p>当用户处于会员身份时，在会员板块提取的离线账号在离线账号页面显示名称异常：</p>
                <ul>
                    <li><strong>错误显示：</strong>"离线VIP账号提取"</li>
                    <li><strong>应该显示：</strong>具体的游戏名称</li>
                </ul>
                
                <h4>🔍 问题原因：</h4>
                <p>在会员提取离线账号时，订单名称被硬编码为通用描述，而不是使用具体的游戏名称。</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试会员离线账号名称</h3>
            <div class="form-group">
                <label>用户ID：</label>
                <input type="number" id="userId" placeholder="输入用户ID" value="">
            </div>
            <button onclick="testMemberOfflineNames()">测试会员离线账号名称</button>
            <div id="testResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 修复方案</h3>
            <div class="result info">
                <h4>修复前的代码：</h4>
                <div class="code-block">'ord_name' => ($accountType === 'online' ? '在线' : '离线') . 'VIP账号提取',</div>
                
                <h4>修复后的代码：</h4>
                <div class="code-block">// 查询商品信息以获取正确的游戏名称
$goods = Db::name('goods')->where('id', $account->ac_goods)->find();
$gameName = $goods ? $goods['goods_name'] : '未知游戏';

'ord_name' => $gameName, // 使用具体的游戏名称而不是通用描述</div>
                
                <h4>修复效果：</h4>
                <ul>
                    <li>✅ 会员提取的离线账号显示具体游戏名称</li>
                    <li>✅ 离线账号页面显示正确的游戏信息</li>
                    <li>✅ 与其他类型的账号显示保持一致</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. 测试步骤</h3>
            <div class="result">
                <h4>完整测试流程：</h4>
                <ol>
                    <li><strong>准备测试环境：</strong>确保用户有会员身份</li>
                    <li><strong>提取离线账号：</strong>在会员板块提取一个离线账号</li>
                    <li><strong>检查显示效果：</strong>查看离线账号页面的显示名称</li>
                    <li><strong>使用测试工具：</strong>使用本页面的测试功能验证修复效果</li>
                </ol>
                
                <h4>预期结果：</h4>
                <ul>
                    <li>修复前：显示"离线VIP账号提取"</li>
                    <li>修复后：显示具体的游戏名称（如"CS:GO"、"PUBG"等）</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testMemberOfflineNames() {
            const userId = document.getElementById('userId').value;
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试会员离线账号名称...';
            
            fetch(`/admin/TestController/testMemberOfflineAccountNames?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        const memberOrders = data.data.member_offline_orders.orders;
                        const nameAnalysis = data.data.name_analysis;
                        
                        let statusClass = 'success';
                        let hasIssues = nameAnalysis.some(item => item.issue !== '正常');
                        
                        if (hasIssues) {
                            statusClass = 'warning';
                        }
                        
                        resultDiv.className = `result ${statusClass}`;
                        resultDiv.innerHTML = `
                            <h4>📊 测试结果：</h4>
                            <p><strong>用户：</strong>${data.data.user_info.username} (ID: ${data.data.user_info.id})</p>
                            <p><strong>会员离线订单数量：</strong>${data.data.member_offline_orders.count}</p>
                            
                            ${memberOrders.length > 0 ? `
                                <h5>📋 会员离线账号订单详情：</h5>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>订单ID</th>
                                            <th>显示名称</th>
                                            <th>实际游戏</th>
                                            <th>商品ID</th>
                                            <th>名称正确</th>
                                            <th>问题</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${nameAnalysis.map(item => `
                                            <tr>
                                                <td>${item.order_id}</td>
                                                <td>${item.ord_name}</td>
                                                <td>${item.game_name || '未知'}</td>
                                                <td>${item.ac_goods}</td>
                                                <td class="${item.name_correct ? 'issue-no' : 'issue-yes'}">${item.name_correct ? '是' : '否'}</td>
                                                <td class="${item.issue === '正常' ? 'issue-no' : 'issue-yes'}">${item.issue}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            ` : '<p>该用户没有会员提取的离线账号订单</p>'}
                            
                            <h5>📈 修复状态：</h5>
                            <p><strong>描述：</strong>${data.data.fix_status.description}</p>
                            <p><strong>预期行为：</strong>${data.data.fix_status.expected_behavior}</p>
                            
                            <h5>📊 所有离线订单统计：</h5>
                            <p><strong>总数：</strong>${data.data.all_offline_orders.count}</p>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `<p>❌ 测试失败：${data.msg}</p>`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<p>❌ 请求失败：${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
