/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */ ;
layui.define(["table", "form"], function(t) {
	var e = layui.$,
        $ = layui.$,
		i = layui.table,
		n = layui.form;
	i.render({
			elem: "#LAY-app-content-list2",
			url: "/admin/base/noticeList",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 60,
						title: "ID",
						sort: !0
					},
					{
						field: "not_title",
						width: 100,
						title: "文章标题",
						sort: !0
					},
					{
						field: "not_des",
						title: "文章描述",
						minWidth: 100
					},
					{
						field: "not_key",
						title: "文章关键词",
						minWidth: 100
					},
					{
						field: "not_system",
						title: "文章位置",
						minWidth: 80,
						align: "center"
					},
					{
						field: "not_img",
						title: "文章首图",
						minWidth: 80,
						align: "center"
					},
					{
						field: "time",
						title: "添加时间",
						minWidth: 80,
						align: "center"
					},
					{
						title: "操作",
						width: 150,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-com"
					}
				]
			],
			page: !0,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list2)",
			function(t) {
				t.data;
				"del" === t.event ? layer.confirm("确定删除？",
					function(e) {
					    console.log(t.data)
						t.del(), layer.close(e)
						
						layui.$.ajax({
                                    url: '/admin/base/delNotice',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "修改文章",
					content: "/admin/index/upNotice?id="+t.data.id,
					area: ["800px", "500px"],
					btn: ["确定", "取消"],
					yes: function(t, e) {
						var n = window["layui-layer-iframe" + t],
							l = "layuiadmin-app-form-submit",
							a = e.find("iframe")
							.contents()
							.find("#" + l);
						n.layui.form.on("submit(" + l + ")",
							function(e) {
								    $.ajax({
                                        url: '/admin/base/upNotice',
                                        method: 'post',
                                        data: e.field,
                                        success: function(res){
                                        }
                                    });
								e.field;
								layer.msg('保存成功');
								i.reload("LAY-app-content-list2"),
									layer.close(t)
							}), a.trigger("click")
					},
					success: function(t, e) {
                            
					}
				})

			}
			),
		t("notice", {})
});