/** layuiAdmin.std-v1.4.0 LPPL License By https://www.layui.com/admin/ */ ;
layui.define(["table", "form"], function(t) {
	var e = layui.$,
        $ = layui.$,
		i = layui.table,
		n = layui.form;
	i.render({
			elem: "#LAY-app-content-list2",
			url: "/admin/base/accountList",
			cols: [
				[{
						type: "checkbox",
						fixed: "left"
					},
					{
						field: "id",
						width: 100,
						title: "ID",
						sort: !0
					},
                    {
						field: "ac_goods",
						title: "属于商品",
						minWidth: 100
					},
					{
						field: "ac_name",
						minWidth: 100,
						title: "账号",
					},
					{
						field: "ac_password",
						title: "密码",
						minWidth: 100
					},
					{
						field: "ac_states",
						title: "是否可用",
						minWidth: 80,
						align: "center",
						toolbar: "#ac_states"
					},
					{
						field: "ac_uid",
						title: "租赁用户",
						minWidth: 80,
						align: "center",
					},
					{
						field: "goods_Type",
						title: "账号类型",
						minWidth: 80,
						align: "center",
						toolbar: "#ac_type"
					},
					{
						field: "time",
						title: "添加时间",
						minWidth: 80,
						align: "center",
					},
					{
						field: "exit_time",
						title: "过期时间",
						minWidth: 80,
						align: "center",
						templet: function(d) {
							if (d.exit_time) {
								var date = new Date(d.exit_time * 1000);
								var formattedDate = date.getFullYear() + '-' +
									('0' + (date.getMonth() + 1)).slice(-2) + '-' +
									('0' + date.getDate()).slice(-2) + ' ' +
									('0' + date.getHours()).slice(-2) + ':' +
									('0' + date.getMinutes()).slice(-2) + ':' +
									('0' + date.getSeconds()).slice(-2);
								return formattedDate;
							} else {
								return "账号暂未被租赁";
							}
						}
					},
					{
						title: "操作",
						width: 150,
						align: "center",
						fixed: "right",
						toolbar: "#table-content-com"
					}
				]
			],
			page: !0,
			limit: 10,
			limits: [10,20,30,40,50,60,70,80,100],
			text: "对不起，加载出现异常！"
		}),
		i.on("tool(LAY-app-content-list2)",
			function(t) {
				t.data;
				"del" === t.event ? layer.confirm("确定删除此账号？",
					function(e) {
					    console.log(t.data)
						t.del(), layer.close(e)
						layui.$.ajax({
                                    url: '/admin/base/delAccount',
                                    method: 'post',
                                    data: {id:t.data.id},
                                    success: function(res){
                                    layer.msg('删除成功');
                                    
                                    }
                                });
					}) : "edit" === t.event && layer.open({
					type: 2,
					title: "编辑商品",
					content: "/admin/index/upGoods?id="+t.data.id,
					area: ["500px", "500px"],
					btn: ["确定", "取消"],
					yes: function(t, e) {
						var n = window["layui-layer-iframe" + t],
							l = "layuiadmin-app-form-submit",
							a = e.find("iframe")
							.contents()
							.find("#" + l);
						n.layui.form.on("submit(" + l + ")",
							function(e) {
							    console.log(e.field)
								    $.ajax({
                                        url: '/admin/base/upGoods',
                                        method: 'post',
                                        data: e.field,
                                        success: function(res){
                                        }
                                    });
								e.field;
								layer.msg('保存成功');
								i.reload("LAY-app-content-list2"),
									layer.close(t)
							}), a.trigger("click")
					},
					success: function(t, e) {
                            
					}
				})

			}),
		t("accountlist", {})
});