<!DOCTYPE html>
<html lang="zh-CN">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>CDK卡密兑换 - {$system.sy_title}</title>
   <link href="/static/css/tailwind.min.css" rel="stylesheet">
   <script src="/static/js/jquery.min.js"></script>
   <style>
       .loader {
           border-top-color: #3498db;
           -webkit-animation: spinner 1.5s linear infinite;
           animation: spinner 1.5s linear infinite;
       }
        @-webkit-keyframes spinner {
           0% { -webkit-transform: rotate(0deg); }
           100% { -webkit-transform: rotate(360deg); }
       }
        @keyframes spinner {
           0% { transform: rotate(0deg); }
           100% { transform: rotate(360deg); }
       }
   </style>
</head>
<body class="bg-gray-100 text-gray-800">
    <!-- 引用头部 -->
   {include file='../app/user/view/ticket/header.html'}
    <!-- Main Content -->
   <main class="container mx-auto px-4 py-8">
       <div class="flex flex-col md:flex-row bg-white shadow-md rounded-lg overflow-hidden mb-8">
           <!-- 左侧用户信息 -->
           <div class="md:w-1/4 p-4 bg-gray-50">
               <div class="flex flex-col items-center">
                   <img src="{$user.us_logo?$user.us_logo:'/static/images/us.jpeg'}" alt="User Logo" class="w-24 h-24 rounded-full border-2 border-gray-300 mb-4">
                   <span class="text-lg font-semibold">{$user.us_username}</span>
               </div>
               <!-- 引用导航栏 -->
               {include file='../app/user/view/ticket/index.html'}
           </div>
            <!-- 右侧兑换区域 -->
           <div class="md:w-3/4 p-4">
               <div class="bg-white rounded-lg shadow-sm p-6">
                   <h2 class="text-2xl font-semibold text-center text-gray-800 mb-6">CDK卡密兑换</h2>
                   
                   <div class="max-w-xl mx-auto">
                       <div class="mb-6">
                           <input type="text" 
                                  id="cdkCode" 
                                  class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors" 
                                  placeholder="输入CDK以激活您的权益..."
                                  required>
                       </div>
                       
                       <button id="exchangeBtn" 
                               class="w-full bg-blue-500 text-white py-3 px-6 rounded-lg hover:bg-blue-600 transform transition-all duration-200 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50">
                           立即兑换
                       </button>
                   </div>
                    <div id="hintMsg" class="mt-6 text-center text-gray-600"></div>
               </div>
           </div>
       </div>
   </main>
    <!-- 兑换成功弹窗 -->
   <div id="successModal" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center hidden">
       <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-lg mx-auto transform transition-transform duration-300 scale-95">
           <div class="flex justify-between items-center mb-4">
               <h3 class="text-xl font-semibold">兑换成功</h3>
               <button onclick="closeSuccessModal()" class="text-gray-400 hover:text-gray-600">&times;</button>
           </div>
           <div class="space-y-4">
               <!-- 普通卡密结果显示 -->
               <div id="regularCardResult" class="bg-gray-50 p-4 rounded-lg">
                   <div class="grid grid-cols-1 gap-3">
                       <div class="flex justify-between">
                           <span class="text-gray-600">到期时间:</span>
                           <span id="expiryDate" class="font-medium"></span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">账号:</span>
                           <span id="accountInfo" class="font-medium"></span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">密码:</span>
                           <span id="passwordInfo" class="font-medium"></span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">令牌:</span>
                           <span id="tokenInfo" class="font-medium"></span>
                       </div>
                   </div>
               </div>
               
               <!-- 会员卡密结果显示 -->
               <div id="membershipCardResult" class="bg-gray-50 p-4 rounded-lg hidden">
                   <div class="grid grid-cols-1 gap-3">
                       <div class="flex justify-between">
                           <span class="text-gray-600">会员类型:</span>
                           <span id="membershipType" class="font-medium"></span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">到期时间:</span>
                           <span id="membershipExpiryDate" class="font-medium"></span>
                       </div>
                   </div>
               </div>
               
               <!-- 永久版卡密结果显示 -->
               <div id="permanentCardResult" class="bg-gray-50 p-4 rounded-lg hidden">
                   <div class="grid grid-cols-1 gap-3">
                       <div class="flex justify-between">
                           <span class="text-gray-600">商品名称:</span>
                           <span id="goodsName" class="font-medium"></span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">说明:</span>
                           <span id="instructions" class="font-medium"></span>
                       </div>
                   </div>
               </div>

               <!-- 离线卡密结果显示 -->
               <div id="offlineCardResult" class="bg-gray-50 p-4 rounded-lg hidden">
                   <div class="grid grid-cols-1 gap-3">
                       <div class="flex justify-between">
                           <span class="text-gray-600">商品名称:</span>
                           <span id="offlineGoodsName" class="font-medium"></span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">游戏类型:</span>
                           <span class="font-medium text-blue-600">离线游戏</span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">账号:</span>
                           <span id="offlineAccountName" class="font-medium"></span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">密码:</span>
                           <span id="offlineAccountPassword" class="font-medium"></span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">有效期:</span>
                           <span class="font-medium text-green-600">永久有效</span>
                       </div>
                       <div class="flex justify-between">
                           <span class="text-gray-600">说明:</span>
                           <span id="offlineInstructions" class="font-medium"></span>
                       </div>
                   </div>
               </div>
               
               <div class="flex justify-center">
                   <button onclick="window.location.href='/user/index/onshop'" 
                           class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                       前往用户中心
                   </button>
               </div>
           </div>
       </div>
   </div>
    <!-- 全局加载动画 -->
   <div id="globalLoader" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center hidden">
       <div class="loader ease-linear rounded-full border-8 border-t-8 border-gray-200 h-32 w-32"></div>
   </div>
    <script>
       function showLoader() {
           $('#globalLoader').fadeIn(200);
       }
        function hideLoader() {
           $('#globalLoader').fadeOut(200);
       }
        function closeSuccessModal() {
           $('#successModal').fadeOut(200);
       }
        $(document).ready(function() {
           $('#exchangeBtn').click(function() {
               const cdkCode = $('#cdkCode').val().trim();
               if (!cdkCode) {
                   $('#hintMsg').html('<div class="text-red-500">请输入CDK卡密</div>');
                   return;
               }
                showLoader();
               $.ajax({
                   url: '/user/CdkExchange/exchange',
                   type: 'POST',
                   data: { cdk_code: cdkCode },
                   success: function(res) {
                       hideLoader();
                       if (res.code === 0) {
                           // 根据返回结果判断卡密类型并显示对应内容
                           if (res.membership_type) {
                               // 会员卡密
                               $('#regularCardResult').addClass('hidden');
                               $('#permanentCardResult').addClass('hidden');
                               $('#offlineCardResult').addClass('hidden');
                               $('#membershipCardResult').removeClass('hidden');

                               $('#membershipType').text(res.membership_type);
                               $('#membershipExpiryDate').text(res.expiry_date);
                           } else if (res.is_offline) {
                               // 离线卡密
                               $('#regularCardResult').addClass('hidden');
                               $('#membershipCardResult').addClass('hidden');
                               $('#permanentCardResult').addClass('hidden');
                               $('#offlineCardResult').removeClass('hidden');

                               $('#offlineGoodsName').text(res.goods_name);
                               $('#offlineAccountName').text(res.account_name || '请在用户中心查看');
                               $('#offlineAccountPassword').text(res.account_password || '请在用户中心查看');
                               $('#offlineInstructions').text(res.instructions);
                           } else if (res.goods_name) {
                               // 永久版卡密
                               $('#regularCardResult').addClass('hidden');
                               $('#membershipCardResult').addClass('hidden');
                               $('#offlineCardResult').addClass('hidden');
                               $('#permanentCardResult').removeClass('hidden');

                               $('#goodsName').text(res.goods_name);
                               $('#instructions').text(res.instructions);
                           } else {
                               // 普通卡密
                               $('#membershipCardResult').addClass('hidden');
                               $('#permanentCardResult').addClass('hidden');
                               $('#offlineCardResult').addClass('hidden');
                               $('#regularCardResult').removeClass('hidden');

                               $('#expiryDate').text(res.expiry_date);
                               $('#accountInfo').text(res.account);
                               $('#passwordInfo').text(res.password);
                               $('#tokenInfo').text(res.token);
                           }
                           
                           $('#successModal').fadeIn(200);
                       } else {
                           $('#hintMsg').html(`<div class="text-red-500">${res.message}</div>`);
                       }
                   },
                   error: function() {
                       hideLoader();
                       $('#hintMsg').html('<div class="text-red-500">服务器错误，请稍后再试</div>');
                   }
               });
           });
            // 点击模态框外部关闭
           $(document).on('click', function(e) {
               if ($(e.target).is('#successModal')) {
                   closeSuccessModal();
               }
           });
       });
   </script>
</body>
</html>