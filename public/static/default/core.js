!(function (e) {
  var a = {}
  function t(s) {
    if (a[s]) return a[s].exports
    var i = (a[s] = { i: s, l: !1, exports: {} })
    return e[s].call(i.exports, i, i.exports, t), (i.l = !0), i.exports
  }
  ;(t.m = e),
    (t.c = a),
    (t.d = function (e, a, s) {
      t.o(e, a) || Object.defineProperty(e, a, { enumerable: !0, get: s })
    }),
    (t.r = function (e) {
      'undefined' != typeof Symbol &&
        Symbol.toStringTag &&
        Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
        Object.defineProperty(e, '__esModule', { value: !0 })
    }),
    (t.t = function (e, a) {
      if ((1 & a && (e = t(e)), 8 & a)) return e
      if (4 & a && 'object' == typeof e && e && e.__esModule) return e
      var s = Object.create(null)
      if (
        (t.r(s),
        Object.defineProperty(s, 'default', { enumerable: !0, value: e }),
        2 & a && 'string' != typeof e)
      )
        for (var i in e)
          t.d(
            s,
            i,
            function (a) {
              return e[a]
            }.bind(null, i)
          )
      return s
    }),
    (t.n = function (e) {
      var a =
        e && e.__esModule
          ? function () {
              return e.default
            }
          : function () {
              return e
            }
      return t.d(a, 'a', a), a
    }),
    (t.o = function (e, a) {
      return Object.prototype.hasOwnProperty.call(e, a)
    }),
    (t.p = '//zuhaowan.zuhaowan.com/shanghu/www3.0/'),
    t((t.s = '/NAK'))
})({
  '/NAK': function (e, a, t) {
    'use strict'
    t.r(a)
    t('Wp+W')
  },
  'Wp+W': function (e, a, t) {
    !(function () {
      'use strict'
      var e,
        a,
        t,
        s = function (a, t) {
          if (!(this instanceof s)) return new s(a, t)
          var i = {
              direction: 'horizontal',
              touchEventsTarget: 'container',
              initialSlide: 0,
              speed: 300,
              autoplay: !1,
              autoplayDisableOnInteraction: !0,
              autoplayStopOnLast: !1,
              iOSEdgeSwipeDetection: !1,
              iOSEdgeSwipeThreshold: 20,
              freeMode: !1,
              freeModeMomentum: !0,
              freeModeMomentumRatio: 1,
              freeModeMomentumBounce: !0,
              freeModeMomentumBounceRatio: 1,
              freeModeMomentumVelocityRatio: 1,
              freeModeSticky: !1,
              freeModeMinimumVelocity: 0.02,
              autoHeight: !1,
              setWrapperSize: !1,
              virtualTranslate: !1,
              effect: 'slide',
              coverflow: {
                rotate: 50,
                stretch: 0,
                depth: 100,
                modifier: 1,
                slideShadows: !0
              },
              flip: { slideShadows: !0, limitRotation: !0 },
              cube: {
                slideShadows: !0,
                shadow: !0,
                shadowOffset: 20,
                shadowScale: 0.94
              },
              fade: { crossFade: !1 },
              parallax: !1,
              zoom: !1,
              zoomMax: 3,
              zoomMin: 1,
              zoomToggle: !0,
              scrollbar: null,
              scrollbarHide: !0,
              scrollbarDraggable: !1,
              scrollbarSnapOnRelease: !1,
              keyboardControl: !1,
              mousewheelControl: !1,
              mousewheelReleaseOnEdges: !1,
              mousewheelInvert: !1,
              mousewheelForceToAxis: !1,
              mousewheelSensitivity: 1,
              mousewheelEventsTarged: 'container',
              hashnav: !1,
              hashnavWatchState: !1,
              history: !1,
              replaceState: !1,
              breakpoints: void 0,
              spaceBetween: 0,
              slidesPerView: 1,
              slidesPerColumn: 1,
              slidesPerColumnFill: 'column',
              slidesPerGroup: 1,
              centeredSlides: !1,
              slidesOffsetBefore: 0,
              slidesOffsetAfter: 0,
              roundLengths: !1,
              touchRatio: 1,
              touchAngle: 45,
              simulateTouch: !0,
              shortSwipes: !0,
              longSwipes: !0,
              longSwipesRatio: 0.5,
              longSwipesMs: 300,
              followFinger: !0,
              onlyExternal: !1,
              threshold: 0,
              touchMoveStopPropagation: !0,
              touchReleaseOnEdges: !1,
              uniqueNavElements: !0,
              pagination: null,
              paginationElement: 'span',
              paginationClickable: !1,
              paginationHide: !1,
              paginationBulletRender: null,
              paginationProgressRender: null,
              paginationFractionRender: null,
              paginationCustomRender: null,
              paginationType: 'bullets',
              resistance: !0,
              resistanceRatio: 0.85,
              nextButton: null,
              prevButton: null,
              watchSlidesProgress: !1,
              watchSlidesVisibility: !1,
              grabCursor: !1,
              preventClicks: !0,
              preventClicksPropagation: !0,
              slideToClickedSlide: !1,
              lazyLoading: !1,
              lazyLoadingInPrevNext: !1,
              lazyLoadingInPrevNextAmount: 1,
              lazyLoadingOnTransitionStart: !1,
              preloadImages: !0,
              updateOnImagesReady: !0,
              loop: !1,
              loopAdditionalSlides: 0,
              loopedSlides: null,
              control: void 0,
              controlInverse: !1,
              controlBy: 'slide',
              normalizeSlideIndex: !0,
              allowSwipeToPrev: !0,
              allowSwipeToNext: !0,
              swipeHandler: null,
              noSwiping: !0,
              noSwipingClass: 'swiper-no-swiping',
              passiveListeners: !0,
              containerModifierClass: 'swiper-container-',
              slideClass: 'swiper-slide',
              slideActiveClass: 'swiper-slide-active',
              slideDuplicateActiveClass: 'swiper-slide-duplicate-active',
              slideVisibleClass: 'swiper-slide-visible',
              slideDuplicateClass: 'swiper-slide-duplicate',
              slideNextClass: 'swiper-slide-next',
              slideDuplicateNextClass: 'swiper-slide-duplicate-next',
              slidePrevClass: 'swiper-slide-prev',
              slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',
              wrapperClass: 'swiper-wrapper',
              bulletClass: 'swiper-pagination-bullet',
              bulletActiveClass: 'swiper-pagination-bullet-active',
              buttonDisabledClass: 'swiper-button-disabled',
              paginationCurrentClass: 'swiper-pagination-current',
              paginationTotalClass: 'swiper-pagination-total',
              paginationHiddenClass: 'swiper-pagination-hidden',
              paginationProgressbarClass: 'swiper-pagination-progressbar',
              paginationClickableClass: 'swiper-pagination-clickable',
              paginationModifierClass: 'swiper-pagination-',
              lazyLoadingClass: 'swiper-lazy',
              lazyStatusLoadingClass: 'swiper-lazy-loading',
              lazyStatusLoadedClass: 'swiper-lazy-loaded',
              lazyPreloaderClass: 'swiper-lazy-preloader',
              notificationClass: 'swiper-notification',
              preloaderClass: 'preloader',
              zoomContainerClass: 'swiper-zoom-container',
              observer: !1,
              observeParents: !1,
              a11y: !1,
              prevSlideMessage: 'Previous slide',
              nextSlideMessage: 'Next slide',
              firstSlideMessage: 'This is the first slide',
              lastSlideMessage: 'This is the last slide',
              paginationBulletMessage: 'Go to slide {{index}}',
              runCallbacksOnInit: !0
            },
            n = t && t.virtualTranslate
          t = t || {}
          var o = {}
          for (var l in t)
            if (
              'object' != typeof t[l] ||
              null === t[l] ||
              t[l].nodeType ||
              t[l] === window ||
              t[l] === document ||
              (void 0 !== r && t[l] instanceof r) ||
              ('undefined' != typeof jQuery && t[l] instanceof jQuery)
            )
              o[l] = t[l]
            else for (var p in ((o[l] = {}), t[l])) o[l][p] = t[l][p]
          for (var d in i)
            if (void 0 === t[d]) t[d] = i[d]
            else if ('object' == typeof t[d])
              for (var u in i[d]) void 0 === t[d][u] && (t[d][u] = i[d][u])
          var c = this
          if (
            ((c.params = t),
            (c.originalParams = o),
            (c.classNames = []),
            void 0 !== e && void 0 !== r && (e = r),
            (void 0 !== e ||
              (e =
                void 0 === r
                  ? window.Dom7 || window.Zepto || window.jQuery
                  : r)) &&
              ((c.$ = e),
              (c.currentBreakpoint = void 0),
              (c.getActiveBreakpoint = function () {
                if (!c.params.breakpoints) return !1
                var e,
                  a = !1,
                  t = []
                for (e in c.params.breakpoints)
                  c.params.breakpoints.hasOwnProperty(e) && t.push(e)
                t.sort(function (e, a) {
                  return parseInt(e, 10) > parseInt(a, 10)
                })
                for (var s = 0; s < t.length; s++)
                  (e = t[s]) >= window.innerWidth && !a && (a = e)
                return a || 'max'
              }),
              (c.setBreakpoint = function () {
                var e = c.getActiveBreakpoint()
                if (e && c.currentBreakpoint !== e) {
                  var a =
                      e in c.params.breakpoints
                        ? c.params.breakpoints[e]
                        : c.originalParams,
                    t =
                      c.params.loop &&
                      a.slidesPerView !== c.params.slidesPerView
                  for (var s in a) c.params[s] = a[s]
                  ;(c.currentBreakpoint = e), t && c.destroyLoop && c.reLoop(!0)
                }
              }),
              c.params.breakpoints && c.setBreakpoint(),
              (c.container = e(a)),
              0 !== c.container.length))
          ) {
            if (c.container.length > 1) {
              var m = []
              return (
                c.container.each(function () {
                  m.push(new s(this, t))
                }),
                m
              )
            }
            ;(c.container[0].swiper = c),
              c.container.data('swiper', c),
              c.classNames.push(
                c.params.containerModifierClass + c.params.direction
              ),
              c.params.freeMode &&
                c.classNames.push(
                  c.params.containerModifierClass + 'free-mode'
                ),
              c.support.flexbox ||
                (c.classNames.push(
                  c.params.containerModifierClass + 'no-flexbox'
                ),
                (c.params.slidesPerColumn = 1)),
              c.params.autoHeight &&
                c.classNames.push(
                  c.params.containerModifierClass + 'autoheight'
                ),
              (c.params.parallax || c.params.watchSlidesVisibility) &&
                (c.params.watchSlidesProgress = !0),
              c.params.touchReleaseOnEdges && (c.params.resistanceRatio = 0),
              ['cube', 'coverflow', 'flip'].indexOf(c.params.effect) >= 0 &&
                (c.support.transforms3d
                  ? ((c.params.watchSlidesProgress = !0),
                    c.classNames.push(c.params.containerModifierClass + '3d'))
                  : (c.params.effect = 'slide')),
              'slide' !== c.params.effect &&
                c.classNames.push(
                  c.params.containerModifierClass + c.params.effect
                ),
              'cube' === c.params.effect &&
                ((c.params.resistanceRatio = 0),
                (c.params.slidesPerView = 1),
                (c.params.slidesPerColumn = 1),
                (c.params.slidesPerGroup = 1),
                (c.params.centeredSlides = !1),
                (c.params.spaceBetween = 0),
                (c.params.virtualTranslate = !0)),
              ('fade' !== c.params.effect && 'flip' !== c.params.effect) ||
                ((c.params.slidesPerView = 1),
                (c.params.slidesPerColumn = 1),
                (c.params.slidesPerGroup = 1),
                (c.params.watchSlidesProgress = !0),
                (c.params.spaceBetween = 0),
                void 0 === n && (c.params.virtualTranslate = !0)),
              c.params.grabCursor &&
                c.support.touch &&
                (c.params.grabCursor = !1),
              (c.wrapper = c.container.children('.' + c.params.wrapperClass)),
              c.params.pagination &&
                ((c.paginationContainer = e(c.params.pagination)),
                c.params.uniqueNavElements &&
                  'string' == typeof c.params.pagination &&
                  c.paginationContainer.length > 1 &&
                  1 === c.container.find(c.params.pagination).length &&
                  (c.paginationContainer = c.container.find(
                    c.params.pagination
                  )),
                'bullets' === c.params.paginationType &&
                c.params.paginationClickable
                  ? c.paginationContainer.addClass(
                      c.params.paginationModifierClass + 'clickable'
                    )
                  : (c.params.paginationClickable = !1),
                c.paginationContainer.addClass(
                  c.params.paginationModifierClass + c.params.paginationType
                )),
              (c.params.nextButton || c.params.prevButton) &&
                (c.params.nextButton &&
                  ((c.nextButton = e(c.params.nextButton)),
                  c.params.uniqueNavElements &&
                    'string' == typeof c.params.nextButton &&
                    c.nextButton.length > 1 &&
                    1 === c.container.find(c.params.nextButton).length &&
                    (c.nextButton = c.container.find(c.params.nextButton))),
                c.params.prevButton &&
                  ((c.prevButton = e(c.params.prevButton)),
                  c.params.uniqueNavElements &&
                    'string' == typeof c.params.prevButton &&
                    c.prevButton.length > 1 &&
                    1 === c.container.find(c.params.prevButton).length &&
                    (c.prevButton = c.container.find(c.params.prevButton)))),
              (c.isHorizontal = function () {
                return 'horizontal' === c.params.direction
              }),
              (c.rtl =
                c.isHorizontal() &&
                ('rtl' === c.container[0].dir.toLowerCase() ||
                  'rtl' === c.container.css('direction'))),
              c.rtl &&
                c.classNames.push(c.params.containerModifierClass + 'rtl'),
              c.rtl &&
                (c.wrongRTL = '-webkit-box' === c.wrapper.css('display')),
              c.params.slidesPerColumn > 1 &&
                c.classNames.push(c.params.containerModifierClass + 'multirow'),
              c.device.android &&
                c.classNames.push(c.params.containerModifierClass + 'android'),
              c.container.addClass(c.classNames.join(' ')),
              (c.translate = 0),
              (c.progress = 0),
              (c.velocity = 0),
              (c.lockSwipeToNext = function () {
                ;(c.params.allowSwipeToNext = !1),
                  !1 === c.params.allowSwipeToPrev &&
                    c.params.grabCursor &&
                    c.unsetGrabCursor()
              }),
              (c.lockSwipeToPrev = function () {
                ;(c.params.allowSwipeToPrev = !1),
                  !1 === c.params.allowSwipeToNext &&
                    c.params.grabCursor &&
                    c.unsetGrabCursor()
              }),
              (c.lockSwipes = function () {
                ;(c.params.allowSwipeToNext = c.params.allowSwipeToPrev = !1),
                  c.params.grabCursor && c.unsetGrabCursor()
              }),
              (c.unlockSwipeToNext = function () {
                ;(c.params.allowSwipeToNext = !0),
                  !0 === c.params.allowSwipeToPrev &&
                    c.params.grabCursor &&
                    c.setGrabCursor()
              }),
              (c.unlockSwipeToPrev = function () {
                ;(c.params.allowSwipeToPrev = !0),
                  !0 === c.params.allowSwipeToNext &&
                    c.params.grabCursor &&
                    c.setGrabCursor()
              }),
              (c.unlockSwipes = function () {
                ;(c.params.allowSwipeToNext = c.params.allowSwipeToPrev = !0),
                  c.params.grabCursor && c.setGrabCursor()
              }),
              (c.setGrabCursor = function (e) {
                ;(c.container[0].style.cursor = 'move'),
                  (c.container[0].style.cursor = e
                    ? '-webkit-grabbing'
                    : '-webkit-grab'),
                  (c.container[0].style.cursor = e
                    ? '-moz-grabbin'
                    : '-moz-grab'),
                  (c.container[0].style.cursor = e ? 'grabbing' : 'grab')
              }),
              (c.unsetGrabCursor = function () {
                c.container[0].style.cursor = ''
              }),
              c.params.grabCursor && c.setGrabCursor(),
              (c.imagesToLoad = []),
              (c.imagesLoaded = 0),
              (c.loadImage = function (e, a, t, s, i, r) {
                var n
                function o() {
                  r && r()
                }
                e.complete && i
                  ? o()
                  : a
                  ? (((n = new window.Image()).onload = o),
                    (n.onerror = o),
                    s && (n.sizes = s),
                    t && (n.srcset = t),
                    a && (n.src = a))
                  : o()
              }),
              (c.preloadImages = function () {
                function e() {
                  void 0 !== c &&
                    null !== c &&
                    c &&
                    (void 0 !== c.imagesLoaded && c.imagesLoaded++,
                    c.imagesLoaded === c.imagesToLoad.length &&
                      (c.params.updateOnImagesReady && c.update(),
                      c.emit('onImagesReady', c)))
                }
                c.imagesToLoad = c.container.find('img')
                for (var a = 0; a < c.imagesToLoad.length; a++)
                  c.loadImage(
                    c.imagesToLoad[a],
                    c.imagesToLoad[a].currentSrc ||
                      c.imagesToLoad[a].getAttribute('src'),
                    c.imagesToLoad[a].srcset ||
                      c.imagesToLoad[a].getAttribute('srcset'),
                    c.imagesToLoad[a].sizes ||
                      c.imagesToLoad[a].getAttribute('sizes'),
                    !0,
                    e
                  )
              }),
              (c.autoplayTimeoutId = void 0),
              (c.autoplaying = !1),
              (c.autoplayPaused = !1),
              (c.startAutoplay = function () {
                return (
                  void 0 === c.autoplayTimeoutId &&
                  !!c.params.autoplay &&
                  !c.autoplaying &&
                  ((c.autoplaying = !0), c.emit('onAutoplayStart', c), void D())
                )
              }),
              (c.stopAutoplay = function (e) {
                c.autoplayTimeoutId &&
                  (c.autoplayTimeoutId && clearTimeout(c.autoplayTimeoutId),
                  (c.autoplaying = !1),
                  (c.autoplayTimeoutId = void 0),
                  c.emit('onAutoplayStop', c))
              }),
              (c.pauseAutoplay = function (e) {
                c.autoplayPaused ||
                  (c.autoplayTimeoutId && clearTimeout(c.autoplayTimeoutId),
                  (c.autoplayPaused = !0),
                  0 === e
                    ? ((c.autoplayPaused = !1), D())
                    : c.wrapper.transitionEnd(function () {
                        c &&
                          ((c.autoplayPaused = !1),
                          c.autoplaying ? D() : c.stopAutoplay())
                      }))
              }),
              (c.minTranslate = function () {
                return -c.snapGrid[0]
              }),
              (c.maxTranslate = function () {
                return -c.snapGrid[c.snapGrid.length - 1]
              }),
              (c.updateAutoHeight = function () {
                var e,
                  a = [],
                  t = 0
                if (
                  'auto' !== c.params.slidesPerView &&
                  c.params.slidesPerView > 1
                )
                  for (e = 0; e < Math.ceil(c.params.slidesPerView); e++) {
                    var s = c.activeIndex + e
                    if (s > c.slides.length) break
                    a.push(c.slides.eq(s)[0])
                  }
                else a.push(c.slides.eq(c.activeIndex)[0])
                for (e = 0; e < a.length; e++)
                  if (void 0 !== a[e]) {
                    var i = a[e].offsetHeight
                    t = i > t ? i : t
                  }
                t && c.wrapper.css('height', t + 'px')
              }),
              (c.updateContainerSize = function () {
                var e, a
                ;(e =
                  void 0 !== c.params.width
                    ? c.params.width
                    : c.container[0].clientWidth),
                  (a =
                    void 0 !== c.params.height
                      ? c.params.height
                      : c.container[0].clientHeight),
                  (0 === e && c.isHorizontal()) ||
                    (0 === a && !c.isHorizontal()) ||
                    ((e =
                      e -
                      parseInt(c.container.css('padding-left'), 10) -
                      parseInt(c.container.css('padding-right'), 10)),
                    (a =
                      a -
                      parseInt(c.container.css('padding-top'), 10) -
                      parseInt(c.container.css('padding-bottom'), 10)),
                    (c.width = e),
                    (c.height = a),
                    (c.size = c.isHorizontal() ? c.width : c.height))
              }),
              (c.updateSlidesSize = function () {
                ;(c.slides = c.wrapper.children('.' + c.params.slideClass)),
                  (c.snapGrid = []),
                  (c.slidesGrid = []),
                  (c.slidesSizesGrid = [])
                var e,
                  a = c.params.spaceBetween,
                  t = -c.params.slidesOffsetBefore,
                  s = 0,
                  i = 0
                if (void 0 !== c.size) {
                  var r, n
                  'string' == typeof a &&
                    a.indexOf('%') >= 0 &&
                    (a = (parseFloat(a.replace('%', '')) / 100) * c.size),
                    (c.virtualSize = -a),
                    c.rtl
                      ? c.slides.css({ marginLeft: '', marginTop: '' })
                      : c.slides.css({ marginRight: '', marginBottom: '' }),
                    c.params.slidesPerColumn > 1 &&
                      ((r =
                        Math.floor(
                          c.slides.length / c.params.slidesPerColumn
                        ) ===
                        c.slides.length / c.params.slidesPerColumn
                          ? c.slides.length
                          : Math.ceil(
                              c.slides.length / c.params.slidesPerColumn
                            ) * c.params.slidesPerColumn),
                      'auto' !== c.params.slidesPerView &&
                        'row' === c.params.slidesPerColumnFill &&
                        (r = Math.max(
                          r,
                          c.params.slidesPerView * c.params.slidesPerColumn
                        )))
                  var o,
                    l = c.params.slidesPerColumn,
                    p = r / l,
                    d = p - (c.params.slidesPerColumn * p - c.slides.length)
                  for (e = 0; e < c.slides.length; e++) {
                    n = 0
                    var u,
                      m,
                      h,
                      g = c.slides.eq(e)
                    if (c.params.slidesPerColumn > 1)
                      'column' === c.params.slidesPerColumnFill
                        ? ((h = e - (m = Math.floor(e / l)) * l),
                          (m > d || (m === d && h === l - 1)) &&
                            ++h >= l &&
                            ((h = 0), m++),
                          (u = m + (h * r) / l),
                          g.css({
                            '-webkit-box-ordinal-group': u,
                            '-moz-box-ordinal-group': u,
                            '-ms-flex-order': u,
                            '-webkit-order': u,
                            order: u
                          }))
                        : (m = e - (h = Math.floor(e / p)) * p),
                        g
                          .css(
                            'margin-' + (c.isHorizontal() ? 'top' : 'left'),
                            0 !== h &&
                              c.params.spaceBetween &&
                              c.params.spaceBetween + 'px'
                          )
                          .attr('data-swiper-column', m)
                          .attr('data-swiper-row', h)
                    'none' !== g.css('display') &&
                      ('auto' === c.params.slidesPerView
                        ? ((n = c.isHorizontal()
                            ? g.outerWidth(!0)
                            : g.outerHeight(!0)),
                          c.params.roundLengths && (n = L(n)))
                        : ((n =
                            (c.size - (c.params.slidesPerView - 1) * a) /
                            c.params.slidesPerView),
                          c.params.roundLengths && (n = L(n)),
                          c.isHorizontal()
                            ? (c.slides[e].style.width = n + 'px')
                            : (c.slides[e].style.height = n + 'px')),
                      (c.slides[e].swiperSlideSize = n),
                      c.slidesSizesGrid.push(n),
                      c.params.centeredSlides
                        ? ((t = t + n / 2 + s / 2 + a),
                          0 === s && 0 !== e && (t = t - c.size / 2 - a),
                          0 === e && (t = t - c.size / 2 - a),
                          Math.abs(t) < 0.001 && (t = 0),
                          i % c.params.slidesPerGroup == 0 &&
                            c.snapGrid.push(t),
                          c.slidesGrid.push(t))
                        : (i % c.params.slidesPerGroup == 0 &&
                            c.snapGrid.push(t),
                          c.slidesGrid.push(t),
                          (t = t + n + a)),
                      (c.virtualSize += n + a),
                      (s = n),
                      i++)
                  }
                  if (
                    ((c.virtualSize =
                      Math.max(c.virtualSize, c.size) +
                      c.params.slidesOffsetAfter),
                    c.rtl &&
                      c.wrongRTL &&
                      ('slide' === c.params.effect ||
                        'coverflow' === c.params.effect) &&
                      c.wrapper.css({
                        width: c.virtualSize + c.params.spaceBetween + 'px'
                      }),
                    (c.support.flexbox && !c.params.setWrapperSize) ||
                      (c.isHorizontal()
                        ? c.wrapper.css({
                            width: c.virtualSize + c.params.spaceBetween + 'px'
                          })
                        : c.wrapper.css({
                            height: c.virtualSize + c.params.spaceBetween + 'px'
                          })),
                    c.params.slidesPerColumn > 1 &&
                      ((c.virtualSize = (n + c.params.spaceBetween) * r),
                      (c.virtualSize =
                        Math.ceil(c.virtualSize / c.params.slidesPerColumn) -
                        c.params.spaceBetween),
                      c.isHorizontal()
                        ? c.wrapper.css({
                            width: c.virtualSize + c.params.spaceBetween + 'px'
                          })
                        : c.wrapper.css({
                            height: c.virtualSize + c.params.spaceBetween + 'px'
                          }),
                      c.params.centeredSlides))
                  ) {
                    for (o = [], e = 0; e < c.snapGrid.length; e++)
                      c.snapGrid[e] < c.virtualSize + c.snapGrid[0] &&
                        o.push(c.snapGrid[e])
                    c.snapGrid = o
                  }
                  if (!c.params.centeredSlides) {
                    for (o = [], e = 0; e < c.snapGrid.length; e++)
                      c.snapGrid[e] <= c.virtualSize - c.size &&
                        o.push(c.snapGrid[e])
                    ;(c.snapGrid = o),
                      Math.floor(c.virtualSize - c.size) -
                        Math.floor(c.snapGrid[c.snapGrid.length - 1]) >
                        1 && c.snapGrid.push(c.virtualSize - c.size)
                  }
                  0 === c.snapGrid.length && (c.snapGrid = [0]),
                    0 !== c.params.spaceBetween &&
                      (c.isHorizontal()
                        ? c.rtl
                          ? c.slides.css({ marginLeft: a + 'px' })
                          : c.slides.css({ marginRight: a + 'px' })
                        : c.slides.css({ marginBottom: a + 'px' })),
                    c.params.watchSlidesProgress && c.updateSlidesOffset()
                }
              }),
              (c.updateSlidesOffset = function () {
                for (var e = 0; e < c.slides.length; e++)
                  c.slides[e].swiperSlideOffset = c.isHorizontal()
                    ? c.slides[e].offsetLeft
                    : c.slides[e].offsetTop
              }),
              (c.currentSlidesPerView = function () {
                var e,
                  a,
                  t = 1
                if (c.params.centeredSlides) {
                  var s,
                    i = c.slides[c.activeIndex].swiperSlideSize
                  for (e = c.activeIndex + 1; e < c.slides.length; e++)
                    c.slides[e] &&
                      !s &&
                      (t++,
                      (i += c.slides[e].swiperSlideSize) > c.size && (s = !0))
                  for (a = c.activeIndex - 1; a >= 0; a--)
                    c.slides[a] &&
                      !s &&
                      (t++,
                      (i += c.slides[a].swiperSlideSize) > c.size && (s = !0))
                } else
                  for (e = c.activeIndex + 1; e < c.slides.length; e++)
                    c.slidesGrid[e] - c.slidesGrid[c.activeIndex] < c.size &&
                      t++
                return t
              }),
              (c.updateSlidesProgress = function (e) {
                if (
                  (void 0 === e && (e = c.translate || 0),
                  0 !== c.slides.length)
                ) {
                  void 0 === c.slides[0].swiperSlideOffset &&
                    c.updateSlidesOffset()
                  var a = -e
                  c.rtl && (a = e),
                    c.slides.removeClass(c.params.slideVisibleClass)
                  for (var t = 0; t < c.slides.length; t++) {
                    var s = c.slides[t],
                      i =
                        (a +
                          (c.params.centeredSlides ? c.minTranslate() : 0) -
                          s.swiperSlideOffset) /
                        (s.swiperSlideSize + c.params.spaceBetween)
                    if (c.params.watchSlidesVisibility) {
                      var r = -(a - s.swiperSlideOffset),
                        n = r + c.slidesSizesGrid[t]
                      ;((r >= 0 && r < c.size) ||
                        (n > 0 && n <= c.size) ||
                        (r <= 0 && n >= c.size)) &&
                        c.slides.eq(t).addClass(c.params.slideVisibleClass)
                    }
                    s.progress = c.rtl ? -i : i
                  }
                }
              }),
              (c.updateProgress = function (e) {
                void 0 === e && (e = c.translate || 0)
                var a = c.maxTranslate() - c.minTranslate(),
                  t = c.isBeginning,
                  s = c.isEnd
                0 === a
                  ? ((c.progress = 0), (c.isBeginning = c.isEnd = !0))
                  : ((c.progress = (e - c.minTranslate()) / a),
                    (c.isBeginning = c.progress <= 0),
                    (c.isEnd = c.progress >= 1)),
                  c.isBeginning && !t && c.emit('onReachBeginning', c),
                  c.isEnd && !s && c.emit('onReachEnd', c),
                  c.params.watchSlidesProgress && c.updateSlidesProgress(e),
                  c.emit('onProgress', c, c.progress)
              }),
              (c.updateActiveIndex = function () {
                var e,
                  a,
                  t,
                  s = c.rtl ? c.translate : -c.translate
                for (a = 0; a < c.slidesGrid.length; a++)
                  void 0 !== c.slidesGrid[a + 1]
                    ? s >= c.slidesGrid[a] &&
                      s <
                        c.slidesGrid[a + 1] -
                          (c.slidesGrid[a + 1] - c.slidesGrid[a]) / 2
                      ? (e = a)
                      : s >= c.slidesGrid[a] &&
                        s < c.slidesGrid[a + 1] &&
                        (e = a + 1)
                    : s >= c.slidesGrid[a] && (e = a)
                c.params.normalizeSlideIndex &&
                  (e < 0 || void 0 === e) &&
                  (e = 0),
                  (t = Math.floor(e / c.params.slidesPerGroup)) >=
                    c.snapGrid.length && (t = c.snapGrid.length - 1),
                  e !== c.activeIndex &&
                    ((c.snapIndex = t),
                    (c.previousIndex = c.activeIndex),
                    (c.activeIndex = e),
                    c.updateClasses(),
                    c.updateRealIndex())
              }),
              (c.updateRealIndex = function () {
                c.realIndex = parseInt(
                  c.slides.eq(c.activeIndex).attr('data-swiper-slide-index') ||
                    c.activeIndex,
                  10
                )
              }),
              (c.updateClasses = function () {
                c.slides.removeClass(
                  c.params.slideActiveClass +
                    ' ' +
                    c.params.slideNextClass +
                    ' ' +
                    c.params.slidePrevClass +
                    ' ' +
                    c.params.slideDuplicateActiveClass +
                    ' ' +
                    c.params.slideDuplicateNextClass +
                    ' ' +
                    c.params.slideDuplicatePrevClass
                )
                var a = c.slides.eq(c.activeIndex)
                a.addClass(c.params.slideActiveClass),
                  t.loop &&
                    (a.hasClass(c.params.slideDuplicateClass)
                      ? c.wrapper
                          .children(
                            '.' +
                              c.params.slideClass +
                              ':not(.' +
                              c.params.slideDuplicateClass +
                              ')[data-swiper-slide-index="' +
                              c.realIndex +
                              '"]'
                          )
                          .addClass(c.params.slideDuplicateActiveClass)
                      : c.wrapper
                          .children(
                            '.' +
                              c.params.slideClass +
                              '.' +
                              c.params.slideDuplicateClass +
                              '[data-swiper-slide-index="' +
                              c.realIndex +
                              '"]'
                          )
                          .addClass(c.params.slideDuplicateActiveClass))
                var s = a
                  .next('.' + c.params.slideClass)
                  .addClass(c.params.slideNextClass)
                c.params.loop &&
                  0 === s.length &&
                  (s = c.slides.eq(0)).addClass(c.params.slideNextClass)
                var i = a
                  .prev('.' + c.params.slideClass)
                  .addClass(c.params.slidePrevClass)
                if (
                  (c.params.loop &&
                    0 === i.length &&
                    (i = c.slides.eq(-1)).addClass(c.params.slidePrevClass),
                  t.loop &&
                    (s.hasClass(c.params.slideDuplicateClass)
                      ? c.wrapper
                          .children(
                            '.' +
                              c.params.slideClass +
                              ':not(.' +
                              c.params.slideDuplicateClass +
                              ')[data-swiper-slide-index="' +
                              s.attr('data-swiper-slide-index') +
                              '"]'
                          )
                          .addClass(c.params.slideDuplicateNextClass)
                      : c.wrapper
                          .children(
                            '.' +
                              c.params.slideClass +
                              '.' +
                              c.params.slideDuplicateClass +
                              '[data-swiper-slide-index="' +
                              s.attr('data-swiper-slide-index') +
                              '"]'
                          )
                          .addClass(c.params.slideDuplicateNextClass),
                    i.hasClass(c.params.slideDuplicateClass)
                      ? c.wrapper
                          .children(
                            '.' +
                              c.params.slideClass +
                              ':not(.' +
                              c.params.slideDuplicateClass +
                              ')[data-swiper-slide-index="' +
                              i.attr('data-swiper-slide-index') +
                              '"]'
                          )
                          .addClass(c.params.slideDuplicatePrevClass)
                      : c.wrapper
                          .children(
                            '.' +
                              c.params.slideClass +
                              '.' +
                              c.params.slideDuplicateClass +
                              '[data-swiper-slide-index="' +
                              i.attr('data-swiper-slide-index') +
                              '"]'
                          )
                          .addClass(c.params.slideDuplicatePrevClass)),
                  c.paginationContainer && c.paginationContainer.length > 0)
                ) {
                  var r,
                    n = c.params.loop
                      ? Math.ceil(
                          (c.slides.length - 2 * c.loopedSlides) /
                            c.params.slidesPerGroup
                        )
                      : c.snapGrid.length
                  if (
                    (c.params.loop
                      ? ((r = Math.ceil(
                          (c.activeIndex - c.loopedSlides) /
                            c.params.slidesPerGroup
                        )) >
                          c.slides.length - 1 - 2 * c.loopedSlides &&
                          (r -= c.slides.length - 2 * c.loopedSlides),
                        r > n - 1 && (r -= n),
                        r < 0 &&
                          'bullets' !== c.params.paginationType &&
                          (r = n + r))
                      : (r =
                          void 0 !== c.snapIndex
                            ? c.snapIndex
                            : c.activeIndex || 0),
                    'bullets' === c.params.paginationType &&
                      c.bullets &&
                      c.bullets.length > 0 &&
                      (c.bullets.removeClass(c.params.bulletActiveClass),
                      c.paginationContainer.length > 1
                        ? c.bullets.each(function () {
                            e(this).index() === r &&
                              e(this).addClass(c.params.bulletActiveClass)
                          })
                        : c.bullets.eq(r).addClass(c.params.bulletActiveClass)),
                    'fraction' === c.params.paginationType &&
                      (c.paginationContainer
                        .find('.' + c.params.paginationCurrentClass)
                        .text(r + 1),
                      c.paginationContainer
                        .find('.' + c.params.paginationTotalClass)
                        .text(n)),
                    'progress' === c.params.paginationType)
                  ) {
                    var o = (r + 1) / n,
                      l = o,
                      p = 1
                    c.isHorizontal() || ((p = o), (l = 1)),
                      c.paginationContainer
                        .find('.' + c.params.paginationProgressbarClass)
                        .transform(
                          'translate3d(0,0,0) scaleX(' +
                            l +
                            ') scaleY(' +
                            p +
                            ')'
                        )
                        .transition(c.params.speed)
                  }
                  'custom' === c.params.paginationType &&
                    c.params.paginationCustomRender &&
                    (c.paginationContainer.html(
                      c.params.paginationCustomRender(c, r + 1, n)
                    ),
                    c.emit('onPaginationRendered', c, c.paginationContainer[0]))
                }
                c.params.loop ||
                  (c.params.prevButton &&
                    c.prevButton &&
                    c.prevButton.length > 0 &&
                    (c.isBeginning
                      ? (c.prevButton.addClass(c.params.buttonDisabledClass),
                        c.params.a11y && c.a11y && c.a11y.disable(c.prevButton))
                      : (c.prevButton.removeClass(c.params.buttonDisabledClass),
                        c.params.a11y &&
                          c.a11y &&
                          c.a11y.enable(c.prevButton))),
                  c.params.nextButton &&
                    c.nextButton &&
                    c.nextButton.length > 0 &&
                    (c.isEnd
                      ? (c.nextButton.addClass(c.params.buttonDisabledClass),
                        c.params.a11y && c.a11y && c.a11y.disable(c.nextButton))
                      : (c.nextButton.removeClass(c.params.buttonDisabledClass),
                        c.params.a11y &&
                          c.a11y &&
                          c.a11y.enable(c.nextButton))))
              }),
              (c.updatePagination = function () {
                if (
                  c.params.pagination &&
                  c.paginationContainer &&
                  c.paginationContainer.length > 0
                ) {
                  var e = ''
                  if ('bullets' === c.params.paginationType) {
                    for (
                      var a = c.params.loop
                          ? Math.ceil(
                              (c.slides.length - 2 * c.loopedSlides) /
                                c.params.slidesPerGroup
                            )
                          : c.snapGrid.length,
                        t = 0;
                      t < a;
                      t++
                    )
                      c.params.paginationBulletRender
                        ? (e += c.params.paginationBulletRender(
                            c,
                            t,
                            c.params.bulletClass
                          ))
                        : (e +=
                            '<' +
                            c.params.paginationElement +
                            ' class="' +
                            c.params.bulletClass +
                            '"></' +
                            c.params.paginationElement +
                            '>')
                    c.paginationContainer.html(e),
                      (c.bullets = c.paginationContainer.find(
                        '.' + c.params.bulletClass
                      )),
                      c.params.paginationClickable &&
                        c.params.a11y &&
                        c.a11y &&
                        c.a11y.initPagination()
                  }
                  'fraction' === c.params.paginationType &&
                    ((e = c.params.paginationFractionRender
                      ? c.params.paginationFractionRender(
                          c,
                          c.params.paginationCurrentClass,
                          c.params.paginationTotalClass
                        )
                      : '<span class="' +
                        c.params.paginationCurrentClass +
                        '"></span> / <span class="' +
                        c.params.paginationTotalClass +
                        '"></span>'),
                    c.paginationContainer.html(e)),
                    'progress' === c.params.paginationType &&
                      ((e = c.params.paginationProgressRender
                        ? c.params.paginationProgressRender(
                            c,
                            c.params.paginationProgressbarClass
                          )
                        : '<span class="' +
                          c.params.paginationProgressbarClass +
                          '"></span>'),
                      c.paginationContainer.html(e)),
                    'custom' !== c.params.paginationType &&
                      c.emit(
                        'onPaginationRendered',
                        c,
                        c.paginationContainer[0]
                      )
                }
              }),
              (c.update = function (e) {
                var a
                c &&
                  (c.updateContainerSize(),
                  c.updateSlidesSize(),
                  c.updateProgress(),
                  c.updatePagination(),
                  c.updateClasses(),
                  c.params.scrollbar && c.scrollbar && c.scrollbar.set(),
                  e
                    ? (c.controller &&
                        c.controller.spline &&
                        (c.controller.spline = void 0),
                      c.params.freeMode
                        ? (t(), c.params.autoHeight && c.updateAutoHeight())
                        : (('auto' === c.params.slidesPerView ||
                            c.params.slidesPerView > 1) &&
                          c.isEnd &&
                          !c.params.centeredSlides
                            ? c.slideTo(c.slides.length - 1, 0, !1, !0)
                            : c.slideTo(c.activeIndex, 0, !1, !0)) || t())
                    : c.params.autoHeight && c.updateAutoHeight())
                function t() {
                  c.rtl, c.translate
                  ;(a = Math.min(
                    Math.max(c.translate, c.maxTranslate()),
                    c.minTranslate()
                  )),
                    c.setWrapperTranslate(a),
                    c.updateActiveIndex(),
                    c.updateClasses()
                }
              }),
              (c.onResize = function (e) {
                c.params.onBeforeResize && c.params.onBeforeResize(c),
                  c.params.breakpoints && c.setBreakpoint()
                var a = c.params.allowSwipeToPrev,
                  t = c.params.allowSwipeToNext
                ;(c.params.allowSwipeToPrev = c.params.allowSwipeToNext = !0),
                  c.updateContainerSize(),
                  c.updateSlidesSize(),
                  ('auto' === c.params.slidesPerView ||
                    c.params.freeMode ||
                    e) &&
                    c.updatePagination(),
                  c.params.scrollbar && c.scrollbar && c.scrollbar.set(),
                  c.controller &&
                    c.controller.spline &&
                    (c.controller.spline = void 0)
                var s = !1
                if (c.params.freeMode) {
                  var i = Math.min(
                    Math.max(c.translate, c.maxTranslate()),
                    c.minTranslate()
                  )
                  c.setWrapperTranslate(i),
                    c.updateActiveIndex(),
                    c.updateClasses(),
                    c.params.autoHeight && c.updateAutoHeight()
                } else
                  c.updateClasses(),
                    (s =
                      ('auto' === c.params.slidesPerView ||
                        c.params.slidesPerView > 1) &&
                      c.isEnd &&
                      !c.params.centeredSlides
                        ? c.slideTo(c.slides.length - 1, 0, !1, !0)
                        : c.slideTo(c.activeIndex, 0, !1, !0))
                c.params.lazyLoading && !s && c.lazy && c.lazy.load(),
                  (c.params.allowSwipeToPrev = a),
                  (c.params.allowSwipeToNext = t),
                  c.params.onAfterResize && c.params.onAfterResize(c)
              }),
              (c.touchEventsDesktop = {
                start: 'mousedown',
                move: 'mousemove',
                end: 'mouseup'
              }),
              window.navigator.pointerEnabled
                ? (c.touchEventsDesktop = {
                    start: 'pointerdown',
                    move: 'pointermove',
                    end: 'pointerup'
                  })
                : window.navigator.msPointerEnabled &&
                  (c.touchEventsDesktop = {
                    start: 'MSPointerDown',
                    move: 'MSPointerMove',
                    end: 'MSPointerUp'
                  }),
              (c.touchEvents = {
                start:
                  c.support.touch || !c.params.simulateTouch
                    ? 'touchstart'
                    : c.touchEventsDesktop.start,
                move:
                  c.support.touch || !c.params.simulateTouch
                    ? 'touchmove'
                    : c.touchEventsDesktop.move,
                end:
                  c.support.touch || !c.params.simulateTouch
                    ? 'touchend'
                    : c.touchEventsDesktop.end
              }),
              (window.navigator.pointerEnabled ||
                window.navigator.msPointerEnabled) &&
                ('container' === c.params.touchEventsTarget
                  ? c.container
                  : c.wrapper
                ).addClass('swiper-wp8-' + c.params.direction),
              (c.initEvents = function (e) {
                var a = e ? 'off' : 'on',
                  s = e ? 'removeEventListener' : 'addEventListener',
                  i =
                    'container' === c.params.touchEventsTarget
                      ? c.container[0]
                      : c.wrapper[0],
                  r = c.support.touch ? i : document,
                  n = !!c.params.nested
                if (c.browser.ie)
                  i[s](c.touchEvents.start, c.onTouchStart, !1),
                    r[s](c.touchEvents.move, c.onTouchMove, n),
                    r[s](c.touchEvents.end, c.onTouchEnd, !1)
                else {
                  if (c.support.touch) {
                    var o = !(
                      'touchstart' !== c.touchEvents.start ||
                      !c.support.passiveListener ||
                      !c.params.passiveListeners
                    ) && { passive: !0, capture: !1 }
                    i[s](c.touchEvents.start, c.onTouchStart, o),
                      i[s](c.touchEvents.move, c.onTouchMove, n),
                      i[s](c.touchEvents.end, c.onTouchEnd, o)
                  }
                  ;((t.simulateTouch && !c.device.ios && !c.device.android) ||
                    (t.simulateTouch && !c.support.touch && c.device.ios)) &&
                    (i[s]('mousedown', c.onTouchStart, !1),
                    document[s]('mousemove', c.onTouchMove, n),
                    document[s]('mouseup', c.onTouchEnd, !1))
                }
                window[s]('resize', c.onResize),
                  c.params.nextButton &&
                    c.nextButton &&
                    c.nextButton.length > 0 &&
                    (c.nextButton[a]('click', c.onClickNext),
                    c.params.a11y &&
                      c.a11y &&
                      c.nextButton[a]('keydown', c.a11y.onEnterKey)),
                  c.params.prevButton &&
                    c.prevButton &&
                    c.prevButton.length > 0 &&
                    (c.prevButton[a]('click', c.onClickPrev),
                    c.params.a11y &&
                      c.a11y &&
                      c.prevButton[a]('keydown', c.a11y.onEnterKey)),
                  c.params.pagination &&
                    c.params.paginationClickable &&
                    (c.paginationContainer[a](
                      'click',
                      '.' + c.params.bulletClass,
                      c.onClickIndex
                    ),
                    c.params.a11y &&
                      c.a11y &&
                      c.paginationContainer[a](
                        'keydown',
                        '.' + c.params.bulletClass,
                        c.a11y.onEnterKey
                      )),
                  (c.params.preventClicks ||
                    c.params.preventClicksPropagation) &&
                    i[s]('click', c.preventClicks, !0)
              }),
              (c.attachEvents = function () {
                c.initEvents()
              }),
              (c.detachEvents = function () {
                c.initEvents(!0)
              }),
              (c.allowClick = !0),
              (c.preventClicks = function (e) {
                c.allowClick ||
                  (c.params.preventClicks && e.preventDefault(),
                  c.params.preventClicksPropagation &&
                    c.animating &&
                    (e.stopPropagation(), e.stopImmediatePropagation()))
              }),
              (c.onClickNext = function (e) {
                e.preventDefault(), (c.isEnd && !c.params.loop) || c.slideNext()
              }),
              (c.onClickPrev = function (e) {
                e.preventDefault(),
                  (c.isBeginning && !c.params.loop) || c.slidePrev()
              }),
              (c.onClickIndex = function (a) {
                a.preventDefault()
                var t = e(this).index() * c.params.slidesPerGroup
                c.params.loop && (t += c.loopedSlides), c.slideTo(t)
              }),
              (c.updateClickedSlide = function (a) {
                var t = B(a, '.' + c.params.slideClass),
                  s = !1
                if (t)
                  for (var i = 0; i < c.slides.length; i++)
                    c.slides[i] === t && (s = !0)
                if (!t || !s)
                  return (
                    (c.clickedSlide = void 0), void (c.clickedIndex = void 0)
                  )
                if (
                  ((c.clickedSlide = t),
                  (c.clickedIndex = e(t).index()),
                  c.params.slideToClickedSlide &&
                    void 0 !== c.clickedIndex &&
                    c.clickedIndex !== c.activeIndex)
                ) {
                  var r,
                    n = c.clickedIndex,
                    o =
                      'auto' === c.params.slidesPerView
                        ? c.currentSlidesPerView()
                        : c.params.slidesPerView
                  if (c.params.loop) {
                    if (c.animating) return
                    ;(r = parseInt(
                      e(c.clickedSlide).attr('data-swiper-slide-index'),
                      10
                    )),
                      c.params.centeredSlides
                        ? n < c.loopedSlides - o / 2 ||
                          n > c.slides.length - c.loopedSlides + o / 2
                          ? (c.fixLoop(),
                            (n = c.wrapper
                              .children(
                                '.' +
                                  c.params.slideClass +
                                  '[data-swiper-slide-index="' +
                                  r +
                                  '"]:not(.' +
                                  c.params.slideDuplicateClass +
                                  ')'
                              )
                              .eq(0)
                              .index()),
                            setTimeout(function () {
                              c.slideTo(n)
                            }, 0))
                          : c.slideTo(n)
                        : n > c.slides.length - o
                        ? (c.fixLoop(),
                          (n = c.wrapper
                            .children(
                              '.' +
                                c.params.slideClass +
                                '[data-swiper-slide-index="' +
                                r +
                                '"]:not(.' +
                                c.params.slideDuplicateClass +
                                ')'
                            )
                            .eq(0)
                            .index()),
                          setTimeout(function () {
                            c.slideTo(n)
                          }, 0))
                        : c.slideTo(n)
                  } else c.slideTo(n)
                }
              })
            var h,
              g,
              f,
              v,
              w,
              y,
              x,
              T,
              b,
              S,
              C,
              z,
              M = 'input, select, textarea, button, video',
              E = Date.now(),
              P = []
            for (var I in ((c.animating = !1),
            (c.touches = {
              startX: 0,
              startY: 0,
              currentX: 0,
              currentY: 0,
              diff: 0
            }),
            (c.onTouchStart = function (a) {
              if (
                (a.originalEvent && (a = a.originalEvent),
                (C = 'touchstart' === a.type) ||
                  !('which' in a) ||
                  3 !== a.which)
              )
                if (c.params.noSwiping && B(a, '.' + c.params.noSwipingClass))
                  c.allowClick = !0
                else if (
                  !c.params.swipeHandler ||
                  B(a, c.params.swipeHandler)
                ) {
                  var t = (c.touches.currentX =
                      'touchstart' === a.type
                        ? a.targetTouches[0].pageX
                        : a.pageX),
                    s = (c.touches.currentY =
                      'touchstart' === a.type
                        ? a.targetTouches[0].pageY
                        : a.pageY)
                  if (
                    !(
                      c.device.ios &&
                      c.params.iOSEdgeSwipeDetection &&
                      t <= c.params.iOSEdgeSwipeThreshold
                    )
                  ) {
                    if (
                      ((h = !0),
                      (g = !1),
                      (f = !0),
                      (w = void 0),
                      (z = void 0),
                      (c.touches.startX = t),
                      (c.touches.startY = s),
                      (v = Date.now()),
                      (c.allowClick = !0),
                      c.updateContainerSize(),
                      (c.swipeDirection = void 0),
                      c.params.threshold > 0 && (T = !1),
                      'touchstart' !== a.type)
                    ) {
                      var i = !0
                      e(a.target).is(M) && (i = !1),
                        document.activeElement &&
                          e(document.activeElement).is(M) &&
                          document.activeElement.blur(),
                        i && a.preventDefault()
                    }
                    c.emit('onTouchStart', c, a)
                  }
                }
            }),
            (c.onTouchMove = function (a) {
              if (
                (a.originalEvent && (a = a.originalEvent),
                !C || 'mousemove' !== a.type)
              ) {
                if (a.preventedByNestedSwiper)
                  return (
                    (c.touches.startX =
                      'touchmove' === a.type
                        ? a.targetTouches[0].pageX
                        : a.pageX),
                    void (c.touches.startY =
                      'touchmove' === a.type
                        ? a.targetTouches[0].pageY
                        : a.pageY)
                  )
                if (c.params.onlyExternal)
                  return (
                    (c.allowClick = !1),
                    void (
                      h &&
                      ((c.touches.startX = c.touches.currentX =
                        'touchmove' === a.type
                          ? a.targetTouches[0].pageX
                          : a.pageX),
                      (c.touches.startY = c.touches.currentY =
                        'touchmove' === a.type
                          ? a.targetTouches[0].pageY
                          : a.pageY),
                      (v = Date.now()))
                    )
                  )
                if (C && c.params.touchReleaseOnEdges && !c.params.loop)
                  if (c.isHorizontal()) {
                    if (
                      (c.touches.currentX < c.touches.startX &&
                        c.translate <= c.maxTranslate()) ||
                      (c.touches.currentX > c.touches.startX &&
                        c.translate >= c.minTranslate())
                    )
                      return
                  } else if (
                    (c.touches.currentY < c.touches.startY &&
                      c.translate <= c.maxTranslate()) ||
                    (c.touches.currentY > c.touches.startY &&
                      c.translate >= c.minTranslate())
                  )
                    return
                if (
                  C &&
                  document.activeElement &&
                  a.target === document.activeElement &&
                  e(a.target).is(M)
                )
                  return (g = !0), void (c.allowClick = !1)
                if (
                  (f && c.emit('onTouchMove', c, a),
                  !(a.targetTouches && a.targetTouches.length > 1))
                ) {
                  var s
                  if (
                    ((c.touches.currentX =
                      'touchmove' === a.type
                        ? a.targetTouches[0].pageX
                        : a.pageX),
                    (c.touches.currentY =
                      'touchmove' === a.type
                        ? a.targetTouches[0].pageY
                        : a.pageY),
                    void 0 === w)
                  )
                    (c.isHorizontal() &&
                      c.touches.currentY === c.touches.startY) ||
                    (!c.isHorizontal() &&
                      c.touches.currentX === c.touches.startX)
                      ? (w = !1)
                      : ((s =
                          (180 *
                            Math.atan2(
                              Math.abs(c.touches.currentY - c.touches.startY),
                              Math.abs(c.touches.currentX - c.touches.startX)
                            )) /
                          Math.PI),
                        (w = c.isHorizontal()
                          ? s > c.params.touchAngle
                          : 90 - s > c.params.touchAngle))
                  if (
                    (w && c.emit('onTouchMoveOpposite', c, a),
                    void 0 === z &&
                      ((c.touches.currentX === c.touches.startX &&
                        c.touches.currentY === c.touches.startY) ||
                        (z = !0)),
                    h)
                  )
                    if (w) h = !1
                    else if (z) {
                      ;(c.allowClick = !1),
                        c.emit('onSliderMove', c, a),
                        a.preventDefault(),
                        c.params.touchMoveStopPropagation &&
                          !c.params.nested &&
                          a.stopPropagation(),
                        g ||
                          (t.loop && c.fixLoop(),
                          (x = c.getWrapperTranslate()),
                          c.setWrapperTransition(0),
                          c.animating &&
                            c.wrapper.trigger(
                              'webkitTransitionEnd transitionend oTransitionEnd MSTransitionEnd msTransitionEnd'
                            ),
                          c.params.autoplay &&
                            c.autoplaying &&
                            (c.params.autoplayDisableOnInteraction
                              ? c.stopAutoplay()
                              : c.pauseAutoplay()),
                          (S = !1),
                          !c.params.grabCursor ||
                            (!0 !== c.params.allowSwipeToNext &&
                              !0 !== c.params.allowSwipeToPrev) ||
                            c.setGrabCursor(!0)),
                        (g = !0)
                      var i = (c.touches.diff = c.isHorizontal()
                        ? c.touches.currentX - c.touches.startX
                        : c.touches.currentY - c.touches.startY)
                      ;(i *= c.params.touchRatio),
                        c.rtl && (i = -i),
                        (c.swipeDirection = i > 0 ? 'prev' : 'next'),
                        (y = i + x)
                      var r = !0
                      if (
                        (i > 0 && y > c.minTranslate()
                          ? ((r = !1),
                            c.params.resistance &&
                              (y =
                                c.minTranslate() -
                                1 +
                                Math.pow(
                                  -c.minTranslate() + x + i,
                                  c.params.resistanceRatio
                                )))
                          : i < 0 &&
                            y < c.maxTranslate() &&
                            ((r = !1),
                            c.params.resistance &&
                              (y =
                                c.maxTranslate() +
                                1 -
                                Math.pow(
                                  c.maxTranslate() - x - i,
                                  c.params.resistanceRatio
                                ))),
                        r && (a.preventedByNestedSwiper = !0),
                        !c.params.allowSwipeToNext &&
                          'next' === c.swipeDirection &&
                          y < x &&
                          (y = x),
                        !c.params.allowSwipeToPrev &&
                          'prev' === c.swipeDirection &&
                          y > x &&
                          (y = x),
                        c.params.threshold > 0)
                      ) {
                        if (!(Math.abs(i) > c.params.threshold || T))
                          return void (y = x)
                        if (!T)
                          return (
                            (T = !0),
                            (c.touches.startX = c.touches.currentX),
                            (c.touches.startY = c.touches.currentY),
                            (y = x),
                            void (c.touches.diff = c.isHorizontal()
                              ? c.touches.currentX - c.touches.startX
                              : c.touches.currentY - c.touches.startY)
                          )
                      }
                      c.params.followFinger &&
                        ((c.params.freeMode || c.params.watchSlidesProgress) &&
                          c.updateActiveIndex(),
                        c.params.freeMode &&
                          (0 === P.length &&
                            P.push({
                              position:
                                c.touches[
                                  c.isHorizontal() ? 'startX' : 'startY'
                                ],
                              time: v
                            }),
                          P.push({
                            position:
                              c.touches[
                                c.isHorizontal() ? 'currentX' : 'currentY'
                              ],
                            time: new window.Date().getTime()
                          })),
                        c.updateProgress(y),
                        c.setWrapperTranslate(y))
                    }
                }
              }
            }),
            (c.onTouchEnd = function (a) {
              if (
                (a.originalEvent && (a = a.originalEvent),
                f && c.emit('onTouchEnd', c, a),
                (f = !1),
                h)
              ) {
                c.params.grabCursor &&
                  g &&
                  h &&
                  (!0 === c.params.allowSwipeToNext ||
                    !0 === c.params.allowSwipeToPrev) &&
                  c.setGrabCursor(!1)
                var t,
                  s = Date.now(),
                  i = s - v
                if (
                  (c.allowClick &&
                    (c.updateClickedSlide(a),
                    c.emit('onTap', c, a),
                    i < 300 &&
                      s - E > 300 &&
                      (b && clearTimeout(b),
                      (b = setTimeout(function () {
                        c &&
                          (c.params.paginationHide &&
                            c.paginationContainer.length > 0 &&
                            !e(a.target).hasClass(c.params.bulletClass) &&
                            c.paginationContainer.toggleClass(
                              c.params.paginationHiddenClass
                            ),
                          c.emit('onClick', c, a))
                      }, 300))),
                    i < 300 &&
                      s - E < 300 &&
                      (b && clearTimeout(b), c.emit('onDoubleTap', c, a))),
                  (E = Date.now()),
                  setTimeout(function () {
                    c && (c.allowClick = !0)
                  }, 0),
                  h && g && c.swipeDirection && 0 !== c.touches.diff && y !== x)
                )
                  if (
                    ((h = g = !1),
                    (t = c.params.followFinger
                      ? c.rtl
                        ? c.translate
                        : -c.translate
                      : -y),
                    c.params.freeMode)
                  ) {
                    if (t < -c.minTranslate())
                      return void c.slideTo(c.activeIndex)
                    if (t > -c.maxTranslate())
                      return void (c.slides.length < c.snapGrid.length
                        ? c.slideTo(c.snapGrid.length - 1)
                        : c.slideTo(c.slides.length - 1))
                    if (c.params.freeModeMomentum) {
                      if (P.length > 1) {
                        var r = P.pop(),
                          n = P.pop(),
                          o = r.position - n.position,
                          l = r.time - n.time
                        ;(c.velocity = o / l),
                          (c.velocity = c.velocity / 2),
                          Math.abs(c.velocity) <
                            c.params.freeModeMinimumVelocity &&
                            (c.velocity = 0),
                          (l > 150 ||
                            new window.Date().getTime() - r.time > 300) &&
                            (c.velocity = 0)
                      } else c.velocity = 0
                      ;(c.velocity =
                        c.velocity * c.params.freeModeMomentumVelocityRatio),
                        (P.length = 0)
                      var p = 1e3 * c.params.freeModeMomentumRatio,
                        d = c.velocity * p,
                        u = c.translate + d
                      c.rtl && (u = -u)
                      var m,
                        w = !1,
                        T =
                          20 *
                          Math.abs(c.velocity) *
                          c.params.freeModeMomentumBounceRatio
                      if (u < c.maxTranslate())
                        c.params.freeModeMomentumBounce
                          ? (u + c.maxTranslate() < -T &&
                              (u = c.maxTranslate() - T),
                            (m = c.maxTranslate()),
                            (w = !0),
                            (S = !0))
                          : (u = c.maxTranslate())
                      else if (u > c.minTranslate())
                        c.params.freeModeMomentumBounce
                          ? (u - c.minTranslate() > T &&
                              (u = c.minTranslate() + T),
                            (m = c.minTranslate()),
                            (w = !0),
                            (S = !0))
                          : (u = c.minTranslate())
                      else if (c.params.freeModeSticky) {
                        var C,
                          z = 0
                        for (z = 0; z < c.snapGrid.length; z += 1)
                          if (c.snapGrid[z] > -u) {
                            C = z
                            break
                          }
                        ;(u =
                          Math.abs(c.snapGrid[C] - u) <
                            Math.abs(c.snapGrid[C - 1] - u) ||
                          'next' === c.swipeDirection
                            ? c.snapGrid[C]
                            : c.snapGrid[C - 1]),
                          c.rtl || (u = -u)
                      }
                      if (0 !== c.velocity)
                        p = c.rtl
                          ? Math.abs((-u - c.translate) / c.velocity)
                          : Math.abs((u - c.translate) / c.velocity)
                      else if (c.params.freeModeSticky)
                        return void c.slideReset()
                      c.params.freeModeMomentumBounce && w
                        ? (c.updateProgress(m),
                          c.setWrapperTransition(p),
                          c.setWrapperTranslate(u),
                          c.onTransitionStart(),
                          (c.animating = !0),
                          c.wrapper.transitionEnd(function () {
                            c &&
                              S &&
                              (c.emit('onMomentumBounce', c),
                              c.setWrapperTransition(c.params.speed),
                              c.setWrapperTranslate(m),
                              c.wrapper.transitionEnd(function () {
                                c && c.onTransitionEnd()
                              }))
                          }))
                        : c.velocity
                        ? (c.updateProgress(u),
                          c.setWrapperTransition(p),
                          c.setWrapperTranslate(u),
                          c.onTransitionStart(),
                          c.animating ||
                            ((c.animating = !0),
                            c.wrapper.transitionEnd(function () {
                              c && c.onTransitionEnd()
                            })))
                        : c.updateProgress(u),
                        c.updateActiveIndex()
                    }
                    ;(!c.params.freeModeMomentum ||
                      i >= c.params.longSwipesMs) &&
                      (c.updateProgress(), c.updateActiveIndex())
                  } else {
                    var M,
                      I = 0,
                      k = c.slidesSizesGrid[0]
                    for (
                      M = 0;
                      M < c.slidesGrid.length;
                      M += c.params.slidesPerGroup
                    )
                      void 0 !== c.slidesGrid[M + c.params.slidesPerGroup]
                        ? t >= c.slidesGrid[M] &&
                          t < c.slidesGrid[M + c.params.slidesPerGroup] &&
                          ((I = M),
                          (k =
                            c.slidesGrid[M + c.params.slidesPerGroup] -
                            c.slidesGrid[M]))
                        : t >= c.slidesGrid[M] &&
                          ((I = M),
                          (k =
                            c.slidesGrid[c.slidesGrid.length - 1] -
                            c.slidesGrid[c.slidesGrid.length - 2]))
                    var L = (t - c.slidesGrid[I]) / k
                    if (i > c.params.longSwipesMs) {
                      if (!c.params.longSwipes)
                        return void c.slideTo(c.activeIndex)
                      'next' === c.swipeDirection &&
                        (L >= c.params.longSwipesRatio
                          ? c.slideTo(I + c.params.slidesPerGroup)
                          : c.slideTo(I)),
                        'prev' === c.swipeDirection &&
                          (L > 1 - c.params.longSwipesRatio
                            ? c.slideTo(I + c.params.slidesPerGroup)
                            : c.slideTo(I))
                    } else {
                      if (!c.params.shortSwipes)
                        return void c.slideTo(c.activeIndex)
                      'next' === c.swipeDirection &&
                        c.slideTo(I + c.params.slidesPerGroup),
                        'prev' === c.swipeDirection && c.slideTo(I)
                    }
                  }
                else h = g = !1
              }
            }),
            (c._slideTo = function (e, a) {
              return c.slideTo(e, a, !0, !0)
            }),
            (c.slideTo = function (e, a, t, s) {
              void 0 === t && (t = !0),
                void 0 === e && (e = 0),
                e < 0 && (e = 0),
                (c.snapIndex = Math.floor(e / c.params.slidesPerGroup)),
                c.snapIndex >= c.snapGrid.length &&
                  (c.snapIndex = c.snapGrid.length - 1)
              var i = -c.snapGrid[c.snapIndex]
              if (
                (c.params.autoplay &&
                  c.autoplaying &&
                  (s || !c.params.autoplayDisableOnInteraction
                    ? c.pauseAutoplay(a)
                    : c.stopAutoplay()),
                c.updateProgress(i),
                c.params.normalizeSlideIndex)
              )
                for (var r = 0; r < c.slidesGrid.length; r++)
                  -Math.floor(100 * i) >= Math.floor(100 * c.slidesGrid[r]) &&
                    (e = r)
              return (
                !(
                  !c.params.allowSwipeToNext &&
                  i < c.translate &&
                  i < c.minTranslate()
                ) &&
                !(
                  !c.params.allowSwipeToPrev &&
                  i > c.translate &&
                  i > c.maxTranslate() &&
                  (c.activeIndex || 0) !== e
                ) &&
                (void 0 === a && (a = c.params.speed),
                (c.previousIndex = c.activeIndex || 0),
                (c.activeIndex = e),
                c.updateRealIndex(),
                (c.rtl && -i === c.translate) || (!c.rtl && i === c.translate)
                  ? (c.params.autoHeight && c.updateAutoHeight(),
                    c.updateClasses(),
                    'slide' !== c.params.effect && c.setWrapperTranslate(i),
                    !1)
                  : (c.updateClasses(),
                    c.onTransitionStart(t),
                    0 === a || c.browser.lteIE9
                      ? (c.setWrapperTranslate(i),
                        c.setWrapperTransition(0),
                        c.onTransitionEnd(t))
                      : (c.setWrapperTranslate(i),
                        c.setWrapperTransition(a),
                        c.animating ||
                          ((c.animating = !0),
                          c.wrapper.transitionEnd(function () {
                            c && c.onTransitionEnd(t)
                          }))),
                    !0))
              )
            }),
            (c.onTransitionStart = function (e) {
              void 0 === e && (e = !0),
                c.params.autoHeight && c.updateAutoHeight(),
                c.lazy && c.lazy.onTransitionStart(),
                e &&
                  (c.emit('onTransitionStart', c),
                  c.activeIndex !== c.previousIndex &&
                    (c.emit('onSlideChangeStart', c),
                    c.activeIndex > c.previousIndex
                      ? c.emit('onSlideNextStart', c)
                      : c.emit('onSlidePrevStart', c)))
            }),
            (c.onTransitionEnd = function (e) {
              ;(c.animating = !1),
                c.setWrapperTransition(0),
                void 0 === e && (e = !0),
                c.lazy && c.lazy.onTransitionEnd(),
                e &&
                  (c.emit('onTransitionEnd', c),
                  c.activeIndex !== c.previousIndex &&
                    (c.emit('onSlideChangeEnd', c),
                    c.activeIndex > c.previousIndex
                      ? c.emit('onSlideNextEnd', c)
                      : c.emit('onSlidePrevEnd', c))),
                c.params.history &&
                  c.history &&
                  c.history.setHistory(c.params.history, c.activeIndex),
                c.params.hashnav && c.hashnav && c.hashnav.setHash()
            }),
            (c.slideNext = function (e, a, t) {
              if (c.params.loop) {
                if (c.animating) return !1
                c.fixLoop()
                c.container[0].clientLeft
                return c.slideTo(
                  c.activeIndex + c.params.slidesPerGroup,
                  a,
                  e,
                  t
                )
              }
              return c.slideTo(c.activeIndex + c.params.slidesPerGroup, a, e, t)
            }),
            (c._slideNext = function (e) {
              return c.slideNext(!0, e, !0)
            }),
            (c.slidePrev = function (e, a, t) {
              if (c.params.loop) {
                if (c.animating) return !1
                c.fixLoop()
                c.container[0].clientLeft
                return c.slideTo(c.activeIndex - 1, a, e, t)
              }
              return c.slideTo(c.activeIndex - 1, a, e, t)
            }),
            (c._slidePrev = function (e) {
              return c.slidePrev(!0, e, !0)
            }),
            (c.slideReset = function (e, a, t) {
              return c.slideTo(c.activeIndex, a, e)
            }),
            (c.disableTouchControl = function () {
              return (c.params.onlyExternal = !0), !0
            }),
            (c.enableTouchControl = function () {
              return (c.params.onlyExternal = !1), !0
            }),
            (c.setWrapperTransition = function (e, a) {
              c.wrapper.transition(e),
                'slide' !== c.params.effect &&
                  c.effects[c.params.effect] &&
                  c.effects[c.params.effect].setTransition(e),
                c.params.parallax && c.parallax && c.parallax.setTransition(e),
                c.params.scrollbar &&
                  c.scrollbar &&
                  c.scrollbar.setTransition(e),
                c.params.control &&
                  c.controller &&
                  c.controller.setTransition(e, a),
                c.emit('onSetTransition', c, e)
            }),
            (c.setWrapperTranslate = function (e, a, t) {
              var s = 0,
                i = 0
              c.isHorizontal() ? (s = c.rtl ? -e : e) : (i = e),
                c.params.roundLengths && ((s = L(s)), (i = L(i))),
                c.params.virtualTranslate ||
                  (c.support.transforms3d
                    ? c.wrapper.transform(
                        'translate3d(' + s + 'px, ' + i + 'px, 0px)'
                      )
                    : c.wrapper.transform(
                        'translate(' + s + 'px, ' + i + 'px)'
                      )),
                (c.translate = c.isHorizontal() ? s : i)
              var r = c.maxTranslate() - c.minTranslate()
              ;(0 === r ? 0 : (e - c.minTranslate()) / r) !== c.progress &&
                c.updateProgress(e),
                a && c.updateActiveIndex(),
                'slide' !== c.params.effect &&
                  c.effects[c.params.effect] &&
                  c.effects[c.params.effect].setTranslate(c.translate),
                c.params.parallax &&
                  c.parallax &&
                  c.parallax.setTranslate(c.translate),
                c.params.scrollbar &&
                  c.scrollbar &&
                  c.scrollbar.setTranslate(c.translate),
                c.params.control &&
                  c.controller &&
                  c.controller.setTranslate(c.translate, t),
                c.emit('onSetTranslate', c, c.translate)
            }),
            (c.getTranslate = function (e, a) {
              var t, s, i, r
              return (
                void 0 === a && (a = 'x'),
                c.params.virtualTranslate
                  ? c.rtl
                    ? -c.translate
                    : c.translate
                  : ((i = window.getComputedStyle(e, null)),
                    window.WebKitCSSMatrix
                      ? ((s = i.transform || i.webkitTransform).split(',')
                          .length > 6 &&
                          (s = s
                            .split(', ')
                            .map(function (e) {
                              return e.replace(',', '.')
                            })
                            .join(', ')),
                        (r = new window.WebKitCSSMatrix('none' === s ? '' : s)))
                      : (t = (r =
                          i.MozTransform ||
                          i.OTransform ||
                          i.MsTransform ||
                          i.msTransform ||
                          i.transform ||
                          i
                            .getPropertyValue('transform')
                            .replace('translate(', 'matrix(1, 0, 0, 1,'))
                          .toString()
                          .split(',')),
                    'x' === a &&
                      (s = window.WebKitCSSMatrix
                        ? r.m41
                        : 16 === t.length
                        ? parseFloat(t[12])
                        : parseFloat(t[4])),
                    'y' === a &&
                      (s = window.WebKitCSSMatrix
                        ? r.m42
                        : 16 === t.length
                        ? parseFloat(t[13])
                        : parseFloat(t[5])),
                    c.rtl && s && (s = -s),
                    s || 0)
              )
            }),
            (c.getWrapperTranslate = function (e) {
              return (
                void 0 === e && (e = c.isHorizontal() ? 'x' : 'y'),
                c.getTranslate(c.wrapper[0], e)
              )
            }),
            (c.observers = []),
            (c.initObservers = function () {
              if (c.params.observeParents)
                for (var e = c.container.parents(), a = 0; a < e.length; a++)
                  H(e[a])
              H(c.container[0], { childList: !1 }),
                H(c.wrapper[0], { attributes: !1 })
            }),
            (c.disconnectObservers = function () {
              for (var e = 0; e < c.observers.length; e++)
                c.observers[e].disconnect()
              c.observers = []
            }),
            (c.createLoop = function () {
              c.wrapper
                .children(
                  '.' + c.params.slideClass + '.' + c.params.slideDuplicateClass
                )
                .remove()
              var a = c.wrapper.children('.' + c.params.slideClass)
              'auto' !== c.params.slidesPerView ||
                c.params.loopedSlides ||
                (c.params.loopedSlides = a.length),
                (c.loopedSlides = parseInt(
                  c.params.loopedSlides || c.params.slidesPerView,
                  10
                )),
                (c.loopedSlides =
                  c.loopedSlides + c.params.loopAdditionalSlides),
                c.loopedSlides > a.length && (c.loopedSlides = a.length)
              var t,
                s = [],
                i = []
              for (
                a.each(function (t, r) {
                  var n = e(this)
                  t < c.loopedSlides && i.push(r),
                    t < a.length && t >= a.length - c.loopedSlides && s.push(r),
                    n.attr('data-swiper-slide-index', t)
                }),
                  t = 0;
                t < i.length;
                t++
              )
                c.wrapper.append(
                  e(i[t].cloneNode(!0)).addClass(c.params.slideDuplicateClass)
                )
              for (t = s.length - 1; t >= 0; t--)
                c.wrapper.prepend(
                  e(s[t].cloneNode(!0)).addClass(c.params.slideDuplicateClass)
                )
            }),
            (c.destroyLoop = function () {
              c.wrapper
                .children(
                  '.' + c.params.slideClass + '.' + c.params.slideDuplicateClass
                )
                .remove(),
                c.slides.removeAttr('data-swiper-slide-index')
            }),
            (c.reLoop = function (e) {
              var a = c.activeIndex - c.loopedSlides
              c.destroyLoop(),
                c.createLoop(),
                c.updateSlidesSize(),
                e && c.slideTo(a + c.loopedSlides, 0, !1)
            }),
            (c.fixLoop = function () {
              var e
              c.activeIndex < c.loopedSlides
                ? ((e = c.slides.length - 3 * c.loopedSlides + c.activeIndex),
                  (e += c.loopedSlides),
                  c.slideTo(e, 0, !1, !0))
                : (('auto' === c.params.slidesPerView &&
                    c.activeIndex >= 2 * c.loopedSlides) ||
                    c.activeIndex >
                      c.slides.length - 2 * c.params.slidesPerView) &&
                  ((e = -c.slides.length + c.activeIndex + c.loopedSlides),
                  (e += c.loopedSlides),
                  c.slideTo(e, 0, !1, !0))
            }),
            (c.appendSlide = function (e) {
              if (
                (c.params.loop && c.destroyLoop(),
                'object' == typeof e && e.length)
              )
                for (var a = 0; a < e.length; a++)
                  e[a] && c.wrapper.append(e[a])
              else c.wrapper.append(e)
              c.params.loop && c.createLoop(),
                (c.params.observer && c.support.observer) || c.update(!0)
            }),
            (c.prependSlide = function (e) {
              c.params.loop && c.destroyLoop()
              var a = c.activeIndex + 1
              if ('object' == typeof e && e.length) {
                for (var t = 0; t < e.length; t++)
                  e[t] && c.wrapper.prepend(e[t])
                a = c.activeIndex + e.length
              } else c.wrapper.prepend(e)
              c.params.loop && c.createLoop(),
                (c.params.observer && c.support.observer) || c.update(!0),
                c.slideTo(a, 0, !1)
            }),
            (c.removeSlide = function (e) {
              c.params.loop &&
                (c.destroyLoop(),
                (c.slides = c.wrapper.children('.' + c.params.slideClass)))
              var a,
                t = c.activeIndex
              if ('object' == typeof e && e.length) {
                for (var s = 0; s < e.length; s++)
                  (a = e[s]),
                    c.slides[a] && c.slides.eq(a).remove(),
                    a < t && t--
                t = Math.max(t, 0)
              } else
                (a = e),
                  c.slides[a] && c.slides.eq(a).remove(),
                  a < t && t--,
                  (t = Math.max(t, 0))
              c.params.loop && c.createLoop(),
                (c.params.observer && c.support.observer) || c.update(!0),
                c.params.loop
                  ? c.slideTo(t + c.loopedSlides, 0, !1)
                  : c.slideTo(t, 0, !1)
            }),
            (c.removeAllSlides = function () {
              for (var e = [], a = 0; a < c.slides.length; a++) e.push(a)
              c.removeSlide(e)
            }),
            (c.effects = {
              fade: {
                setTranslate: function () {
                  for (var e = 0; e < c.slides.length; e++) {
                    var a = c.slides.eq(e),
                      t = -a[0].swiperSlideOffset
                    c.params.virtualTranslate || (t -= c.translate)
                    var s = 0
                    c.isHorizontal() || ((s = t), (t = 0))
                    var i = c.params.fade.crossFade
                      ? Math.max(1 - Math.abs(a[0].progress), 0)
                      : 1 + Math.min(Math.max(a[0].progress, -1), 0)
                    a.css({ opacity: i }).transform(
                      'translate3d(' + t + 'px, ' + s + 'px, 0px)'
                    )
                  }
                },
                setTransition: function (e) {
                  if (
                    (c.slides.transition(e),
                    c.params.virtualTranslate && 0 !== e)
                  ) {
                    var a = !1
                    c.slides.transitionEnd(function () {
                      if (!a && c) {
                        ;(a = !0), (c.animating = !1)
                        for (
                          var e = [
                              'webkitTransitionEnd',
                              'transitionend',
                              'oTransitionEnd',
                              'MSTransitionEnd',
                              'msTransitionEnd'
                            ],
                            t = 0;
                          t < e.length;
                          t++
                        )
                          c.wrapper.trigger(e[t])
                      }
                    })
                  }
                }
              },
              flip: {
                setTranslate: function () {
                  for (var a = 0; a < c.slides.length; a++) {
                    var t = c.slides.eq(a),
                      s = t[0].progress
                    c.params.flip.limitRotation &&
                      (s = Math.max(Math.min(t[0].progress, 1), -1))
                    var i = -180 * s,
                      r = 0,
                      n = -t[0].swiperSlideOffset,
                      o = 0
                    if (
                      (c.isHorizontal()
                        ? c.rtl && (i = -i)
                        : ((o = n), (n = 0), (r = -i), (i = 0)),
                      (t[0].style.zIndex =
                        -Math.abs(Math.round(s)) + c.slides.length),
                      c.params.flip.slideShadows)
                    ) {
                      var l = c.isHorizontal()
                          ? t.find('.swiper-slide-shadow-left')
                          : t.find('.swiper-slide-shadow-top'),
                        p = c.isHorizontal()
                          ? t.find('.swiper-slide-shadow-right')
                          : t.find('.swiper-slide-shadow-bottom')
                      0 === l.length &&
                        ((l = e(
                          '<div class="swiper-slide-shadow-' +
                            (c.isHorizontal() ? 'left' : 'top') +
                            '"></div>'
                        )),
                        t.append(l)),
                        0 === p.length &&
                          ((p = e(
                            '<div class="swiper-slide-shadow-' +
                              (c.isHorizontal() ? 'right' : 'bottom') +
                              '"></div>'
                          )),
                          t.append(p)),
                        l.length && (l[0].style.opacity = Math.max(-s, 0)),
                        p.length && (p[0].style.opacity = Math.max(s, 0))
                    }
                    t.transform(
                      'translate3d(' +
                        n +
                        'px, ' +
                        o +
                        'px, 0px) rotateX(' +
                        r +
                        'deg) rotateY(' +
                        i +
                        'deg)'
                    )
                  }
                },
                setTransition: function (a) {
                  if (
                    (c.slides
                      .transition(a)
                      .find(
                        '.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left'
                      )
                      .transition(a),
                    c.params.virtualTranslate && 0 !== a)
                  ) {
                    var t = !1
                    c.slides.eq(c.activeIndex).transitionEnd(function () {
                      if (
                        !t &&
                        c &&
                        e(this).hasClass(c.params.slideActiveClass)
                      ) {
                        ;(t = !0), (c.animating = !1)
                        for (
                          var a = [
                              'webkitTransitionEnd',
                              'transitionend',
                              'oTransitionEnd',
                              'MSTransitionEnd',
                              'msTransitionEnd'
                            ],
                            s = 0;
                          s < a.length;
                          s++
                        )
                          c.wrapper.trigger(a[s])
                      }
                    })
                  }
                }
              },
              cube: {
                setTranslate: function () {
                  var a,
                    t = 0
                  c.params.cube.shadow &&
                    (c.isHorizontal()
                      ? (0 ===
                          (a = c.wrapper.find('.swiper-cube-shadow')).length &&
                          ((a = e('<div class="swiper-cube-shadow"></div>')),
                          c.wrapper.append(a)),
                        a.css({ height: c.width + 'px' }))
                      : 0 ===
                          (a = c.container.find('.swiper-cube-shadow'))
                            .length &&
                        ((a = e('<div class="swiper-cube-shadow"></div>')),
                        c.container.append(a)))
                  for (var s = 0; s < c.slides.length; s++) {
                    var i = c.slides.eq(s),
                      r = 90 * s,
                      n = Math.floor(r / 360)
                    c.rtl && ((r = -r), (n = Math.floor(-r / 360)))
                    var o = Math.max(Math.min(i[0].progress, 1), -1),
                      l = 0,
                      p = 0,
                      d = 0
                    s % 4 == 0
                      ? ((l = 4 * -n * c.size), (d = 0))
                      : (s - 1) % 4 == 0
                      ? ((l = 0), (d = 4 * -n * c.size))
                      : (s - 2) % 4 == 0
                      ? ((l = c.size + 4 * n * c.size), (d = c.size))
                      : (s - 3) % 4 == 0 &&
                        ((l = -c.size), (d = 3 * c.size + 4 * c.size * n)),
                      c.rtl && (l = -l),
                      c.isHorizontal() || ((p = l), (l = 0))
                    var u =
                      'rotateX(' +
                      (c.isHorizontal() ? 0 : -r) +
                      'deg) rotateY(' +
                      (c.isHorizontal() ? r : 0) +
                      'deg) translate3d(' +
                      l +
                      'px, ' +
                      p +
                      'px, ' +
                      d +
                      'px)'
                    if (
                      (o <= 1 &&
                        o > -1 &&
                        ((t = 90 * s + 90 * o),
                        c.rtl && (t = 90 * -s - 90 * o)),
                      i.transform(u),
                      c.params.cube.slideShadows)
                    ) {
                      var m = c.isHorizontal()
                          ? i.find('.swiper-slide-shadow-left')
                          : i.find('.swiper-slide-shadow-top'),
                        h = c.isHorizontal()
                          ? i.find('.swiper-slide-shadow-right')
                          : i.find('.swiper-slide-shadow-bottom')
                      0 === m.length &&
                        ((m = e(
                          '<div class="swiper-slide-shadow-' +
                            (c.isHorizontal() ? 'left' : 'top') +
                            '"></div>'
                        )),
                        i.append(m)),
                        0 === h.length &&
                          ((h = e(
                            '<div class="swiper-slide-shadow-' +
                              (c.isHorizontal() ? 'right' : 'bottom') +
                              '"></div>'
                          )),
                          i.append(h)),
                        m.length && (m[0].style.opacity = Math.max(-o, 0)),
                        h.length && (h[0].style.opacity = Math.max(o, 0))
                    }
                  }
                  if (
                    (c.wrapper.css({
                      '-webkit-transform-origin':
                        '50% 50% -' + c.size / 2 + 'px',
                      '-moz-transform-origin': '50% 50% -' + c.size / 2 + 'px',
                      '-ms-transform-origin': '50% 50% -' + c.size / 2 + 'px',
                      'transform-origin': '50% 50% -' + c.size / 2 + 'px'
                    }),
                    c.params.cube.shadow)
                  )
                    if (c.isHorizontal())
                      a.transform(
                        'translate3d(0px, ' +
                          (c.width / 2 + c.params.cube.shadowOffset) +
                          'px, ' +
                          -c.width / 2 +
                          'px) rotateX(90deg) rotateZ(0deg) scale(' +
                          c.params.cube.shadowScale +
                          ')'
                      )
                    else {
                      var g = Math.abs(t) - 90 * Math.floor(Math.abs(t) / 90),
                        f =
                          1.5 -
                          (Math.sin((2 * g * Math.PI) / 360) / 2 +
                            Math.cos((2 * g * Math.PI) / 360) / 2),
                        v = c.params.cube.shadowScale,
                        w = c.params.cube.shadowScale / f,
                        y = c.params.cube.shadowOffset
                      a.transform(
                        'scale3d(' +
                          v +
                          ', 1, ' +
                          w +
                          ') translate3d(0px, ' +
                          (c.height / 2 + y) +
                          'px, ' +
                          -c.height / 2 / w +
                          'px) rotateX(-90deg)'
                      )
                    }
                  var x = c.isSafari || c.isUiWebView ? -c.size / 2 : 0
                  c.wrapper.transform(
                    'translate3d(0px,0,' +
                      x +
                      'px) rotateX(' +
                      (c.isHorizontal() ? 0 : t) +
                      'deg) rotateY(' +
                      (c.isHorizontal() ? -t : 0) +
                      'deg)'
                  )
                },
                setTransition: function (e) {
                  c.slides
                    .transition(e)
                    .find(
                      '.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left'
                    )
                    .transition(e),
                    c.params.cube.shadow &&
                      !c.isHorizontal() &&
                      c.container.find('.swiper-cube-shadow').transition(e)
                }
              },
              coverflow: {
                setTranslate: function () {
                  for (
                    var a = c.translate,
                      t = c.isHorizontal()
                        ? -a + c.width / 2
                        : -a + c.height / 2,
                      s = c.isHorizontal()
                        ? c.params.coverflow.rotate
                        : -c.params.coverflow.rotate,
                      i = c.params.coverflow.depth,
                      r = 0,
                      n = c.slides.length;
                    r < n;
                    r++
                  ) {
                    var o = c.slides.eq(r),
                      l = c.slidesSizesGrid[r],
                      p =
                        ((t - o[0].swiperSlideOffset - l / 2) / l) *
                        c.params.coverflow.modifier,
                      d = c.isHorizontal() ? s * p : 0,
                      u = c.isHorizontal() ? 0 : s * p,
                      m = -i * Math.abs(p),
                      h = c.isHorizontal() ? 0 : c.params.coverflow.stretch * p,
                      g = c.isHorizontal() ? c.params.coverflow.stretch * p : 0
                    Math.abs(g) < 0.001 && (g = 0),
                      Math.abs(h) < 0.001 && (h = 0),
                      Math.abs(m) < 0.001 && (m = 0),
                      Math.abs(d) < 0.001 && (d = 0),
                      Math.abs(u) < 0.001 && (u = 0)
                    var f =
                      'translate3d(' +
                      g +
                      'px,' +
                      h +
                      'px,' +
                      m +
                      'px)  rotateX(' +
                      u +
                      'deg) rotateY(' +
                      d +
                      'deg)'
                    if (
                      (o.transform(f),
                      (o[0].style.zIndex = 1 - Math.abs(Math.round(p))),
                      c.params.coverflow.slideShadows)
                    ) {
                      var v = c.isHorizontal()
                          ? o.find('.swiper-slide-shadow-left')
                          : o.find('.swiper-slide-shadow-top'),
                        w = c.isHorizontal()
                          ? o.find('.swiper-slide-shadow-right')
                          : o.find('.swiper-slide-shadow-bottom')
                      0 === v.length &&
                        ((v = e(
                          '<div class="swiper-slide-shadow-' +
                            (c.isHorizontal() ? 'left' : 'top') +
                            '"></div>'
                        )),
                        o.append(v)),
                        0 === w.length &&
                          ((w = e(
                            '<div class="swiper-slide-shadow-' +
                              (c.isHorizontal() ? 'right' : 'bottom') +
                              '"></div>'
                          )),
                          o.append(w)),
                        v.length && (v[0].style.opacity = p > 0 ? p : 0),
                        w.length && (w[0].style.opacity = -p > 0 ? -p : 0)
                    }
                  }
                  c.browser.ie &&
                    (c.wrapper[0].style.perspectiveOrigin = t + 'px 50%')
                },
                setTransition: function (e) {
                  c.slides
                    .transition(e)
                    .find(
                      '.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left'
                    )
                    .transition(e)
                }
              }
            }),
            (c.lazy = {
              initialImageLoaded: !1,
              loadImageInSlide: function (a, t) {
                if (
                  void 0 !== a &&
                  (void 0 === t && (t = !0), 0 !== c.slides.length)
                ) {
                  var s = c.slides.eq(a),
                    i = s.find(
                      '.' +
                        c.params.lazyLoadingClass +
                        ':not(.' +
                        c.params.lazyStatusLoadedClass +
                        '):not(.' +
                        c.params.lazyStatusLoadingClass +
                        ')'
                    )
                  !s.hasClass(c.params.lazyLoadingClass) ||
                    s.hasClass(c.params.lazyStatusLoadedClass) ||
                    s.hasClass(c.params.lazyStatusLoadingClass) ||
                    (i = i.add(s[0])),
                    0 !== i.length &&
                      i.each(function () {
                        var a = e(this)
                        a.addClass(c.params.lazyStatusLoadingClass)
                        var i = a.attr('data-background'),
                          r = a.attr('data-src'),
                          n = a.attr('data-srcset'),
                          o = a.attr('data-sizes')
                        c.loadImage(a[0], r || i, n, o, !1, function () {
                          if (void 0 !== c && null !== c && c) {
                            if (
                              (i
                                ? (a.css(
                                    'background-image',
                                    'url("' + i + '")'
                                  ),
                                  a.removeAttr('data-background'))
                                : (n &&
                                    (a.attr('srcset', n),
                                    a.removeAttr('data-srcset')),
                                  o &&
                                    (a.attr('sizes', o),
                                    a.removeAttr('data-sizes')),
                                  r &&
                                    (a.attr('src', r),
                                    a.removeAttr('data-src'))),
                              a
                                .addClass(c.params.lazyStatusLoadedClass)
                                .removeClass(c.params.lazyStatusLoadingClass),
                              s
                                .find(
                                  '.' +
                                    c.params.lazyPreloaderClass +
                                    ', .' +
                                    c.params.preloaderClass
                                )
                                .remove(),
                              c.params.loop && t)
                            ) {
                              var e = s.attr('data-swiper-slide-index')
                              if (s.hasClass(c.params.slideDuplicateClass)) {
                                var l = c.wrapper.children(
                                  '[data-swiper-slide-index="' +
                                    e +
                                    '"]:not(.' +
                                    c.params.slideDuplicateClass +
                                    ')'
                                )
                                c.lazy.loadImageInSlide(l.index(), !1)
                              } else {
                                var p = c.wrapper.children(
                                  '.' +
                                    c.params.slideDuplicateClass +
                                    '[data-swiper-slide-index="' +
                                    e +
                                    '"]'
                                )
                                c.lazy.loadImageInSlide(p.index(), !1)
                              }
                            }
                            c.emit('onLazyImageReady', c, s[0], a[0])
                          }
                        }),
                          c.emit('onLazyImageLoad', c, s[0], a[0])
                      })
                }
              },
              load: function () {
                var a,
                  t = c.params.slidesPerView
                if (
                  ('auto' === t && (t = 0),
                  c.lazy.initialImageLoaded || (c.lazy.initialImageLoaded = !0),
                  c.params.watchSlidesVisibility)
                )
                  c.wrapper
                    .children('.' + c.params.slideVisibleClass)
                    .each(function () {
                      c.lazy.loadImageInSlide(e(this).index())
                    })
                else if (t > 1)
                  for (a = c.activeIndex; a < c.activeIndex + t; a++)
                    c.slides[a] && c.lazy.loadImageInSlide(a)
                else c.lazy.loadImageInSlide(c.activeIndex)
                if (c.params.lazyLoadingInPrevNext)
                  if (
                    t > 1 ||
                    (c.params.lazyLoadingInPrevNextAmount &&
                      c.params.lazyLoadingInPrevNextAmount > 1)
                  ) {
                    var s = c.params.lazyLoadingInPrevNextAmount,
                      i = t,
                      r = Math.min(
                        c.activeIndex + i + Math.max(s, i),
                        c.slides.length
                      ),
                      n = Math.max(c.activeIndex - Math.max(i, s), 0)
                    for (a = c.activeIndex + t; a < r; a++)
                      c.slides[a] && c.lazy.loadImageInSlide(a)
                    for (a = n; a < c.activeIndex; a++)
                      c.slides[a] && c.lazy.loadImageInSlide(a)
                  } else {
                    var o = c.wrapper.children('.' + c.params.slideNextClass)
                    o.length > 0 && c.lazy.loadImageInSlide(o.index())
                    var l = c.wrapper.children('.' + c.params.slidePrevClass)
                    l.length > 0 && c.lazy.loadImageInSlide(l.index())
                  }
              },
              onTransitionStart: function () {
                c.params.lazyLoading &&
                  (c.params.lazyLoadingOnTransitionStart ||
                    (!c.params.lazyLoadingOnTransitionStart &&
                      !c.lazy.initialImageLoaded)) &&
                  c.lazy.load()
              },
              onTransitionEnd: function () {
                c.params.lazyLoading &&
                  !c.params.lazyLoadingOnTransitionStart &&
                  c.lazy.load()
              }
            }),
            (c.scrollbar = {
              isTouched: !1,
              setDragPosition: function (e) {
                var a = c.scrollbar,
                  t =
                    (c.isHorizontal()
                      ? 'touchstart' === e.type || 'touchmove' === e.type
                        ? e.targetTouches[0].pageX
                        : e.pageX || e.clientX
                      : 'touchstart' === e.type || 'touchmove' === e.type
                      ? e.targetTouches[0].pageY
                      : e.pageY || e.clientY) -
                    a.track.offset()[c.isHorizontal() ? 'left' : 'top'] -
                    a.dragSize / 2,
                  s = -c.minTranslate() * a.moveDivider,
                  i = -c.maxTranslate() * a.moveDivider
                t < s ? (t = s) : t > i && (t = i),
                  (t = -t / a.moveDivider),
                  c.updateProgress(t),
                  c.setWrapperTranslate(t, !0)
              },
              dragStart: function (e) {
                var a = c.scrollbar
                ;(a.isTouched = !0),
                  e.preventDefault(),
                  e.stopPropagation(),
                  a.setDragPosition(e),
                  clearTimeout(a.dragTimeout),
                  a.track.transition(0),
                  c.params.scrollbarHide && a.track.css('opacity', 1),
                  c.wrapper.transition(100),
                  a.drag.transition(100),
                  c.emit('onScrollbarDragStart', c)
              },
              dragMove: function (e) {
                var a = c.scrollbar
                a.isTouched &&
                  (e.preventDefault ? e.preventDefault() : (e.returnValue = !1),
                  a.setDragPosition(e),
                  c.wrapper.transition(0),
                  a.track.transition(0),
                  a.drag.transition(0),
                  c.emit('onScrollbarDragMove', c))
              },
              dragEnd: function (e) {
                var a = c.scrollbar
                a.isTouched &&
                  ((a.isTouched = !1),
                  c.params.scrollbarHide &&
                    (clearTimeout(a.dragTimeout),
                    (a.dragTimeout = setTimeout(function () {
                      a.track.css('opacity', 0), a.track.transition(400)
                    }, 1e3))),
                  c.emit('onScrollbarDragEnd', c),
                  c.params.scrollbarSnapOnRelease && c.slideReset())
              },
              draggableEvents:
                !1 !== c.params.simulateTouch || c.support.touch
                  ? c.touchEvents
                  : c.touchEventsDesktop,
              enableDraggable: function () {
                var a = c.scrollbar,
                  t = c.support.touch ? a.track : document
                e(a.track).on(a.draggableEvents.start, a.dragStart),
                  e(t).on(a.draggableEvents.move, a.dragMove),
                  e(t).on(a.draggableEvents.end, a.dragEnd)
              },
              disableDraggable: function () {
                var a = c.scrollbar,
                  t = c.support.touch ? a.track : document
                e(a.track).off(a.draggableEvents.start, a.dragStart),
                  e(t).off(a.draggableEvents.move, a.dragMove),
                  e(t).off(a.draggableEvents.end, a.dragEnd)
              },
              set: function () {
                if (c.params.scrollbar) {
                  var a = c.scrollbar
                  ;(a.track = e(c.params.scrollbar)),
                    c.params.uniqueNavElements &&
                      'string' == typeof c.params.scrollbar &&
                      a.track.length > 1 &&
                      1 === c.container.find(c.params.scrollbar).length &&
                      (a.track = c.container.find(c.params.scrollbar)),
                    (a.drag = a.track.find('.swiper-scrollbar-drag')),
                    0 === a.drag.length &&
                      ((a.drag = e(
                        '<div class="swiper-scrollbar-drag"></div>'
                      )),
                      a.track.append(a.drag)),
                    (a.drag[0].style.width = ''),
                    (a.drag[0].style.height = ''),
                    (a.trackSize = c.isHorizontal()
                      ? a.track[0].offsetWidth
                      : a.track[0].offsetHeight),
                    (a.divider = c.size / c.virtualSize),
                    (a.moveDivider = a.divider * (a.trackSize / c.size)),
                    (a.dragSize = a.trackSize * a.divider),
                    c.isHorizontal()
                      ? (a.drag[0].style.width = a.dragSize + 'px')
                      : (a.drag[0].style.height = a.dragSize + 'px'),
                    a.divider >= 1
                      ? (a.track[0].style.display = 'none')
                      : (a.track[0].style.display = ''),
                    c.params.scrollbarHide && (a.track[0].style.opacity = 0)
                }
              },
              setTranslate: function () {
                if (c.params.scrollbar) {
                  var e,
                    a = c.scrollbar,
                    t = (c.translate, a.dragSize)
                  ;(e = (a.trackSize - a.dragSize) * c.progress),
                    c.rtl && c.isHorizontal()
                      ? (e = -e) > 0
                        ? ((t = a.dragSize - e), (e = 0))
                        : -e + a.dragSize > a.trackSize && (t = a.trackSize + e)
                      : e < 0
                      ? ((t = a.dragSize + e), (e = 0))
                      : e + a.dragSize > a.trackSize && (t = a.trackSize - e),
                    c.isHorizontal()
                      ? (c.support.transforms3d
                          ? a.drag.transform('translate3d(' + e + 'px, 0, 0)')
                          : a.drag.transform('translateX(' + e + 'px)'),
                        (a.drag[0].style.width = t + 'px'))
                      : (c.support.transforms3d
                          ? a.drag.transform('translate3d(0px, ' + e + 'px, 0)')
                          : a.drag.transform('translateY(' + e + 'px)'),
                        (a.drag[0].style.height = t + 'px')),
                    c.params.scrollbarHide &&
                      (clearTimeout(a.timeout),
                      (a.track[0].style.opacity = 1),
                      (a.timeout = setTimeout(function () {
                        ;(a.track[0].style.opacity = 0), a.track.transition(400)
                      }, 1e3)))
                }
              },
              setTransition: function (e) {
                c.params.scrollbar && c.scrollbar.drag.transition(e)
              }
            }),
            (c.controller = {
              LinearSpline: function (e, a) {
                var t,
                  s,
                  i,
                  r,
                  n,
                  o = function (e, a) {
                    for (s = -1, t = e.length; t - s > 1; )
                      e[(i = (t + s) >> 1)] <= a ? (s = i) : (t = i)
                    return t
                  }
                ;(this.x = e), (this.y = a), (this.lastIndex = e.length - 1)
                this.x.length
                this.interpolate = function (e) {
                  return e
                    ? ((n = o(this.x, e)),
                      (r = n - 1),
                      ((e - this.x[r]) * (this.y[n] - this.y[r])) /
                        (this.x[n] - this.x[r]) +
                        this.y[r])
                    : 0
                }
              },
              getInterpolateFunction: function (e) {
                c.controller.spline ||
                  (c.controller.spline = c.params.loop
                    ? new c.controller.LinearSpline(c.slidesGrid, e.slidesGrid)
                    : new c.controller.LinearSpline(c.snapGrid, e.snapGrid))
              },
              setTranslate: function (e, a) {
                var t,
                  i,
                  r = c.params.control
                function n(a) {
                  ;(e =
                    a.rtl && 'horizontal' === a.params.direction
                      ? -c.translate
                      : c.translate),
                    'slide' === c.params.controlBy &&
                      (c.controller.getInterpolateFunction(a),
                      (i = -c.controller.spline.interpolate(-e))),
                    (i && 'container' !== c.params.controlBy) ||
                      ((t =
                        (a.maxTranslate() - a.minTranslate()) /
                        (c.maxTranslate() - c.minTranslate())),
                      (i = (e - c.minTranslate()) * t + a.minTranslate())),
                    c.params.controlInverse && (i = a.maxTranslate() - i),
                    a.updateProgress(i),
                    a.setWrapperTranslate(i, !1, c),
                    a.updateActiveIndex()
                }
                if (Array.isArray(r))
                  for (var o = 0; o < r.length; o++)
                    r[o] !== a && r[o] instanceof s && n(r[o])
                else r instanceof s && a !== r && n(r)
              },
              setTransition: function (e, a) {
                var t,
                  i = c.params.control
                function r(a) {
                  a.setWrapperTransition(e, c),
                    0 !== e &&
                      (a.onTransitionStart(),
                      a.wrapper.transitionEnd(function () {
                        i &&
                          (a.params.loop &&
                            'slide' === c.params.controlBy &&
                            a.fixLoop(),
                          a.onTransitionEnd())
                      }))
                }
                if (Array.isArray(i))
                  for (t = 0; t < i.length; t++)
                    i[t] !== a && i[t] instanceof s && r(i[t])
                else i instanceof s && a !== i && r(i)
              }
            }),
            (c.hashnav = {
              onHashCange: function (e, a) {
                var t = document.location.hash.replace('#', '')
                t !== c.slides.eq(c.activeIndex).attr('data-hash') &&
                  c.slideTo(
                    c.wrapper
                      .children(
                        '.' + c.params.slideClass + '[data-hash="' + t + '"]'
                      )
                      .index()
                  )
              },
              attachEvents: function (a) {
                var t = a ? 'off' : 'on'
                e(window)[t]('hashchange', c.hashnav.onHashCange)
              },
              setHash: function () {
                if (c.hashnav.initialized && c.params.hashnav)
                  if (
                    c.params.replaceState &&
                    window.history &&
                    window.history.replaceState
                  )
                    window.history.replaceState(
                      null,
                      null,
                      '#' + c.slides.eq(c.activeIndex).attr('data-hash') || !1
                    )
                  else {
                    var e = c.slides.eq(c.activeIndex),
                      a = e.attr('data-hash') || e.attr('data-history')
                    document.location.hash = a || ''
                  }
              },
              init: function () {
                if (c.params.hashnav && !c.params.history) {
                  c.hashnav.initialized = !0
                  var e = document.location.hash.replace('#', '')
                  if (e)
                    for (var a = 0, t = c.slides.length; a < t; a++) {
                      var s = c.slides.eq(a)
                      if (
                        (s.attr('data-hash') || s.attr('data-history')) === e &&
                        !s.hasClass(c.params.slideDuplicateClass)
                      ) {
                        var i = s.index()
                        c.slideTo(i, 0, c.params.runCallbacksOnInit, !0)
                      }
                    }
                  c.params.hashnavWatchState && c.hashnav.attachEvents()
                }
              },
              destroy: function () {
                c.params.hashnavWatchState && c.hashnav.attachEvents(!0)
              }
            }),
            (c.history = {
              init: function () {
                if (c.params.history) {
                  if (!window.history || !window.history.pushState)
                    return (c.params.history = !1), void (c.params.hashnav = !0)
                  ;(c.history.initialized = !0),
                    (this.paths = this.getPathValues()),
                    (this.paths.key || this.paths.value) &&
                      (this.scrollToSlide(
                        0,
                        this.paths.value,
                        c.params.runCallbacksOnInit
                      ),
                      c.params.replaceState ||
                        window.addEventListener(
                          'popstate',
                          this.setHistoryPopState
                        ))
                }
              },
              setHistoryPopState: function () {
                ;(c.history.paths = c.history.getPathValues()),
                  c.history.scrollToSlide(
                    c.params.speed,
                    c.history.paths.value,
                    !1
                  )
              },
              getPathValues: function () {
                var e = window.location.pathname.slice(1).split('/'),
                  a = e.length
                return { key: e[a - 2], value: e[a - 1] }
              },
              setHistory: function (e, a) {
                if (c.history.initialized && c.params.history) {
                  var t = c.slides.eq(a),
                    s = this.slugify(t.attr('data-history'))
                  window.location.pathname.includes(e) || (s = e + '/' + s),
                    c.params.replaceState
                      ? window.history.replaceState(null, null, s)
                      : window.history.pushState(null, null, s)
                }
              },
              slugify: function (e) {
                return e
                  .toString()
                  .toLowerCase()
                  .replace(/\s+/g, '-')
                  .replace(/[^\w\-]+/g, '')
                  .replace(/\-\-+/g, '-')
                  .replace(/^-+/, '')
                  .replace(/-+$/, '')
              },
              scrollToSlide: function (e, a, t) {
                if (a)
                  for (var s = 0, i = c.slides.length; s < i; s++) {
                    var r = c.slides.eq(s)
                    if (
                      this.slugify(r.attr('data-history')) === a &&
                      !r.hasClass(c.params.slideDuplicateClass)
                    ) {
                      var n = r.index()
                      c.slideTo(n, e, t)
                    }
                  }
                else c.slideTo(0, e, t)
              }
            }),
            (c.disableKeyboardControl = function () {
              ;(c.params.keyboardControl = !1), e(document).off('keydown', G)
            }),
            (c.enableKeyboardControl = function () {
              ;(c.params.keyboardControl = !0), e(document).on('keydown', G)
            }),
            (c.mousewheel = {
              event: !1,
              lastScrollTime: new window.Date().getTime()
            }),
            c.params.mousewheelControl &&
              (c.mousewheel.event =
                navigator.userAgent.indexOf('firefox') > -1
                  ? 'DOMMouseScroll'
                  : (function () {
                      var e = 'onwheel' in document
                      if (!e) {
                        var a = document.createElement('div')
                        a.setAttribute('onwheel', 'return;'),
                          (e = 'function' == typeof a.onwheel)
                      }
                      return (
                        !e &&
                          document.implementation &&
                          document.implementation.hasFeature &&
                          !0 !== document.implementation.hasFeature('', '') &&
                          (e = document.implementation.hasFeature(
                            'Events.wheel',
                            '3.0'
                          )),
                        e
                      )
                    })()
                  ? 'wheel'
                  : 'mousewheel'),
            (c.disableMousewheelControl = function () {
              if (!c.mousewheel.event) return !1
              var a = c.container
              return (
                'container' !== c.params.mousewheelEventsTarged &&
                  (a = e(c.params.mousewheelEventsTarged)),
                a.off(c.mousewheel.event, X),
                (c.params.mousewheelControl = !1),
                !0
              )
            }),
            (c.enableMousewheelControl = function () {
              if (!c.mousewheel.event) return !1
              var a = c.container
              return (
                'container' !== c.params.mousewheelEventsTarged &&
                  (a = e(c.params.mousewheelEventsTarged)),
                a.on(c.mousewheel.event, X),
                (c.params.mousewheelControl = !0),
                !0
              )
            }),
            (c.parallax = {
              setTranslate: function () {
                c.container
                  .children(
                    '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]'
                  )
                  .each(function () {
                    A(this, c.progress)
                  }),
                  c.slides.each(function () {
                    var a = e(this)
                    a.find(
                      '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]'
                    ).each(function () {
                      A(this, Math.min(Math.max(a[0].progress, -1), 1))
                    })
                  })
              },
              setTransition: function (a) {
                void 0 === a && (a = c.params.speed),
                  c.container
                    .find(
                      '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]'
                    )
                    .each(function () {
                      var t = e(this),
                        s =
                          parseInt(
                            t.attr('data-swiper-parallax-duration'),
                            10
                          ) || a
                      0 === a && (s = 0), t.transition(s)
                    })
              }
            }),
            (c.zoom = {
              scale: 1,
              currentScale: 1,
              isScaling: !1,
              gesture: {
                slide: void 0,
                slideWidth: void 0,
                slideHeight: void 0,
                image: void 0,
                imageWrap: void 0,
                zoomMax: c.params.zoomMax
              },
              image: {
                isTouched: void 0,
                isMoved: void 0,
                currentX: void 0,
                currentY: void 0,
                minX: void 0,
                minY: void 0,
                maxX: void 0,
                maxY: void 0,
                width: void 0,
                height: void 0,
                startX: void 0,
                startY: void 0,
                touchesStart: {},
                touchesCurrent: {}
              },
              velocity: {
                x: void 0,
                y: void 0,
                prevPositionX: void 0,
                prevPositionY: void 0,
                prevTime: void 0
              },
              getDistanceBetweenTouches: function (e) {
                if (e.targetTouches.length < 2) return 1
                var a = e.targetTouches[0].pageX,
                  t = e.targetTouches[0].pageY,
                  s = e.targetTouches[1].pageX,
                  i = e.targetTouches[1].pageY
                return Math.sqrt(Math.pow(s - a, 2) + Math.pow(i - t, 2))
              },
              onGestureStart: function (a) {
                var t = c.zoom
                if (!c.support.gestures) {
                  if (
                    'touchstart' !== a.type ||
                    ('touchstart' === a.type && a.targetTouches.length < 2)
                  )
                    return
                  t.gesture.scaleStart = t.getDistanceBetweenTouches(a)
                }
                ;(t.gesture.slide && t.gesture.slide.length) ||
                ((t.gesture.slide = e(this)),
                0 === t.gesture.slide.length &&
                  (t.gesture.slide = c.slides.eq(c.activeIndex)),
                (t.gesture.image = t.gesture.slide.find('img, svg, canvas')),
                (t.gesture.imageWrap = t.gesture.image.parent(
                  '.' + c.params.zoomContainerClass
                )),
                (t.gesture.zoomMax =
                  t.gesture.imageWrap.attr('data-swiper-zoom') ||
                  c.params.zoomMax),
                0 !== t.gesture.imageWrap.length)
                  ? (t.gesture.image.transition(0), (t.isScaling = !0))
                  : (t.gesture.image = void 0)
              },
              onGestureChange: function (e) {
                var a = c.zoom
                if (!c.support.gestures) {
                  if (
                    'touchmove' !== e.type ||
                    ('touchmove' === e.type && e.targetTouches.length < 2)
                  )
                    return
                  a.gesture.scaleMove = a.getDistanceBetweenTouches(e)
                }
                a.gesture.image &&
                  0 !== a.gesture.image.length &&
                  (c.support.gestures
                    ? (a.scale = e.scale * a.currentScale)
                    : (a.scale =
                        (a.gesture.scaleMove / a.gesture.scaleStart) *
                        a.currentScale),
                  a.scale > a.gesture.zoomMax &&
                    (a.scale =
                      a.gesture.zoomMax -
                      1 +
                      Math.pow(a.scale - a.gesture.zoomMax + 1, 0.5)),
                  a.scale < c.params.zoomMin &&
                    (a.scale =
                      c.params.zoomMin +
                      1 -
                      Math.pow(c.params.zoomMin - a.scale + 1, 0.5)),
                  a.gesture.image.transform(
                    'translate3d(0,0,0) scale(' + a.scale + ')'
                  ))
              },
              onGestureEnd: function (e) {
                var a = c.zoom
                ;(!c.support.gestures &&
                  ('touchend' !== e.type ||
                    ('touchend' === e.type && e.changedTouches.length < 2))) ||
                  (a.gesture.image &&
                    0 !== a.gesture.image.length &&
                    ((a.scale = Math.max(
                      Math.min(a.scale, a.gesture.zoomMax),
                      c.params.zoomMin
                    )),
                    a.gesture.image
                      .transition(c.params.speed)
                      .transform('translate3d(0,0,0) scale(' + a.scale + ')'),
                    (a.currentScale = a.scale),
                    (a.isScaling = !1),
                    1 === a.scale && (a.gesture.slide = void 0)))
              },
              onTouchStart: function (e, a) {
                var t = e.zoom
                t.gesture.image &&
                  0 !== t.gesture.image.length &&
                  (t.image.isTouched ||
                    ('android' === e.device.os && a.preventDefault(),
                    (t.image.isTouched = !0),
                    (t.image.touchesStart.x =
                      'touchstart' === a.type
                        ? a.targetTouches[0].pageX
                        : a.pageX),
                    (t.image.touchesStart.y =
                      'touchstart' === a.type
                        ? a.targetTouches[0].pageY
                        : a.pageY)))
              },
              onTouchMove: function (e) {
                var a = c.zoom
                if (
                  a.gesture.image &&
                  0 !== a.gesture.image.length &&
                  ((c.allowClick = !1), a.image.isTouched && a.gesture.slide)
                ) {
                  a.image.isMoved ||
                    ((a.image.width = a.gesture.image[0].offsetWidth),
                    (a.image.height = a.gesture.image[0].offsetHeight),
                    (a.image.startX =
                      c.getTranslate(a.gesture.imageWrap[0], 'x') || 0),
                    (a.image.startY =
                      c.getTranslate(a.gesture.imageWrap[0], 'y') || 0),
                    (a.gesture.slideWidth = a.gesture.slide[0].offsetWidth),
                    (a.gesture.slideHeight = a.gesture.slide[0].offsetHeight),
                    a.gesture.imageWrap.transition(0),
                    c.rtl && (a.image.startX = -a.image.startX),
                    c.rtl && (a.image.startY = -a.image.startY))
                  var t = a.image.width * a.scale,
                    s = a.image.height * a.scale
                  if (
                    !(t < a.gesture.slideWidth && s < a.gesture.slideHeight)
                  ) {
                    if (
                      ((a.image.minX = Math.min(
                        a.gesture.slideWidth / 2 - t / 2,
                        0
                      )),
                      (a.image.maxX = -a.image.minX),
                      (a.image.minY = Math.min(
                        a.gesture.slideHeight / 2 - s / 2,
                        0
                      )),
                      (a.image.maxY = -a.image.minY),
                      (a.image.touchesCurrent.x =
                        'touchmove' === e.type
                          ? e.targetTouches[0].pageX
                          : e.pageX),
                      (a.image.touchesCurrent.y =
                        'touchmove' === e.type
                          ? e.targetTouches[0].pageY
                          : e.pageY),
                      !a.image.isMoved && !a.isScaling)
                    ) {
                      if (
                        (c.isHorizontal() &&
                          Math.floor(a.image.minX) ===
                            Math.floor(a.image.startX) &&
                          a.image.touchesCurrent.x < a.image.touchesStart.x) ||
                        (Math.floor(a.image.maxX) ===
                          Math.floor(a.image.startX) &&
                          a.image.touchesCurrent.x > a.image.touchesStart.x)
                      )
                        return void (a.image.isTouched = !1)
                      if (
                        (!c.isHorizontal() &&
                          Math.floor(a.image.minY) ===
                            Math.floor(a.image.startY) &&
                          a.image.touchesCurrent.y < a.image.touchesStart.y) ||
                        (Math.floor(a.image.maxY) ===
                          Math.floor(a.image.startY) &&
                          a.image.touchesCurrent.y > a.image.touchesStart.y)
                      )
                        return void (a.image.isTouched = !1)
                    }
                    e.preventDefault(),
                      e.stopPropagation(),
                      (a.image.isMoved = !0),
                      (a.image.currentX =
                        a.image.touchesCurrent.x -
                        a.image.touchesStart.x +
                        a.image.startX),
                      (a.image.currentY =
                        a.image.touchesCurrent.y -
                        a.image.touchesStart.y +
                        a.image.startY),
                      a.image.currentX < a.image.minX &&
                        (a.image.currentX =
                          a.image.minX +
                          1 -
                          Math.pow(a.image.minX - a.image.currentX + 1, 0.8)),
                      a.image.currentX > a.image.maxX &&
                        (a.image.currentX =
                          a.image.maxX -
                          1 +
                          Math.pow(a.image.currentX - a.image.maxX + 1, 0.8)),
                      a.image.currentY < a.image.minY &&
                        (a.image.currentY =
                          a.image.minY +
                          1 -
                          Math.pow(a.image.minY - a.image.currentY + 1, 0.8)),
                      a.image.currentY > a.image.maxY &&
                        (a.image.currentY =
                          a.image.maxY -
                          1 +
                          Math.pow(a.image.currentY - a.image.maxY + 1, 0.8)),
                      a.velocity.prevPositionX ||
                        (a.velocity.prevPositionX = a.image.touchesCurrent.x),
                      a.velocity.prevPositionY ||
                        (a.velocity.prevPositionY = a.image.touchesCurrent.y),
                      a.velocity.prevTime || (a.velocity.prevTime = Date.now()),
                      (a.velocity.x =
                        (a.image.touchesCurrent.x - a.velocity.prevPositionX) /
                        (Date.now() - a.velocity.prevTime) /
                        2),
                      (a.velocity.y =
                        (a.image.touchesCurrent.y - a.velocity.prevPositionY) /
                        (Date.now() - a.velocity.prevTime) /
                        2),
                      Math.abs(
                        a.image.touchesCurrent.x - a.velocity.prevPositionX
                      ) < 2 && (a.velocity.x = 0),
                      Math.abs(
                        a.image.touchesCurrent.y - a.velocity.prevPositionY
                      ) < 2 && (a.velocity.y = 0),
                      (a.velocity.prevPositionX = a.image.touchesCurrent.x),
                      (a.velocity.prevPositionY = a.image.touchesCurrent.y),
                      (a.velocity.prevTime = Date.now()),
                      a.gesture.imageWrap.transform(
                        'translate3d(' +
                          a.image.currentX +
                          'px, ' +
                          a.image.currentY +
                          'px,0)'
                      )
                  }
                }
              },
              onTouchEnd: function (e, a) {
                var t = e.zoom
                if (t.gesture.image && 0 !== t.gesture.image.length) {
                  if (!t.image.isTouched || !t.image.isMoved)
                    return (t.image.isTouched = !1), void (t.image.isMoved = !1)
                  ;(t.image.isTouched = !1), (t.image.isMoved = !1)
                  var s = 300,
                    i = 300,
                    r = t.velocity.x * s,
                    n = t.image.currentX + r,
                    o = t.velocity.y * i,
                    l = t.image.currentY + o
                  0 !== t.velocity.x &&
                    (s = Math.abs((n - t.image.currentX) / t.velocity.x)),
                    0 !== t.velocity.y &&
                      (i = Math.abs((l - t.image.currentY) / t.velocity.y))
                  var p = Math.max(s, i)
                  ;(t.image.currentX = n), (t.image.currentY = l)
                  var d = t.image.width * t.scale,
                    u = t.image.height * t.scale
                  ;(t.image.minX = Math.min(
                    t.gesture.slideWidth / 2 - d / 2,
                    0
                  )),
                    (t.image.maxX = -t.image.minX),
                    (t.image.minY = Math.min(
                      t.gesture.slideHeight / 2 - u / 2,
                      0
                    )),
                    (t.image.maxY = -t.image.minY),
                    (t.image.currentX = Math.max(
                      Math.min(t.image.currentX, t.image.maxX),
                      t.image.minX
                    )),
                    (t.image.currentY = Math.max(
                      Math.min(t.image.currentY, t.image.maxY),
                      t.image.minY
                    )),
                    t.gesture.imageWrap
                      .transition(p)
                      .transform(
                        'translate3d(' +
                          t.image.currentX +
                          'px, ' +
                          t.image.currentY +
                          'px,0)'
                      )
                }
              },
              onTransitionEnd: function (e) {
                var a = e.zoom
                a.gesture.slide &&
                  e.previousIndex !== e.activeIndex &&
                  (a.gesture.image.transform('translate3d(0,0,0) scale(1)'),
                  a.gesture.imageWrap.transform('translate3d(0,0,0)'),
                  (a.gesture.slide =
                    a.gesture.image =
                    a.gesture.imageWrap =
                      void 0),
                  (a.scale = a.currentScale = 1))
              },
              toggleZoom: function (a, t) {
                var s,
                  i,
                  r,
                  n,
                  o,
                  l,
                  p,
                  d,
                  u,
                  c,
                  m,
                  h,
                  g,
                  f,
                  v,
                  w,
                  y = a.zoom
                ;(y.gesture.slide ||
                  ((y.gesture.slide = a.clickedSlide
                    ? e(a.clickedSlide)
                    : a.slides.eq(a.activeIndex)),
                  (y.gesture.image = y.gesture.slide.find('img, svg, canvas')),
                  (y.gesture.imageWrap = y.gesture.image.parent(
                    '.' + a.params.zoomContainerClass
                  ))),
                y.gesture.image && 0 !== y.gesture.image.length) &&
                  (void 0 === y.image.touchesStart.x && t
                    ? ((s =
                        'touchend' === t.type
                          ? t.changedTouches[0].pageX
                          : t.pageX),
                      (i =
                        'touchend' === t.type
                          ? t.changedTouches[0].pageY
                          : t.pageY))
                    : ((s = y.image.touchesStart.x),
                      (i = y.image.touchesStart.y)),
                  y.scale && 1 !== y.scale
                    ? ((y.scale = y.currentScale = 1),
                      y.gesture.imageWrap
                        .transition(300)
                        .transform('translate3d(0,0,0)'),
                      y.gesture.image
                        .transition(300)
                        .transform('translate3d(0,0,0) scale(1)'),
                      (y.gesture.slide = void 0))
                    : ((y.scale = y.currentScale =
                        y.gesture.imageWrap.attr('data-swiper-zoom') ||
                        a.params.zoomMax),
                      t
                        ? ((v = y.gesture.slide[0].offsetWidth),
                          (w = y.gesture.slide[0].offsetHeight),
                          (r = y.gesture.slide.offset().left + v / 2 - s),
                          (n = y.gesture.slide.offset().top + w / 2 - i),
                          (p = y.gesture.image[0].offsetWidth),
                          (d = y.gesture.image[0].offsetHeight),
                          (u = p * y.scale),
                          (c = d * y.scale),
                          (g = -(m = Math.min(v / 2 - u / 2, 0))),
                          (f = -(h = Math.min(w / 2 - c / 2, 0))),
                          (o = r * y.scale),
                          (l = n * y.scale),
                          o < m && (o = m),
                          o > g && (o = g),
                          l < h && (l = h),
                          l > f && (l = f))
                        : ((o = 0), (l = 0)),
                      y.gesture.imageWrap
                        .transition(300)
                        .transform('translate3d(' + o + 'px, ' + l + 'px,0)'),
                      y.gesture.image
                        .transition(300)
                        .transform(
                          'translate3d(0,0,0) scale(' + y.scale + ')'
                        )))
              },
              attachEvents: function (a) {
                var t = a ? 'off' : 'on'
                if (c.params.zoom) {
                  c.slides
                  var s = !(
                    'touchstart' !== c.touchEvents.start ||
                    !c.support.passiveListener ||
                    !c.params.passiveListeners
                  ) && { passive: !0, capture: !1 }
                  c.support.gestures
                    ? (c.slides[t]('gesturestart', c.zoom.onGestureStart, s),
                      c.slides[t]('gesturechange', c.zoom.onGestureChange, s),
                      c.slides[t]('gestureend', c.zoom.onGestureEnd, s))
                    : 'touchstart' === c.touchEvents.start &&
                      (c.slides[t](
                        c.touchEvents.start,
                        c.zoom.onGestureStart,
                        s
                      ),
                      c.slides[t](
                        c.touchEvents.move,
                        c.zoom.onGestureChange,
                        s
                      ),
                      c.slides[t](c.touchEvents.end, c.zoom.onGestureEnd, s)),
                    c[t]('touchStart', c.zoom.onTouchStart),
                    c.slides.each(function (a, s) {
                      e(s).find('.' + c.params.zoomContainerClass).length > 0 &&
                        e(s)[t](c.touchEvents.move, c.zoom.onTouchMove)
                    }),
                    c[t]('touchEnd', c.zoom.onTouchEnd),
                    c[t]('transitionEnd', c.zoom.onTransitionEnd),
                    c.params.zoomToggle && c.on('doubleTap', c.zoom.toggleZoom)
                }
              },
              init: function () {
                c.zoom.attachEvents()
              },
              destroy: function () {
                c.zoom.attachEvents(!0)
              }
            }),
            (c._plugins = []),
            c.plugins)) {
              var k = c.plugins[I](c, c.params[I])
              k && c._plugins.push(k)
            }
            return (
              (c.callPlugins = function (e) {
                for (var a = 0; a < c._plugins.length; a++)
                  e in c._plugins[a] &&
                    c._plugins[a][e](
                      arguments[1],
                      arguments[2],
                      arguments[3],
                      arguments[4],
                      arguments[5]
                    )
              }),
              (c.emitterEventListeners = {}),
              (c.emit = function (e) {
                var a
                if (
                  (c.params[e] &&
                    c.params[e](
                      arguments[1],
                      arguments[2],
                      arguments[3],
                      arguments[4],
                      arguments[5]
                    ),
                  c.emitterEventListeners[e])
                )
                  for (a = 0; a < c.emitterEventListeners[e].length; a++)
                    c.emitterEventListeners[e][a](
                      arguments[1],
                      arguments[2],
                      arguments[3],
                      arguments[4],
                      arguments[5]
                    )
                c.callPlugins &&
                  c.callPlugins(
                    e,
                    arguments[1],
                    arguments[2],
                    arguments[3],
                    arguments[4],
                    arguments[5]
                  )
              }),
              (c.on = function (e, a) {
                return (
                  (e = Y(e)),
                  c.emitterEventListeners[e] ||
                    (c.emitterEventListeners[e] = []),
                  c.emitterEventListeners[e].push(a),
                  c
                )
              }),
              (c.off = function (e, a) {
                var t
                if (((e = Y(e)), void 0 === a))
                  return (c.emitterEventListeners[e] = []), c
                if (
                  c.emitterEventListeners[e] &&
                  0 !== c.emitterEventListeners[e].length
                ) {
                  for (t = 0; t < c.emitterEventListeners[e].length; t++)
                    c.emitterEventListeners[e][t] === a &&
                      c.emitterEventListeners[e].splice(t, 1)
                  return c
                }
              }),
              (c.once = function (e, a) {
                e = Y(e)
                var t = function () {
                  a(
                    arguments[0],
                    arguments[1],
                    arguments[2],
                    arguments[3],
                    arguments[4]
                  ),
                    c.off(e, t)
                }
                return c.on(e, t), c
              }),
              (c.a11y = {
                makeFocusable: function (e) {
                  return e.attr('tabIndex', '0'), e
                },
                addRole: function (e, a) {
                  return e.attr('role', a), e
                },
                addLabel: function (e, a) {
                  return e.attr('aria-label', a), e
                },
                disable: function (e) {
                  return e.attr('aria-disabled', !0), e
                },
                enable: function (e) {
                  return e.attr('aria-disabled', !1), e
                },
                onEnterKey: function (a) {
                  13 === a.keyCode &&
                    (e(a.target).is(c.params.nextButton)
                      ? (c.onClickNext(a),
                        c.isEnd
                          ? c.a11y.notify(c.params.lastSlideMessage)
                          : c.a11y.notify(c.params.nextSlideMessage))
                      : e(a.target).is(c.params.prevButton) &&
                        (c.onClickPrev(a),
                        c.isBeginning
                          ? c.a11y.notify(c.params.firstSlideMessage)
                          : c.a11y.notify(c.params.prevSlideMessage)),
                    e(a.target).is('.' + c.params.bulletClass) &&
                      e(a.target)[0].click())
                },
                liveRegion: e(
                  '<span class="' +
                    c.params.notificationClass +
                    '" aria-live="assertive" aria-atomic="true"></span>'
                ),
                notify: function (e) {
                  var a = c.a11y.liveRegion
                  0 !== a.length && (a.html(''), a.html(e))
                },
                init: function () {
                  c.params.nextButton &&
                    c.nextButton &&
                    c.nextButton.length > 0 &&
                    (c.a11y.makeFocusable(c.nextButton),
                    c.a11y.addRole(c.nextButton, 'button'),
                    c.a11y.addLabel(c.nextButton, c.params.nextSlideMessage)),
                    c.params.prevButton &&
                      c.prevButton &&
                      c.prevButton.length > 0 &&
                      (c.a11y.makeFocusable(c.prevButton),
                      c.a11y.addRole(c.prevButton, 'button'),
                      c.a11y.addLabel(c.prevButton, c.params.prevSlideMessage)),
                    e(c.container).append(c.a11y.liveRegion)
                },
                initPagination: function () {
                  c.params.pagination &&
                    c.params.paginationClickable &&
                    c.bullets &&
                    c.bullets.length &&
                    c.bullets.each(function () {
                      var a = e(this)
                      c.a11y.makeFocusable(a),
                        c.a11y.addRole(a, 'button'),
                        c.a11y.addLabel(
                          a,
                          c.params.paginationBulletMessage.replace(
                            /{{index}}/,
                            a.index() + 1
                          )
                        )
                    })
                },
                destroy: function () {
                  c.a11y.liveRegion &&
                    c.a11y.liveRegion.length > 0 &&
                    c.a11y.liveRegion.remove()
                }
              }),
              (c.init = function () {
                c.params.loop && c.createLoop(),
                  c.updateContainerSize(),
                  c.updateSlidesSize(),
                  c.updatePagination(),
                  c.params.scrollbar &&
                    c.scrollbar &&
                    (c.scrollbar.set(),
                    c.params.scrollbarDraggable &&
                      c.scrollbar.enableDraggable()),
                  'slide' !== c.params.effect &&
                    c.effects[c.params.effect] &&
                    (c.params.loop || c.updateProgress(),
                    c.effects[c.params.effect].setTranslate()),
                  c.params.loop
                    ? c.slideTo(
                        c.params.initialSlide + c.loopedSlides,
                        0,
                        c.params.runCallbacksOnInit
                      )
                    : (c.slideTo(
                        c.params.initialSlide,
                        0,
                        c.params.runCallbacksOnInit
                      ),
                      0 === c.params.initialSlide &&
                        (c.parallax &&
                          c.params.parallax &&
                          c.parallax.setTranslate(),
                        c.lazy &&
                          c.params.lazyLoading &&
                          (c.lazy.load(), (c.lazy.initialImageLoaded = !0)))),
                  c.attachEvents(),
                  c.params.observer && c.support.observer && c.initObservers(),
                  c.params.preloadImages &&
                    !c.params.lazyLoading &&
                    c.preloadImages(),
                  c.params.zoom && c.zoom && c.zoom.init(),
                  c.params.autoplay && c.startAutoplay(),
                  c.params.keyboardControl &&
                    c.enableKeyboardControl &&
                    c.enableKeyboardControl(),
                  c.params.mousewheelControl &&
                    c.enableMousewheelControl &&
                    c.enableMousewheelControl(),
                  c.params.hashnavReplaceState &&
                    (c.params.replaceState = c.params.hashnavReplaceState),
                  c.params.history && c.history && c.history.init(),
                  c.params.hashnav && c.hashnav && c.hashnav.init(),
                  c.params.a11y && c.a11y && c.a11y.init(),
                  c.emit('onInit', c)
              }),
              (c.cleanupStyles = function () {
                c.container
                  .removeClass(c.classNames.join(' '))
                  .removeAttr('style'),
                  c.wrapper.removeAttr('style'),
                  c.slides &&
                    c.slides.length &&
                    c.slides
                      .removeClass(
                        [
                          c.params.slideVisibleClass,
                          c.params.slideActiveClass,
                          c.params.slideNextClass,
                          c.params.slidePrevClass
                        ].join(' ')
                      )
                      .removeAttr('style')
                      .removeAttr('data-swiper-column')
                      .removeAttr('data-swiper-row'),
                  c.paginationContainer &&
                    c.paginationContainer.length &&
                    c.paginationContainer.removeClass(
                      c.params.paginationHiddenClass
                    ),
                  c.bullets &&
                    c.bullets.length &&
                    c.bullets.removeClass(c.params.bulletActiveClass),
                  c.params.prevButton &&
                    e(c.params.prevButton).removeClass(
                      c.params.buttonDisabledClass
                    ),
                  c.params.nextButton &&
                    e(c.params.nextButton).removeClass(
                      c.params.buttonDisabledClass
                    ),
                  c.params.scrollbar &&
                    c.scrollbar &&
                    (c.scrollbar.track &&
                      c.scrollbar.track.length &&
                      c.scrollbar.track.removeAttr('style'),
                    c.scrollbar.drag &&
                      c.scrollbar.drag.length &&
                      c.scrollbar.drag.removeAttr('style'))
              }),
              (c.destroy = function (e, a) {
                c.detachEvents(),
                  c.stopAutoplay(),
                  c.params.scrollbar &&
                    c.scrollbar &&
                    c.params.scrollbarDraggable &&
                    c.scrollbar.disableDraggable(),
                  c.params.loop && c.destroyLoop(),
                  a && c.cleanupStyles(),
                  c.disconnectObservers(),
                  c.params.zoom && c.zoom && c.zoom.destroy(),
                  c.params.keyboardControl &&
                    c.disableKeyboardControl &&
                    c.disableKeyboardControl(),
                  c.params.mousewheelControl &&
                    c.disableMousewheelControl &&
                    c.disableMousewheelControl(),
                  c.params.a11y && c.a11y && c.a11y.destroy(),
                  c.params.history &&
                    !c.params.replaceState &&
                    window.removeEventListener(
                      'popstate',
                      c.history.setHistoryPopState
                    ),
                  c.params.hashnav && c.hashnav && c.hashnav.destroy(),
                  c.emit('onDestroy'),
                  !1 !== e && (c = null)
              }),
              c.init(),
              c
            )
          }
          function L(e) {
            return Math.floor(e)
          }
          function D() {
            var e = c.params.autoplay,
              a = c.slides.eq(c.activeIndex)
            a.attr('data-swiper-autoplay') &&
              (e = a.attr('data-swiper-autoplay') || c.params.autoplay),
              (c.autoplayTimeoutId = setTimeout(function () {
                c.params.loop
                  ? (c.fixLoop(), c._slideNext(), c.emit('onAutoplay', c))
                  : c.isEnd
                  ? t.autoplayStopOnLast
                    ? c.stopAutoplay()
                    : (c._slideTo(0), c.emit('onAutoplay', c))
                  : (c._slideNext(), c.emit('onAutoplay', c))
              }, e))
          }
          function B(a, t) {
            var s = e(a.target)
            if (!s.is(t))
              if ('string' == typeof t) s = s.parents(t)
              else if (t.nodeType) {
                var i
                return (
                  s.parents().each(function (e, a) {
                    a === t && (i = t)
                  }),
                  i ? t : void 0
                )
              }
            if (0 !== s.length) return s[0]
          }
          function H(e, a) {
            a = a || {}
            var t = new (window.MutationObserver ||
              window.WebkitMutationObserver)(function (e) {
              e.forEach(function (e) {
                c.onResize(!0), c.emit('onObserverUpdate', c, e)
              })
            })
            t.observe(e, {
              attributes: void 0 === a.attributes || a.attributes,
              childList: void 0 === a.childList || a.childList,
              characterData: void 0 === a.characterData || a.characterData
            }),
              c.observers.push(t)
          }
          function G(e) {
            e.originalEvent && (e = e.originalEvent)
            var a = e.keyCode || e.charCode
            if (
              !c.params.allowSwipeToNext &&
              ((c.isHorizontal() && 39 === a) ||
                (!c.isHorizontal() && 40 === a))
            )
              return !1
            if (
              !c.params.allowSwipeToPrev &&
              ((c.isHorizontal() && 37 === a) ||
                (!c.isHorizontal() && 38 === a))
            )
              return !1
            if (
              !(
                e.shiftKey ||
                e.altKey ||
                e.ctrlKey ||
                e.metaKey ||
                (document.activeElement &&
                  document.activeElement.nodeName &&
                  ('input' === document.activeElement.nodeName.toLowerCase() ||
                    'textarea' ===
                      document.activeElement.nodeName.toLowerCase()))
              )
            ) {
              if (37 === a || 39 === a || 38 === a || 40 === a) {
                var t = !1
                if (
                  c.container.parents('.' + c.params.slideClass).length > 0 &&
                  0 ===
                    c.container.parents('.' + c.params.slideActiveClass).length
                )
                  return
                var s = { left: window.pageXOffset, top: window.pageYOffset },
                  i = window.innerWidth,
                  r = window.innerHeight,
                  n = c.container.offset()
                c.rtl && (n.left = n.left - c.container[0].scrollLeft)
                for (
                  var o = [
                      [n.left, n.top],
                      [n.left + c.width, n.top],
                      [n.left, n.top + c.height],
                      [n.left + c.width, n.top + c.height]
                    ],
                    l = 0;
                  l < o.length;
                  l++
                ) {
                  var p = o[l]
                  p[0] >= s.left &&
                    p[0] <= s.left + i &&
                    p[1] >= s.top &&
                    p[1] <= s.top + r &&
                    (t = !0)
                }
                if (!t) return
              }
              c.isHorizontal()
                ? ((37 !== a && 39 !== a) ||
                    (e.preventDefault
                      ? e.preventDefault()
                      : (e.returnValue = !1)),
                  ((39 === a && !c.rtl) || (37 === a && c.rtl)) &&
                    c.slideNext(),
                  ((37 === a && !c.rtl) || (39 === a && c.rtl)) &&
                    c.slidePrev())
                : ((38 !== a && 40 !== a) ||
                    (e.preventDefault
                      ? e.preventDefault()
                      : (e.returnValue = !1)),
                  40 === a && c.slideNext(),
                  38 === a && c.slidePrev()),
                c.emit('onKeyPress', c, a)
            }
          }
          function X(e) {
            e.originalEvent && (e = e.originalEvent)
            var a,
              t,
              s,
              i,
              r,
              n = 0,
              o = c.rtl ? -1 : 1,
              l =
                ((t = 0),
                (s = 0),
                (i = 0),
                (r = 0),
                'detail' in (a = e) && (s = a.detail),
                'wheelDelta' in a && (s = -a.wheelDelta / 120),
                'wheelDeltaY' in a && (s = -a.wheelDeltaY / 120),
                'wheelDeltaX' in a && (t = -a.wheelDeltaX / 120),
                'axis' in a &&
                  a.axis === a.HORIZONTAL_AXIS &&
                  ((t = s), (s = 0)),
                (i = 10 * t),
                (r = 10 * s),
                'deltaY' in a && (r = a.deltaY),
                'deltaX' in a && (i = a.deltaX),
                (i || r) &&
                  a.deltaMode &&
                  (1 === a.deltaMode
                    ? ((i *= 40), (r *= 40))
                    : ((i *= 800), (r *= 800))),
                i && !t && (t = i < 1 ? -1 : 1),
                r && !s && (s = r < 1 ? -1 : 1),
                { spinX: t, spinY: s, pixelX: i, pixelY: r })
            if (c.params.mousewheelForceToAxis)
              if (c.isHorizontal()) {
                if (!(Math.abs(l.pixelX) > Math.abs(l.pixelY))) return
                n = l.pixelX * o
              } else {
                if (!(Math.abs(l.pixelY) > Math.abs(l.pixelX))) return
                n = l.pixelY
              }
            else
              n =
                Math.abs(l.pixelX) > Math.abs(l.pixelY)
                  ? -l.pixelX * o
                  : -l.pixelY
            if (0 !== n) {
              if ((c.params.mousewheelInvert && (n = -n), c.params.freeMode)) {
                var p =
                    c.getWrapperTranslate() +
                    n * c.params.mousewheelSensitivity,
                  d = c.isBeginning,
                  u = c.isEnd
                if (
                  (p >= c.minTranslate() && (p = c.minTranslate()),
                  p <= c.maxTranslate() && (p = c.maxTranslate()),
                  c.setWrapperTransition(0),
                  c.setWrapperTranslate(p),
                  c.updateProgress(),
                  c.updateActiveIndex(),
                  ((!d && c.isBeginning) || (!u && c.isEnd)) &&
                    c.updateClasses(),
                  c.params.freeModeSticky
                    ? (clearTimeout(c.mousewheel.timeout),
                      (c.mousewheel.timeout = setTimeout(function () {
                        c.slideReset()
                      }, 300)))
                    : c.params.lazyLoading && c.lazy && c.lazy.load(),
                  c.emit('onScroll', c, e),
                  c.params.autoplay &&
                    c.params.autoplayDisableOnInteraction &&
                    c.stopAutoplay(),
                  0 === p || p === c.maxTranslate())
                )
                  return
              } else {
                if (
                  new window.Date().getTime() - c.mousewheel.lastScrollTime >
                  60
                )
                  if (n < 0)
                    if ((c.isEnd && !c.params.loop) || c.animating) {
                      if (c.params.mousewheelReleaseOnEdges) return !0
                    } else c.slideNext(), c.emit('onScroll', c, e)
                  else if ((c.isBeginning && !c.params.loop) || c.animating) {
                    if (c.params.mousewheelReleaseOnEdges) return !0
                  } else c.slidePrev(), c.emit('onScroll', c, e)
                c.mousewheel.lastScrollTime = new window.Date().getTime()
              }
              return (
                e.preventDefault ? e.preventDefault() : (e.returnValue = !1), !1
              )
            }
          }
          function A(a, t) {
            var s, i, r
            a = e(a)
            var n = c.rtl ? -1 : 1
            ;(s = a.attr('data-swiper-parallax') || '0'),
              (i = a.attr('data-swiper-parallax-x')),
              (r = a.attr('data-swiper-parallax-y')),
              i || r
                ? ((i = i || '0'), (r = r || '0'))
                : c.isHorizontal()
                ? ((i = s), (r = '0'))
                : ((r = s), (i = '0')),
              (i =
                i.indexOf('%') >= 0
                  ? parseInt(i, 10) * t * n + '%'
                  : i * t * n + 'px'),
              (r =
                r.indexOf('%') >= 0 ? parseInt(r, 10) * t + '%' : r * t + 'px'),
              a.transform('translate3d(' + i + ', ' + r + ',0px)')
          }
          function Y(e) {
            return (
              0 !== e.indexOf('on') &&
                (e =
                  e[0] !== e[0].toUpperCase()
                    ? 'on' + e[0].toUpperCase() + e.substring(1)
                    : 'on' + e),
              e
            )
          }
        }
      s.prototype = {
        isSafari:
          ((t = window.navigator.userAgent.toLowerCase()),
          t.indexOf('safari') >= 0 &&
            t.indexOf('chrome') < 0 &&
            t.indexOf('android') < 0),
        isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(
          window.navigator.userAgent
        ),
        isArray: function (e) {
          return '[object Array]' === Object.prototype.toString.apply(e)
        },
        browser: {
          ie:
            window.navigator.pointerEnabled ||
            window.navigator.msPointerEnabled,
          ieTouch:
            (window.navigator.msPointerEnabled &&
              window.navigator.msMaxTouchPoints > 1) ||
            (window.navigator.pointerEnabled &&
              window.navigator.maxTouchPoints > 1),
          lteIE9:
            ((a = document.createElement('div')),
            (a.innerHTML = '\x3c!--[if lte IE 9]><i></i><![endif]--\x3e'),
            1 === a.getElementsByTagName('i').length)
        },
        device: (function () {
          var e = window.navigator.userAgent,
            a = e.match(/(Android);?[\s\/]+([\d.]+)?/),
            t = e.match(/(iPad).*OS\s([\d_]+)/),
            s = e.match(/(iPod)(.*OS\s([\d_]+))?/),
            i = !t && e.match(/(iPhone\sOS|iOS)\s([\d_]+)/)
          return { ios: t || i || s, android: a }
        })(),
        support: {
          touch:
            (window.Modernizr && !0 === Modernizr.touch) ||
            !!(
              'ontouchstart' in window ||
              (window.DocumentTouch && document instanceof DocumentTouch)
            ),
          transforms3d:
            (window.Modernizr && !0 === Modernizr.csstransforms3d) ||
            (function () {
              var e = document.createElement('div').style
              return (
                'webkitPerspective' in e ||
                'MozPerspective' in e ||
                'OPerspective' in e ||
                'MsPerspective' in e ||
                'perspective' in e
              )
            })(),
          flexbox: (function () {
            for (
              var e = document.createElement('div').style,
                a =
                  'alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient'.split(
                    ' '
                  ),
                t = 0;
              t < a.length;
              t++
            )
              if (a[t] in e) return !0
          })(),
          observer:
            'MutationObserver' in window || 'WebkitMutationObserver' in window,
          passiveListener: (function () {
            var e = !1
            try {
              var a = Object.defineProperty({}, 'passive', {
                get: function () {
                  e = !0
                }
              })
              window.addEventListener('testPassiveListener', null, a)
            } catch (e) {}
            return e
          })(),
          gestures: 'ongesturestart' in window
        },
        plugins: {}
      }
      for (
        var i,
          r = (function () {
            var e = function (e) {
                var a = 0
                for (a = 0; a < e.length; a++) this[a] = e[a]
                return (this.length = e.length), this
              },
              a = function (a, t) {
                var s = [],
                  i = 0
                if (a && !t && a instanceof e) return a
                if (a)
                  if ('string' == typeof a) {
                    var r,
                      n,
                      o = a.trim()
                    if (o.indexOf('<') >= 0 && o.indexOf('>') >= 0) {
                      var l = 'div'
                      for (
                        0 === o.indexOf('<li') && (l = 'ul'),
                          0 === o.indexOf('<tr') && (l = 'tbody'),
                          (0 !== o.indexOf('<td') && 0 !== o.indexOf('<th')) ||
                            (l = 'tr'),
                          0 === o.indexOf('<tbody') && (l = 'table'),
                          0 === o.indexOf('<option') && (l = 'select'),
                          (n = document.createElement(l)).innerHTML = a,
                          i = 0;
                        i < n.childNodes.length;
                        i++
                      )
                        s.push(n.childNodes[i])
                    } else
                      for (
                        r =
                          t || '#' !== a[0] || a.match(/[ .<>:~]/)
                            ? (t || document).querySelectorAll(a)
                            : [document.getElementById(a.split('#')[1])],
                          i = 0;
                        i < r.length;
                        i++
                      )
                        r[i] && s.push(r[i])
                  } else if (a.nodeType || a === window || a === document)
                    s.push(a)
                  else if (a.length > 0 && a[0].nodeType)
                    for (i = 0; i < a.length; i++) s.push(a[i])
                return new e(s)
              }
            return (
              (e.prototype = {
                addClass: function (e) {
                  if (void 0 === e) return this
                  for (var a = e.split(' '), t = 0; t < a.length; t++)
                    for (var s = 0; s < this.length; s++)
                      this[s].classList.add(a[t])
                  return this
                },
                removeClass: function (e) {
                  for (var a = e.split(' '), t = 0; t < a.length; t++)
                    for (var s = 0; s < this.length; s++)
                      this[s].classList.remove(a[t])
                  return this
                },
                hasClass: function (e) {
                  return !!this[0] && this[0].classList.contains(e)
                },
                toggleClass: function (e) {
                  for (var a = e.split(' '), t = 0; t < a.length; t++)
                    for (var s = 0; s < this.length; s++)
                      this[s].classList.toggle(a[t])
                  return this
                },
                attr: function (e, a) {
                  if (1 === arguments.length && 'string' == typeof e)
                    return this[0] ? this[0].getAttribute(e) : void 0
                  for (var t = 0; t < this.length; t++)
                    if (2 === arguments.length) this[t].setAttribute(e, a)
                    else
                      for (var s in e)
                        (this[t][s] = e[s]), this[t].setAttribute(s, e[s])
                  return this
                },
                removeAttr: function (e) {
                  for (var a = 0; a < this.length; a++)
                    this[a].removeAttribute(e)
                  return this
                },
                data: function (e, a) {
                  if (void 0 !== a) {
                    for (var t = 0; t < this.length; t++) {
                      var s = this[t]
                      s.dom7ElementDataStorage ||
                        (s.dom7ElementDataStorage = {}),
                        (s.dom7ElementDataStorage[e] = a)
                    }
                    return this
                  }
                  if (this[0]) {
                    var i = this[0].getAttribute('data-' + e)
                    return (
                      i ||
                      (this[0].dom7ElementDataStorage &&
                      (e in this[0].dom7ElementDataStorage)
                        ? this[0].dom7ElementDataStorage[e]
                        : void 0)
                    )
                  }
                },
                transform: function (e) {
                  for (var a = 0; a < this.length; a++) {
                    var t = this[a].style
                    t.webkitTransform =
                      t.MsTransform =
                      t.msTransform =
                      t.MozTransform =
                      t.OTransform =
                      t.transform =
                        e
                  }
                  return this
                },
                transition: function (e) {
                  'string' != typeof e && (e += 'ms')
                  for (var a = 0; a < this.length; a++) {
                    var t = this[a].style
                    t.webkitTransitionDuration =
                      t.MsTransitionDuration =
                      t.msTransitionDuration =
                      t.MozTransitionDuration =
                      t.OTransitionDuration =
                      t.transitionDuration =
                        e
                  }
                  return this
                },
                on: function (e, t, s, i) {
                  function r(e) {
                    var i = e.target
                    if (a(i).is(t)) s.call(i, e)
                    else
                      for (var r = a(i).parents(), n = 0; n < r.length; n++)
                        a(r[n]).is(t) && s.call(r[n], e)
                  }
                  var n,
                    o,
                    l = e.split(' ')
                  for (n = 0; n < this.length; n++)
                    if ('function' == typeof t || !1 === t)
                      for (
                        'function' == typeof t &&
                          ((s = arguments[1]), (i = arguments[2] || !1)),
                          o = 0;
                        o < l.length;
                        o++
                      )
                        this[n].addEventListener(l[o], s, i)
                    else
                      for (o = 0; o < l.length; o++)
                        this[n].dom7LiveListeners ||
                          (this[n].dom7LiveListeners = []),
                          this[n].dom7LiveListeners.push({
                            listener: s,
                            liveListener: r
                          }),
                          this[n].addEventListener(l[o], r, i)
                  return this
                },
                off: function (e, a, t, s) {
                  for (var i = e.split(' '), r = 0; r < i.length; r++)
                    for (var n = 0; n < this.length; n++)
                      if ('function' == typeof a || !1 === a)
                        'function' == typeof a &&
                          ((t = arguments[1]), (s = arguments[2] || !1)),
                          this[n].removeEventListener(i[r], t, s)
                      else if (this[n].dom7LiveListeners)
                        for (
                          var o = 0;
                          o < this[n].dom7LiveListeners.length;
                          o++
                        )
                          this[n].dom7LiveListeners[o].listener === t &&
                            this[n].removeEventListener(
                              i[r],
                              this[n].dom7LiveListeners[o].liveListener,
                              s
                            )
                  return this
                },
                once: function (e, a, t, s) {
                  var i = this
                  'function' == typeof a &&
                    ((a = !1), (t = arguments[1]), (s = arguments[2])),
                    i.on(
                      e,
                      a,
                      function r(n) {
                        t(n), i.off(e, a, r, s)
                      },
                      s
                    )
                },
                trigger: function (e, a) {
                  for (var t = 0; t < this.length; t++) {
                    var s
                    try {
                      s = new window.CustomEvent(e, {
                        detail: a,
                        bubbles: !0,
                        cancelable: !0
                      })
                    } catch (t) {
                      ;(s = document.createEvent('Event')).initEvent(e, !0, !0),
                        (s.detail = a)
                    }
                    this[t].dispatchEvent(s)
                  }
                  return this
                },
                transitionEnd: function (e) {
                  var a,
                    t = [
                      'webkitTransitionEnd',
                      'transitionend',
                      'oTransitionEnd',
                      'MSTransitionEnd',
                      'msTransitionEnd'
                    ],
                    s = this
                  function i(r) {
                    if (r.target === this)
                      for (e.call(this, r), a = 0; a < t.length; a++)
                        s.off(t[a], i)
                  }
                  if (e) for (a = 0; a < t.length; a++) s.on(t[a], i)
                  return this
                },
                width: function () {
                  return this[0] === window
                    ? window.innerWidth
                    : this.length > 0
                    ? parseFloat(this.css('width'))
                    : null
                },
                outerWidth: function (e) {
                  return this.length > 0
                    ? e
                      ? this[0].offsetWidth +
                        parseFloat(this.css('margin-right')) +
                        parseFloat(this.css('margin-left'))
                      : this[0].offsetWidth
                    : null
                },
                height: function () {
                  return this[0] === window
                    ? window.innerHeight
                    : this.length > 0
                    ? parseFloat(this.css('height'))
                    : null
                },
                outerHeight: function (e) {
                  return this.length > 0
                    ? e
                      ? this[0].offsetHeight +
                        parseFloat(this.css('margin-top')) +
                        parseFloat(this.css('margin-bottom'))
                      : this[0].offsetHeight
                    : null
                },
                offset: function () {
                  if (this.length > 0) {
                    var e = this[0],
                      a = e.getBoundingClientRect(),
                      t = document.body,
                      s = e.clientTop || t.clientTop || 0,
                      i = e.clientLeft || t.clientLeft || 0,
                      r = window.pageYOffset || e.scrollTop,
                      n = window.pageXOffset || e.scrollLeft
                    return { top: a.top + r - s, left: a.left + n - i }
                  }
                  return null
                },
                css: function (e, a) {
                  var t
                  if (1 === arguments.length) {
                    if ('string' != typeof e) {
                      for (t = 0; t < this.length; t++)
                        for (var s in e) this[t].style[s] = e[s]
                      return this
                    }
                    if (this[0])
                      return window
                        .getComputedStyle(this[0], null)
                        .getPropertyValue(e)
                  }
                  if (2 === arguments.length && 'string' == typeof e) {
                    for (t = 0; t < this.length; t++) this[t].style[e] = a
                    return this
                  }
                  return this
                },
                each: function (e) {
                  for (var a = 0; a < this.length; a++)
                    e.call(this[a], a, this[a])
                  return this
                },
                html: function (e) {
                  if (void 0 === e) return this[0] ? this[0].innerHTML : void 0
                  for (var a = 0; a < this.length; a++) this[a].innerHTML = e
                  return this
                },
                text: function (e) {
                  if (void 0 === e)
                    return this[0] ? this[0].textContent.trim() : null
                  for (var a = 0; a < this.length; a++) this[a].textContent = e
                  return this
                },
                is: function (t) {
                  if (!this[0]) return !1
                  var s, i
                  if ('string' == typeof t) {
                    var r = this[0]
                    if (r === document) return t === document
                    if (r === window) return t === window
                    if (r.matches) return r.matches(t)
                    if (r.webkitMatchesSelector)
                      return r.webkitMatchesSelector(t)
                    if (r.mozMatchesSelector) return r.mozMatchesSelector(t)
                    if (r.msMatchesSelector) return r.msMatchesSelector(t)
                    for (s = a(t), i = 0; i < s.length; i++)
                      if (s[i] === this[0]) return !0
                    return !1
                  }
                  if (t === document) return this[0] === document
                  if (t === window) return this[0] === window
                  if (t.nodeType || t instanceof e) {
                    for (s = t.nodeType ? [t] : t, i = 0; i < s.length; i++)
                      if (s[i] === this[0]) return !0
                    return !1
                  }
                  return !1
                },
                index: function () {
                  if (this[0]) {
                    for (
                      var e = this[0], a = 0;
                      null !== (e = e.previousSibling);

                    )
                      1 === e.nodeType && a++
                    return a
                  }
                },
                eq: function (a) {
                  if (void 0 === a) return this
                  var t,
                    s = this.length
                  return new e(
                    a > s - 1
                      ? []
                      : a < 0
                      ? (t = s + a) < 0
                        ? []
                        : [this[t]]
                      : [this[a]]
                  )
                },
                append: function (a) {
                  var t, s
                  for (t = 0; t < this.length; t++)
                    if ('string' == typeof a) {
                      var i = document.createElement('div')
                      for (i.innerHTML = a; i.firstChild; )
                        this[t].appendChild(i.firstChild)
                    } else if (a instanceof e)
                      for (s = 0; s < a.length; s++) this[t].appendChild(a[s])
                    else this[t].appendChild(a)
                  return this
                },
                prepend: function (a) {
                  var t, s
                  for (t = 0; t < this.length; t++)
                    if ('string' == typeof a) {
                      var i = document.createElement('div')
                      for (
                        i.innerHTML = a, s = i.childNodes.length - 1;
                        s >= 0;
                        s--
                      )
                        this[t].insertBefore(
                          i.childNodes[s],
                          this[t].childNodes[0]
                        )
                    } else if (a instanceof e)
                      for (s = 0; s < a.length; s++)
                        this[t].insertBefore(a[s], this[t].childNodes[0])
                    else this[t].insertBefore(a, this[t].childNodes[0])
                  return this
                },
                insertBefore: function (e) {
                  for (var t = a(e), s = 0; s < this.length; s++)
                    if (1 === t.length)
                      t[0].parentNode.insertBefore(this[s], t[0])
                    else if (t.length > 1)
                      for (var i = 0; i < t.length; i++)
                        t[i].parentNode.insertBefore(
                          this[s].cloneNode(!0),
                          t[i]
                        )
                },
                insertAfter: function (e) {
                  for (var t = a(e), s = 0; s < this.length; s++)
                    if (1 === t.length)
                      t[0].parentNode.insertBefore(this[s], t[0].nextSibling)
                    else if (t.length > 1)
                      for (var i = 0; i < t.length; i++)
                        t[i].parentNode.insertBefore(
                          this[s].cloneNode(!0),
                          t[i].nextSibling
                        )
                },
                next: function (t) {
                  return this.length > 0
                    ? t
                      ? this[0].nextElementSibling &&
                        a(this[0].nextElementSibling).is(t)
                        ? new e([this[0].nextElementSibling])
                        : new e([])
                      : this[0].nextElementSibling
                      ? new e([this[0].nextElementSibling])
                      : new e([])
                    : new e([])
                },
                nextAll: function (t) {
                  var s = [],
                    i = this[0]
                  if (!i) return new e([])
                  for (; i.nextElementSibling; ) {
                    var r = i.nextElementSibling
                    t ? a(r).is(t) && s.push(r) : s.push(r), (i = r)
                  }
                  return new e(s)
                },
                prev: function (t) {
                  return this.length > 0
                    ? t
                      ? this[0].previousElementSibling &&
                        a(this[0].previousElementSibling).is(t)
                        ? new e([this[0].previousElementSibling])
                        : new e([])
                      : this[0].previousElementSibling
                      ? new e([this[0].previousElementSibling])
                      : new e([])
                    : new e([])
                },
                prevAll: function (t) {
                  var s = [],
                    i = this[0]
                  if (!i) return new e([])
                  for (; i.previousElementSibling; ) {
                    var r = i.previousElementSibling
                    t ? a(r).is(t) && s.push(r) : s.push(r), (i = r)
                  }
                  return new e(s)
                },
                parent: function (e) {
                  for (var t = [], s = 0; s < this.length; s++)
                    e
                      ? a(this[s].parentNode).is(e) &&
                        t.push(this[s].parentNode)
                      : t.push(this[s].parentNode)
                  return a(a.unique(t))
                },
                parents: function (e) {
                  for (var t = [], s = 0; s < this.length; s++)
                    for (var i = this[s].parentNode; i; )
                      e ? a(i).is(e) && t.push(i) : t.push(i),
                        (i = i.parentNode)
                  return a(a.unique(t))
                },
                find: function (a) {
                  for (var t = [], s = 0; s < this.length; s++)
                    for (
                      var i = this[s].querySelectorAll(a), r = 0;
                      r < i.length;
                      r++
                    )
                      t.push(i[r])
                  return new e(t)
                },
                children: function (t) {
                  for (var s = [], i = 0; i < this.length; i++)
                    for (var r = this[i].childNodes, n = 0; n < r.length; n++)
                      t
                        ? 1 === r[n].nodeType && a(r[n]).is(t) && s.push(r[n])
                        : 1 === r[n].nodeType && s.push(r[n])
                  return new e(a.unique(s))
                },
                remove: function () {
                  for (var e = 0; e < this.length; e++)
                    this[e].parentNode &&
                      this[e].parentNode.removeChild(this[e])
                  return this
                },
                add: function () {
                  var e, t
                  for (e = 0; e < arguments.length; e++) {
                    var s = a(arguments[e])
                    for (t = 0; t < s.length; t++)
                      (this[this.length] = s[t]), this.length++
                  }
                  return this
                }
              }),
              (a.fn = e.prototype),
              (a.unique = function (e) {
                for (var a = [], t = 0; t < e.length; t++)
                  -1 === a.indexOf(e[t]) && a.push(e[t])
                return a
              }),
              a
            )
          })(),
          n = ['jQuery', 'Zepto', 'Dom7'],
          o = 0;
        o < n.length;
        o++
      )
        window[n[o]] && l(window[n[o]])
      function l(e) {
        e.fn.swiper = function (a) {
          var t
          return (
            e(this).each(function () {
              var e = new s(this, a)
              t || (t = e)
            }),
            t
          )
        }
      }
      ;(i = void 0 === r ? window.Dom7 || window.Zepto || window.jQuery : r) &&
        ('transitionEnd' in i.fn ||
          (i.fn.transitionEnd = function (e) {
            var a,
              t = [
                'webkitTransitionEnd',
                'transitionend',
                'oTransitionEnd',
                'MSTransitionEnd',
                'msTransitionEnd'
              ],
              s = this
            function i(r) {
              if (r.target === this)
                for (e.call(this, r), a = 0; a < t.length; a++) s.off(t[a], i)
            }
            if (e) for (a = 0; a < t.length; a++) s.on(t[a], i)
            return this
          }),
        'transform' in i.fn ||
          (i.fn.transform = function (e) {
            for (var a = 0; a < this.length; a++) {
              var t = this[a].style
              t.webkitTransform =
                t.MsTransform =
                t.msTransform =
                t.MozTransform =
                t.OTransform =
                t.transform =
                  e
            }
            return this
          }),
        'transition' in i.fn ||
          (i.fn.transition = function (e) {
            'string' != typeof e && (e += 'ms')
            for (var a = 0; a < this.length; a++) {
              var t = this[a].style
              t.webkitTransitionDuration =
                t.MsTransitionDuration =
                t.msTransitionDuration =
                t.MozTransitionDuration =
                t.OTransitionDuration =
                t.transitionDuration =
                  e
            }
            return this
          }),
        'outerWidth' in i.fn ||
          (i.fn.outerWidth = function (e) {
            return this.length > 0
              ? e
                ? this[0].offsetWidth +
                  parseFloat(this.css('margin-right')) +
                  parseFloat(this.css('margin-left'))
                : this[0].offsetWidth
              : null
          })),
        (window.Swiper = s)
    })(),
      (e.exports = window.Swiper)
  }
})
