<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>编辑商品</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <!-- 修改 xm-select 为本地资源 -->
  <link rel="stylesheet" href="/layuiadmin/layui/css/xm-select.css">
  <script src="/layuiadmin/layui/xm-select.js"></script>
</head>
<body>

  <div class="layui-form" lay-filter="layuiadmin-app-form-list" id="layuiadmin-app-form-list" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
      <label class="layui-form-label">商品ID</label>
      <div class="layui-input-inline">
        <input type="text" name="id" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input" value="{$goodsAll.id}" readonly style="background-color: #f5f5f5">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">商品名称</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_name" lay-verify="required" placeholder="商品名称" autocomplete="off" class="layui-input" value="{$goodsAll.goods_name}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">选择分类</label>
      <div class="layui-input-inline">
        <select id="select" name="goods_class" lay-verify="required">
            {foreach $goodsclassAll as $index=>$val}
              <option value="{$val.id}">{$val.cl_name}</option>
            {/foreach}
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">商品主图</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img" lay-verify="required" placeholder="请上传图片" autocomplete="off" class="layui-input" value="{$goodsAll.goods_img}">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-img1">上传图片</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">商品图片2</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img2" placeholder="选填" autocomplete="off" class="layui-input" value="{$goodsAll.goods_img2}">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-img2">上传图片</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">商品图片3</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img3" placeholder="选填" autocomplete="off" class="layui-input" value="{$goodsAll.goods_img3}">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-img3">上传图片</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">商品图片4</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_img4" placeholder="选填" autocomplete="off" class="layui-input" value="{$goodsAll.goods_img4}">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-img4">上传图片</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">商品视频</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_video" placeholder="选填" autocomplete="off" class="layui-input" value="{$goodsAll.goods_video}">
      </div>
      <button style="float: left;" type="button" class="layui-btn" id="upload-video">上传视频</button> 
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">永久版定价</label>
      <div class="layui-input-inline">
        <input type="number" name="permanent_price2" lay-verify="required" placeholder="请输入永久版定价" autocomplete="off" class="layui-input" value="{$goodsAll.permanent_price2}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">销量</label>
      <div class="layui-input-inline">
        <input type="text" name="goods_xl" lay-verify="required" placeholder="默认为0" autocomplete="off" class="layui-input" value="{$goodsAll.goods_xl}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">是否允许提取</label>
      <div class="layui-input-inline">
        <select name="limited" lay-verify="required">
          <option value="0" {$goodsAll.limited == 0 ? 'selected' : ''}>不允许</option>
          <option value="1" {$goodsAll.limited == 1 ? 'selected' : ''}>允许</option>
        </select>
      </div>
    </div>

    <!-- 添加在线版状态控制 -->
    <div class="layui-form-item">
      <label class="layui-form-label">在线版状态</label>
      <div class="layui-input-inline">
        <select name="online_status" lay-verify="required">
          <option value="0" {$goodsAll.online_status == 0 ? 'selected' : ''}>不可售</option>
          <option value="1" {$goodsAll.online_status == 1 ? 'selected' : ''}>可售</option>
        </select>
      </div>
      <div class="layui-form-mid layui-word-aux">控制时租、日租、永久版是否可售</div>
    </div>

    <!-- 添加离线版状态控制 -->
    <div class="layui-form-item">
      <label class="layui-form-label">离线版状态</label>
      <div class="layui-input-inline">
        <select name="offline_status" lay-verify="required">
          <option value="0" {$goodsAll.offline_status == 0 ? 'selected' : ''}>不可售</option>
          <option value="1" {$goodsAll.offline_status == 1 ? 'selected' : ''}>可售</option>
        </select>
      </div>
      <div class="layui-form-mid layui-word-aux">控制离线版是否可售</div>
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label">描述</label>
      <div class="layui-input-inline" style="width: 800px;">
        <textarea id="goods_des" name="goods_des" lay-verify="content" style="width: 100%; height: 300px;" autocomplete="off" class="layui-textarea">{$goodsAll.goods_des}</textarea>
      </div>
    </div>

    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="layuiadmin-app-form-submit" id="layuiadmin-app-form-submit" value="确认添加">
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
  document.querySelector("#select").value="{$goodsAll.goods_class}";
  layui.config({
    base: '../../../layuiadmin/'
  }).extend({
    index: 'lib/index'
  }).use(['index', 'form', 'upload', 'layedit'], function(){
    var $ = layui.$
    ,form = layui.form
    ,upload = layui.upload
    ,layedit = layui.layedit;
    
    // 建立编辑器
    var editIndex = layedit.build('goods_des', {
      height: 300,
      uploadImage: {
        url: '/uploads',
        type: 'get'
      }
    });

    // 自定义验证规则
    form.verify({
      content: function(value) {
        layedit.sync(editIndex);
      }
    });
    
    form.render();

    //图片上传
    upload.render({
      elem: '#upload-img1'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });

    upload.render({
      elem: '#upload-img2'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });

    upload.render({
      elem: '#upload-img3'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });

    upload.render({
      elem: '#upload-img4'
      ,url: '/uploads'
      ,accept: 'images'
      ,method: 'get'
      ,acceptMime: 'image/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });

    //视频上传
    upload.render({
      elem: '#upload-video'
      ,url: '/uploads'
      ,accept: 'video'
      ,method: 'get'
      ,acceptMime: 'video/*'
      ,done: function(res){
        $(this.item).prev("div").children("input").val(res.data.src)
      }
    });
  })
  </script>
</body>
</html>