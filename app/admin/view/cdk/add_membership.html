<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增会员卡密</title>
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
    <style>
        body {
            background: #f8f9fa;
            margin: 0;
            padding: 15px;
            box-sizing: border-box;
        }
        .layui-card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .layui-card-header {
            background: linear-gradient(135deg, #009688 0%, #00bcd4 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            padding: 15px 20px;
        }
        .layui-card-header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        .layui-card-body {
            padding: 20px;
            max-height: calc(100vh - 150px);
            overflow-y: auto;
        }
        .layui-form-item {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
        }
        .layui-form-label {
            background: #f0f9ff;
            border: 1px solid #e0e6ed;
            border-radius: 6px;
            width: 140px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            margin-right: 15px;
            flex-shrink: 0;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
        }
        .layui-input-block {
            flex: 1;
            margin-left: 0;
        }
        .layui-input, .layui-select, select {
            border-radius: 6px;
            border: 1px solid #e0e6ed;
            width: 100%;
            height: 40px;
            line-height: 20px;
            box-sizing: border-box;
            padding: 10px 15px;
            vertical-align: middle;
        }
        .layui-input:focus {
            border-color: #009688;
            box-shadow: 0 0 0 2px rgba(0, 150, 136, 0.2);
        }
        .layui-input:focus {
            border-color: #009688;
            box-shadow: 0 0 0 2px rgba(0, 150, 136, 0.2);
        }
        .layui-btn {
            border-radius: 20px;
            padding: 10px 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            height: 40px;
            min-width: 100px;
            font-size: 14px;
        }
        .layui-btn-normal {
            background: linear-gradient(135deg, #009688 0%, #00bcd4 100%);
        }

        /* 按钮容器居中 */
        .button-container .layui-input-block {
            text-align: center;
        }
    </style>
</head>

<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2>🎫 新增会员卡密</h2>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" id="addMembershipCdkForm">
                <!-- 会员类型选择框 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">会员类型</label>
                    <div class="layui-input-block">
                        <select name="membership_type" id="membershipType" lay-filter="membershipType" lay-search required>
                            <option value="">请选择会员类型</option>
                            {foreach $membershipTypes as $type}
                            <option value="{$type.id}" data-validity="{$type.validity_period}">{$type.membership_type} (¥{$type.price})</option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <!-- 有效期显示（只读） -->
                <div class="layui-form-item">
                    <label class="layui-form-label">有效期(月)</label>
                    <div class="layui-input-block">
                        <input type="text" id="validityPeriodDisplay" class="layui-input" readonly>
                        <input type="hidden" name="validity_period" id="validityPeriod">
                    </div>
                </div>

                <!-- 生成数量 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">生成数量</label>
                    <div class="layui-input-block">
                        <input type="number" name="num" id="generateNum" placeholder="请输入生成数量" autocomplete="off" class="layui-input" value="1" min="1" max="100" required>
                    </div>
                </div>

                <!-- 提交和重置按钮 -->
                <div class="layui-form-item button-container">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="submitAddMembershipCdk">立即提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>

                <!-- 生成预览 -->
                <div class="layui-form-item">
                    <div id="cdkPreview" class="layui-input-block" style="color: #5FB878; font-weight: bold;"></div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script>
    layui.config({
        base: '/layuiadmin/' // 静态资源所在路径
    }).extend({
        index: 'lib/index' // 主入口模块
    }).use(['index', 'form'], function () {
        var $ = layui.$, form = layui.form;

        // 监听会员类型选择，自动填充有效期
        form.on('select(membershipType)', function(data){
            var validityPeriod = $(data.elem).find("option:selected").attr("data-validity");
            $('#validityPeriodDisplay').val(validityPeriod);
            $('#validityPeriod').val(validityPeriod);
        });

        // 监听表单提交
        form.on('submit(submitAddMembershipCdk)', function (data) {
            // 验证表单
            if(!data.field.membership_type) {
                layer.msg('请选择会员类型', {icon: 2});
                return false;
            }
            
            var num = parseInt(data.field.num);
            if(isNaN(num) || num < 1 || num > 100) {
                layer.msg('生成数量必须在1-100之间', {icon: 2});
                return false;
            }
            
            $.ajax({
                url: '/admin/CdkController/addMembershipCdk',
                type: 'POST',
                data: data.field,
                success: function (response) {
                    if (response.msg === 1) {
                        if (response.cdk_codes && response.cdk_codes.length > 0) {
                            // 批量生成的情况
                            $('#cdkPreview').html('生成成功，共 ' + response.cdk_codes.length + ' 个会员卡密');
                            showCdkCodesModal(response.cdk_codes, '会员卡密');
                        } else {
                            // 单个生成的情况
                            $('#cdkPreview').text('生成成功: ' + response.cdk_code);
                            showSingleCdkModal(response.cdk_code, '会员卡密');
                        }

                        // 不自动关闭，等用户操作完弹窗后再提示
                        layer.msg('🎉 会员卡密生成成功！', { icon: 1, time: 2000 });
                    } else {
                        layer.msg('添加失败：' + response.content, { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('服务器错误，请稍后再试', { icon: 2 });
                }
            });
            return false;
        });

        // 显示批量生成的卡密弹窗
        function showCdkCodesModal(codes, type) {
            var content = '<div style="max-height: 400px; overflow-y: auto;">';
            content += '<div style="margin-bottom: 15px; text-align: center;"><strong>🎉 生成的' + type + '列表</strong></div>';
            codes.forEach((code, index) => {
                content += `
                    <div style="display: flex; align-items: center; padding: 8px; border: 1px solid #e8e8e8; margin-bottom: 5px; border-radius: 6px; background: #f9f9f9;">
                        <span style="flex: 1; font-family: monospace; font-size: 13px; color: #333;">${index + 1}. ${code}</span>
                        <button onclick="copyToClipboard('${code}'); closeModalAfterCopy();" style="background: #009688; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">📋 复制</button>
                    </div>
                `;
            });
            content += '</div>';

            var modalIndex = layer.open({
                type: 1,
                title: '🎫 生成的' + type + '列表 (' + codes.length + '个)',
                area: ['600px', '500px'],
                content: content,
                btn: ['📋 全部复制', '关闭'],
                yes: function() {
                    var allCodes = codes.join('\n');
                    copyToClipboard(allCodes);
                    closeModalAfterCopy();
                },
                btn2: function() {
                    closeCurrentPage();
                }
            });
            window.currentModalIndex = modalIndex;
        }

        // 显示单个卡密弹窗
        function showSingleCdkModal(code, type) {
            var content = `
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 24px; margin-bottom: 15px;">🎉</div>
                    <div style="font-size: 18px; color: #009688; font-weight: bold; margin-bottom: 15px;">${type}生成成功！</div>
                    <div style="margin-bottom: 10px; color: #666;">您的卡密码：</div>
                    <div style="padding: 15px; background: #f0f9ff; border: 2px dashed #009688; border-radius: 8px; font-family: monospace; word-break: break-all; font-size: 16px; color: #333; margin-bottom: 15px;">
                        ${code}
                    </div>
                    <button onclick="copyToClipboard('${code}'); closeModalAfterCopy();" style="background: #009688; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-size: 14px;">
                        📋 复制卡密
                    </button>
                </div>
            `;

            var modalIndex = layer.open({
                type: 1,
                title: '🎫 ' + type + '生成成功',
                area: ['450px', '350px'],
                content: content,
                btn: ['关闭'],
                yes: function() {
                    closeCurrentPage();
                }
            });
            window.currentModalIndex = modalIndex;
        }

        // 将函数定义为全局函数
        window.copyToClipboard = function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    layer.msg('📋 复制成功！', {icon: 1});
                }).catch(() => {
                    // 如果现代API失败，使用兼容性方案
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // 兼容性方案
                fallbackCopyTextToClipboard(text);
            }
        };

        // 兼容性复制方案
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.top = "-9999px";
            textArea.style.left = "-9999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    layer.msg('📋 复制成功！', {icon: 1});
                } else {
                    layer.msg('复制失败，请手动复制', {icon: 2});
                }
            } catch (err) {
                layer.msg('复制失败，请手动复制', {icon: 2});
            }

            document.body.removeChild(textArea);
        }

        // 复制后关闭弹窗
        window.closeModalAfterCopy = function() {
            setTimeout(function() {
                if (window.currentModalIndex) {
                    layer.close(window.currentModalIndex);
                }
                closeCurrentPage();
            }, 1000); // 1秒后关闭
        };

        // 关闭当前页面
        function closeCurrentPage() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
            parent.layui.table.reload('LAY-app-cdk-list');
        }
    });
</script>
</body>

</html>