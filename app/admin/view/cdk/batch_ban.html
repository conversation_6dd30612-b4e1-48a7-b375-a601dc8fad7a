<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>批量封禁卡密 - 管理后台</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css">
    <style>
        .batch-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .batch-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .batch-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .cdk-textarea {
            min-height: 200px !important;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }

        .help-text {
            color: #999;
            font-size: 12px;
            margin-top: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .result-container {
            margin-top: 20px;
            display: none;
        }

        .result-summary {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .result-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result-details {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .detail-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }

        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="batch-container">
        <div class="batch-header">
            <h1>🚫 批量封禁卡密</h1>
            <p>一次性封禁多张卡密，提高管理效率</p>
        </div>

        <div class="warning-box">
            <strong>⚠️ 重要提醒：</strong>
            <ul style="margin: 10px 0 0 20px;">
                <li>批量封禁操作不可逆，请谨慎操作</li>
                <li>如果卡密已被使用，将自动扣除用户对应权益</li>
                <li>单次最多可封禁100张卡密</li>
                <li>每行输入一张卡密码，系统会自动去除空行</li>
            </ul>
        </div>

        <div class="batch-form">
            <form class="layui-form" lay-filter="batchBanForm">
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">卡密列表</label>
                    <div class="layui-input-block">
                        <textarea name="cdk_codes" placeholder="请输入要封禁的卡密码，一行一个&#10;例如：&#10;ABC123DEF456&#10;XYZ789GHI012&#10;..."
                                  class="layui-textarea cdk-textarea" required></textarea>
                        <div class="help-text">
                            💡 每行输入一张卡密码，支持批量粘贴，最多100张
                        </div>
                    </div>
                </div>

                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">封禁原因</label>
                    <div class="layui-input-block">
                        <textarea name="reason" placeholder="请输入批量封禁的原因（选填）" class="layui-textarea"></textarea>
                        <div class="help-text">
                            📝 封禁原因将应用到所有卡密，便于后续查询和管理
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-danger layui-btn-lg" lay-submit lay-filter="batchBan">
                            🚫 开始批量封禁
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary layui-btn-lg">
                            🔄 重置表单
                        </button>
                        <a href="/admin/CdkController/banCdk" class="layui-btn layui-btn-normal layui-btn-lg">
                            ← 返回单个封禁
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <div class="result-container" id="resultContainer">
            <div class="result-summary" id="resultSummary"></div>
            <div class="layui-card">
                <div class="layui-card-header">📊 详细结果</div>
                <div class="layui-card-body">
                    <div class="result-details" id="resultDetails"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="/layuiadmin/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.$;

            // 监听批量封禁提交
            form.on('submit(batchBan)', function(data){
                var cdkCodes = data.field.cdk_codes.trim();
                var reason = data.field.reason.trim();

                if (!cdkCodes) {
                    layer.msg('请输入要封禁的卡密码', {icon: 2});
                    return false;
                }

                // 统计卡密数量
                var cdkList = cdkCodes.split('\n').filter(function(line) {
                    return line.trim() !== '';
                });

                if (cdkList.length === 0) {
                    layer.msg('请输入有效的卡密码', {icon: 2});
                    return false;
                }

                if (cdkList.length > 100) {
                    layer.msg('单次最多只能封禁100张卡密', {icon: 2});
                    return false;
                }

                // 二次确认
                layer.confirm('确定要批量封禁 ' + cdkList.length + ' 张卡密吗？<br><br><span style="color: #f56c6c;">此操作不可逆，如果卡密已使用将扣除用户权益！</span>', {
                    icon: 3,
                    title: '确认批量封禁',
                    area: ['450px', '250px']
                }, function(index){
                    layer.close(index);

                    // 显示加载
                    var loadIndex = layer.load(1, {shade: [0.3,'#fff']});

                    $.ajax({
                        url: '/admin/CdkController/batchBanCdk',
                        type: 'POST',
                        data: {
                            cdk_codes: cdkCodes,
                            reason: reason
                        },
                        success: function(res) {
                            layer.close(loadIndex);

                            if (res.code === 1) {
                                layer.msg(res.msg, {icon: 1, time: 3000});
                                showResults(res.data);
                            } else {
                                layer.msg(res.msg, {icon: 2, time: 5000});
                            }
                        },
                        error: function() {
                            layer.close(loadIndex);
                            layer.msg('网络错误，请稍后重试', {icon: 2});
                        }
                    });
                });

                return false;
            });

            // 显示批量处理结果
            function showResults(data) {
                var container = document.getElementById('resultContainer');
                var summary = document.getElementById('resultSummary');
                var details = document.getElementById('resultDetails');

                // 显示汇总信息
                summary.className = 'result-summary result-success';
                summary.innerHTML = `
                    <h3>📊 批量封禁完成</h3>
                    <p><strong>总数：</strong>${data.total} 张 |
                       <strong>成功：</strong><span style="color: #28a745;">${data.success} 张</span> |
                       <strong>失败：</strong><span style="color: #dc3545;">${data.failed} 张</span></p>
                `;

                // 显示详细结果
                var detailsHtml = '';
                data.details.forEach(function(item) {
                    var statusClass = item.status === 'success' ? 'status-success' : 'status-failed';
                    var statusText = item.status === 'success' ? '✅ 成功' : '❌ 失败';

                    detailsHtml += `
                        <div class="detail-item">
                            <span><strong>${item.cdk_code}</strong></span>
                            <span>
                                <span class="${statusClass}">${statusText}</span>
                                <span style="margin-left: 10px; color: #666;">${item.message}</span>
                            </span>
                        </div>
                    `;
                });

                details.innerHTML = detailsHtml;
                container.style.display = 'block';

                // 滚动到结果区域
                container.scrollIntoView({ behavior: 'smooth' });
            }
        });
    </script>
</body>
</html>
