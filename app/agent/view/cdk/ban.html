<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>封禁卡密</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <style>
        .ban-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .ban-form {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .danger-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .info-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid #dc3545;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #1f2937;
            font-weight: bold;
        }
        .result-box {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
    </style>
</head>
<body>
    <div class="ban-container">
        <div class="ban-form">
            <h2 style="text-align: center; margin-bottom: 30px; color: #dc3545;">🚫 封禁卡密</h2>
            
            <div class="warning-box">
                <strong>📢 代理权限说明：</strong>
                <ul style="margin: 10px 0 0 20px;">
                    <li>您只能封禁自己生成的卡密</li>
                    <li>无法封禁其他代理或管理员生成的卡密</li>
                </ul>
            </div>
            
            <div class="danger-box">
                <strong>⚠️ 重要警告：</strong>
                <ul style="margin: 10px 0 0 20px;">
                    <li>封禁操作不可逆，请谨慎操作</li>
                    <li>未使用的卡密将被直接封禁，无法再使用</li>
                    <li>已使用的卡密将扣除用户对应权益：</li>
                    <ul style="margin-left: 20px;">
                        <li><strong>永久版/离线卡密</strong>：删除用户对应的游戏账号</li>
                        <li><strong>会员/在线卡密</strong>：扣除用户对应的时间</li>
                    </ul>
                </ul>
            </div>
            
            <form class="layui-form" lay-filter="banForm">
                <div class="layui-form-item">
                    <label class="layui-form-label">卡密码</label>
                    <div class="layui-input-block">
                        <input type="text" name="cdk_code" placeholder="请输入要封禁的卡密码" autocomplete="off" class="layui-input" required>
                    </div>
                </div>
                
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">封禁原因</label>
                    <div class="layui-input-block">
                        <textarea name="reason" placeholder="请输入封禁原因（必填）" class="layui-textarea" required></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-danger" lay-submit lay-filter="banCdk">🚫 确认封禁</button>
                        <button type="reset" class="layui-btn layui-btn-primary">🔄 重置</button>
                    </div>
                </div>
            </form>
        </div>
        
        <div id="resultBox" class="result-box">
            <div id="resultContent"></div>
        </div>
    </div>

    <script src="/layuiadmin/layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;

        // 监听封禁提交
        form.on('submit(banCdk)', function(data){
            var cdkCode = data.field.cdk_code.trim();
            var reason = data.field.reason.trim();
            
            if (!cdkCode) {
                layer.msg('请输入卡密码', {icon: 2});
                return false;
            }
            
            if (!reason) {
                layer.msg('请输入封禁原因', {icon: 2});
                return false;
            }
            
            // 二次确认
            layer.confirm('确定要封禁卡密 "' + cdkCode + '" 吗？<br><br><span style="color: #f56c6c;">此操作不可逆，如果卡密已使用将扣除用户权益！</span>', {
                icon: 3,
                title: '确认封禁',
                area: ['400px', '200px']
            }, function(index){
                layer.close(index);
                
                // 显示加载
                var loadIndex = layer.load(1, {shade: [0.1,'#fff']});
                
                $.ajax({
                    url: '/agent/Index/banCdk',
                    type: 'POST',
                    data: {
                        cdk_code: cdkCode,
                        reason: reason
                    },
                    success: function(res) {
                        layer.close(loadIndex);
                        
                        if (res.code === 1) {
                            layer.msg(res.msg, {icon: 1, time: 3000});
                            
                            // 显示结果
                            var resultBox = $('#resultBox');
                            var resultContent = $('#resultContent');
                            resultBox.show();
                            resultContent.html('<div style="color: #28a745; text-align: center; padding: 20px;"><h3>✅ ' + res.msg + '</h3></div>');
                            
                            // 重置表单
                            form.val('banForm', {
                                cdk_code: '',
                                reason: ''
                            });
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 3000});
                            
                            // 显示错误
                            var resultBox = $('#resultBox');
                            var resultContent = $('#resultContent');
                            resultBox.show();
                            resultContent.html('<div style="color: #dc3545; text-align: center; padding: 20px;"><h3>❌ ' + res.msg + '</h3></div>');
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('封禁失败，请稍后重试', {icon: 2});
                    }
                });
            });
            
            return false;
        });
    });
    </script>
</body>
</html>
