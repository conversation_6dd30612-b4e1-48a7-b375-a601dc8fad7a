<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增CDK卡密</title>
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
    <style>
        .cdk-form-container {
            background: #f8f9fa;
            min-height: 100vh;
            padding: 15px;
            box-sizing: border-box;
        }
        .cdk-form-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 100%;
            margin: 0 auto;
            height: auto;
        }
        .cdk-form-header {
            background: linear-gradient(135deg, #009688 0%, #00bcd4 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .cdk-form-header h2 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
        }
        .cdk-form-header .subtitle {
            margin-top: 5px;
            opacity: 0.9;
            font-size: 13px;
        }
        .cdk-form-body {
            padding: 25px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }
        .form-step {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #009688;
        }
        .step-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #009688 0%, #00bcd4 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
            font-size: 14px;
        }
        .step-content h3 {
            margin: 0 0 3px 0;
            color: #333;
            font-size: 15px;
            font-weight: 600;
        }
        .step-content p {
            margin: 0;
            color: #666;
            font-size: 12px;
        }
        .enhanced-form-item {
            margin-bottom: 20px;
        }
        .enhanced-form-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .enhanced-form-item .layui-form-label {
            background: #f0f9ff;
            border: 1px solid #e0e6ed;
            color: #333;
            font-weight: 500;
            border-radius: 6px;
            width: 140px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            margin-right: 15px;
            flex-shrink: 0;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        .enhanced-form-item .layui-input-block {
            flex: 1;
            margin-left: 0;
        }
        .enhanced-form-item .layui-input,
        .enhanced-form-item .layui-select,
        .enhanced-form-item select {
            border-radius: 6px;
            border: 1px solid #e0e6ed;
            transition: all 0.3s ease;
            width: 100%;
            height: 40px;
            line-height: 20px;
            box-sizing: border-box;
            padding: 10px 15px;
            vertical-align: middle;
        }
        .enhanced-form-item .layui-input:focus {
            border-color: #009688;
            box-shadow: 0 0 0 2px rgba(0, 150, 136, 0.2);
        }
        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .quantity-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .quantity-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #009688;
            background: white;
            color: #009688;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            font-size: 16px;
            box-sizing: border-box;
        }
        .quantity-btn:hover {
            background: #009688;
            color: white;
            transform: scale(1.05);
        }
        .quantity-input {
            width: 70px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            height: 40px;
            line-height: 20px;
            box-sizing: border-box;
            padding: 10px;
            vertical-align: middle;
        }
        .quick-select {
            display: flex;
            gap: 6px;
            margin-top: 8px;
            flex-wrap: wrap;
        }
        .quick-btn {
            padding: 4px 10px;
            background: #e0f2f1;
            color: #009688;
            border: 1px solid #009688;
            border-radius: 12px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s ease;
        }
        .quick-btn:hover {
            background: #009688;
            color: white;
        }
        .submit-section {
            background: #f0f9ff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .submit-btn {
            background: linear-gradient(135deg, #009688 0%, #00bcd4 100%);
            border: none;
            border-radius: 20px;
            padding: 10px 25px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            height: 40px;
            min-width: 120px;
        }
        .submit-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(0, 150, 136, 0.3);
        }
        .preview-section {
            background: linear-gradient(135deg, #e0f2f1 0%, #f3e5f5 100%);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .preview-text {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        .loading-spinner {
            display: none;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #009688;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
            flex-shrink: 0;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success-animation {
            animation: successPulse 0.6s ease-in-out;
        }
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .cdk-form-container {
                padding: 10px;
            }
            .cdk-form-body {
                padding: 15px;
            }
            .enhanced-form-item {
                flex-direction: column;
                align-items: flex-start;
            }
            .enhanced-form-item .layui-form-label {
                width: 100%;
                margin-right: 0;
                margin-bottom: 8px;
                font-size: 13px;
            }
            .enhanced-form-item .layui-input-block {
                width: 100%;
            }
            .form-step {
                padding: 12px;
            }
            .step-content h3 {
                font-size: 14px;
            }
            .step-content p {
                font-size: 11px;
            }
            .quantity-selector {
                justify-content: center;
            }
            .quick-select {
                justify-content: center;
            }
        }
    </style>
</head>

<body>
<div class="cdk-form-container">
    <div class="cdk-form-card">
        <div class="cdk-form-header">
            <h2>🎫 新增CDK卡密</h2>
            <div class="subtitle">快速生成游戏账号卡密，支持批量操作</div>
        </div>
        <div class="cdk-form-body">
            <!-- 步骤指示 -->
            <div class="form-step">
                <div class="step-icon">1</div>
                <div class="step-content">
                    <h3>选择商品和卡密类型</h3>
                    <p>选择要生成卡密的游戏商品和卡密有效期类型</p>
                </div>
            </div>

            <form class="layui-form" id="addCdkForm">
                <!-- 商品类型选择框 -->
                <div class="enhanced-form-item">
                    <label class="layui-form-label">🎮 商品类型</label>
                    <div class="layui-input-block">
                        <select name="goods_id" id="goodsType" lay-filter="goodsType" lay-search required>
                            <option value="">请选择游戏商品</option>
                            <!-- 通过 JavaScript 动态添加商品名称选项 -->
                        </select>
                    </div>
                </div>

                <!-- 卡密类型选择框 -->
                <div class="enhanced-form-item">
                    <label class="layui-form-label">⏰ 卡密类型</label>
                    <div class="layui-input-block">
                        <select name="cdk_type" id="cdkType" lay-filter="cdkType" required>
                            <option value="">请选择有效期类型</option>
                            <option value="year">🗓️ 年卡 (365天)</option>
                            <option value="month">📅 月卡 (30天)</option>
                            <option value="day">📆 日卡 (自定义天数)</option>
                            <option value="hour">⏱️ 小时卡 (自定义小时)</option>
                        </select>
                    </div>
                </div>

                <!-- 时长输入框 -->
                <div class="enhanced-form-item" id="durationField" style="display: none;">
                    <label class="layui-form-label">⏳ 时长设置</label>
                    <div class="layui-input-block">
                        <input type="number" name="expiry_date2" id="expiryDate2" placeholder="请输入具体时长数值" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux" id="durationHint" style="margin-top: 5px; color: #999; font-size: 12px;">根据卡密类型输入对应的数值</div>
                    </div>
                </div>

                <!-- 步骤指示 -->
                <div class="form-step">
                    <div class="step-icon">2</div>
                    <div class="step-content">
                        <h3>设置生成数量</h3>
                        <p>选择要生成的卡密数量，支持批量生成最多100张</p>
                    </div>
                </div>

                <!-- 生成数量 -->
                <div class="enhanced-form-item">
                    <label class="layui-form-label">🔢 生成数量</label>
                    <div class="layui-input-block">
                        <div class="quantity-controls">
                            <div class="quantity-selector">
                                <div class="quantity-btn" onclick="decreaseQuantity()">-</div>
                                <input type="number" name="num" id="generateNum" class="layui-input quantity-input" value="1" min="1" max="100" required>
                                <div class="quantity-btn" onclick="increaseQuantity()">+</div>
                            </div>
                            <div class="quick-select">
                                <span style="color: #666; font-size: 12px; margin-right: 10px;">快速选择：</span>
                                <div class="quick-btn" onclick="setQuantity(1)">1张</div>
                                <div class="quick-btn" onclick="setQuantity(5)">5张</div>
                                <div class="quick-btn" onclick="setQuantity(10)">10张</div>
                                <div class="quick-btn" onclick="setQuantity(20)">20张</div>
                                <div class="quick-btn" onclick="setQuantity(50)">50张</div>
                                <div class="quick-btn" onclick="setQuantity(100)">100张</div>
                            </div>
                            <div class="layui-form-mid layui-word-aux" style="color: #999; font-size: 12px;">
                                💡 批量生成可以提高工作效率，建议根据实际需求选择合适的数量
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交区域 -->
                <div class="submit-section">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; color: #333;">🚀 准备生成卡密</h3>
                        <p style="margin: 5px 0 0 0; color: #666; font-size: 13px;">确认信息无误后点击生成按钮</p>
                    </div>
                    <div style="text-align: center;">
                        <button class="layui-btn submit-btn" lay-submit lay-filter="submitAddCdk">
                            <span class="loading-spinner" id="loadingSpinner"></span>
                            <span id="submitText">🎯 立即生成卡密</span>
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary" style="margin-left: 15px; border-radius: 20px; display: inline-flex; align-items: center; justify-content: center; line-height: 1; height: 40px; min-width: 100px;">
                            🔄 重置表单
                        </button>
                    </div>
                </div>

                <!-- 生成预览 -->
                <div class="preview-section" id="previewSection" style="display: none;">
                    <div class="preview-text" id="cdkPreview">等待生成结果...</div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script src="/static/js/jquery/1.10.2/jquery.min.js"></script>
<script>
    layui.config({
        base: '/layuiadmin/' // 静态资源所在路径
    }).extend({
        index: 'lib/index' // 主入口模块
    }).use(['index', 'form'], function () {
        var $ = layui.$, form = layui.form;

        // 加载商品名称到商品类型下拉框
        $.get('/user/CdkExchange/getGoodsNames', function (response) {
            if (response.code === 0) {
                var goods = response.goods;
                var optionsHtml = goods.map(item => `<option value="${item.id}">${item.goods_name}</option>`).join('');
                $('#goodsType').append(optionsHtml);
                form.render('select'); // 重新渲染 select
            }
        });

        // 监听卡密类型选择
        form.on('select(cdkType)', function (data) {
            var selectedType = data.value;
            var durationField = $('#durationField');
            var durationHint = $('#durationHint');

            if (selectedType === 'year') {
                durationField.show();
                durationHint.text('年卡默认365天，可自定义年数');
                $('#expiryDate2').attr('placeholder', '请输入年数 (如: 1表示1年)');
            } else if (selectedType === 'month') {
                durationField.show();
                durationHint.text('月卡默认30天，可自定义月数');
                $('#expiryDate2').attr('placeholder', '请输入月数 (如: 3表示3个月)');
            } else if (selectedType === 'day') {
                durationField.show();
                durationHint.text('请输入具体天数');
                $('#expiryDate2').attr('placeholder', '请输入天数 (如: 7表示7天)');
            } else if (selectedType === 'hour') {
                durationField.show();
                durationHint.text('请输入具体小时数');
                $('#expiryDate2').attr('placeholder', '请输入小时数 (如: 24表示24小时)');
            } else {
                durationField.hide();
            }
        });

        // 数量控制函数
        window.increaseQuantity = function() {
            var input = $('#generateNum');
            var currentVal = parseInt(input.val()) || 1;
            if (currentVal < 100) {
                input.val(currentVal + 1);
                updateQuantityDisplay();
            }
        };

        window.decreaseQuantity = function() {
            var input = $('#generateNum');
            var currentVal = parseInt(input.val()) || 1;
            if (currentVal > 1) {
                input.val(currentVal - 1);
                updateQuantityDisplay();
            }
        };

        window.setQuantity = function(num) {
            $('#generateNum').val(num);
            updateQuantityDisplay();
            // 添加点击动画
            $('.quick-btn').removeClass('active');
            event.target.classList.add('active');
            setTimeout(() => event.target.classList.remove('active'), 200);
        };

        function updateQuantityDisplay() {
            var num = parseInt($('#generateNum').val()) || 1;
            var preview = $('#cdkPreview');
            if (num === 1) {
                preview.text('准备生成 1 张卡密');
            } else {
                preview.text('准备批量生成 ' + num + ' 张卡密');
            }
        }

        // 监听数量输入变化
        $('#generateNum').on('input', function() {
            var val = parseInt($(this).val());
            if (val > 100) $(this).val(100);
            if (val < 1) $(this).val(1);
            updateQuantityDisplay();
        });

        // 初始化预览
        updateQuantityDisplay();
        $('#previewSection').show();

        // 监听表单提交
        form.on('submit(submitAddCdk)', function (data) {
            // 验证生成数量
            var num = parseInt(data.field.num);
            if(isNaN(num) || num < 1 || num > 100) {
                layer.msg('生成数量必须在1-100之间', {icon: 2});
                return false;
            }

            // 显示加载状态
            showLoading();

            $.ajax({
                url: '/admin/CdkController/add',
                type: 'POST',
                data: data.field,
                success: function (response) {
                    hideLoading();

                    if (response.msg === 1) {
                        var previewSection = $('#previewSection');
                        var cdkPreview = $('#cdkPreview');

                        if (response.cdk_codes && response.cdk_codes.length > 0) {
                            // 批量生成的情况
                            cdkPreview.html(`
                                <div style="text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 10px;">🎉</div>
                                    <div style="font-size: 18px; color: #52c41a; font-weight: bold;">批量生成成功！</div>
                                    <div style="margin-top: 8px; color: #666;">共生成 ${response.cdk_codes.length} 张卡密</div>
                                </div>
                            `);
                            // 立即显示批量卡密弹窗
                            showCdkCodesModal(response.cdk_codes, 'CDK卡密');
                        } else {
                            // 单个生成的情况
                            cdkPreview.html(`
                                <div style="text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 10px;">✅</div>
                                    <div style="font-size: 18px; color: #52c41a; font-weight: bold;">生成成功！</div>
                                    <div style="margin-top: 8px; color: #666;">卡密已生成</div>
                                </div>
                            `);
                            // 立即显示单个卡密弹窗
                            showSingleCdkModal(response.cdk_code, 'CDK卡密');
                        }

                        // 添加成功动画
                        previewSection.addClass('success-animation');
                        setTimeout(() => previewSection.removeClass('success-animation'), 600);

                        // 不自动关闭，等用户操作完弹窗后再提示
                        layer.msg('🎉 卡密生成成功！', { icon: 1, time: 2000 });
                    } else {
                        $('#cdkPreview').html(`
                            <div style="text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                                <div style="font-size: 16px; color: #ff4d4f; font-weight: bold;">生成失败</div>
                                <div style="margin-top: 8px; color: #666;">${response.content || '未知错误'}</div>
                            </div>
                        `);
                        layer.msg('生成失败：' + response.content, { icon: 2 });
                    }
                },
                error: function () {
                    hideLoading();
                    $('#cdkPreview').html(`
                        <div style="text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                            <div style="font-size: 16px; color: #ff4d4f; font-weight: bold;">网络错误</div>
                            <div style="margin-top: 8px; color: #666;">请检查网络连接后重试</div>
                        </div>
                    `);
                    layer.msg('服务器错误，请稍后再试', { icon: 2 });
                }
            });
            return false;
        });

        // 显示加载状态
        function showLoading() {
            $('#loadingSpinner').show();
            $('#submitText').text('🔄 正在生成中...');
            $('.submit-btn').prop('disabled', true);
            $('#cdkPreview').html(`
                <div style="text-align: center;">
                    <div class="loading-spinner" style="display: inline-block; margin-right: 10px;"></div>
                    <span>正在生成卡密，请稍候...</span>
                </div>
            `);
        }

        // 隐藏加载状态
        function hideLoading() {
            $('#loadingSpinner').hide();
            $('#submitText').text('🎯 立即生成卡密');
            $('.submit-btn').prop('disabled', false);
        }

        // 显示批量生成的卡密弹窗
        function showCdkCodesModal(codes, type) {
            var content = '<div style="max-height: 400px; overflow-y: auto;">';
            content += '<div style="margin-bottom: 15px; text-align: center;"><strong>🎯 生成的' + type + '列表</strong></div>';
            codes.forEach((code, index) => {
                content += `
                    <div style="display: flex; align-items: center; padding: 8px; border: 1px solid #e8e8e8; margin-bottom: 5px; border-radius: 6px; background: #f0f9ff;">
                        <span style="flex: 1; font-family: monospace; font-size: 13px; color: #333;">${index + 1}. ${code}</span>
                        <button onclick="copyToClipboard('${code}'); closeModalAfterCopy();" style="background: #1890ff; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">📋 复制</button>
                    </div>
                `;
            });
            content += '</div>';

            var modalIndex = layer.open({
                type: 1,
                title: '🎯 生成的' + type + '列表 (' + codes.length + '个)',
                area: ['600px', '500px'],
                content: content,
                btn: ['📋 全部复制', '关闭'],
                yes: function() {
                    var allCodes = codes.join('\n');
                    copyToClipboard(allCodes);
                    closeModalAfterCopy();
                },
                btn2: function() {
                    closeCurrentPage();
                }
            });
            window.currentModalIndex = modalIndex;
        }

        // 显示单个卡密弹窗
        function showSingleCdkModal(code, type) {
            var content = `
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 24px; margin-bottom: 15px;">🎯</div>
                    <div style="font-size: 18px; color: #1890ff; font-weight: bold; margin-bottom: 15px;">${type}生成成功！</div>
                    <div style="margin-bottom: 10px; color: #666;">您的卡密码：</div>
                    <div style="padding: 15px; background: #f0f9ff; border: 2px dashed #1890ff; border-radius: 8px; font-family: monospace; word-break: break-all; font-size: 16px; color: #333; margin-bottom: 15px;">
                        ${code}
                    </div>
                    <button onclick="copyToClipboard('${code}'); closeModalAfterCopy();" style="background: #1890ff; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-size: 14px;">
                        📋 复制卡密
                    </button>
                </div>
            `;

            var modalIndex = layer.open({
                type: 1,
                title: '🎯 ' + type + '生成成功',
                area: ['450px', '350px'],
                content: content,
                btn: ['关闭'],
                yes: function() {
                    closeCurrentPage();
                }
            });
            window.currentModalIndex = modalIndex;
        }

        // 复制到剪贴板
        window.copyToClipboard = function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    layer.msg('📋 复制成功！', {icon: 1});
                });
            } else {
                // 兼容性方案
                var textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                layer.msg('📋 复制成功！', {icon: 1});
            }
        };

        // 复制后关闭弹窗
        window.closeModalAfterCopy = function() {
            setTimeout(function() {
                if (window.currentModalIndex) {
                    layer.close(window.currentModalIndex);
                }
                closeCurrentPage();
            }, 1000); // 1秒后关闭
        };

        // 关闭当前页面
        function closeCurrentPage() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
            parent.layui.table.reload('LAY-app-cdk-list');
        }
    });
</script>
</body>

</html>
