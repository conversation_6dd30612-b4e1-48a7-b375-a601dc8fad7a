<?php
use think\facade\Db;

// 应用公共文件

/**
 * 获取文件完整URL
 * @param string $url 文件路径
 * @return string
 */
function get_file_url($url = '')
{
    if (empty($url)) {
        return '';
    }
    
    // 如果已经是完整URL则直接返回
    if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) {
        return $url;
    }
    
    // 获取网站域名
    $domain = Db::name('system')->where('id', 1)->value('sy_url');
    
    // 拼接完整URL
    return rtrim($domain, '/') . $url;
}