<!DOCTYPE html>
<html lang="zh-CN">
    
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 移动设备优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <style>
        /* 响应式基础设置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.5;
            color: #333;
            background-color: #f8f9fa;
            -webkit-text-size-adjust: 100%;
            -webkit-font-smoothing: antialiased;
        }
        
        img {
            max-width: 100%;
            height: auto;
        }
        
        /* 容器样式 */
        .container-fluid {
            width: 100%;
            padding-right: 15px;
            padding-left: 15px;
            margin-right: auto;
            margin-left: auto;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            padding-right: 15px;
            padding-left: 15px;
            margin-right: auto;
            margin-left: auto;
        }
        
        @media (max-width: 1200px) {
            .container {
                max-width: 100%;
            }
        }
        
        /* 自适应导航栏 */
        .navbar {
            border-radius: 0;
            margin-bottom: 0;
        }
        
        @media (max-width: 768px) {
            .navbar-header {
                float: none;
            }
            .navbar-toggle {
                display: block;
            }
            .navbar-collapse {
                border-top: 1px solid transparent;
                box-shadow: inset 0 1px 0 rgba(255,255,255,0.1);
            }
            .navbar-collapse.collapse {
                display: none !important;
            }
            .navbar-collapse.collapse.in {
                display: block !important;
            }
            .navbar-nav {
                float: none !important;
                margin: 7.5px -15px;
            }
            .navbar-nav > li {
                float: none;
            }
            .navbar-nav > li > a {
                padding-top: 10px;
                padding-bottom: 10px;
            }
        }
        
        /* 响应式标签栏 */
        .type-wrap .warp {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            padding: 10px;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: all .3s;
        }
        
        .type-wrap .warp .name {
            width: auto;
            color: #07111b;
            line-height: 32px;
            font-weight: 700;
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .type-wrap .warp .items {
            width: auto;
            display: flex;
            flex-wrap: wrap;
            flex: 1;
        }
        
        .type-wrap .warp .items li {
            line-height: 16px;
            padding: 8px 12px;
            border-radius: 20px;
            margin: 5px;
            white-space: nowrap;
            list-style: none;
            transition: all 0.3s;
        }
        
        .type-wrap .warp .items li a {
            color: #1c1f21;
            text-decoration: none;
        }
        
        .type-wrap .warp .items li.cur {
            background-color: rgba(233,142,70,.1);
        }
        
        .type-wrap .warp .items li.cur a {
            color: #e98e46;
        }
        
        .type-wrap .warp .items li:hover {
            background-color: rgba(233,142,70,.05);
        }
        
        /* 响应式游戏列表 */
        .game-item {
            padding: 10px;
        }
        
        .thumbnail {
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s;
            border: none;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            background-color: #fff;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .thumbnail:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }
        
        .thumbnail > a {
            overflow: hidden;
            display: block;
            position: relative;
            padding-top: 56.25%; /* 16:9 比例 */
        }
        
        .thumbnail > a img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }
        
        .thumbnail:hover > a img {
            transform: scale(1.05);
        }
        
        .caption {
            padding: 15px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .caption h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #333;
        }
        
        .price-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
        }
        
        .price-container {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
        
        .original-price {
            color: #999;
            text-decoration: line-through;
            font-size: 14px;
        }
        
        .current-price {
            color: goldenrod;
            font-weight: bold;
            font-size: 18px;
        }
        
        .discount-price {
            color: #ff4757;
            font-size: 14px;
        }
        
        .member-tag {
            background-color: #ff4757;
            color: white;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            display: inline-block;
        }
        
        /* 响应式栅格适配 */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -10px;
            margin-left: -10px;
        }
        
        [class*="col-"] {
            position: relative;
            width: 100%;
            padding-right: 10px;
            padding-left: 10px;
        }
        
        /* 大屏设备 (≥992px) */
        @media (min-width: 992px) {
            .col-md-4 {
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
            }
        }
        
        /* 中等设备 (≥768px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .col-sm-6 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
        
        /* 小屏设备 (<768px) */
        @media (max-width: 767px) {
            .col-xs-12 {
                flex: 0 0 100%;
                max-width: 100%;
            }
            
            .type-wrap .warp {
                padding: 10px 5px;
            }
            
            .type-wrap .warp .items li {
                padding: 5px 10px;
                margin: 3px;
                font-size: 12px;
            }
            
            .search-title {
                font-size: 18px;
            }
            
            .caption h4 {
                font-size: 14px;
            }
            
            .current-price {
                font-size: 16px;
            }
        }
        
        /* 搜索框样式优化 */
        .search-box {
            margin: 25px 0;
            position: relative;
        }
        
        .search-title {
            margin-bottom: 15px;
            font-size: 22px;
            font-weight: 600;
            color: #333;
            position: relative;
            display: inline-block;
        }
        
        .search-title:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -5px;
            width: 40px;
            height: 3px;
            background: #e98e46;
            border-radius: 3px;
        }
        
        .search-form {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .search-input-container {
            position: relative;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0 3px 12px rgba(0,0,0,0.06);
            padding: 6px;
            display: flex;
            align-items: center;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #eee;
        }
        
        .search-input-container:hover, 
        .search-input-container:focus-within {
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-color: rgba(233,142,70,0.3);
        }
        
        .search-input-container input {
            flex: 1;
            border: none;
            height: 44px;
            font-size: 15px;
            background: transparent;
            padding: 0 15px 0 40px;
            outline: none;
            color: #333;
        }
        
        .search-input-container input::placeholder {
            color: #999;
            transition: color 0.3s;
        }
        
        .search-input-container input:focus::placeholder {
            color: #ccc;
        }
        
        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-button {
            background-color: #e98e46;
            border: none;
            color: white;
            padding: 0 25px;
            height: 44px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 15px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-button:hover {
            background-color: #d37a35;
        }
        
        .search-button:active {
            transform: translateY(1px);
        }
        
        /* 响应式调整 */
        @media (max-width: 767px) {
            .search-input-container {
                padding: 4px;
            }
            
            .search-input-container input {
                height: 40px;
                font-size: 14px;
                padding: 0 10px 0 35px;
            }
            
            .search-button {
                padding: 0 15px;
                height: 40px;
                font-size: 14px;
            }
            
            .search-title {
                font-size: 18px;
            }
            
            .search-icon {
                left: 12px;
            }
        }
        
        /* 页脚优化 */
        .footer {
            padding: 20px 0;
            margin-top: 40px;
        }
        
        .footer-content {
            position: relative;
            bottom: 0;
            width: 100%;
            text-align: center;
            padding: 10px 0;
        }
        
        .footer-content a {
            color: #666;
            text-decoration: none;
        }
        
        /* 修复部分应用样式 */
        @media (max-width: 768px) {
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
        
        /* 添加以下样式以适配谷歌浏览器 */
        .type .type-wrap .warp {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
        }
        
        .type .type-wrap .warp .items {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox; 
            display: flex;
        }
    </style>
    
    <meta name="referrer" content="strict-origin-when-cross-origin">
    <title>租号猫首页-Steam游戏帐号租用-租号猫</title>
    
    <!-- 本地化资源链接 -->
    <link rel="stylesheet" type="text/css" href="/static/products/css/kefu.css">
    <link rel="stylesheet" href="/static/products/plugins/css/animate.min.css">
    <link rel="stylesheet" href="/static/products/plugins/bootstrap-3.4.1/css/bootstrap.min.css">
    <link rel="SHORTCUT ICON" href="/static/products/img/favicon.ico" />
    <link rel="stylesheet" href="/static/products/jquery-ui/jquery-ui.css">
    <link rel="stylesheet" href="/static/products/jquery-ui/jquery-ui.structure.css">
    <link rel="stylesheet" href="/static/products/jquery-ui/jquery-ui.theme.css">
    <link rel="stylesheet" href="/static/products/jQuery_sign/css/qiandao_style.css">
    <link rel="stylesheet" href="/static/products/swiper/swiper.min.css">
    <link rel="stylesheet" href="/static/products/swiper/swiper-bundle.css">
    <link rel="stylesheet" href="/static/products/css/font-awesome.min.css">
</head>

<body>
    <!-- 导航栏 -->
    <div style="position: fixed; width: 100%; z-index: 999;">
        <nav class="navbar navbar-default" style="margin-bottom: 0px">
            <div class="container" style="width: 1230px;">
                <!-- 导航栏头部 -->
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle collapsed" data-toggle="collapse"
                            data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                        <span class="sr-only">切换导航</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="/index/" style="margin-top: 0px;">
                        <span style="font-size: 24px; font-weight: bold; color: #333;">租号猫</span>
                    </a>
                </div>
                
                <!-- 导航栏菜单 -->
                <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
                    <ul class="nav navbar-nav">
                        <li><a href="./">首页</a></li>
                        <li><a href="/user/index/feedback">工单管理</a></li>
                        <li><a href="/user/CdkExchange/index">卡密兑换</a></li>
                        <li><a href="/user/index/notice">帮助中心</a></li>
                        <li><a href="/home/<USER>">全部游戏</a></li>
                        <li><a href="/user/index/onshop">用户中心</a></li>
                    </ul>
                    <ul class="dropdown nav navbar-nav navbar-right">
                    </ul>
                </div>
            </div>
        </nav>
    </div>
    <div style="margin-top:52px">
        <!-- 搜索游戏 -->
        <div class="container search-box">
            <h2 class="search-title">搜索游戏</h2>
            <form method="get" class="search-form">
                <div class="search-input-container">
                    <span class="search-icon">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" name="keyWords" placeholder="输入游戏名称搜索" value="{$searchQuery ?? ''}" autocomplete="off">
                    <button type="submit" class="search-button">搜索</button>
                </div>
            </form>
        </div>
        <div class="container">
            <div class="top-wrap">
                <div class="type main-wrap">
                    <div class="type-wrap filter">
                        <div class="warp category-filter">
                            <span class="name">
                                标签：
                            </span>
                            <ul class="items">
                                <li data-category-id="0" class="cur">
                                    <a href="javascript:;" data-tag="" onclick="filterByTags(this)">
                                        不限
                                    </a>
                                </li>
                                {foreach $gameTags as $tag}
                                <li data-category-id="{$tag.id}">
                                    <a href="javascript:;" data-tag="{$tag.name}" onclick="filterByTags(this)">
                                        {$tag.name}
                                    </a>
                                </li>
                                {/foreach}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 游戏列表 -->
            <div class="main">
                <h2 class="search-title">游戏列表</h2>
                <div class="row">
                    {foreach $goods as $item}
                    <div class="col-xs-12 col-sm-6 col-md-4 game-item" data-tags="{$item.goods_key}">
                        <div class="thumbnail">
                            <a href="/home/<USER>/goodsDetail?id={$item.id}" tabindex="0">
                                <img src="{$item.goods_img}" alt="{$item.goods_name}">
                            </a>
                            <div class="caption">
                                <h4 title="{$item.goods_name}">{$item.goods_name}</h4>
                                <div class="price-section">
                                    <div>
                                        <img src="/static/products/img/steam.png" alt="Steam" style="width: 20px;height: 20px;">
                                        <span class="member-tag">会员免费</span>
                                    </div>
                                    <div class="price-container">
                                        <div class="original-price">￥{$item.goods_money}</div>
                                        <div class="current-price">￥{$item.permanent_price2}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
    <footer class="footer">
        <div class="footer-content">
            <span class="text-muted">
                <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">
                    闽ICP备2024030079号-1
                </a>
            </span>
        </div>
    </footer>
    
    <!-- 本地化JS资源 -->
    <script src="/static/products/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.min.js"></script>
    <script src="/static/products/js/qrcode.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        var navbarToggle = document.querySelector('.navbar-toggle');
        var navbarCollapse = document.getElementById('bs-example-navbar-collapse-1');
        
        if (navbarToggle && navbarCollapse) {
            navbarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 切换导航菜单的显示状态
                if (navbarCollapse.classList.contains('in')) {
                    navbarCollapse.classList.remove('in');
                    navbarCollapse.style.height = '0';
                    navbarCollapse.setAttribute('aria-expanded', 'false');
                } else {
                    navbarCollapse.classList.add('in');
                    navbarCollapse.style.height = 'auto';
                    navbarCollapse.setAttribute('aria-expanded', 'true');
                }
            });
            
            // 点击菜单项后自动折叠菜单（移动设备）
            var menuItems = document.querySelectorAll('.navbar-collapse .nav li a');
            menuItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        navbarCollapse.classList.remove('in');
                        navbarCollapse.style.height = '0';
                        navbarCollapse.setAttribute('aria-expanded', 'false');
                    }
                });
            });
        }
    });
    
    let selectedTags = [];
    
    function filterGames() {
        const searchQuery = document.querySelector('input[name="keyWords"]').value.toLowerCase();
        const gameItems = document.querySelectorAll('.game-item');
        
        gameItems.forEach(item => {
            const gameName = item.querySelector('h4').textContent.toLowerCase();
            const itemTags = (item.getAttribute('data-tags') || '').split(',');
            
            const matchesSearch = searchQuery === '' || gameName.includes(searchQuery);
            const matchesTags = selectedTags.length === 0 || selectedTags.some(tag => itemTags.includes(tag));
            
            item.style.display = (matchesSearch && matchesTags) ? '' : 'none';
        });
    }

    function filterByTags(element) {
        const tag = element.getAttribute('data-tag');
        const parent = element.parentElement;
        
        if(tag === '') {
            selectedTags = [];
            document.querySelectorAll('.items li').forEach(li => li.classList.remove('cur'));
            parent.classList.add('cur');
        } else {
            document.querySelector('.items li[data-category-id="0"]').classList.remove('cur');
            
            if(parent.classList.contains('cur')) {
                parent.classList.remove('cur');
                selectedTags = selectedTags.filter(t => t !== tag);
            } else {
                parent.classList.add('cur');
                selectedTags.push(tag);
            }
        }

        filterGames();
    }

    // 当仅过滤标签而不需要刷新页面时，才拦截表单提交
    document.querySelector('form').addEventListener('submit', function(e) {
        // 如果只是在用标签筛选，不需要重新提交表单
        if (selectedTags.length > 0 && document.querySelector('input[name="keyWords"]').value === '') {
            e.preventDefault();
            filterGames();
        }
        // 否则让表单正常提交，进行后端搜索
    });

    // 添加搜索框输入事件监听,实现实时前端过滤
    document.querySelector('input[name="keyWords"]').addEventListener('input', filterGames);

    // 页面加载完成后，如果URL中有搜索参数，立即触发前端过滤
    document.addEventListener('DOMContentLoaded', function() {
        // 如果搜索框有值（来自URL参数），立即执行前端过滤
        if (document.querySelector('input[name="keyWords"]').value) {
            filterGames();
        }
    });

    // 添加窗口大小改变监听，优化移动端体验
    window.addEventListener('resize', function() {
        // 对于小屏幕，自动收起导航栏
        if (window.innerWidth <= 768) {
            const navCollapse = document.querySelector('.navbar-collapse');
            if (navCollapse && navCollapse.classList.contains('in')) {
                document.querySelector('.navbar-toggle').click();
            }
        }
    });
    </script>
</body>
</html>