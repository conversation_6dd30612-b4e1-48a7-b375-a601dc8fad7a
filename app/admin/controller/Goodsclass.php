<?php
namespace app\admin\controller;

use think\facade\View;

/**
 * 商品分类控制器
 * 
 * 此控制器用于处理商品分类相关的请求，
 * 实际功能已迁移到Index控制器中，此文件仅作为兼容性处理
 */
class Goodsclass extends BaseController
{
    /**
     * 商品分类列表页面
     * 重定向到Index控制器的goodsclass方法
     */
    public function index()
    {
        // 重定向到正确的控制器方法
        return redirect('/admin/index/goodsclass');
    }
    
    /**
     * 商品分类管理页面
     * 重定向到Index控制器的goodsclass方法
     */
    public function goodsclass()
    {
        // 重定向到正确的控制器方法
        return redirect('/admin/index/goodsclass');
    }
}
