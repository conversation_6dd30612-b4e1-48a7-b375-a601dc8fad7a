

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layDate 日期组件功能演示一</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-card layadmin-header">
    <div class="layui-breadcrumb" lay-filter="breadcrumb">
      <a lay-href="">主页</a>
      <a><cite>组件</cite></a>
      <a><cite>日期时间</cite></a>
    </div>
  </div>
  
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">常规用法</div>
          <div class="layui-card-body" pad15>
            
            <div class="layui-form">
              <div class="layui-form-item">
                <div class="layui-inline">
                  <label class="layui-form-label">中文版</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-normal-cn" placeholder="yyyy-MM-dd">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">国际版</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-normal-en" placeholder="yyyy-MM-dd">
                  </div>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
      
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">五大选择器</div>
          <div class="layui-card-body" pad15>
            
            <div class="layui-form" wid100>
              <div class="layui-form-item">
                <div class="layui-inline">
                  <label class="layui-form-label">年选择器</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-type-year" placeholder="yyyy">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">年月选择器</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-type-month" placeholder="yyyy-MM">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">日期选择器</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-type-date" placeholder="yyyy-MM-dd">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">时间选择器</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-type-time" placeholder="HH:mm:ss">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">日期时间选择器</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-type-datetime" placeholder="yyyy-MM-dd HH:mm:ss">
                  </div>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
      
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">范围选择</div>
          <div class="layui-card-body" pad15>
            
            <div class="layui-form" wid100>
              <div class="layui-form-item">
                <div class="layui-inline">
                  <label class="layui-form-label">日期范围</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-range-date" placeholder=" - ">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">年范围</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-range-year" placeholder=" - ">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">年月范围</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-range-month" placeholder=" - ">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">时间范围</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-range-time" placeholder=" - ">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">日期时间范围</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-range-datetime" placeholder=" - ">
                  </div>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
      
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">自定义格式</div>
          <div class="layui-card-body" pad15>
            
            <div class="layui-form" wid100>
              <div class="layui-form-item">
                <div class="layui-inline">
                  <label class="layui-form-label">请选择日期</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-format-date1" placeholder="yyyy年MM月dd日">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">请选择日期</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-format-date2" placeholder="dd/MM/yyyy">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">请选择月份</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-format-month" placeholder="yyyyMM">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">请选择时间</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-format-time" placeholder="H点m分">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">请选择范围</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-format-range1" placeholder=" ~ ">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label">请选择范围</label>
                  <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="test-laydate-format-range2" placeholder="开始 到 结束">
                  </div>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
      
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">同时绑定多个</div>
          <div class="layui-card-body" pad15>  
            <div class="layui-form" wid100>
              <div class="layui-form-item">
                <div class="layui-inline">
                  <input type="text" class="layui-input test-laydate-item" placeholder="yyyy-MM-dd">
                </div>
                <div class="layui-inline">
                  <input type="text" class="layui-input test-laydate-item" placeholder="yyyy-MM-dd">
                </div>
                <div class="layui-inline">
                  <input type="text" class="layui-input test-laydate-item" placeholder="yyyy-MM-dd">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      
      
    </div>
  </div>
  
  <script src="/layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'laydate'], function(){
    var laydate = layui.laydate;
    
    //示例代码
    
    //常规用法
    laydate.render({
      elem: '#test-laydate-normal-cn'
    });
    
    //国际版
    laydate.render({
      elem: '#test-laydate-normal-en'
      ,lang: 'en'
    });
    
    //年选择器
    laydate.render({
      elem: '#test-laydate-type-year'
      ,type: 'year'
    });
    
    //年月选择器
    laydate.render({
      elem: '#test-laydate-type-month'
      ,type: 'month'
    });
    
    //日期选择器
    laydate.render({
      elem: '#test-laydate-type-date'
      //,type: 'date' //type 默认为 date，所以可不填
    });
    
    //时间选择器
    laydate.render({
      elem: '#test-laydate-type-time'
      ,type: 'time'
    });
    
    //日期时间选择器
    laydate.render({
      elem: '#test-laydate-type-datetime'
      ,type: 'datetime'
    });
    
    //日期范围
    laydate.render({
      elem: '#test-laydate-range-date'
      ,range: true
    });
    
    //年范围
    laydate.render({
      elem: '#test-laydate-range-year'
      ,type: 'year'
      ,range: true
    });
    
    //年月范围
    laydate.render({
      elem: '#test-laydate-range-month'
      ,type: 'month'
      ,range: true
    });
    
    //时间范围
    laydate.render({
      elem: '#test-laydate-range-time'
      ,type: 'time'
      ,range: true
    });
    
    //日期时间范围
    laydate.render({
      elem: '#test-laydate-range-datetime'
      ,type: 'datetime'
      ,range: true
    });
    
    //自定义格式
    laydate.render({
      elem: '#test-laydate-format-date1'
      ,format: 'yyyy年MM月dd日'
    });
    laydate.render({
      elem: '#test-laydate-format-date2'
      ,format: 'dd/MM/yyyy'
    });
    laydate.render({
      elem: '#test-laydate-format-month'
      ,type: 'month'
      ,format: 'yyyyMM'
    });
    laydate.render({
      elem: '#test-laydate-format-time'
      ,type: 'time'
      ,format: 'H点m分'
    });
    laydate.render({
      elem: '#test-laydate-format-range1'
      ,range: '~'
    });
    laydate.render({
      elem: '#test-laydate-format-range2'
      ,type: 'datetime'
      ,range: '到'
      ,format: 'yyyy年M月d日H时m分s秒'
    });
    
    //同时绑定多个
    lay('.test-laydate-item').each(function(){
      laydate.render({
        elem: this
        ,trigger: 'click'
      });
    });
    
  });
  </script>
</body>
</html>