<?php
namespace app\admin\model;

use think\Model;

class Cdk extends Model
{
    // 设置完整的数据表名（不加前缀）
    protected $table = 'cdk_cards';
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 自定义获取器，处理状态显示
    public function getStatusAttr($value)
    {
        $status = ['unused' => '未使用', 'used' => '已使用'];
        return $status[$value];
    }

    public function goods()
    {
        return $this->belongsTo(Goods::class, 'shop', 'id');
    }
}
